package ma.almobadara.backend.dto.getbeneficiary;

import lombok.*;
import ma.almobadara.backend.dto.administration.TagDTO;
import org.springframework.hateoas.RepresentationModel;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Set;

@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@Builder
@AllArgsConstructor
@ToString
public class GetListDTO extends RepresentationModel<GetListDTO> implements Serializable {

	private static final long serialVersionUID = 7316619986300858856L;

	private Long id;
	private String code;
	private Boolean independent;
	private Boolean archived;
	private String firstName;
	private String lastName;
	private String firstNameAr;
	private String lastNameAr;
	private String sex;
	private String phoneNumber;
	private String address;
	private String addressAr;
	private Date birthDate;
	private Long cityId;
	private String city;
	private String region;
	private String country;
	private String identityCode;
	private Long typeIdentityId;
	private String typeIdentity;
	private Set<GetServiceDTO> services;
	private String pictureUrl;
	private String pictureBase64;
	private Long beneficiaryStatutId;
	private String zoneCode;
	private String zoneName;
	private String zoneNameAr;
	private String comment;
	private String rqComplete ;
	private String rqReject;
	private String familyPhoneNumber;
	private String familyIdentityCode;
	private List<TagDTO> tags;

}
