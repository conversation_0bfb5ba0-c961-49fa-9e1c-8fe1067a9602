package ma.almobadara.backend.repository.takenInCharge;

import ma.almobadara.backend.model.beneficiary.Beneficiary;
import ma.almobadara.backend.model.takenInCharge.TakenInChargeBeneficiary;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

@Repository
public interface TakenInChargeBeneficiaryRepository extends JpaRepository<TakenInChargeBeneficiary, Long> {

    List<TakenInChargeBeneficiary> findByTakenInChargeId(Long takenInChargeId);

    @Query("SELECT t.beneficiary FROM TakenInChargeBeneficiary t WHERE t.id = :id")
    Beneficiary findBeneficiaryByTakenInChargeBeneficiaryId(@Param("id") Long id);

    //existsByBeneficiaryId

    boolean existsByBeneficiaryId(Long beneficiaryId);


    List<TakenInChargeBeneficiary> findByBeneficiaryId(Long id);
    
    List<TakenInChargeBeneficiary> findByBeneficiaryIdIn(Set<Long> beneficiaryIds);

    //countByBeneficiaryId

    Long countByBeneficiaryId(Long id);
}
