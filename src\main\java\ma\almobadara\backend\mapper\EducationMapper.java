package ma.almobadara.backend.mapper;

import ma.almobadara.backend.model.beneficiary.Beneficiary;
import ma.almobadara.backend.model.beneficiary.Education;
import ma.almobadara.backend.dto.beneficiary.BeneficiaryDTO;
import ma.almobadara.backend.dto.beneficiary.EducationDTO;
import ma.almobadara.backend.dto.getbeneficiary.GetEducationDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface EducationMapper {


	@Mapping(source = "cityId", target = "city.id")
	@Mapping(source = "schoolYearId", target = "schoolYear.id")
	@Mapping(source = "schoolLevelId", target = "schoolLevel.id")
	@Mapping(source = "honorId", target = "honor.id")
	@Mapping(source = "majorId", target = "major.id")
	@Mapping(source = "educationSystemTypeId", target = "educationSystemType.id")
	EducationDTO educationToEducationDTO(Education education);

	Iterable<EducationDTO> educationToEducationDTO(Iterable<Education> educations);


	@Mapping(source = "city.id", target = "cityId")
	@Mapping(source = "schoolYear.id", target = "schoolYearId")
	@Mapping(source = "schoolLevel.id", target = "schoolLevelId")
	@Mapping(source = "honor.id", target = "honorId")
	@Mapping(source = "major.id", target = "majorId")
	@Mapping(source = "educationSystemType.id", target = "educationSystemTypeId")
	Education educationDTOToEducation(EducationDTO educationDTO);

	Iterable<Education> educationDTOToEducation(Iterable<EducationDTO> educations);


	GetEducationDTO educationToGetEducationDTO(Education education);

	Iterable<GetEducationDTO> educationToGetEducationDTO(Iterable<Education> educations);

	@Mapping(target = "scholarshipBeneficiaries", ignore = true)
	@Mapping(target = "beneficiaryServices", ignore = true)
	@Mapping(target = "educations", ignore = true)
	@Mapping(target = "allergies", ignore = true)
	@Mapping(target = "diseases", ignore = true)
	@Mapping(target = "notes", ignore = true)
	@Mapping(target = "documents", ignore = true)
	@Mapping(target = "epsResidents", ignore = true)
	@Mapping(target = "diseaseTreatments", ignore = true)
	@Mapping(target = "person", ignore = true)
	@Mapping(target = "takenInChargeBeneficiaries", ignore = true)
	@Mapping(target = "handicapped", ignore = true)
	@Mapping(target = "glasses", ignore = true)
	BeneficiaryDTO beneficiaryToBeneficiaryDTO(Beneficiary beneficiary);


}
