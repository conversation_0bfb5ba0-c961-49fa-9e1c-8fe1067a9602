package ma.almobadara.backend.model.takenInCharge;

import jakarta.persistence.*;
import lombok.*;
import ma.almobadara.backend.model.administration.CacheAdUser;
import org.hibernate.annotations.CreationTimestamp;

import java.time.LocalDateTime;
import java.util.Date;

@Entity
@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class TakenInChargeOperationHistorique {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String action;

    private Double amount;

    private Double managementFees;

    private String userName;

    @ManyToOne
    @JoinColumn(name="operation_id", nullable = false)
    private TakenInChargeOperation operation;

    @CreationTimestamp
    private LocalDateTime createdAt;

    private String comment;

}
