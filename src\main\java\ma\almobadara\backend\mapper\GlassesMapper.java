package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.beneficiary.GlassesDto;
import ma.almobadara.backend.model.beneficiary.Glasses;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface GlassesMapper {

    GlassesDto glassesModelToDto(Glasses glasses);

    Iterable<GlassesDto> glassesListModelToDto(Iterable<Glasses> glasses);

    Glasses glassesDtoModelToModel(GlassesDto glassesDto);

    Iterable<Glasses> glassesDTOToGlasses(Iterable<GlassesDto> glassesDtos);

}
