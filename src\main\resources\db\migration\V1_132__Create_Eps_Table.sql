-- Create the eps table
CREATE TABLE eps (
                     id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
                     code VARCHAR(255) UNIQUE NOT NULL,
                     name VA<PERSON>HAR(255) NOT NULL,
                     name_ar VARCHAR(255) NOT NULL,
                     address <PERSON><PERSON><PERSON><PERSON>(255),
                     address_ar VARCHAR(255),
                     is_deleted BOOLEAN NOT NULL DEFAULT FALSE,
                     status BOOLEAN NOT NULL DEFAULT FALSE,
                     beneficiary_type VARCHAR(50),
                     age_group_ids VARCHAR(1000),
                     comment TEXT,
                     city_ids VARCHAR(1000),
                     contact_first_name VARCHAR(255) NOT NULL,
                     contact_last_name VA<PERSON>HAR(255) NOT NULL,
                     contact_first_name_ar VARCHAR(255) NOT NULL,
                     contact_last_name_ar VARCHAR(255) NOT NULL,
                     contact_phone VARCHAR(20) NOT NULL,
                     contact_email VARCHAR(255),
                     contact_function_id BIGINT,
                     creation_date TIM<PERSON><PERSON><PERSON> DEFAULT CURRENT_TIMESTAMP,
                     update_date TIM<PERSON><PERSON><PERSON> DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE zone
    ADD COLUMN eps_id BIGINT;

ALTER TABLE zone
    ADD CONSTRAINT fk_zone_eps FOREIGN KEY (eps_id) REFERENCES eps(id) ON DELETE SET NULL;

