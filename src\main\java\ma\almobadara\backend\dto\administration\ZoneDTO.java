package ma.almobadara.backend.dto.administration;

import lombok.Getter;
import lombok.Setter;
import ma.almobadara.backend.dto.CityWithRegionAndCountryDTO;
import ma.almobadara.backend.model.administration.Eps;
import ma.almobadara.backend.model.administration.HistoryZone;


import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
public class ZoneDTO {
    private Long id;
    private String code;
    private String name;
    private String nameAr;
    private String details;
    private boolean isDeleted;
    private List<Long> cityIds; // List of city IDs
    private List<CityWithRegionAndCountryDTO> cityDetails;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private Long assistantId;
    private String assistantName;
    private String assistantCode;
    private String assistantEmail;
    private LocalDate assistantDateAffectationToZone;
    private LocalDate assistantDateEndAffectationToZone;
    private boolean hasAssistant;
    // number of beneficiaries not boolean
    private boolean hasBeneficiaries;
    // we shuld have the countin the beneficiaries
    private int beneficiariesCount;
    private List<ZoneDetailsDTO> beneficiaries;
    private int totalBeneficiaries; // Nombre total de bénéficiaires
    private int beneficiariesPage;
    private int beneficiariesPageSize;


    private boolean status = true;

    private List<HistoryZoneDTO> historyZones;
    private int totalAssistants;
    private int assistantsPage;
    private int assistantsPageSize;

    private List<SousZoneDTO> sousZones;

    private Long epsId;
    private Eps eps;
    private Boolean oldZone;

}
