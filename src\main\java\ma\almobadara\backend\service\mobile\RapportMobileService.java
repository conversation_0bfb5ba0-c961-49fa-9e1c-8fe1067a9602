package ma.almobadara.backend.service.mobile;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.beneficiary.HistoryRapportDto;
import ma.almobadara.backend.dto.beneficiary.RapportMobileDto;
import ma.almobadara.backend.model.beneficiary.HistoryRapport;
import ma.almobadara.backend.repository.beneficiary.HistoryRapportRepository;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@RefreshScope
@Service
@RequiredArgsConstructor
@Slf4j
public class RapportMobileService {
    private final HistoryRapportRepository historyRapportRepository;
    public List<RapportMobileDto> getRapportByDonorId(Long id) {
        List<HistoryRapport> historyRapport = historyRapportRepository.findByDonorId(id);
        List<RapportMobileDto> historyRapportDtos = new ArrayList<>();
        Set<Long> processedRapportIds = new HashSet<>();

        for (HistoryRapport historyRapport1 : historyRapport) {
            Long rapportId = historyRapport1.getRapport().getId();
            if (!processedRapportIds.contains(rapportId)) {
                RapportMobileDto rapportMobileDto = RapportMobileDto.builder()
                        .rapportDate(historyRapport1.getRapport().getDateRapport())
                        .rapportName(historyRapport1.getRapport().getCodeRapport())
                        .rapportId(rapportId)
                        .beneficiaryName(historyRapport1.getBeneficiary().getPerson().getFirstName() + " " +
                                historyRapport1.getBeneficiary().getPerson().getLastName())
                        .build();
                historyRapportDtos.add(rapportMobileDto);
                processedRapportIds.add(rapportId);
            }
        }
        return historyRapportDtos;
    }
}
