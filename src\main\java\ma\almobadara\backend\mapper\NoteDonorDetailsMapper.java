package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.donor.DonorNoteDetailsDTO;
import ma.almobadara.backend.model.donor.NoteDonor;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring")
public interface NoteDonorDetailsMapper {

    @Mapping(source = "note.id", target = "id")
    @Mapping(source = "note.objet", target = "objet")
    @Mapping(source = "note.content", target = "content")
    @Mapping(source = "note.createdDate", target = "createdDate")
    @Mapping(source = "note.createdBy", target = "createdBy")
    DonorNoteDetailsDTO noteDonorToDto(NoteDonor noteDonor);
    List<DonorNoteDetailsDTO> noteDonorListToDtoList(List<NoteDonor> noteDonors);

}
