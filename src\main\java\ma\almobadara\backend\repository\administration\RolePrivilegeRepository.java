package ma.almobadara.backend.repository.administration;

import ma.almobadara.backend.model.administration.RolePrivilege;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface RolePrivilegeRepository extends JpaRepository<RolePrivilege, Long> {
    List<RolePrivilege> findByRole_Id(Long roleId);
    List<RolePrivilege> findByPrivilege_Id(Long privilegeId);
    List<RolePrivilege> findByFeature_Id(Long featureId);

    @Query("SELECT COUNT(rp) > 0 FROM RolePrivilege rp WHERE rp.role.id = :roleId AND rp.feature.code = :featureCode AND rp.privilege.code = :privilegeCode")
    boolean existsByRoleIdAndFeatureCodeAndPrivilegeCode(@Param("roleId") Long roleId, @Param("featureCode") String featureCode, @Param("privilegeCode") String privilegeCode);
}
