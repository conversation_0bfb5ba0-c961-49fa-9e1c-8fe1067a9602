package ma.almobadara.backend.model.family;

import jakarta.persistence.*;
import lombok.*;
import ma.almobadara.backend.model.communs.Document;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@IdClass(DocumentFamilyId.class)
public class DocumentFamily{

    @Id
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "family_id")
    private Family family;

    @Id
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "document_id")
    private Document document;

}
