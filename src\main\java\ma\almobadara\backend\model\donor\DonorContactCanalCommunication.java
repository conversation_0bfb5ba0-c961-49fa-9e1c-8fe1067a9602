package ma.almobadara.backend.model.donor;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
public class DonorContactCanalCommunication {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private Long canalCommunicationId;
    @ManyToOne
    @JoinColumn(name = "donor_contact_id")
    private DonorContact donorContact;


}
