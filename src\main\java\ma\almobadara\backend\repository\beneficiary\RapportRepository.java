package ma.almobadara.backend.repository.beneficiary;

import feign.Param;
import ma.almobadara.backend.model.beneficiary.Rapport;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Repository
public interface RapportRepository extends JpaRepository<Rapport, Long> {

    Page<Rapport> findByBeneficiaryId(Long beneficiaryId, Pageable pageable);

    Optional<Rapport> getByBeneficiaryId(Long beneficiaryId);

    @Modifying
    @Transactional

    @Query("UPDATE Rapport r SET r.donor.id = :newDonorId WHERE r.donor.id = :oldDonorId")
    void updateDonorId(@Param("oldDonorId") Long oldDonorId, @Param("newDonorId") Long newDonorId);

    @Query("SELECT MAX(r.numberRapport) FROM Rapport r")
    Optional<Long> findMaxNumberRapport();

    Optional<Rapport> findByBeneficiaryIdAndId(Long beneficiaryId, Long id);

    @Query("SELECT MAX(r.numberRapport) FROM Rapport r WHERE r.beneficiary.id = :beneficiaryId")
    Long findLastNumberRapportByBeneficiaryId(@Param("beneficiaryId") Long beneficiaryId);

    @Query("SELECT MAX(r.numberRapport) FROM Rapport r " +
            "WHERE r.beneficiary.id = :beneficiaryId " +
            "AND EXTRACT(YEAR FROM r.dateRapport) = :year")
    Long findLastNumberRapportByBeneficiaryAndYear(@Param("beneficiaryId") Long beneficiaryId,
                                                   @Param("year") int year);


}
