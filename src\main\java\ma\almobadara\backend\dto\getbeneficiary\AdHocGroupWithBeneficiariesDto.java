package ma.almobadara.backend.dto.getbeneficiary;

import lombok.*;
import ma.almobadara.backend.dto.CityWithRegionAndCountryDTO;
import ma.almobadara.backend.dto.referentiel.CityDTO;
import ma.almobadara.backend.dto.referentiel.TypePriseEnChargeDTO;

import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AdHocGroupWithBeneficiariesDto {

    private Long groupId;
    private String groupCode;
    private String groupName;
    private Long groupCityId;
    private CityDTO city;
    private CityWithRegionAndCountryDTO info;
    private String groupFullNameContact;
    private String groupPhoneNumber;
    private String groupComment;
    private String groupStatus;
    private Long numberOfMembers;
    private LocalDateTime groupCreatedAt;
    private String beneficiaryStatut;
    private List<TypePriseEnChargeDTO> typePriseEnCharges;
    private List<Long> typePriseEnChargeIds;
    private List<GetListDTO> beneficiaries;

}
