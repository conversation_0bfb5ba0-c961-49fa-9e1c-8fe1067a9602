# Auth0 Integration for Mobile APIs

This document explains the implementation of Auth0 authentication for mobile APIs in the AlMobadara backend application.

## Overview

The application now supports two types of authentication:
- Azure AD authentication for web APIs
- Auth0 authentication for mobile APIs

## Configuration Changes

### 1. Auth0 Properties

The Auth0 configuration is defined in `application.yml`:
```yaml
auth0:
  issuer-uri: https://dev-hkis5vra7ph2slk3.us.auth0.com
  jwk-set-uri: https://dev-hkis5vra7ph2slk3.us.auth0.com/.well-known/jwks.json
  audience: https://dev-hkis5vra7ph2slk3.us.auth0.com/api/v2/
```

### 2. JWT Decoder Configuration

Created a new `JwtDecoderConfig` class to handle JWT decoder beans and avoid circular dependencies:

```java
@Configuration
@RequiredArgsConstructor
public class JwtDecoderConfig {
    private final OAuth2ResourceServerConfiguration oAuth2ResourceServerConfiguration;
    private final Auth0Properties auth0Properties;
    private final CacheAdUserRepository cacheAdUserRepository;

    @Bean
    public JwtDecoder azureJwtDecoder() {
        // Azure JWT decoder configuration
    }

    @Bean
    public JwtDecoder auth0JwtDecoder() {
        // Auth0 JWT decoder configuration
    }
}
```

Key features:
- Separate configuration for Azure and Auth0 decoders
- Handles issuer validation with trailing slash normalization
- Includes audience validation
- Detailed logging for debugging

### 3. Security Configuration

Modified `SecurityConfig` to:
- Route requests to appropriate decoders based on path
- Handle mobile endpoints with Auth0 authentication
- Keep web endpoints with Azure authentication

```java
@Configuration
@EnableWebSecurity
public class SecurityConfig {
    private final JwtDecoder azureJwtDecoder;
    private final JwtDecoder auth0JwtDecoder;

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) {
        // Security configuration
    }
}
```

### 4. JWT Authentication Filter

Created `JwtAuthFilter` to handle mobile API authentication:

```java
@Component
@RequiredArgsConstructor
public class JwtAuthFilter extends OncePerRequestFilter {
    private final JwtDecoder auth0JwtDecoder;

    @Override
    protected void doFilterInternal(...) {
        // Token validation and authentication
    }
}
```

Features:
- Validates Authorization header format
- Removes "Bearer " prefix
- Uses Auth0 decoder for token validation
- Creates authentication token with JWT subject
- Detailed error handling and logging

## Authentication Flow

### Mobile API Authentication

1. Request comes in with Authorization header:
   ```
   Authorization: Bearer <auth0-token>
   ```

2. `JwtAuthFilter` intercepts the request:
   - Checks if path starts with `/mobile/`
   - Excludes `/login` and `/reset-password` endpoints
   - Validates Authorization header format
   - Removes "Bearer " prefix

3. Token Validation:
   - Uses Auth0 decoder to validate token
   - Checks issuer and audience
   - Creates authentication token
   - Sets authentication in security context

4. Request Processing:
   - If validation succeeds, request continues
   - If validation fails, returns 401 Unauthorized

### Web API Authentication

1. Request comes in with Authorization header:
   ```
   Authorization: Bearer <azure-token>
   ```

2. `SecurityConfig` routes to Azure decoder:
   - Validates token format
   - Checks Azure-specific claims
   - Creates authentication token

## Error Handling

The implementation includes comprehensive error handling:

1. Missing or Invalid Authorization Header:
   ```
   Unauthorized: Valid Authorization header is required
   ```

2. Invalid Token:
   ```
   Unauthorized: Invalid token
   ```

3. Detailed logging for debugging:
   - Token validation steps
   - Issuer and audience validation
   - Full JWT claims
   - Error messages

## Testing

To test the implementation:

1. Mobile API Test:
   ```bash
   curl -X GET \
     'http://your-api-url/mobile/your-endpoint' \
     -H 'Authorization: Bearer your-auth0-token'
   ```

2. Web API Test:
   ```bash
   curl -X GET \
     'http://your-api-url/your-endpoint' \
     -H 'Authorization: Bearer your-azure-token'
   ```

## Logging

The implementation includes detailed logging at DEBUG level:

```yaml
logging:
  level:
    ma.almobadara.backend: DEBUG
    org.springframework.security: DEBUG
```

Logs show:
- Request paths
- Token validation steps
- Issuer and audience validation
- Full JWT claims
- Error messages

## Security Considerations

1. Token Validation:
   - Validates issuer
   - Validates audience
   - Checks token expiration
   - Verifies token signature

2. Path-based Routing:
   - Mobile APIs use Auth0
   - Web APIs use Azure
   - Public endpoints excluded

3. Error Handling:
   - Detailed error messages
   - Secure error responses
   - Comprehensive logging

## Future Improvements

Potential enhancements:
1. Add role-based access control
2. Implement token refresh mechanism
3. Add rate limiting
4. Enhance error messages
5. Add more detailed logging 