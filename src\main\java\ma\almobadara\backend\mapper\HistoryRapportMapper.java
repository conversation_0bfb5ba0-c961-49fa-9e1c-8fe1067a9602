package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.beneficiary.HistoryRapportDto;
import ma.almobadara.backend.model.beneficiary.HistoryRapport;
import ma.almobadara.backend.model.donor.Donor;
import ma.almobadara.backend.model.donor.DonorPhysical;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "spring")
public interface HistoryRapportMapper {

    HistoryRapportMapper INSTANCE = Mappers.getMapper(HistoryRapportMapper.class);

    @Mapping(source = "beneficiary.id", target = "beneficiaryId")
    @Mapping(source = "rapport.id", target = "rapportId")
    @Mapping(source = "donor.id", target = "donorId")
    @Mapping(source = "agendaRapport.id", target = "agendaRapportId")
    @Mapping(source = "donor", target = "donorName", qualifiedByName = "mapDonorName")
    @Mapping(source = "rapport.codeRapport", target = "codeRapport")
    //@Mapping(source = "rapport.numberRapport", target = "numberRapport")
    @Mapping(source = "agendaRapport.numberRapport", target = "numberRapport")
    @Mapping(source = "agendaRapport.year", target = "year")
    HistoryRapportDto toDto(HistoryRapport historyRapport);

    List<HistoryRapportDto> toDtoList(List<HistoryRapport> historyRapports);

    @Named("mapDonorName")
    default String mapDonorName(Donor donor) {
        if (donor == null) {
            return null;
        }

        if (donor instanceof DonorPhysical) {
            DonorPhysical donorPhysical = (DonorPhysical) donor;
            return (donorPhysical.getFirstName() != null ? donorPhysical.getFirstName() : "") + " " +
                    (donorPhysical.getLastName() != null ? donorPhysical.getLastName() : "");
        }
        return "";
    }
}