info:
  component: AlMobadara PMGMT Backend

server:
  port: 8001

springdoc:
  packagesToScan: ma.almobadara.pmgmt
  api-docs:
    path: /api-docs

management:
  endpoints:
    web:
      exposure:
        include: "*"


azure-enterprise-app-properties:
  tenant-id: 1299c058-f4c7-42c6-9a4e-20f2cdb65448
  client-id: 2e2e7b55-4c96-475c-81ec-5d45ad853b74
  client-secret: ****************************************
  service-root-beta: https://graph.microsoft.com/v1.0/

auth0:
  issuer-uri: https://dev-hkis5vra7ph2slk3.us.auth0.com
  jwk-set-uri: https://dev-hkis5vra7ph2slk3.us.auth0.com/.well-known/jwks.json
  audience: https://dev-hkis5vra7ph2slk3.us.auth0.com/api/v2/


spring:
  config:
    use-legacy-processing: true
  datasource:
    url: ****************************************************************
    username: postgres
    password: admin
    driver-class-name: org.postgresql.Driver
  jpa:
    database: POSTGRESQL
    show-sql: false
    hibernate:
      ddl-auto: validate
      properties:
        hibernate.jdbc.time_zone: UTC
  # Email configuration for sending emails via Gmail
  mail:
    host: smtp.gmail.com
    port: 587
    username: <EMAIL> # que pour test
    password: vjqukacjtwcasvxx # we can use the environment variable to store the password ${EMAIL-PASSWORD} just to  add  in the deployment environment
    #possible to use external plugins fir security like JASYPT
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true


  security:
    oauth2:
      resourceserver:
        jwt:
          jwk-set-uri: ${oauth2.resourceserver.jwk-set-uri}
          issuer-uri: ${oauth2.resourceserver.issuer-uri}

  servlet:
    multipart:
      enabled: true
      file-size-threshold: 2KB
      maxFileSize: 10MB
      maxRequestSize: 10MB
  jackson:
    time-zone: UTC
    serialization:
      FAIL_ON_EMPTY_BEANS: false
  cache:
    type: redis
  redis:
    host: localhost
    port: 6379

  flyway:
    enabled: true
    baseline-on-migrate: true
    url: ****************************************************************
    user: postgres
    password: admin
    locations: classpath:db/migration
    clean-disabled: true
    outOfOrder : true

oauth2:
  resourceserver:
    tenant-id: ${azure-enterprise-app-properties.tenant-id}
    client-id: ${azure-enterprise-app-properties.client-id}
    jwk-set-uri: https://login.microsoftonline.com/${oauth2.resourceserver.tenant-id}/discovery/v2.0/keys
    issuer-uri: https://sts.windows.net/${oauth2.resourceserver.tenant-id}/



minio:
  access:
    name: minioadmin
    secret: minioadmin
  default:
    folder: /
  url: http://127.0.0.1:9000
  bucket: documents
  beneficiariesFolder: beneficiary/
  familiesFolder: families/
  campagnesFolder: campagnes/
  membersFolder: members/
  epsResponsableFolder: epsResponsable/
  donorsFolder: doners/
  donationsFolder: donations/
  takenInChargesFolder: supports/
  assistantFolder: assistants/
  profilePicture:
    folder: profil_picture
    abv: pdp
  reportsFolder: reports
  picturesFolder: pictures
  picture:
      abv: IMG


ref:
  uri: http://localhost:8022/api
  username: user
  password: user

# Impersonation token configuration
impersonation:
  token:
    secret: ${IMPERSONATION_TOKEN_SECRET:almobadara_impersonation_token_secret_key_for_secure_tokens}
    expiration: 3600 # Token expiration in seconds (1 hour)

jwt:
  secret: thisisalongcodejusttogenerateasignaturetokenaccessandrefreshforalmobadara
  expiration: 3600000 # Token expiration in milliseconds (1 hour)
  refresh-token:
    expiration: 604800000 # Refresh token expiration in milliseconds (7 days)

logging:
  level:
    ma.almobadara.backend: DEBUG
    org.springframework.security: DEBUG
