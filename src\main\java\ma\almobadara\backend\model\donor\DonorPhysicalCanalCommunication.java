package ma.almobadara.backend.model.donor;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DonorPhysicalCanalCommunication {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private Long canalCommunicationId;
    @ManyToOne
    @JoinColumn(name = "donor_physical_id")
    private DonorPhysical donorPhysical;

}
