package ma.almobadara.backend.config;

import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.enumeration.RoleCode;
import ma.almobadara.backend.model.administration.CacheAdUser;
import ma.almobadara.backend.repository.administration.RolePrivilegeRepository;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class AccessChecker {

    private final RolePrivilegeRepository rolePrivilegeRepository;

    public AccessChecker(RolePrivilegeRepository rolePrivilegeRepository) {
        this.rolePrivilegeRepository = rolePrivilegeRepository;
    }

    public boolean hasAccess(String featureCode, String privilegeCode) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

        log.debug("Checking access for featureCode: {}, privilegeCode: {}", featureCode, privilegeCode);
        log.debug("Authentication: {}", authentication != null ? authentication.getClass().getSimpleName() : "null");
        log.debug("Principal: {}", authentication != null ? authentication.getPrincipal().getClass().getSimpleName() : "null");

        if (authentication == null || !(authentication.getPrincipal() instanceof CacheAdUser connectedUser)) {
            log.warn("Access denied: authentication is null or principal is not CacheAdUser. Principal type: {}",
                authentication != null ? authentication.getPrincipal().getClass().getSimpleName() : "null");
            return false;
        }

        log.debug("Connected user: {}, Role: {}", connectedUser.getMail(), connectedUser.getRole().getCode());

        Long profileId = connectedUser.getRole().getId();
        if(connectedUser.getRole().getCode().equals(RoleCode.ADMIN.getCode())){
            log.debug("Access granted: user has ADMIN role");
            return true;
        }

        boolean hasAccess = rolePrivilegeRepository.existsByRoleIdAndFeatureCodeAndPrivilegeCode(
                profileId, featureCode, privilegeCode
        );

        log.debug("Access check result: {} for roleId: {}, featureCode: {}, privilegeCode: {}",
            hasAccess, profileId, featureCode, privilegeCode);

        return hasAccess;
    }

    public boolean hasAnyAccess(String[] permissions) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

        if (authentication == null || !(authentication.getPrincipal() instanceof CacheAdUser connectedUser)) {
            return false;
        }


        Long profileId = connectedUser.getRole().getId();


        for (String permission : permissions) {
            String[] parts = permission.split(":");
            if (parts.length != 2) {
                throw new IllegalArgumentException("Invalid permission format. Expected 'featureCode:privilegeCode'.");
            }

            String featureCode = parts[0];
            String privilegeCode = parts[1];


            if (rolePrivilegeRepository.existsByRoleIdAndFeatureCodeAndPrivilegeCode(profileId, featureCode, privilegeCode)) {
                return true;
            }
        }

        return false;
    }

}
