package ma.almobadara.backend.repository.beneficiary;

import ma.almobadara.backend.model.beneficiary.BeneficiaryHandicap;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface BeneficiaryHandicapRepository extends JpaRepository<BeneficiaryHandicap, Long> {
    List<BeneficiaryHandicap> findByBeneficiaryId(Long id);
    //deleteByBeneficiaryId
    @Modifying
    @Query("DELETE FROM BeneficiaryHandicap b WHERE b.beneficiary.id = :id")
    void deleteByBeneficiaryId(Long id);

}
