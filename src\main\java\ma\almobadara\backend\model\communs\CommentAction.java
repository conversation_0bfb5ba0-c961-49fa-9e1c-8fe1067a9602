package ma.almobadara.backend.model.communs;

import jakarta.persistence.*;
import lombok.*;
import ma.almobadara.backend.model.administration.CacheAdUser;

import java.time.LocalDate;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CommentAction {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private LocalDate date;
    private String content;
    private Boolean labelComment;
    @ManyToOne
    @JoinColumn(name = "author_id")
    private CacheAdUser author;
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "action_id")
    private Action action;

}
