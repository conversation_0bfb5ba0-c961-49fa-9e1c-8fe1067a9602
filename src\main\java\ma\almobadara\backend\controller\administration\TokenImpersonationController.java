package ma.almobadara.backend.controller.administration;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.config.HasAccessToModule;
import ma.almobadara.backend.dto.administration.ImpersonationTokenDTO;
import ma.almobadara.backend.enumeration.Functionality;
import ma.almobadara.backend.enumeration.Module;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.exceptions.UserNotFoundException;
import ma.almobadara.backend.service.administration.TokenImpersonationService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping(value = "/impersonation")
public class TokenImpersonationController {

    private final TokenImpersonationService tokenImpersonationService;

    @PostMapping("/impersonate/{userId}")
    @HasAccessToModule(modules = {Module.USER}, functionalities = {Functionality.UPDATE})
    @Operation(summary = "Generate Impersonation Token", description = "Generate a token to impersonate another user", tags = {"Impersonation"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Token generated successfully", 
                    content = @Content(schema = @Schema(implementation = ImpersonationTokenDTO.class))),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - User does not have permission to impersonate"),
            @ApiResponse(responseCode = "404", description = "User not found"),
    })
    public ResponseEntity<ImpersonationTokenDTO> generateImpersonationToken(@PathVariable Long userId) throws TechnicalException {
        log.info("Start resource generateImpersonationToken for userId: {}", userId);
        
        try {
            ImpersonationTokenDTO tokenDTO = tokenImpersonationService.generateImpersonationToken(userId);
            log.info("End resource generateImpersonationToken - Successfully generated token for user with ID: {}", userId);
            return ResponseEntity.ok(tokenDTO);
        } catch (UserNotFoundException ex) {
            log.error("User not found: {}", ex.getMessage());
            throw ex;
        } catch (TechnicalException ex) {
            log.error("Technical error generating impersonation token: {}", ex.getMessage());
            throw ex;
        }
    }
}
