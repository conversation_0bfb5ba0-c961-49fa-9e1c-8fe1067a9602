package ma.almobadara.backend.dto.donation;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;
import ma.almobadara.backend.dto.CityWithRegionAndCountryDTO;
import ma.almobadara.backend.dto.administration.TagDTO;
import ma.almobadara.backend.dto.communs.DocumentDTO;
import ma.almobadara.backend.dto.donor.DonorDTO;
import ma.almobadara.backend.dto.referentiel.CanalDonationDTO;
import ma.almobadara.backend.dto.referentiel.CurrencyDTO;
import ma.almobadara.backend.model.communs.Document;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
@JsonIgnoreProperties(value = { "executionDate" }, allowGetters = true)
public class DonationDTO {

	private Long id;
	private Long IdNonIdentifed;
	private Long IdKafalat;
	private String code;

	private Double value;
	private Double kafalatvalue;
	private Double nonIdentifiedValue;


	private Date receptionDate;

	private boolean identifiedDonor;

	private String unidentifiedDonorName;

	private String comment;
	private String kafalatComment;
	private String nonIdentifiedComment;

	private String transactionNumber;

	private LocalDateTime createdAt;

	private String type;


	private Double valueCurrency;
	private Double kafalatValueCurrency;
	private Double nonIdentifiedValueCurrency;

	private CanalDonationDTO canalDonation;

	private DonorDTO donor;

	private List<DocumentDonationDTO> documentDonations;

	private List<DonationProductNatureDTO> donationProductNatures;

	private List<BudgetLineDTO> budgetLines;
	private List<BudgetLineDTO> budgetLineskafalat;

	private CurrencyDTO currency;
	private CurrencyDTO kafalatCurrency;
	private CurrencyDTO nonIdentifiedCurrency;

	private CityWithRegionAndCountryDTO info;

	private Boolean archived;

	private String KafalatStatus;
	private String nonIdentifiedStatus;
	private Boolean enableCurrency;
	List<DocumentDTO> documents;

	private List<TagDTO> tags;



	// we should add her the fileName and the




}
