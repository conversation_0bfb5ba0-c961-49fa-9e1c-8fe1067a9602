package ma.almobadara.backend.dto.referentiel;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;
import java.io.Serializable;


import java.util.Objects;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
@JsonIgnoreProperties(value="true")
public class DonorStatusDTO implements Serializable{
	private static final long serialVersionUID = 1L;

	private Long id;


	private String code;
	private String name;
	private String nameAr;
	private String nameEn;



	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	@Override
	public boolean equals(Object o) {
		if (this == o) {
			return true;
		}
		if (!(o instanceof DonorStatusDTO parDonorStatusDTO)) {
			return false;
		}

        if (this.id == null) {
			return false;
		}
		return Objects.equals(this.id, parDonorStatusDTO.id);
	}

	@Override
	public int hashCode() {
		return Objects.hash(this.id);
	}

	// prettier-ignore
	@Override
	public String toString() {
		return "ParDonorStatusDTO{" +
				"id=" + getId() +
				", name='" + getName() + "'" +
				"}";
	}

}
