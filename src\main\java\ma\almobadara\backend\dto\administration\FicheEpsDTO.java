package ma.almobadara.backend.dto.administration;

import lombok.Data;
import ma.almobadara.backend.dto.CityWithRegionAndCountryDTO;
import ma.almobadara.backend.dto.referentiel.DonorContactFunctionDTO;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Data
public class FicheEpsDTO {

    private Long id;
    private String code;
    private String name;
    private String nameAr;
    private String address;
    private String addressAr;
    private boolean isDeleted;
    private boolean status;
    private String beneficiaryType;
    private String ageGroups;
    private String comment;
    private List<CityWithRegionAndCountryDTO> cityDetails;

    private Long capacity;
    private Date dateCreated;

    private LocalDateTime creationDate;
    private LocalDateTime updateDate;
    private ZoneDTOLight zone;
}
