package ma.almobadara.backend.dto.communs;

import lombok.*;
import ma.almobadara.backend.dto.administration.TagDTO;
import ma.almobadara.backend.dto.referentiel.TypeDocumentDonorDTO;
import ma.almobadara.backend.model.administration.Tag;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class DocumentDTO {

	private Long id;
	private String label;
	private Date documentDate;
	private Date expiryDate;
	private String comment;
	private MultipartFile file;
	private String file64;
	private String fileUrl;
	private String fileName;
	private TypeDocumentDonorDTO type;
	private String code;
	private List<TagDTO> tags;

}
