package ma.almobadara.backend.repository.beneficiary;

import ma.almobadara.backend.model.beneficiary.BeneficiaryYearCount;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface BeneficiaryYearCountRepository extends JpaRepository<BeneficiaryYearCount, Long> {

    Optional<BeneficiaryYearCount> findByYear(String year);
}
