CREATE TABLE IF NOT EXISTS public.beneficiary_aide_complementaire (
                                                                      beneficiary_id BIGINT NOT NULL,
                                                                      aide_complementaire_id BIGINT NOT NULL,
                                                                      PRIMARY KEY (beneficiary_id, aide_complementaire_id),

    CONSTRAINT fk_beneficiary
    FOREIGN KEY (beneficiary_id)
    REFERENCES public.beneficiary(id)
    ON DELETE CASCADE,

    CONSTRAINT fk_aide_complementaire
    FOREIG<PERSON> KEY (aide_complementaire_id)
    REFERENCES public.aide_complementaire(id)
    ON DELETE CASCADE
    );

CREATE INDEX idx_beneficiary_aide_complementaire_beneficiary_id
    ON public.beneficiary_aide_complementaire (beneficiary_id);

CREATE INDEX idx_beneficiary_aide_complementaire_aide_complementaire_id
    ON public.beneficiary_aide_complementaire (aide_complementaire_id);
