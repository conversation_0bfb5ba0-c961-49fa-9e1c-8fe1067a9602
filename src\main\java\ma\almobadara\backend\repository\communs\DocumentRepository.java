package ma.almobadara.backend.repository.communs;

import ma.almobadara.backend.model.administration.Eps;
import ma.almobadara.backend.model.communs.Document;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.data.repository.query.Param;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Repository
public interface DocumentRepository extends JpaRepository<Document, Long> {

    @Query("SELECT d FROM Document d")
    Page<Document> findAllPaged(Pageable pageable);

}
