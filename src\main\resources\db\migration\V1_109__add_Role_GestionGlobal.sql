
WITH new_role AS (
INSERT INTO role (code, name, description, creation_date, update_date)
VALUES ('GESTIONNAIRE_GLOBAL', 'Gestionnaire Global', 'Gestionnaire Global', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    RETURNING id
    )

INSERT INTO role_privilege (role_id, privilege_id, feature_id, creation_date, update_date)
SELECT
    (SELECT id FROM new_role) AS role_id, -- ID du nouveau rôle
    p.id,
    f.id,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
FROM privilege p, feature f
WHERE p.id BETWEEN 1 AND 9
  AND f.id BETWEEN 1 AND 4;
