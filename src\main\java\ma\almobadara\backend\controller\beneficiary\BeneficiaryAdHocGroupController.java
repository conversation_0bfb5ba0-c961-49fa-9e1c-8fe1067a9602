package ma.almobadara.backend.controller.beneficiary;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.persistence.EntityNotFoundException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.config.HasAccessToModule;
import ma.almobadara.backend.dto.beneficiary.*;
import ma.almobadara.backend.dto.getbeneficiary.AdHocGroupWithBeneficiariesDto;
import ma.almobadara.backend.dto.getbeneficiary.GetListDTO;
import ma.almobadara.backend.enumeration.Functionality;
import ma.almobadara.backend.enumeration.Module;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.service.beneficiary.AddedBeneficiaryResponse;
import ma.almobadara.backend.service.beneficiary.BeneficiaryAdHocGroupService;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@RefreshScope
@RestController
@AllArgsConstructor
@Slf4j
@RequestMapping("/beneficiaries")
public class BeneficiaryAdHocGroupController {

    private final BeneficiaryAdHocGroupService beneficiaryAdHocGroupService;

    @PostMapping(value = "/addAdHocGroup", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    @HasAccessToModule(modules = {Module.BENEFICIARY}, functionalities = {Functionality.CREATE})
    @Operation(summary = "Create an Ad-Hoc Beneficiary", description = "Add a new Ad-Hoc beneficiary", tags = {"Beneficiaries"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "Beneficiary successfully created", content = @Content(array = @ArraySchema(schema = @Schema(implementation = BeneficiaryAdHocGroupDto.class)))),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "500", description = "Server error")})
    public ResponseEntity<BeneficiaryAdHocGroupDto> addBeneficiaryAdHocGroup(@ModelAttribute BeneficiaryAdHocGroupDto beneficiaryAdHocGroupDto) {
        log.info("Adding new Beneficiary ad-hoc groupe: {}", beneficiaryAdHocGroupDto);
        BeneficiaryAdHocGroupDto createdBeneficiaryAdHocGroup = beneficiaryAdHocGroupService.addBeneficiaryAdHocGroup(beneficiaryAdHocGroupDto);
        return new ResponseEntity<>(createdBeneficiaryAdHocGroup, HttpStatus.CREATED);
    }

//    @Operation(summary = "Find All Beneficiaries and Ad-Hoc Groups",
//            description = "Retrieve combined list of beneficiaries and ad-hoc groups",
//            tags = {"Beneficiaries"})
//    @ApiResponses(value = {
//            @ApiResponse(responseCode = "200", description = "Successful operation",
//                    content = @Content(array = @ArraySchema(schema = @Schema(implementation = BeneficiaryAdHocCombinedDto.class))))})
//    @GetMapping(value = "/adHocCombined", produces = "application/json")
//    public ResponseEntity<Page<BeneficiaryAdHocCombinedDto>> getAllBeneficiariesAdHocCombined(
//            @RequestParam Optional<Integer> page,
//            @RequestParam Optional<String> criteria,
//            @RequestParam Optional<String> value1,
//            @RequestParam Optional<String> value2,
//            @RequestParam(required = false) final String searchByNom,
//            @RequestParam(required = false) final String lastNameAr,
//            @RequestParam(required = false) final Boolean searchByTypeBeneficiaire,
//            @RequestParam(required = false) final String searchByStatut,
//            @RequestParam(required = false) final String searchByNumTel,
//            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") final Date minDate,
//            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") final Date maxDate) {
//
//        log.info("Fetching combined beneficiaries and ad-hoc groups with criteria");
//
//        Pageable pageable = PageRequest.of(page.orElse(0), 10);
//        Page<BeneficiaryAdHocCombinedDto> result = beneficiaryAdHocGroupService.getAllBeneficiariesAdHocCombined(
//                criteria, value1, value2, searchByNom, lastNameAr, searchByTypeBeneficiaire, searchByStatut,
//                searchByNumTel, minDate, maxDate, pageable);
//
//        log.info("Found {} combined beneficiaries and ad-hoc groups", result.getTotalElements());
//        return ResponseEntity.ok(result);
//    }


    @GetMapping(value = "/adHocCombined", produces = "application/json")
    public ResponseEntity<Page<BeneficiaryAdHocCombinedDto>> getAllBeneficiariesAdHocCombined(
            @RequestParam(value = "page", defaultValue = "0") int page,
            @RequestParam(value = "size", defaultValue = "10") int size,
            @RequestParam(value = "searchByNom", required = false) String searchByNom,
            @RequestParam(value = "searchByStatut", required = false) String searchByStatut,
            @RequestParam(value = "searchByTypeBeneficiaire", required = false) String searchByTypeBeneficiaire){

        log.info("Fetching all combined beneficiaries and ad-hoc groups with pagination and filters");

        Page<BeneficiaryAdHocCombinedDto> result = beneficiaryAdHocGroupService.getAllBeneficiariesAdHocCombined(page, size, searchByNom,searchByStatut,searchByTypeBeneficiaire);

        log.info("Found {} combined beneficiaries and ad-hoc groups", result.getTotalElements());
        return ResponseEntity.ok(result);
    }



//    @PostMapping("/adHocGroup/{groupId}/addBeneficiaries")
//    public ResponseEntity<BeneficiaryAdHocGroupDto> addBeneficiariesToGroup(
//            @PathVariable Long groupId,
//            @RequestBody BeneficiaryAffectedToGroupDto beneficiaryAffectedToGroupDto) {
//
//        BeneficiaryAdHocGroupDto updatedGroup = beneficiaryAdHocGroupService.addBeneficiariesToAdHocGroup(groupId, beneficiaryAffectedToGroupDto);
//        return new ResponseEntity<>(updatedGroup, HttpStatus.OK);
//    }

    @PostMapping("/adHocGroup/{groupId}/addBeneficiaries")
    public ResponseEntity<String> addBeneficiariesToGroup(
            @PathVariable Long groupId,
            @RequestBody BeneficiaryAffectedToGroupDto beneficiaryAffectedToGroupDto) {

        beneficiaryAdHocGroupService.addBeneficiariesToAdHocGroup(groupId, beneficiaryAffectedToGroupDto);
        return new ResponseEntity<>("Les bénéficiaires ont été ajoutés avec succès au groupe.", HttpStatus.OK);
    }



//    @GetMapping("/{groupId}")
//    public ResponseEntity<List<GetListDTO>> getBeneficiariesByAdHocGroup(@PathVariable Long groupId) {
//        List<GetListDTO> beneficiaries = beneficiaryAdHocGroupService.getBeneficiariesByAdHocGroup(groupId);
//        return ResponseEntity.ok(beneficiaries);
//    }

//    @GetMapping("/{groupId}")
//    public ResponseEntity<List<GetListDTO>> getBeneficiariesByAdHocGroup(@PathVariable Long groupId) {
//        List<GetListDTO> beneficiaries = beneficiaryAdHocGroupService.getBeneficiariesByAdHocGroup(groupId);
//        return ResponseEntity.ok(beneficiaries);
//    }

    @GetMapping("/adHocGroup/{groupId}")
    public ResponseEntity<AdHocGroupWithBeneficiariesDto> getBeneficiariesByAdHocGroup(@PathVariable Long groupId) {
        AdHocGroupWithBeneficiariesDto groupWithBeneficiaries = beneficiaryAdHocGroupService.getBeneficiariesByAdHocGroup(groupId);
        return ResponseEntity.ok(groupWithBeneficiaries);
    }

    @GetMapping("/adHocGroup/all/beneficiaries")
    public ResponseEntity<List<GetListDTO>> getAllBeneficiary() {
        List<GetListDTO> beneficiary = beneficiaryAdHocGroupService.getAllBeneficiary();
        return ResponseEntity.ok(beneficiary);
    }

    @DeleteMapping("/adHocGroup/{groupId}")
    @Operation(summary = "Delete an Ad-Hoc Group if it contains no beneficiaries",
            description = "Soft delete an ad-hoc group if it contains no beneficiaries",
            tags = {"Beneficiaries"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Group successfully deleted"),
            @ApiResponse(responseCode = "400", description = "Group cannot be deleted because it has beneficiaries"),
            @ApiResponse(responseCode = "404", description = "Group not found"),
            @ApiResponse(responseCode = "500", description = "Server error")})
    public ResponseEntity<String> deleteAdHocGroupIfEmpty(@PathVariable Long groupId) {
        try {
            beneficiaryAdHocGroupService.deleteAdHocGroupIfEmpty(groupId);
            return new ResponseEntity<>("Group successfully deleted", HttpStatus.OK);
        } catch (IllegalStateException e) {
            return new ResponseEntity<>(e.getMessage(), HttpStatus.BAD_REQUEST);
        } catch (EntityNotFoundException e) {
            return new ResponseEntity<>(e.getMessage(), HttpStatus.NOT_FOUND);
        }
    }

    @PutMapping("/adHocGroup/{id}")
    @Operation(summary = "Update a Beneficiary Ad-Hoc Group", description = "Update an existing beneficiary ad-hoc group", tags = {"Beneficiaries"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Beneficiary ad-hoc group successfully updated"),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "404", description = "Group not found"),
            @ApiResponse(responseCode = "500", description = "Server error")})
    public ResponseEntity<BeneficiaryAdHocGroupDto> updateBeneficiaryAdHocGroup(
            @PathVariable Long id,
            @RequestBody BeneficiaryAdHocGroupDto beneficiaryAdHocGroupDto) {
        BeneficiaryAdHocGroupDto updatedGroup = beneficiaryAdHocGroupService.updateBeneficiaryAdHocGroup(id, beneficiaryAdHocGroupDto);
        return new ResponseEntity<>(updatedGroup, HttpStatus.OK);
    }


}
