package ma.almobadara.backend.dto.caisse;

import lombok.*;
import ma.almobadara.backend.dto.aideComplemenatire.AideComplementaireDTO;
import ma.almobadara.backend.dto.donation.BudgetLineDTO;
import ma.almobadara.backend.dto.referentiel.TypePriseEnChargeDTO;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CaisseDto {

    private Long id;
    private String code;
    private String name;
    private Long typePriseEnChargeId;
    private String comment;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private boolean isDeleted;
    private TypePriseEnChargeDTO typePriseEnChargeDescription;
    private Date creationDateCaisse;
    private double amount;
    private List<BudgetLineDTO> budgetLines;
    private List<AideComplementaireDTO> aidesComplementaires;
    private long totalAidesComplementaires;

}
