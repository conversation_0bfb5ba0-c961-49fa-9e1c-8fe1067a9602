package ma.almobadara.backend.repository.beneficiary;

import ma.almobadara.backend.model.beneficiary.HistoryBeneficiary;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
@Repository
public interface HistoryBeneficiaryRepository extends JpaRepository<HistoryBeneficiary, Long> {
    List<HistoryBeneficiary> findByBeneficiaryId(Long beneficiaryId);

    //deleteByBeneficiaryId
    @Modifying
    @Query("delete from HistoryBeneficiary h where h.beneficiary.id = :beneficiaryId")
    void deleteByBeneficiaryId(Long beneficiaryId);
}
