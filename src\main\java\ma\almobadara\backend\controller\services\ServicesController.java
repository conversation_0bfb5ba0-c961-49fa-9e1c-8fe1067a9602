package ma.almobadara.backend.controller.services;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.aideComplemenatire.AddAideComplementaireDTO;
import ma.almobadara.backend.dto.aideComplemenatire.AideComplementaireDTO;
import ma.almobadara.backend.dto.donation.BudgetLineDTO;
import ma.almobadara.backend.dto.service.AddServicesDTO;
import ma.almobadara.backend.dto.service.ServicesDTO;
import ma.almobadara.backend.exceptions.ErrorResponse;
import ma.almobadara.backend.exceptions.FunctionalException;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.service.services.ServicesService;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@RefreshScope
@RestController
@AllArgsConstructor
@Slf4j
@RequestMapping("/services")
public class ServicesController {
    private final ServicesService servicesService;

    @PostMapping(value = "/create")
    @Operation(summary = "Create a service", description = "add or update a service", tags = {"services"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = ServicesDTO.class)))),
            @ApiResponse(responseCode = "400", description = "Bad request", content = @Content(schema = @Schema(implementation = ErrorResponse.class)))
            }
    )
    public ResponseEntity<ServicesDTO> addServices(@RequestBody AddServicesDTO addServicesDTO) throws FunctionalException {
        return ResponseEntity.ok(servicesService.addService(addServicesDTO));
    }

    @GetMapping("/findActifServiceEps")
    public ResponseEntity<List<ServicesDTO>> getAllActifServiceEps(){
        return ResponseEntity.ok(servicesService.getServiceEps());
    }


    @GetMapping(value = "/findAll", produces = {"application/json"})
    @Operation(summary = "Find All services", description = "Find All services", tags = {"services"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = ServicesDTO.class))))})
    public ResponseEntity<Page<ServicesDTO>> getAllServices(
            @RequestParam(defaultValue = "0") final Integer page,
            @RequestParam(defaultValue = "10") final Integer size,
            @RequestParam(required = false) final String searchByNom,
            @RequestParam(required = false) final Long searchByCategoryId,
            @RequestParam(required = false) final Boolean searchByPriority,
            @RequestParam(required = false) final Boolean searchByStatut,
            @RequestParam(required = false) final BigDecimal searchByCosts,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") final Date minDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") final Date maxDate
    ) {

        HttpStatus status;
        Page<ServicesDTO> servicesDTOS;
        try {
            servicesDTOS = servicesService.getAllServices(page, size,  searchByNom, searchByCategoryId,searchByPriority ,searchByStatut, searchByCosts, minDate, maxDate);
            status = HttpStatus.OK;
            log.info("End resource getAllServices with size: {}, OK", servicesDTOS.getTotalElements());
        } catch (Exception e) {
            servicesDTOS = new PageImpl<>(Collections.emptyList());
            status = HttpStatus.INTERNAL_SERVER_ERROR;
            log.error("End resource getAllServices, KO: {}", e.getMessage());
        }

        return new ResponseEntity<>(servicesDTOS, status);
    }

    @Operation(summary = "Find Service by ID", description = "Returns a single Service", tags = {"Services"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(schema = @Schema(implementation = ServicesDTO.class))),
            @ApiResponse(responseCode = "404", description = "Service not found")})
    @GetMapping(value = "/{idService}", produces = {"application/json"})
    public ResponseEntity<ServicesDTO> getServiceById(@PathVariable Long idService) {


        ServicesDTO servicesDTO;
        HttpStatus status;
        try {
            servicesDTO = servicesService.getServiceById(idService);
            status = HttpStatus.OK;
            log.info("End resource getAideComplementaireById  : {}, OK", idService);
        } catch (Exception e) {
            servicesDTO = null;
            status = HttpStatus.NOT_FOUND;
            log.error("End resource getAideComplementaireById  : {}, KO: {}", idService, e.getMessage());
        }

        return new ResponseEntity<>(servicesDTO, new HttpHeaders(), status);
    }



    @GetMapping("/active")
    public ResponseEntity<List<ServicesDTO>> getAllActiveServices(
            @RequestParam("serviceTypeId") Long  serviceTypeId) throws TechnicalException {
        List<ServicesDTO> activeServices = servicesService.getAllActiveServices(serviceTypeId);
        return ResponseEntity.ok(activeServices);
    }


    @GetMapping(value = "/findActiveServicesByCategory/{idCategory}", produces = "application/json")
    @Operation(summary = "Find All Active Services by Category", description = "Retrieve all active services for a given category", tags = {"services"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = ServicesDTO.class)))),
            @ApiResponse(responseCode = "400", description = "Invalid category ID"),
            @ApiResponse(responseCode = "500", description = "Internal Server Error")
    })
    public ResponseEntity<List<ServicesDTO>> getAllActiveServicesByCategoryId(@PathVariable Long idCategory) {
        if (idCategory == null || idCategory <= 0) {
            log.warn("Invalid category ID: {}", idCategory);
            return ResponseEntity.badRequest().build();
        }

        List<ServicesDTO> servicesDTOS = servicesService.getAllActiveServicesByCategoryId(idCategory);
        log.info("Fetched {} active services for category ID {}", servicesDTOS.size(), idCategory);

        return ResponseEntity.ok(servicesDTOS);
    }


    @GetMapping("/kafalat")
    public ResponseEntity<List<ServicesDTO>> getAllKafalatServices() throws TechnicalException {
        List<ServicesDTO> activeServices = servicesService.getActiveKafalatServices();
        return ResponseEntity.ok(activeServices);
    }

    @GetMapping("/aide-complementaire")
    public ResponseEntity<List<ServicesDTO>> getAllAideComplementaireServices() throws TechnicalException {
        List<ServicesDTO> activeServices = servicesService.getActiveAideComplementaireServices();
        return ResponseEntity.ok(activeServices);
    }

    //getBudgetLinesByServiceId
    @GetMapping("/budget-lines/{serviceId}")
    public ResponseEntity<List<BudgetLineDTO>> getBudgetLinesByServiceId(@PathVariable Long serviceId) throws TechnicalException {
        List<BudgetLineDTO> budgetLines = servicesService.getBudgetLinesByServiceId(serviceId);
        return ResponseEntity.ok(budgetLines);
    }

}
