-- Create the BudgetLine table
CREATE TABLE budget_line (
                             id BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
                             code VARCHAR(255) UNIQUE,
                             amount DOUBLE PRECISION,
                             comment VARCHAR(255),
                             type VA<PERSON>HA<PERSON>(255),
                             value_currency DOUBLE PRECISION,
                             currency_id BIGINT,
                             created_at TIMESTAMP WITHOUT TIME ZONE,
                             type_prise_en_charge_id BIGINT,
                             executed BOOLEAN DEFAULT FALSE,
                             donation_id BIGINT,
                             CONSTRAINT fk_donation FOREIGN KEY (donation_id) REFERENCES donation(id) ON DELETE CASCADE
);



