package ma.almobadara.backend.dto.aideComplemenatire;

import lombok.*;
import ma.almobadara.backend.dto.administration.TagDTO;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@Builder
public class AddAideComplementaireDTO {
    private Long id;
    private String name;
    private String code;
    private String statut;
    private Double montantPrevu;
    private Double montantExecuter;
    private LocalDateTime datePlanification;
    private LocalDateTime dateExecution;
    private String slogan;
    private LocalDateTime createdAt;
    private LocalDateTime modifiedAt;
    private LocalDateTime dateDebut;
    private LocalDateTime dateFin;
    private BigDecimal costs;
    private Boolean priority;
    private Boolean propositionSystem;
    private Double amountPerBeneficiary;
    private Long serviceId;
    private String commentaire;
    private List<TagDTO> tags;
}
