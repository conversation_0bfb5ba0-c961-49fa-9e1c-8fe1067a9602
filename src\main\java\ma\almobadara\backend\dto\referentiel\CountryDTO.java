package ma.almobadara.backend.dto.referentiel;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;

import java.io.Serializable;
import java.util.Objects;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
@JsonIgnoreProperties(value="true")
@ToString
public class CountryDTO implements Serializable {

	private Long id;
	private String code;
	private String name;
	private String nameAr;
	private String nameFrench;
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}

	public String getNameFrench() {
		return nameFrench;
	}

	public void setNameFrench(String nameFrench) {
		this.nameFrench = nameFrench;
	}
	@Override
	public boolean equals(Object o) {
		if (this == o) {
			return true;
		}
		if (!(o instanceof CountryDTO parCountryDTO)) {
			return false;
		}

        if (this.id == null) {
			return false;
		}
		return Objects.equals(this.id, parCountryDTO.id);
	}
	@Override
	public int hashCode() {
		return Objects.hash(this.id);
	}
	@Override
	public String toString() {
		return "ParCountryDTO{" +
				"id=" + getId() +
				", name='" + getName() + "'" +
				", nameFrench='" + nameFrench + '\'' +
				"}";
	}

}
