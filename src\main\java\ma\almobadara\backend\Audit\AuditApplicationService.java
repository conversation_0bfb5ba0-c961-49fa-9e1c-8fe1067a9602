package ma.almobadara.backend.Audit;



import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import ma.almobadara.backend.Audit.entities.AuditEntity;
import ma.almobadara.backend.Audit.repositories.AuditEntityRepository;
import ma.almobadara.backend.Audit.utils.AuditUtils;
import ma.almobadara.backend.dto.administration.CacheAdUserDTO;
import ma.almobadara.backend.service.administration.TokenImpersonationService;
import jakarta.servlet.http.HttpServletRequest;
import ma.almobadara.backend.util.times.TimeWatch;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.*;
import java.util.stream.Collectors;

import static ma.almobadara.backend.Audit.ObjectConverter.convertObjectToJson;
import static ma.almobadara.backend.util.constants.GlobalConstants.FAMILLE;
import static ma.almobadara.backend.util.constants.GlobalConstants.VIEW;

@Service
public class AuditApplicationService {

    private final Logger log = LoggerFactory.getLogger(getClass());

    public static String CANAL_HEADER_NAME = "canal";
    public static String USER_AGENT_HEADER_NAME = "user-agent";
    private static final String appName = "AlMobadaraApp";

    private final EntityManager entityManager;
    private final TokenImpersonationService impersonationService;
    private final HttpServletRequest request;

    @Autowired
    private AuditEntityRepository auditEntityRepository;
    @Autowired

    private final ObjectMapper objectMapper = new ObjectMapper();

    public AuditApplicationService(EntityManager entityManager, TokenImpersonationService impersonationService, HttpServletRequest request) {
        this.entityManager = entityManager;
        this.impersonationService = impersonationService;
        this.request = request;
    }

    public void audit(String activity, String user, String message, String initialValue, String newValue, String operation, String subOperation) {
        TimeWatch watch = TimeWatch.start();

        String canal = AuditUtils.getCurrentRequest().getHeader(AuditApplicationService.CANAL_HEADER_NAME);
        String sessionId = AuditUtils.getCurrentRequest().getRequestedSessionId();

        String userIpAddress = AuditUtils.getCurrentRequest().getRemoteAddr();
        String userAgent = AuditUtils.getCurrentRequest().getHeader(USER_AGENT_HEADER_NAME);

        String appName = AuditApplicationService.appName;
        long processId = Thread.currentThread().getId();
        String processName = Thread.currentThread().getName();
        String machineName = "Unknown";
        String machineAddress = "Unknown";
        try {
            InetAddress host = InetAddress.getLocalHost();
            machineName = host.getHostName();
            machineAddress = host.getHostAddress();
        } catch (UnknownHostException e) {
        }
        Date auditTime = Calendar.getInstance().getTime();

        // Get impersonation token from request header
        String impersonationToken = request.getHeader("Impersonation-Token");
        if (impersonationToken != null && !impersonationToken.isEmpty()) {
            CacheAdUserDTO cacheAdUserDTO = impersonationService.getImpersonatedUser(impersonationToken);
            if(cacheAdUserDTO != null) {
                user+= " (dans le rôle de " + cacheAdUserDTO.getFirstName() + " " +  cacheAdUserDTO.getLastName() + ")";
            }
        }
        AuditEntity auditEntity = new AuditEntity();
        auditEntity.setCanal(canal);
        auditEntity.setAppName(appName);
        auditEntity.setSessionId(sessionId);
        auditEntity.setActivity(activity);

        auditEntity.setUser(user);
        auditEntity.setUserIpAddress(userIpAddress);
        auditEntity.setUserAgent(userAgent);
        auditEntity.setMessage(message);
        auditEntity.setProcessId(processId);
        auditEntity.setProcess(processName);
        auditEntity.setMachine(machineName);
        auditEntity.setMachineAddress(machineAddress);
        auditEntity.setAuditTime(auditTime);
        auditEntity.setInitialValue(initialValue);
        auditEntity.setNewValue(newValue);
        auditEntity.setOperation(operation);
        auditEntity.setSubOperation(subOperation);

        auditEntityRepository.save(auditEntity);

        log.info(initialValue);

        log.debug("Audit '{}' Canal '{}' " +
                        "AppName '{}' " +
                        "SessionID '{}' " +
                        "Activity '{}' " +
                        "User '{}' " +
                        "UserRemoteIP '{}' " +
                        "UserAgent '{}' " +
                        "Message '{}' " +
                        "ProcessID '{}' " +
                        "ProcessName '{}' " +
                        "MachineName '{}' " +
                        "MachineAddress '{}' " +
                        "AuditTime '{}' ",
                watch.toMS(), canal, appName, sessionId, activity, user, userIpAddress, userAgent, message, processId, processName, machineName, machineAddress, auditTime);
    }

    public Page<AuditEntity> getAllAudits(int page, int size, String searchByValue, String searchByOperation, String searchBySubOperation, String searchByUser, Date minDate, Date maxDate) {
        Sort sort = Sort.by(Sort.Direction.DESC, "auditTime");
        Pageable pageable = PageRequest.of(page, size, sort);

        List<AuditEntity> auditEntityList = auditEntityRepository.findAll(sort);

        if (searchByValue != null || searchByOperation != null || searchBySubOperation != null || searchByUser != null || minDate != null || maxDate != null) {
            auditEntityList = filterAudits(auditEntityList, searchByValue, searchByOperation, searchBySubOperation, searchByUser, minDate, maxDate);
        }

        ObjectMapper mapper = new ObjectMapper();
        List<AuditEntity> filteredAuditEntityList = auditEntityList.stream()
                .filter(auditEntity -> !"Recherche".equals(auditEntity.getSubOperation()) || (auditEntity.getInitialValue() != null || auditEntity.getNewValue() != null))
                .peek(auditEntity -> {
                    try {
                        if (auditEntity.getInitialValue() != null) {
                            auditEntity.setInitialValueJson(mapper.readValue(auditEntity.getInitialValue(), Map.class));
                        }

                        if (auditEntity.getNewValue() != null) {
                            auditEntity.setNewValueJson(mapper.readValue(auditEntity.getNewValue(), Map.class));
                        }
                    } catch (IOException e) {
                        e.printStackTrace(); // Gérer l'exception selon vos besoins
                    }
                })
                .collect(Collectors.toList());

        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), filteredAuditEntityList.size());

        if (start > filteredAuditEntityList.size()) {
            start = filteredAuditEntityList.size();
        }
        if (end > filteredAuditEntityList.size()) {
            end = filteredAuditEntityList.size();
        }

        List<AuditEntity> paginatedList = filteredAuditEntityList.subList(start, end);

        long totalElements = filteredAuditEntityList.size();

        return new PageImpl<>(paginatedList, pageable, totalElements);
    }

    public List<AuditEntity> filterAudits(List<AuditEntity> auditEntityList, String searchByValue, String searchByOperation, String searchBySubOperation, String searchByUser, Date minDate, Date maxDate) {
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<AuditEntity> criteriaQuery = criteriaBuilder.createQuery(AuditEntity.class);
        Root<AuditEntity> root = criteriaQuery.from(AuditEntity.class);

        Predicate predicates = criteriaBuilder.conjunction();

        if (searchByValue != null && !searchByValue.isEmpty()) {
            Predicate initialValuePredicate = criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("initialValue")),
                    "%" + searchByValue.toLowerCase() + "%"
            );
            Predicate newValuePredicate = criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("newValue")),
                    "%" + searchByValue.toLowerCase() + "%"
            );
            Predicate messageePredicate = criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("activity")),
                    "%" + searchByValue.toLowerCase() + "%"
            );
            predicates = criteriaBuilder.or(initialValuePredicate, newValuePredicate, messageePredicate);
        }
//        if (searchByMessage != null && !searchByMessage.isEmpty()) {
//            predicates = criteriaBuilder.and(predicates, criteriaBuilder.like(
//                    criteriaBuilder.lower(root.get("activity")),
//                    "%" + searchByMessage.toLowerCase() + "%"
//            ));
//        }

        if (searchByOperation != null && !searchByOperation.isEmpty()) {
            predicates = criteriaBuilder.and(predicates, criteriaBuilder.equal(root.get("operation"), searchByOperation));
        }

        if (searchBySubOperation != null && !searchBySubOperation.isEmpty()) {
            predicates = criteriaBuilder.and(predicates, criteriaBuilder.equal(root.get("subOperation"), searchBySubOperation));
        }

        if (searchByUser != null && !searchByUser.isEmpty()) {
            predicates = criteriaBuilder.and(predicates, criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("user")),
                    "%" + searchByUser.toLowerCase() + "%"
            ));
        }

        if (minDate != null) {
            predicates = criteriaBuilder.and(predicates, criteriaBuilder.greaterThanOrEqualTo(root.get("auditTime"), minDate));
        }
        if (maxDate != null) {
            predicates = criteriaBuilder.and(predicates, criteriaBuilder.lessThanOrEqualTo(root.get("auditTime"), maxDate));
        }

        criteriaQuery.where(predicates);
        criteriaQuery.orderBy(criteriaBuilder.desc(root.get("auditTime")));

        return entityManager.createQuery(criteriaQuery).getResultList();
    }

    @Transactional
    public void purgeAuditEntries(String operation, String subOperation, String userName, Date startDate, Date endDate) {
        StringBuilder queryBuilder = new StringBuilder("DELETE FROM AuditEntity a WHERE 1=1");

        Map<String, Object> parameters = new HashMap<>();

        if (operation != null) {
            queryBuilder.append(" AND a.operation = :operation");
            parameters.put("operation", operation);
        }
        if (subOperation != null) {
            queryBuilder.append(" AND a.subOperation = :subOperation");
            parameters.put("subOperation", subOperation);
        }
        if (userName != null) {
            queryBuilder.append(" AND a.user = :userName");
            parameters.put("userName", userName);
        }
        if (startDate != null) {
            queryBuilder.append(" AND a.auditTime >= :startDate");
            parameters.put("startDate", startDate);
        }
        if (endDate != null) {
            queryBuilder.append(" AND a.auditTime <= :endDate");
            parameters.put("endDate", endDate);
        }

        Query query = entityManager.createQuery(queryBuilder.toString());
        parameters.forEach(query::setParameter);
        int deletedCount = query.executeUpdate();

    }

}
