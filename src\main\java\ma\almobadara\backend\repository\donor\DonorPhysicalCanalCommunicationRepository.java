package ma.almobadara.backend.repository.donor;

import jakarta.transaction.Transactional;
import ma.almobadara.backend.model.donor.DonorPhysicalCanalCommunication;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface DonorPhysicalCanalCommunicationRepository extends JpaRepository<DonorPhysicalCanalCommunication, Long> {

    @Transactional
    @Modifying
    @Query(value = "DELETE FROM donor_physical_canal_communication WHERE donor_physical_id = :donorId", nativeQuery = true)
    void deleteAllCanalByDonorId(Long donorId);

}
