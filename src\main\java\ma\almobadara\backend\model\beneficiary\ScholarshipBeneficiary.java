package ma.almobadara.backend.model.beneficiary;


import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

import static ma.almobadara.backend.util.strings.HandleSpecialChars.escapeSpecialChars;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ScholarshipBeneficiary extends BaseEntity{

    private Long scholarshipId;
    private Double amount;
    private int periodicity;
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date startDate;
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endDate;
    private String comment;
    private Long currencyId;
    private Double valueCurrency;
    @ManyToOne
    @JoinColumn(name = "beneficiary_id")
    private Beneficiary beneficiary;

    public String getAudit(Map<String,String> params, String beneficiaryName) {

        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String startDate = dateFormat.format(this.startDate);
        String endDate = dateFormat.format(this.startDate);

        return "{"
                + "\"Bénéficiaire\": \"" + escapeSpecialChars(beneficiaryName) + "\","
                + "\"Bourse\": \"" + escapeSpecialChars(params.get("bource")) + "\","
                + "\"Périodicité\": \"" + escapeSpecialChars(String.valueOf(periodicity)) + "\","
                + "\"Date de début\": \"" + escapeSpecialChars(startDate) + "\","
                + "\"Date de fin\": \"" + escapeSpecialChars(endDate) + "\","
                + "\"Montant\": \"" + escapeSpecialChars(String.valueOf(amount)) + "\","
                +(params.get("currency")!=null?
                "\"Devise\": \"" + escapeSpecialChars(params.get("currency")) + "\","
                + "\"Equivalent\": \"" + escapeSpecialChars(String.valueOf(valueCurrency)) + "\",":"")
                + "\"Commentaire\": \"" + escapeSpecialChars(comment) + "\""
                + "}";
    }

}
