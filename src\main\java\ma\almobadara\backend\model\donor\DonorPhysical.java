package ma.almobadara.backend.model.donor;

import jakarta.persistence.Entity;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Transient;
import lombok.*;
import lombok.experimental.SuperBuilder;
import ma.almobadara.backend.dto.referentiel.ProfessionDTO;
import ma.almobadara.backend.dto.referentiel.TypeIdentityDTO;
import ma.almobadara.backend.util.strings.HandleSpecialChars;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static ma.almobadara.backend.util.strings.HandleSpecialChars.escapeSpecialChars;

@EqualsAndHashCode(callSuper = true)
@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@ToString
public class DonorPhysical extends Donor{

    private String firstName;
    private String lastName;
    private String firstNameAr;
    private String lastNameAr;
    private String sex;
    private String email;
    private String phoneNumber;
    private String password;
    private String type;
    private String pictureUrl;
    private Long typeIdentity;
    @Transient
    private TypeIdentityDTO identity;
    private Long professionId;
    @Transient
    private ProfessionDTO profession;
    @OneToMany(targetEntity = DonorPhysicalLanguageCommunication.class, mappedBy = "donorPhysical")
    private Set<DonorPhysicalLanguageCommunication> donorPhysicalLanguageCommunications;

    @OneToMany(targetEntity = DonorPhysicalCanalCommunication.class, mappedBy = "donorPhysical")
    private Set<DonorPhysicalCanalCommunication> donorPhysicalCanalCommunication;


    public String toDTOString(List<String> donorPhysicalCanalCommunications,
                              List<String> donorPhysicalLanguageCommunications) {

        return "{"
                + "\"Nom\": \"" + escapeSpecialChars(lastName) + "\","
                + "\"Prénom\": \"" + escapeSpecialChars(firstName) + "\","
                + "\"Prénom Arabe\": \"" + escapeSpecialChars(firstNameAr) + "\","
                + "\"Nom Arabe\": \"" + escapeSpecialChars(lastNameAr) + "\","
                + "\"Sexe\": \"" + escapeSpecialChars(sex) + "\","
                + "\"Email\": \"" + escapeSpecialChars(email) + "\","
                + "\"Téléphone\": \"" + escapeSpecialChars(phoneNumber) + "\","
                + "\"Statut\": \"" + escapeSpecialChars(super.getStatus().getName()) + "\","
                + "\"Num Identité\": \"" + escapeSpecialChars(super.getIdentityCode()) + "\","
                + "\"Type Identité\": \"" + (identity != null ? escapeSpecialChars(identity.getName()) : "-") + "\","
                + "\"Profession\": \"" + (profession != null ? escapeSpecialChars(profession.getName()) : "-") + "\","
                + "\"Adresse\": \"" + escapeSpecialChars(super.getAddress()) + "\","
                + "\"Adresse Arabe\": \"" + escapeSpecialChars(super.getAddressAr()) + "\","
                + "\"Canal Communications\": \""
                + donorPhysicalCanalCommunications.stream()
                .map(HandleSpecialChars::escapeSpecialChars)
                .collect(Collectors.joining(", ")) + "\","
                + "\"Language Communications\": \""
                + donorPhysicalLanguageCommunications.stream()
                .map(HandleSpecialChars::escapeSpecialChars)
                .collect(Collectors.joining(", ")) + "\","
                + "\"Année Première Donation\": \"" + escapeSpecialChars(super.getFirstDonationYear()) + "\","
                + "\"Commentaire\": \"" + escapeSpecialChars(super.getComment()) + "\","
                + "\"Code\": \"" + escapeSpecialChars(super.getCode()) + "\","
                + "\"Type\": \"" + escapeSpecialChars(type) + "\""
                + "}";
    }




}
