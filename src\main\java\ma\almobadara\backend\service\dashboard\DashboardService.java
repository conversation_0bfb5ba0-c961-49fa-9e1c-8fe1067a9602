package ma.almobadara.backend.service.dashboard;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.clients.RefFeignClient;
import ma.almobadara.backend.controller.referentiel.RefController;
import ma.almobadara.backend.dto.dashboard.*;
import ma.almobadara.backend.repository.aideComplemenatire.AideComplementaireRepository;
import ma.almobadara.backend.dto.referentiel.CategoryBeneficiaryDTO;
import ma.almobadara.backend.dto.referentiel.SchoolLevelDTO;
import ma.almobadara.backend.mapper.BeneficiaryMapper;
import ma.almobadara.backend.model.administration.Zone;
import ma.almobadara.backend.model.beneficiary.BeneficiaryStatut;
import ma.almobadara.backend.model.takenInCharge.TakenInCharge;
import ma.almobadara.backend.model.takenInCharge.TakenInChargeOperation;
import ma.almobadara.backend.repository.administration.EpsRepository;
import ma.almobadara.backend.repository.administration.ServiceCollectEpsRepository;
import ma.almobadara.backend.repository.beneficiary.BeneficiaryAdHocGroupRepository;
import ma.almobadara.backend.repository.beneficiary.BeneficiaryRepository;
import ma.almobadara.backend.repository.donation.DonationRepository;
import ma.almobadara.backend.repository.donor.DonorAnonymeRepository;
import ma.almobadara.backend.repository.donor.DonorMoralRepository;
import ma.almobadara.backend.repository.donor.DonorPhysicalRepository;
import ma.almobadara.backend.repository.donor.DonorRepository;
import ma.almobadara.backend.repository.takenInCharge.TakenInChargeOperationRepository;
import ma.almobadara.backend.repository.takenInCharge.TakenInChargeRepository;
import ma.almobadara.backend.util.times.DateUtils;
import org.springframework.stereotype.Service;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Slf4j
@Service
public class DashboardService {

    private final AideComplementaireRepository aideComplementaireRepository;

    private final BeneficiaryRepository beneficiaryRepository;
    private final DonorRepository donorRepository;
    private final DonationRepository donationRepository;
    private final DonorPhysicalRepository donorPhysicalRepository;
    private final DonorAnonymeRepository donorAnonymeRepository;
    private final DonorMoralRepository donorMoralRepository;
    private final TakenInChargeRepository takenInChargeRepository;
    private final BeneficiaryAdHocGroupRepository beneficiaryAdHocGroupRepository;
    private final BeneficiaryMapper beneficiaryMapper;
    private final RefFeignClient refFeignClient;
    private final RefController refController;
    private final TakenInChargeOperationRepository takenInChargeOperationRepository;
    private final EpsRepository epsRepository;
    private final ServiceCollectEpsRepository serviceCollectEpsRepository;

    public List<MonthlyServiceDataDTO> getGroupedActiveKafalatesByMonthAndType() {
        List<ActiveKafalatesByMonthAndTypeDTO> rawData = beneficiaryRepository.getActiveKafalatesByMonthAndType(
                LocalDate.now().minusYears(1).atStartOfDay(ZoneId.systemDefault()).toInstant());

        // Map the data by month and year
        Map<Integer, Map<Integer, List<ActiveKafalatesByMonthAndTypeDTO>>> groupedData = rawData.stream()
                .collect(Collectors.groupingBy(dto -> dto.getYear(), Collectors.groupingBy(dto -> dto.getMonth())));

        List<MonthlyServiceDataDTO> result = new ArrayList<>();

        // Get the current month and year
        LocalDate currentDate = LocalDate.now();
        int currentMonth = currentDate.getMonthValue();
        int currentYear = currentDate.getYear();

        // Calculate the month to start from
        LocalDate startDate = currentDate.minusMonths(11);

        // Initialize a map to track cumulative totals by service ID
        Map<Integer, Long> cumulativeTotalsByService = new HashMap<>();

        for (LocalDate date = startDate; !date.isAfter(currentDate); date = date.plusMonths(1)) {
            int year = date.getYear();
            int month = date.getMonthValue();

            List<ActiveKafalatesByMonthAndTypeDTO> services;
            if (groupedData.containsKey(year) && groupedData.get(year).containsKey(month)) {
                services = groupedData.get(year).get(month);
            } else {
                services = new ArrayList<>();
            }

            // Update cumulative totals for each service ID
            for (ActiveKafalatesByMonthAndTypeDTO service : services) {
                cumulativeTotalsByService.merge(
                        Math.toIntExact(service.getServicesId()),
                        service.getTotalActiveKafalates(),
                        Long::sum
                );
            }

            MonthlyServiceDataDTO monthlyData = new MonthlyServiceDataDTO(
                    month,
                    DateUtils.getMonthName(month),
                    year,
                    services,
                    new HashMap<>(cumulativeTotalsByService)  // Create a copy to avoid mutation
            );

            result.add(monthlyData);
        }

        return result;
    }


    public List<MonthlyServiceDataDTO> getActiveKafalatesByMonthAndTypeWithoutTakenInCharge() {
        List<ActiveKafalatesByMonthAndTypeDTO> rawData = beneficiaryRepository.getActiveKafalatesByMonthAndTypeWithoutTakenInCharge(
                LocalDate.now().minusYears(1).atStartOfDay(ZoneId.systemDefault()).toInstant());

        // Map the data by month and year
        Map<Integer, Map<Integer, List<ActiveKafalatesByMonthAndTypeDTO>>> groupedData = rawData.stream()
                .collect(Collectors.groupingBy(dto -> dto.getYear(), Collectors.groupingBy(dto -> dto.getMonth())));

        List<MonthlyServiceDataDTO> result = new ArrayList<>();

        // Get the current month and year
        LocalDate currentDate = LocalDate.now();
        int currentMonth = currentDate.getMonthValue();
        int currentYear = currentDate.getYear();

        // Calculate the month to start from
        LocalDate startDate = currentDate.minusMonths(11);

        // Initialize a map to track cumulative totals by service ID
        Map<Integer, Long> cumulativeTotalsByService = new HashMap<>();

        for (LocalDate date = startDate; !date.isAfter(currentDate); date = date.plusMonths(1)) {
            int year = date.getYear();
            int month = date.getMonthValue();

            List<ActiveKafalatesByMonthAndTypeDTO> services;
            if (groupedData.containsKey(year) && groupedData.get(year).containsKey(month)) {
                services = groupedData.get(year).get(month);
            } else {
                services = new ArrayList<>();
            }

            // Update cumulative totals for each service ID
            for (ActiveKafalatesByMonthAndTypeDTO service : services) {
                cumulativeTotalsByService.merge(
                        Math.toIntExact(service.getServicesId()),
                        service.getTotalActiveKafalates(),
                        Long::sum
                );
            }

            MonthlyServiceDataDTO monthlyData = new MonthlyServiceDataDTO(
                    month,
                    DateUtils.getMonthName(month),
                    year,
                    services,
                    new HashMap<>(cumulativeTotalsByService)  // Create a copy to avoid mutation
            );

            result.add(monthlyData);
        }

        return result;
    }

    public List<ActiveKafalatesByMonthAndTypeDTO> getActiveKafalatesOrphansByMonthAndSex() {
        return beneficiaryRepository.getActiveKafalatesOrphansByMonthAndSex(LocalDateTime.now().minusYears(1).atZone(ZoneOffset.ofHours(0)).toInstant());
    }

    public List<ActiveKafalatesByMonthAndCityDTO> getActiveKafalatesOrphansByMonthAndCity() {
        return beneficiaryRepository.getActiveKafalatesOrphansByMonthAndCity(LocalDateTime.now().minusYears(1).atZone(ZoneOffset.ofHours(0)).toInstant());
    }


    public Map<String, List<ActiveKafalatesByMonthAndCityDTO>> getActiveKafalatesOrphansByMonthAndCityAndRegion() {
        Instant maxDate = LocalDateTime.now().minusYears(1).atZone(ZoneOffset.ofHours(0)).toInstant();

        Map<Boolean, List<ActiveKafalatesByMonthAndCityDTO>> kafalatesByForeign = beneficiaryRepository
                .getActiveKafalatesOrphansByMonthAndCityAndRegion(maxDate)
                .stream()
                .map(activeKafalatesByMonthAndCityDTO -> {
                    var cityWithRegionAndCountry = refController.getCityWithRegionAndCountry(activeKafalatesByMonthAndCityDTO.getCityId()).getBody();
                    assert cityWithRegionAndCountry != null;
                    String regionName = cityWithRegionAndCountry.getRegion().getName();
                    String countryName = cityWithRegionAndCountry.getCountry().getName();
                    activeKafalatesByMonthAndCityDTO.setRegion(regionName);
                    activeKafalatesByMonthAndCityDTO.setCountry(countryName);
                    return activeKafalatesByMonthAndCityDTO;
                })
                .collect(Collectors.partitioningBy(kafalate -> kafalate.getCountry().equals("Morocco")));

        Map<String, List<ActiveKafalatesByMonthAndCityDTO>> result = new HashMap<>();
        result.put("foreign", kafalatesByForeign.get(false)); // Add non-Moroccan kafalates to "foreign" category
        result.putAll(kafalatesByForeign.get(true).stream()
                .collect(Collectors.groupingBy(ActiveKafalatesByMonthAndCityDTO::getRegion))); // Group by region for Moroccan kafalates

        return result;
    }


    public List<ActiveKafalatesByMonthAndAge> getActiveKafalatesOrphansByMonthAndAge() {
        Instant maxDate = LocalDateTime.now().minusYears(1).atZone(ZoneOffset.ofHours(0)).toInstant();
        return beneficiaryRepository.getActiveKafalatesOrphansByMonthAndAge(maxDate).stream().map(activeKafalatesByMonthAndAge -> {
             activeKafalatesByMonthAndAge.setAge(ChronoUnit.YEARS.between(activeKafalatesByMonthAndAge.getBirthDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate(), LocalDate.now()));
                    return activeKafalatesByMonthAndAge;
        }).toList();
    }

    public List<MonthlyDonorsDataDto> getNumberDonorByMonth() {
        LocalDateTime maxDate = LocalDateTime.now().minusYears(1);
        List<NombreDonateurByMonth> rawData =  donorRepository.getNumberDonorByMonth(maxDate);

        // Map the data by month and year
        Map<Integer, Map<Integer, List<NombreDonateurByMonth>>> groupedData = rawData.stream()
                .collect(Collectors.groupingBy(NombreDonateurByMonth::getYear, Collectors.groupingBy(NombreDonateurByMonth::getMonth)));

        List<MonthlyDonorsDataDto> result = new ArrayList<>();

        // Get the current month and year
        LocalDate currentDate = LocalDate.now();
        int currentMonth = currentDate.getMonthValue();
        int currentYear = currentDate.getYear();

        // Calculate the month to start from
        LocalDate startDate = currentDate.minusMonths(11);

        for (LocalDate date = startDate; !date.isAfter(currentDate); date = date.plusMonths(1)) {
            int year = date.getYear();
            int month = date.getMonthValue();

            // If data for this month exists, add it to the result
            if (groupedData.containsKey(year) && groupedData.get(year).containsKey(month)) {
                result.add(new MonthlyDonorsDataDto(month, DateUtils.getMonthName(month), year, groupedData.get(year).get(month)));
            } else {
                // If no data exists for this month, add an empty entry
                result.add(new MonthlyDonorsDataDto(month, DateUtils.getMonthName(month), year, new ArrayList<>()));
            }
        }

        return result;
    }

    public List<NombreDonateurByMonth> getNumberDonorActiveByMonthAndSex() {
        LocalDateTime maxDate = LocalDateTime.now().minusYears(1);
        return donorRepository.getNumberDonorActiveByMonthAndSex(maxDate);
    }

    public List<MonthlyTotalDonationDataDto> getTotalDonationsByMonthAndType() {
        LocalDateTime maxDate = LocalDateTime.now().minusYears(1);
        List<TotalDonation> rawData =  donationRepository.getTotalDonationsByMonthAndType(maxDate);
        // Map the data by month and year
        Map<Integer, Map<Integer, List<TotalDonation>>> groupedData = rawData.stream()
                .collect(Collectors.groupingBy(TotalDonation::getYear, Collectors.groupingBy(TotalDonation::getMonth)));

        List<MonthlyTotalDonationDataDto> result = new ArrayList<>();

        // Get the current month and year
        LocalDate currentDate = LocalDate.now();
        int currentMonth = currentDate.getMonthValue();
        int currentYear = currentDate.getYear();

        // Calculate the month to start from
        LocalDate startDate = currentDate.minusMonths(11);

        for (LocalDate date = startDate; !date.isAfter(currentDate); date = date.plusMonths(1)) {
            int year = date.getYear();
            int month = date.getMonthValue();

            // If data for this month exists, add it to the result
            if (groupedData.containsKey(year) && groupedData.get(year).containsKey(month)) {
                result.add(new MonthlyTotalDonationDataDto(month, DateUtils.getMonthName(month), year, groupedData.get(year).get(month)));
            } else {
                // If no data exists for this month, add an empty entry
                result.add(new MonthlyTotalDonationDataDto(month, DateUtils.getMonthName(month), year, new ArrayList<>()));
            }
        }
        return result;
    }

    public List<DonorActive> getDonorPhysicalActiveByMonthAndCity() {
        LocalDateTime maxDate = LocalDateTime.now().minusYears(1);
        return donorRepository.getDonorPhysicalActiveByMonthAndCity(maxDate);
    }

    public Map<String, List<DonorActive>> getActiveDonorPhysicalByMonthAndCityAndRegion() {
        LocalDateTime maxDate = LocalDateTime.now().minusYears(1);
        Map<Boolean, List<DonorActive>> donorByForeign = donorRepository.getDonorPhysicalActiveByMonthAndCity(maxDate)
                .stream()
                .map(donorActive -> {
                    String regionName = (Objects.requireNonNull(refController.getCityWithRegionAndCountry(donorActive.getCityId()).getBody()).getRegion().getName());
                    String countryName = Objects.requireNonNull(refController.getCityWithRegionAndCountry(donorActive.getCityId()).getBody()).getCountry().getName();
                    donorActive.setCountry(countryName);
                    donorActive.setRegion(regionName);
                    return donorActive;
                })
                .collect(Collectors.partitioningBy(donor -> donor.getCountry().equals("Morocco")));

        Map<String, List<DonorActive>> result = new HashMap<>();
        result.put("foreign", donorByForeign.get(false)); // Add non-Moroccan donors to "foreign" category
        result.putAll(donorByForeign.get(true).stream()
                .collect(Collectors.groupingBy(DonorActive::getRegion))); // Group by region for Moroccan donors

        return result;
    }

    public List<DonorActive> getDonorMoralActiveByMonthAndCity() {
        LocalDateTime maxDate = LocalDateTime.now().minusYears(1);
        return donorRepository.getDonorMoralActiveByMonthAndCity(maxDate);
    }

    public Map<String, List<DonorActive>> getActiveDonorMoralByMonthAndCityAndRegion() {
        LocalDateTime maxDate = LocalDateTime.now().minusYears(1);
        Map<Boolean, List<DonorActive>> donorByForeign = donorRepository.getDonorMoralActiveByMonthAndCity(maxDate)
                .stream()
                .map(donorActive -> {
                    String regionName = (Objects.requireNonNull(refController.getCityWithRegionAndCountry(donorActive.getCityId()).getBody()).getRegion().getName());
                    String countryName = Objects.requireNonNull(refController.getCityWithRegionAndCountry(donorActive.getCityId()).getBody()).getCountry().getName();
                    donorActive.setCountry(countryName);
                    donorActive.setRegion(regionName);
                    return donorActive;
                })
                .collect(Collectors.partitioningBy(donor -> donor.getCountry().equals("Morocco")));

        Map<String, List<DonorActive>> result = new HashMap<>();
        result.put("foreign", donorByForeign.get(false)); // Add non-Moroccan donors to "foreign" category
        result.putAll(donorByForeign.get(true).stream()
                .collect(Collectors.groupingBy(DonorActive::getRegion))); // Group by region for Moroccan donors

        return result;
    }

//    public List<BeneficiaryLevel> getBeneficiaryLevel() {
//
//        return donorRepository.getBeneficiaryLevel();
//    }


    public List<BeneficiaryLevel> getBeneficiaryLevel() {
        List<BeneficiaryLevel> beneficiaryLevels = donorRepository.getBeneficiaryLevel();
        for (BeneficiaryLevel level : beneficiaryLevels) {
            SchoolLevelDTO schoolLevelDTO = refController.getParSchoolLevel(level.getSchoolLevel()).getBody();
            if (schoolLevelDTO != null) {
                level.setSchoolLevelName(schoolLevelDTO.getName());
            }
        }

        return beneficiaryLevels;
    }

    public EPSStatsDTO getEPSStats() {
        Map<String, Long> epsByStatus = new HashMap<>();
        Map<String, Long> serviceCollectionByStatus = new HashMap<>();

        // Get EPS by status (active/inactive)
        List<Object[]> epsByStatusData = epsRepository.countEpsByStatus();
        for (Object[] row : epsByStatusData) {
            if(row[0] == null && row[1] == null){
                continue;
            }
            boolean status = (boolean) row[0];
            Long count = ((Number) row[1]).longValue();
            epsByStatus.put(status ? "active" : "inactive", count);
        }

        LocalDate today = LocalDate.now();
        int currentMonth = today.getMonthValue();
        int currentYear = today.getYear();
        // Get service collections by status
        List<Object[]> serviceCollectionByStatusData = serviceCollectEpsRepository.countServiceCollectionByCustomStatus(currentMonth,currentYear);
        for (Object[] row : serviceCollectionByStatusData) {
            if(row[0] == null && row[1] == null){
                continue;
            }
            String status = (String) row[0];
            Long count = ((Number) row[1]).longValue();
            serviceCollectionByStatus.put(status, count);
        }

        return EPSStatsDTO.builder()
                .epsByStatus(epsByStatus)
                .serviceCollectionByStatus(serviceCollectionByStatus)
                .build();
    }

    public DonationStatsDTO getDonationStats() {
        Map<String, Double> donationByDonationType = new HashMap<>();
        Map<String, Double> donationByChannel = new HashMap<>();
        Map<String, Double> donationByService = new HashMap<>();
        Map<String, Double> donationByMonth = new LinkedHashMap<>();
        LocalDate currentMonth = LocalDate.now();
        for (int i = 0; i < 12; i++) {
            String month = currentMonth.minusMonths(i).format(DateTimeFormatter.ofPattern("yyyy-MM"));
            donationByMonth.put(month, 0D);
        }
//        // Get donations by donor type
//        List<Object[]> donationsByDonorType = donationRepository.getDonationsByDonorType();
//        for (Object[] row : donationsByDonorType) {
//            String donorType = (String) row[0];
//            Double amount = ((Number) row[1]).doubleValue();
//
//            // Map discriminator values to readable names
//            switch (donorType) {
//                case "DonorPhysical":
//                    donationByDonorType.put("physique", amount);
//                    break;
//                case "DonorMoral":
//                    donationByDonorType.put("moral", amount);
//                    break;
//                case "DonorAnonyme":
//                    donationByDonorType.put("anonyme", amount);
//                    break;
//                default:
//                    donationByDonorType.put(donorType, amount);
//            }
//        }
//
        // Get donations by donation type
        List<Object[]> donationsByDonationType = donationRepository.getDonationsByDonationType();
        for (Object[] row : donationsByDonationType) {
            String donationType = (String) row[0];
            Double amount = ((Number) row[1]).doubleValue();
            donationByDonationType.put(donationType, amount);
        }

        // Get donations by channel
        List<Object[]> donationsByChannel = donationRepository.getDonationsByChannel();
        for (Object[] row : donationsByChannel) {
            if(row[0] == null){
                continue;
            }
            Long channel =   ((Number)row[0]).longValue();
            String channelName = refFeignClient.getMetCanalDonation(channel).getName();
            Double amount = ((Number) row[1]).doubleValue();
            donationByChannel.put(channelName, amount);
        }

        // Get donations by service
        List<Object[]> donationsByService = donationRepository.getDonationsByService();
        for (Object[] row : donationsByService) {
            String serviceName = (String) row[0];
            Double amount = ((Number) row[1]).doubleValue();
            donationByService.put(serviceName, amount);
        }

        // Get donations by month
        List<Object[]> donationsByMonth = donationRepository.getDonationsByMonth();
        for (Object[] row : donationsByMonth) {
            String month = (String) row[0];
            Double amount = ((Number) row[1]).doubleValue();
            if(donationByMonth.containsKey(month)){
                donationByMonth.put(month, amount);
            }

        }

        return DonationStatsDTO.builder()
                .donationByDonationType(donationByDonationType)
                .donationByChannel(donationByChannel)
                .donationByService(donationByService)
                .donationByMonth(donationByMonth)
                .build();
    }

    public KafalatStatsDTO getKafalatStats() {
        Map<String, Long> kafalatByStatus = new HashMap<>();
        Map<String, Long> kafalatByService = new HashMap<>();
        Map<String, Long> kafalatByMonth = new LinkedHashMap<>();
        LocalDate currentMonth = LocalDate.now();
        for (int i = 0; i < 12; i++) {
            String month = currentMonth.minusMonths(i).format(DateTimeFormatter.ofPattern("yyyy-MM"));
            kafalatByMonth.put(month, 0L);
        }
        // Get kafalat by status (actif/inactif)
        List<Object[]> kafalatsByStatus = takenInChargeRepository.getKafalatsByStatus();
        for (Object[] row : kafalatsByStatus) {
            String status = (String) row[0];
            Long count = ((Number) row[1]).longValue();
            kafalatByStatus.put(status, count);
        }


        // Get kafalat by service
        List<Object[]> kafalatsByService = takenInChargeRepository.getKafalatsByService();
        for (Object[] row : kafalatsByService) {
            String serviceName = (String) row[0];
            Long count = ((Number) row[1]).longValue();
            kafalatByService.put(serviceName, count);
        }

        // Get kafalat by month
        List<Object[]> kafalatsByMonth = takenInChargeRepository.getKafalatsByMonth();
        for (Object[] row : kafalatsByMonth) {
            String month = (String) row[0];
            Long count = ((Number) row[1]).longValue();
            kafalatByMonth.put(month, count);
        }
        return KafalatStatsDTO.builder()
                .kafalatByStatus(kafalatByStatus)
                .kafalatByService(kafalatByService)
                .kafalatByMonth(kafalatByMonth)
                .build();
    }

    public AideComplementaireStatsDTO getAideComplementaireStats() {
        Map<String, Long> aideByStatus = new HashMap<>();
        Map<String, Long> aideAmountByService = new HashMap<>();
        Map<String, Long> aideByDate = new LinkedHashMap<>();
        LocalDate currentMonth = LocalDate.now();
        for (int i = 0; i < 12; i++) {
            String month = currentMonth.minusMonths(i).format(DateTimeFormatter.ofPattern("yyyy-MM"));
            aideByDate.put(month, 0L);
        }
        // Get aide by status
        List<Object[]> aidesByStatus = aideComplementaireRepository.getAideByStatus();
        for (Object[] row : aidesByStatus) {
            String status = (String) row[0];
            Long count = ((Number) row[1]).longValue();
            aideByStatus.put(status, count);
        }

        // Get aide amount by service
        List<Object[]> aidesByService = aideComplementaireRepository.getAideAmountByService();
        for (Object[] row : aidesByService) {
            String serviceName = (String) row[0];
            Long amount = row[1] != null ? ((Number) row[1]).longValue() : 0L;
            aideAmountByService.put(serviceName, amount);
        }

        // Get aide by date
        List<Object[]> aidesByDate = aideComplementaireRepository.getAideByDate();
        for (Object[] row : aidesByDate) {
            String month = (String) row[0];
            Long count = ((Number) row[1]).longValue();
            aideByDate.put(month, count);
        }

        return AideComplementaireStatsDTO.builder()
                .aideByStatus(aideByStatus)
                .aideAmountByService(aideAmountByService)
                .aideByDate(aideByDate)
                .build();
    }

    public GeneralDashboardDTO getGeneralDashboardData() {
        Long totalDonors = donorRepository.countByDeletedAtIsNull();
        Long totalBeneficiaries = beneficiaryRepository.countBeneficiary();
        Double totalDonationAmount = donationRepository.sumByArchivedIsFalseOrArchivedNull();
        Long totalKafalat = takenInChargeRepository.count();

        return new GeneralDashboardDTO(totalDonors, totalBeneficiaries, totalDonationAmount, totalKafalat);
    }

    public DonorStatsDTO getDonorStats() {
        long physiqalCount = donorPhysicalRepository.countByDeletedAtIsNull();
        long moralCount = donorMoralRepository.countByDeletedAtIsNull();
        long anonymousCount = donorAnonymeRepository.countByDeletedAtIsNull();
        Map<String , Long> donorByType = new HashMap<>();
        donorByType.put("physique", physiqalCount);
        donorByType.put("moral", moralCount);
        donorByType.put("anonyme", anonymousCount);
        long actifDonors= donorRepository.countByDonorStatusIdAndDeletedAtIsNull(1L);
        long inactifDonors= donorRepository.countByDonorStatusIdAndDeletedAtIsNull(2L);
        Map<String, Long> donorByStatus = new HashMap<>();
        donorByStatus.put("actif", actifDonors);
        donorByStatus.put("inactif", inactifDonors);
        List<Object[]> totalDonorByCity = donorRepository.countDonorsGroupedByCity();
        Map<String, Long> donorByCity = new LinkedHashMap<>();
        long othersCount = 0;

        int index = 0;
        for (Object[] row : totalDonorByCity) {
            if (row[1] == null) {
                continue; // Skip if cityId is null
            }

            Long cityId = ((Number) row[1]).longValue();
            Long count = ((Number) row[0]).longValue();

            if (index < 5) {
                String cityName = refFeignClient.getParCity(cityId).getName();
                donorByCity.put(cityName, count);
            } else {
                othersCount += count;
            }

            index++;
        }

        if (othersCount > 0) {
            donorByCity.put("autres", othersCount);
        }

        Map<String, Long> donorGrowthOverTime = new LinkedHashMap<>();
        LocalDate currentMonth = LocalDate.now();
        for (int i = 0; i < 12; i++) {
            String month = currentMonth.minusMonths(i).format(DateTimeFormatter.ofPattern("yyyy-MM"));
            donorGrowthOverTime.put(month, 0L);
        }
        List<Object[]> totalDonorByMonth = donorRepository.countDonorsByMonthLastYear();
        for(Object[] row : totalDonorByMonth) {
            String month = (row[0]).toString();
            Long count = ((Number) row[1]).longValue();

            donorGrowthOverTime.put(month, count);
        }
        return new DonorStatsDTO(donorByType,donorByStatus,donorByCity,donorGrowthOverTime);
    }

    public BeneficiaryStatsDTO getBeneficiaryStatsDTO(){
        Map<String, Long> beneficiariesByType = new HashMap<>();
        Map<String, Long> beneficiariesBySex = new HashMap<>();
        Map<String, Long> beneficiariesByStructure = new HashMap<>();
        Map<String, Long> beneficiariesByCategory = new HashMap<>();
        Map<String, Long> beneficiariesByZone = new HashMap<>();
        Map<String, Long> beneficiaryByMonth = new LinkedHashMap<>();
        LocalDate currentMonth = LocalDate.now();
        for (int i = 0; i < 12; i++) {
            String month = currentMonth.minusMonths(i).format(DateTimeFormatter.ofPattern("yyyy-MM"));
            beneficiaryByMonth.put(month, 0L);
        }

        List<Object[]> totalBeneficiaryByType = beneficiaryRepository.countBeneficiariesGroupedByType();
        for(Object[] row : totalBeneficiaryByType) {
            Long count = ((Number) row[0]).longValue();
            BeneficiaryStatut type = ((BeneficiaryStatut) row[1]);

            beneficiariesByType.put(type.getNameStatut(), count);
        }



        List<Object[]> totalBeneficiaryBySex = beneficiaryRepository.countBeneficiariesGroupedBySex();

        for(Object[] row:totalBeneficiaryBySex){
            Long count = ((Number) row[0]).longValue();

            String sex= ((String) row[1]);
            if(sex == null){
                continue;
            }
            beneficiariesBySex.put(sex, count);

        }
        long countIndependant = beneficiaryRepository.countBeneficiaryIndependent();
        long countNotIndependant = beneficiaryRepository.countBeneficiaryNotIndependent();
        long beneficiariesAdHocGroupCount = beneficiaryAdHocGroupRepository.countByIsDeletedIsNullOrIsDeletedFalse();
        long countBeneficiaryAdHocIndividual = beneficiaryRepository.countBeneficiaryAdHocIndividual();
        beneficiariesByStructure.put("independent", countIndependant);
        beneficiariesByStructure.put("non_independent", countNotIndependant);
        beneficiariesByType.put("Bénéficiaires Ad-Hoc Groupe", beneficiariesAdHocGroupCount);
        beneficiariesByType.put("Bénéficiaires Ad-Hoc Individuel", countBeneficiaryAdHocIndividual);
        List<Object[]> totalBeneficiaryByCategory = beneficiaryRepository.countBeneficiariesGroupedByCategory();
        for(Object[] row:totalBeneficiaryByCategory){
            Long count = ((Number) row[0]).longValue();
            if(row[1] == null){
                continue;
            }
            long category = ((Number) row[1]).longValue();
            CategoryBeneficiaryDTO categoryBeneficiary = refFeignClient.getCategoryBeneficiary(category);
            beneficiariesByCategory.put(categoryBeneficiary.getName(), count);
        }

        List<Object[]> totalBeneficiaryByZone = beneficiaryRepository.countBeneficiariesGroupedByZone();

        long autreCount = 0;
        int countDisplayed = 0;

        for (Object[] row : totalBeneficiaryByZone) {
            Long count = ((Number) row[0]).longValue();
            if (row[1] == null) {
                continue;
            }

            String zone = (String) row[1];

            if (countDisplayed < 5) {
                beneficiariesByZone.put(zone, count);
                countDisplayed++;
            } else {
                autreCount += count;
            }
        }

        if (autreCount > 0) {
            beneficiariesByZone.put("Autre", autreCount);
        }

        List<Object[]> totalDonorByMonth = beneficiaryRepository.countDonorsByMonthLastYear();
        for(Object[] row : totalDonorByMonth) {
            String month = (row[0]).toString();
            Long count = ((Number) row[1]).longValue();

            beneficiaryByMonth.put(month, count);
        }

        return new BeneficiaryStatsDTO(beneficiariesByType, beneficiariesBySex, beneficiariesByStructure, beneficiariesByCategory, beneficiariesByZone, beneficiaryByMonth);
    }




}
