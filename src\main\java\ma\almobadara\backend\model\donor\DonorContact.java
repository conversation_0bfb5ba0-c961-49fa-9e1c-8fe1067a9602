package ma.almobadara.backend.model.donor;

import jakarta.persistence.*;
import lombok.*;
import ma.almobadara.backend.dto.referentiel.DonorContactFunctionDTO;
import ma.almobadara.backend.util.strings.HandleSpecialChars;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static ma.almobadara.backend.util.strings.HandleSpecialChars.escapeSpecialChars;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DonorContact {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String firstName;
    private String lastName;
    private String firstNameAr;
    private String lastNameAr;
    private String sex;
    private String email;
    private String phoneNumber;
    private Boolean mainContact;
    private Date birthDate;
    private Long donorContactFunctionId;
    @Transient
    private DonorContactFunctionDTO donorContactFunctionDTO;
    @OneToMany(targetEntity = NoteDonorContact.class, mappedBy = "donorContact")
    private List<NoteDonorContact> noteDonorContacts;
    @OneToMany(targetEntity = DonorContactLanguageCommunication.class, mappedBy = "donorContact")
    private List<DonorContactLanguageCommunication> donorContactLanguageCommunications;
    @OneToMany(targetEntity = DonorContactCanalCommunication.class, mappedBy = "donorContact")
    private List<DonorContactCanalCommunication> donorContactCanalCommunications;
    @ManyToOne
    @JoinColumn(name = "donor_id")
    private DonorMoral donor;


    public String toDTOString(List<String> donorPhysicalCanalCommunications,
                              List<String> donorPhysicalLanguageCommunications,
                              String company) {
        StringBuilder json = new StringBuilder();

        json.append("{")
                .append("\"Nom\": \"").append(escapeSpecialChars(lastName)).append("\",")
                .append("\"Prénom\": \"").append(escapeSpecialChars(firstName)).append("\",")
                .append("\"Prénom Arabe\": \"").append(escapeSpecialChars(firstNameAr)).append("\",")
                .append("\"Nom Arabe\": \"").append(escapeSpecialChars(lastNameAr)).append("\",")
                .append("\"Sexe\": \"").append(escapeSpecialChars(sex)).append("\",")
                .append("\"Email\": \"").append(escapeSpecialChars(email)).append("\",")
                .append("\"Téléphone\": \"").append(escapeSpecialChars(phoneNumber)).append("\",")
                .append("\"Canal Communications\": \"").append(escapeSpecialChars(donorPhysicalCanalCommunications.stream()
                        .map(HandleSpecialChars::escapeSpecialChars)
                        .collect(Collectors.joining(", ")))).append("\",")
                .append("\"Language Communications\": \"").append(escapeSpecialChars(donorPhysicalLanguageCommunications.stream()
                        .map(HandleSpecialChars::escapeSpecialChars)
                        .collect(Collectors.joining(", ")))).append("\",")
                .append("\"Fonction\": \"")
                .append(donorContactFunctionDTO != null ? escapeSpecialChars(donorContactFunctionDTO.getName()) : "-")
                .append("\",")
                .append("\"Entreprise\": \"").append(escapeSpecialChars(company)).append("\"")
                .append("}");

        return json.toString();
    }



}
