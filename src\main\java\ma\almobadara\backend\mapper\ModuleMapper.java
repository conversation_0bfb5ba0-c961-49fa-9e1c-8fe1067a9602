package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.administration.ModuleDTO;
import ma.almobadara.backend.model.administration.Module;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(componentModel = "spring")
public interface ModuleMapper {

    ModuleDTO toDto(Module module);

    Module toEntity(ModuleDTO moduleDTO);

    @Mapping(target = "id", ignore = true)
    void updateModuleFromDto(ModuleDTO moduleDTO, @MappingTarget Module module);
}

