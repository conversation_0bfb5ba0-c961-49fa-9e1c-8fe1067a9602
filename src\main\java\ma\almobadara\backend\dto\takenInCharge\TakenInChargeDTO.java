package ma.almobadara.backend.dto.takenInCharge;

import lombok.*;
import ma.almobadara.backend.dto.administration.TagDTO;
import ma.almobadara.backend.dto.referentiel.ClotureMotifTypeDTO;
import ma.almobadara.backend.dto.service.ServicesDTO;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class TakenInChargeDTO {
	private Long id;
	private String code;
	private Date startDate;
	private Date endDate;
	private String type;
	private LocalDateTime createdAt;
	private ServicesDTO services;
	private Long serviceId;
	private String status;
	private List<TakenInChargeDonorDTO> takenInChargeDonors;
	private List<TakenInChargeBeneficiaryDTO> takenInChargeBeneficiaries;
	private boolean hasOperations;
	private ClotureMotifTypeDTO clotureMotifType;
	private Long clotureMotifTypeId;
	private String clotureMotif;

	private List<TagDTO> tags;
}
