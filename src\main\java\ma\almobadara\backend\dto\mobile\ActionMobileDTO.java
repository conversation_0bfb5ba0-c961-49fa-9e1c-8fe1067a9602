package ma.almobadara.backend.dto.mobile;

import lombok.*;

import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class ActionMobileDTO {
    private Long id;
    private String object;        // Subject of the action
    private String description;   // Content from the latest comment
    private String status;        // Status name
    private Date limitDate;       // Deadline
    private String affectedBy;    // Name of the person the action is assigned to
}
