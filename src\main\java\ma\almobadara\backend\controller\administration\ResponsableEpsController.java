package ma.almobadara.backend.controller.administration;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.administration.ResponsableEpsDto;
import ma.almobadara.backend.dto.family.AddedFamilyMemberResponse;
import ma.almobadara.backend.dto.family.FamilyMemberAddDTO;
import ma.almobadara.backend.exceptions.FunctionalException;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.model.administration.ResponsableEps;
import ma.almobadara.backend.service.administration.ResponsableEpsService;
import ma.almobadara.backend.service.family.FamilyMemberService;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static ma.almobadara.backend.service.administration.CacheAdUserService.logUserInfo;

//@RefreshScope
@RestController
@AllArgsConstructor
@Slf4j
@RequestMapping("/responsable-eps")
public class ResponsableEpsController {
    private final ResponsableEpsService responsableEpsService;

    @PostMapping(value = "", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    @Operation(summary = "Add a Responsable Eps", description = "add a new Responsable Eps", tags = {"responsable-eps"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(schema = @Schema(implementation = FamilyMemberAddDTO.class)))})
    public ResponseEntity<ResponsableEpsDto> addResponsableEps(@ModelAttribute ResponsableEpsDto responsableEpsDto) throws TechnicalException, FunctionalException, IOException {

        logUserInfo("addFamilyMember", String.valueOf(responsableEpsDto));

        ResponsableEpsDto responsableEpsDto1;

        responsableEpsDto1 = responsableEpsService.createResponsableEps(responsableEpsDto);


        return new ResponseEntity<>(responsableEpsDto1, new HttpHeaders(), HttpStatus.OK);
    }
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteResponsableEps(@PathVariable Long id) {
        try {
            responsableEpsService.deleteResponsableEps(id);
            return ResponseEntity.noContent().build();
        } catch (TechnicalException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<List<ResponsableEpsDto>> getAllResponsableEps(@PathVariable Long id) {
        List<ResponsableEpsDto> allResponsableEps = responsableEpsService.findAllResponsableEps(id);
        return ResponseEntity.ok(allResponsableEps);
    }
}
