package ma.almobadara.backend.mapper;
import ma.almobadara.backend.dto.administration.PrivilegeDTO;
import ma.almobadara.backend.model.administration.Privilege;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
@Mapper(componentModel = "spring")
public interface PrivilegeMapper {

    PrivilegeDTO toDto(Privilege privilege);

    Privilege toEntity(PrivilegeDTO privilegeDTO);

    @Mapping(target = "id", ignore = true)
    void updatePrivilegeFromDto(PrivilegeDTO privilegeDTO, @MappingTarget Privilege privilege);
}

