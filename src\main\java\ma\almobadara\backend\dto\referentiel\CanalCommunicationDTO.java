package ma.almobadara.backend.dto.referentiel;


import lombok.*;
import ma.almobadara.backend.dto.donor.DonorContactDTO;
import ma.almobadara.backend.dto.donor.DonorPhysicalDTO;
import java.io.Serializable;


import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
@ToString
public class CanalCommunicationDTO implements Serializable{
	private static final long serialVersionUID = 1L;

	private Long id;

	private String code;
	private String name;
	private String nameAr;
	private String nameEn;

	private List<DonorPhysicalDTO> donorPhysicals;

	private List<DonorContactDTO> donorContacts;

}
