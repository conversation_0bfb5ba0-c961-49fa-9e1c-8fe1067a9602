package ma.almobadara.backend.dto.beneficiary;

import lombok.*;
import ma.almobadara.backend.dto.CityWithRegionAndCountryDTO;
import ma.almobadara.backend.dto.referentiel.*;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class BeneficiaryDonorsDto {

    private Long beneficiaryId;
    private String beneficiaryCode;
    private String firstName;
    private String lastName;
    private String firstNameAr;
    private String lastNameAr;
    private Date birthDate;
    private String address;
    private String addressAr;
    private CityDTO city;
    private CityWithRegionAndCountryDTO info;
    private String schoolLevelType;
    private SchoolLevelDTO schoolLevel;
    private String schoolName;
    private Boolean independent;
    private String phoneNumber;

    private List<DonorInfoDto> donors;

    private FamilyInfoDto family;

}
