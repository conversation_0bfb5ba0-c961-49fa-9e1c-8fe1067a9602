package ma.almobadara.backend.controller.mobile;

import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.beneficiary.RapportMobileDto;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.service.beneficiary.RapportService;
import ma.almobadara.backend.service.mobile.RapportMobileService;
import net.sf.jasperreports.engine.JRException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.nio.file.Files;
import java.util.List;

@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping("/mobile/rapport")
public class RapportMobileController {
    private final RapportMobileService rapportMobileService;
    private final RapportService rapportService;

    @GetMapping("/rapport-for-donor/{id}")
    public ResponseEntity<List<RapportMobileDto>> getRapportForDonor(@PathVariable Long id) {
        try {
            List<RapportMobileDto> rapport = rapportMobileService.getRapportByDonorId(id);
            return new ResponseEntity<>(rapport, HttpStatus.OK);
        } catch (EntityNotFoundException e) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        } catch (Exception e) {
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    @GetMapping("/view-report-with-donor/{rapportId}/{donorId}/{language}")
    public ResponseEntity<byte[]> viewReportWithDonor(@PathVariable Long rapportId,
                                                      @PathVariable Long donorId,
                                                      @PathVariable String language)
            throws JRException, IOException, TechnicalException {

        log.info("Début de la visualisation du rapport PDF pour le rapport {} avec le donateur {} en {}",
                rapportId, donorId, language);

        File pdfFile = rapportService.viewReportWithDonor(rapportId, donorId, "pdf", language);

        if (!pdfFile.exists()) {
            throw new FileNotFoundException("Le rapport PDF n'a pas pu être généré.");
        }

        byte[] pdfBytes = Files.readAllBytes(pdfFile.toPath());
        String fileName = "rapport_" + rapportId + "_" + language + ".pdf";

        log.info("Rapport PDF généré avec succès pour le rapport {} avec le donateur {}", rapportId, donorId);

        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=" + fileName)
                .contentType(MediaType.APPLICATION_PDF)
                .body(pdfBytes);
    }
}
