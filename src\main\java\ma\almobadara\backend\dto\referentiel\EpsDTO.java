package ma.almobadara.backend.dto.referentiel;

import lombok.*;
import org.springframework.hateoas.RepresentationModel;

import java.io.Serializable;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class EpsDTO extends RepresentationModel<EpsDTO> implements Serializable {

	private static final long serialVersionUID = -909136835115395934L;

	private Long id;

	private String code;
	private String name;
	private String nameAr;
	private String nameEn;

}
