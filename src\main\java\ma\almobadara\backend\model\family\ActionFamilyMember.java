package ma.almobadara.backend.model.family;

import jakarta.persistence.*;
import lombok.*;
import ma.almobadara.backend.model.communs.Action;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@IdClass(ActionFamilyMemberId.class)
public class ActionFamilyMember {

    @Id
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "family_member_id")
    private FamilyMember familyMember;

    @Id
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "action_id")
    private Action action;

}
