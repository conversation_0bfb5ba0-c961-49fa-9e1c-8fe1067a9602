package ma.almobadara.backend.model.administration;

import jakarta.persistence.*;
import lombok.*;

import java.util.List;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "type_tag")
public class TypeTag {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    private String name;
    private String description;
    private Boolean readOnly;
    
    @OneToMany(mappedBy = "typeTag", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Tag> tags;
}