package ma.almobadara.backend.model.donation;

import jakarta.persistence.*;
import lombok.*;
import ma.almobadara.backend.model.aideComplemenatire.AideComplementaire;
import ma.almobadara.backend.model.donor.Donor;
import ma.almobadara.backend.model.service.Services;

import java.time.LocalDateTime;
import java.util.Date;

@Entity
@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class DonationHistory {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private Double amount; // Amount of the donation
    private LocalDateTime createdAt; // Timestamp for when the record was created
    private LocalDateTime updatedAt; // Timestamp for when the record was updated

    @ManyToOne
    @JoinColumn(name = "donation_id", nullable = false)
    private Donation donation; // Foreign key relationship with Donation

    @ManyToOne
    @JoinColumn(name = "donor_id", nullable = false)
    private Donor donor; // Foreign key relationship with Donor

    @ManyToOne
    @JoinColumn(name = "new_service_id")
    private Services newService; // Foreign key relationship with new Service

    @ManyToOne
    @JoinColumn(name = "old_service_id")
    private Services oldService; // Foreign key relationship with old Service

    @ManyToOne
    @JoinColumn(name = "aide_complementaire_id")
    private AideComplementaire aideComplementaire;

    private Date executionDate;

    private Double amountInitial;
    private Double amountReserved;
    private String type;
}
