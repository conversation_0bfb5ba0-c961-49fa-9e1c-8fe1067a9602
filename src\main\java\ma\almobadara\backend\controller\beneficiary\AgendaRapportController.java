package ma.almobadara.backend.controller.beneficiary;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.beneficiary.AgendaRapportDto;
import ma.almobadara.backend.dto.beneficiary.AgendaRapportResponseDto;
import ma.almobadara.backend.dto.beneficiary.HistoryRapportDto;
import ma.almobadara.backend.service.beneficiary.AgendaRapportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.NoSuchElementException;

@RefreshScope
@RestController
@AllArgsConstructor
@Slf4j
@RequestMapping("/agenda-rapports")
public class AgendaRapportController {

    @Autowired
    private AgendaRapportService agendaRapportService;

    @PostMapping("/planifier")
    public ResponseEntity<List<AgendaRapportDto>> planifierRapport(@RequestParam Long beneficiaryId,
                                                                   @RequestParam boolean hasPlanifier) {
        List<AgendaRapportDto> rapports = agendaRapportService.planifierRapport(beneficiaryId, hasPlanifier);
        return ResponseEntity.ok(rapports);
    }

    @GetMapping(value = "/beneficiare/{id}")
    @Operation(summary = "Afficher les rapports planifiés par bénéficiaire", description = "Récupère une liste paginée d'agenda rapports liés à un bénéficiaire", tags = {"AgendaRapports"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Agenda rapports récupérés avec succès",
                    content = @Content(array = @ArraySchema(schema = @Schema(implementation = AgendaRapportDto.class)))),
            @ApiResponse(responseCode = "404", description = "Aucun agenda rapport trouvé pour le bénéficiaire"),
            @ApiResponse(responseCode = "500", description = "Erreur interne du serveur")
    })
    public ResponseEntity<Page<AgendaRapportDto>> getAllAgendaRapportByBeneficiaryId(
            @PathVariable Long id,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            HttpServletResponse response) {
        try {
            Page<AgendaRapportDto> agendaRapports = agendaRapportService.getAllAgendaRapportByBeneficiaryId(id, page, size, response);

            if (agendaRapports.isEmpty()) {
                response.setHeader("X-No-AgendaRapports-Found", "No agenda rapports found for beneficiary with id: " + id);
            }

            return new ResponseEntity<>(agendaRapports, HttpStatus.OK);
        } catch (Exception e) {
            log.error("Erreur lors de la récupération des agenda rapports pour le bénéficiaire {}: {}", id, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    @GetMapping(value = "/beneficiary/{beneficiaryId}")
    //http://localhost:8001/agenda-rapports/beneficiary/134?page=0&size=10&plannedDateStart=2025-04-01&plannedDateEnd=2025-12-14&validationDateStart=2025-04-01&validationDateEnd=2025-12-14&beneficiaryCode=1000001-code7&beneficiaryName=ali&reportStatus=RAPPORT_PLANIFIER&numberRapport=1
    @Operation(summary = "Afficher les rapports planifiés par bénéficiaire", description = "Récupère une liste paginée d'agenda rapports liés à un bénéficiaire", tags = {"AgendaRapports"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Agenda rapports récupérés avec succès",
                    content = @Content(array = @ArraySchema(schema = @Schema(implementation = AgendaRapportDto.class)))),
            @ApiResponse(responseCode = "404", description = "Aucun agenda rapport trouvé pour le bénéficiaire"),
            @ApiResponse(responseCode = "500", description = "Erreur interne du serveur")
    })
    public ResponseEntity<Page<AgendaRapportDto>> getAllAgendaRapportByBeneficiaryIdFilter(
            @PathVariable Long beneficiaryId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "8") int size,
            @RequestParam(required = false) String beneficiaryCode,
            @RequestParam(required = false) String beneficiaryName,
            @RequestParam(required = false) Long numberRapport,
            @RequestParam(required = false) String reportStatus,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate plannedDateStart,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate plannedDateEnd,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate validationDateStart,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate validationDateEnd,
            HttpServletResponse response) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<AgendaRapportDto> agendaRapports = agendaRapportService.getAllAgendaRapportByBeneficiaryIdFilter(
                    beneficiaryId, pageable, beneficiaryCode, beneficiaryName, numberRapport, reportStatus,
                    plannedDateStart, plannedDateEnd, validationDateStart, validationDateEnd, response
            );

            if (agendaRapports.isEmpty()) {
                response.setHeader("X-No-AgendaRapports-Found", "No agenda rapports found for beneficiary with id: " + beneficiaryId);
            }

            return new ResponseEntity<>(agendaRapports, HttpStatus.OK);
        } catch (Exception e) {
            log.error("Erreur lors de la récupération des agenda rapports pour le bénéficiaire {}: {}", beneficiaryId, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    @DeleteMapping("/deleteAgendaRapport/{id}")
    @Operation(summary = "Supprimer un Rapport", description = "Supprime une planification existant par son ID", tags = {"AgendaRapports"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Agenda rapport supprimé avec succès"),
            @ApiResponse(responseCode = "404", description = "Agenda rapport non trouvé"),
            @ApiResponse(responseCode = "500", description = "Erreur interne du serveur")
    })
    public ResponseEntity<String> deleteAgendaRapport(@PathVariable Long id) {
        try {
            agendaRapportService.deleteAgendaRapport(id);
            return ResponseEntity.ok("Agenda rapport supprimé avec succès");
        } catch (NoSuchElementException e) {
            log.error("Erreur : Agenda rapport non trouvé : {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body("Agenda rapport non trouvé");
        } catch (Exception e) {
            log.error("Erreur lors de la suppression du agenda rapport : {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Erreur lors de la suppression du agenda rapport");
        }
    }

    @GetMapping("/assistants/{id}")
    public ResponseEntity<Page<AgendaRapportResponseDto>> getAgendaRapportsByAssistant(
            @PathVariable Long id,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "5") int size
    ) {
        Pageable pageable = PageRequest.of(page, size);
        Page<AgendaRapportResponseDto> rapports = agendaRapportService.getAgendaRapportsByAssistant(id, pageable);
        return ResponseEntity.ok(rapports);
    }

    @GetMapping("/assistant/{assistantId}")
    //http://localhost:8001/agenda-rapports/assistant/47?page=0&size=10&plannedDateStart=2025-04-01&plannedDateEnd=2025-12-14&validationDateStart=2025-04-01&validationDateEnd=2025-12-14&beneficiaryCode=1000001-code7&beneficiaryName=ali&reportStatus=RAPPORT_INITIAL
    public ResponseEntity<Page<AgendaRapportResponseDto>> getAgendaRapportsByAssistantFilter(
            @PathVariable Long assistantId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String beneficiaryCode,
            @RequestParam(required = false) Long numberRapport,
            @RequestParam(required = false) String beneficiaryName,
            @RequestParam(required = false) String reportStatus,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate plannedDateStart,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate plannedDateEnd,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate validationDateStart,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate validationDateEnd
    ) {
        Pageable pageable = PageRequest.of(page, size);
        Page<AgendaRapportResponseDto> rapports = agendaRapportService.getAgendaRapportsByAssistantFilter(
                assistantId, pageable, beneficiaryCode, numberRapport, beneficiaryName, reportStatus,
                plannedDateStart, plannedDateEnd, validationDateStart, validationDateEnd
        );
        return ResponseEntity.ok(rapports);
    }

    @PostMapping("/assistant/{assistantId}/rappel")
    public ResponseEntity<Void> envoyerRappel(@PathVariable Long assistantId) {
        agendaRapportService.rappelRapport(assistantId);
        return ResponseEntity.ok().build();
    }


    @GetMapping("/admin")
    public ResponseEntity<Page<AgendaRapportResponseDto>> getAgendaRapportsByAdmin(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String beneficiaryCode,
            @RequestParam(required = false) String beneficiaryName,
            @RequestParam(required = false) Long numberRapport,
            @RequestParam(required = false) String reportStatus,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate plannedDateStart,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate plannedDateEnd,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate validationDateStart,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate validationDateEnd
    ) {
        Pageable pageable = PageRequest.of(page, size);
        Page<AgendaRapportResponseDto> rapports = agendaRapportService.getAgendaRapportsByAdmin(
                pageable, beneficiaryCode, beneficiaryName, numberRapport, reportStatus,
                plannedDateStart, plannedDateEnd, validationDateStart, validationDateEnd
        );
        return ResponseEntity.ok(rapports);
    }


    @GetMapping(value = "/rapport/history/{beneficiaryId}")
     @Operation(summary = "Afficher les rapports planifiés par bénéficiaire", description = "Récupère une liste paginée d'agenda rapports liés à un bénéficiaire", tags = {"AgendaRapports"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Agenda rapports récupérés avec succès",
                    content = @Content(array = @ArraySchema(schema = @Schema(implementation = AgendaRapportDto.class)))),
            @ApiResponse(responseCode = "404", description = "Aucun agenda rapport trouvé pour le bénéficiaire"),
            @ApiResponse(responseCode = "500", description = "Erreur interne du serveur")
    })
    public ResponseEntity<Page<HistoryRapportDto>> getAllAgendaRapportHistoryByFilter(
            @PathVariable Long beneficiaryId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "8") int size,
            @RequestParam(required = false) String communicatedBy,
            @RequestParam(required = false) String donorName,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate dateCommunicatedStart,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate dateCommunicatedEnd,
            HttpServletResponse response) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<HistoryRapportDto> historyRapport = agendaRapportService.getAllAgendaRapportHistoryByFilter(
                    beneficiaryId, pageable, communicatedBy, donorName, dateCommunicatedStart, dateCommunicatedEnd, response
            );

            if (historyRapport.isEmpty()) {
                response.setHeader("X-No-AgendaRapports-Found", "No agenda rapports history found for beneficiary with id: " + beneficiaryId);
            }

            return new ResponseEntity<>(historyRapport, HttpStatus.OK);
        } catch (Exception e) {
            log.error("Erreur lors de la récupération des agenda rapports pour le bénéficiaire {}: {}", beneficiaryId, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    @PostMapping("/planifier-manuell")
    public ResponseEntity<?> planifierRapportManuell(
            @RequestParam Long beneficiaryId,
            @RequestParam boolean hasPlanifier,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date datePlanned) {
        return agendaRapportService.planifierRapportManuell(beneficiaryId, hasPlanifier, datePlanned);
    }

    @PostMapping("/planifier-automatiquement")
    public ResponseEntity<String> planifierRapportsAutomatiquementWithAllBene() {
        try {
            agendaRapportService.planifierRapportsAutomatiquementWithAllBene();
            return ResponseEntity.ok("Planification automatique des rapports terminée avec succès.");
        } catch (Exception e) {
            log.error("Erreur lors de la planification automatique des rapports", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Erreur lors de la planification automatique des rapports.");
        }
    }
}
