package ma.almobadara.backend.controller.mobile;

import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.donor.ReleveDonorDto;
import ma.almobadara.backend.dto.mobile.ReleveDonorMobileDTO;
import ma.almobadara.backend.service.mobile.DonationMobileService;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping("/mobile/donation")
@CrossOrigin(origins = "*")
public class DonationMobileController {

    private final DonationMobileService donationMobileService;

    @GetMapping("/{donorId}/releve-compte")
    public ResponseEntity<List<ReleveDonorMobileDTO>> getReleveCompte(
            @PathVariable Long donorId) {
        try {
            List<ReleveDonorMobileDTO> releveCompte = donationMobileService.getReleveForDonor(donorId);

            return new ResponseEntity<>(releveCompte, HttpStatus.OK);
        } catch (EntityNotFoundException e) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        } catch (Exception e) {
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
