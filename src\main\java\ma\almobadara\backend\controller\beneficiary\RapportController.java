package ma.almobadara.backend.controller.beneficiary;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.persistence.EntityNotFoundException;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.beneficiary.BeneficiaryDonorsDto;
import ma.almobadara.backend.dto.beneficiary.DonorInfoDto;
import ma.almobadara.backend.dto.beneficiary.RapportDto;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.model.AddedRapportResponse;
import ma.almobadara.backend.repository.beneficiary.RapportRepository;
import ma.almobadara.backend.service.beneficiary.RapportService;
import net.sf.jasperreports.engine.JRException;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.net.URISyntaxException;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.List;
import java.util.NoSuchElementException;

import static ma.almobadara.backend.service.administration.CacheAdUserService.logUserInfo;

@RefreshScope
@RestController
@AllArgsConstructor
@Slf4j
@RequestMapping("/rapport")
public class RapportController {

    private final RapportService rapportService;
    private final RapportRepository rapportRepository;

    @PostMapping(value = "/addRapport", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    @Operation(summary = "Ajouter un Rapport", description = "Ajoute un nouveau rapport avec des informations attachées", tags = {"Rapports"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Rapport ajouté avec succès",
                    content = @Content(schema = @Schema(implementation = AddedRapportResponse.class))),
            @ApiResponse(responseCode = "500", description = "Erreur interne du serveur")
    })
    public ResponseEntity<AddedRapportResponse> addRapport(
            @ModelAttribute RapportDto rapportDto,
            @RequestParam("agendaRapportId") Long agendaRapportId) {
        try {
            AddedRapportResponse response = rapportService.addRapport(rapportDto, agendaRapportId);
            return new ResponseEntity<>(response, HttpStatus.OK);
        } catch (Exception e) {
            log.error("Erreur lors de l'ajout du rapport : {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    @GetMapping("/{id}/beneficiary/info")
    public BeneficiaryDonorsDto getBeneficiaryWithDonors(@PathVariable Long id) {
        return rapportService.getBeneficiaryWithDonors(id);
    }


    @GetMapping(value = "/getRapport/{id}", produces = {"application/json"})
    @Operation(summary = "Afficher un rapport", description = "Récupère les détails d'un rapport par ID", tags = {"Rapports"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Détails du rapport récupérés avec succès",
                    content = @Content(schema = @Schema(implementation = RapportDto.class))),
            @ApiResponse(responseCode = "404", description = "Rapport non trouvé"),
            @ApiResponse(responseCode = "500", description = "Erreur interne du serveur")
    })
    public ResponseEntity<RapportDto> getRapport(@PathVariable Long id) {
        try {
            RapportDto rapportDto = rapportService.getRapportDetails(id);
            return new ResponseEntity<>(rapportDto, HttpStatus.OK);
        } catch (EntityNotFoundException e) {
            log.error("Rapport non trouvé avec l'ID {}: {}", id, e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
        } catch (Exception e) {
            log.error("Erreur lors de la récupération du rapport avec l'ID {}: {}", id, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }


    @GetMapping(value = "/getRapportsByBeneficiary/{beneficiaryId}")
    @Operation(summary = "Afficher les rapports par bénéficiaire", description = "Récupère une liste paginée de rapports liés à un bénéficiaire", tags = {"Rapports"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Rapports récupérés avec succès",
                    content = @Content(array = @ArraySchema(schema = @Schema(implementation = RapportDto.class)))),
            @ApiResponse(responseCode = "404", description = "Aucun rapport trouvé pour le bénéficiaire"),
            @ApiResponse(responseCode = "500", description = "Erreur interne du serveur")
    })
    public ResponseEntity<Page<RapportDto>> getRapportsByBeneficiary(
            @PathVariable Long beneficiaryId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            HttpServletResponse response) {
        try {
            Page<RapportDto> rapportDtos = rapportService.getRapportsByBeneficiary(beneficiaryId, page, size, response);

            if (rapportDtos.isEmpty()) {
                response.setHeader("X-No-Rapports-Found", "No rapports found for beneficiary with id: " + beneficiaryId);
            }

            return new ResponseEntity<>(rapportDtos, HttpStatus.OK);
        } catch (Exception e) {
            log.error("Erreur lors de la récupération des rapports pour le bénéficiaire {}: {}", beneficiaryId, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }


    @DeleteMapping("/deleteRapport/{id}")
    @Operation(summary = "Supprimer un Rapport", description = "Supprime un rapport existant par son ID", tags = {"Rapports"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Rapport supprimé avec succès"),
            @ApiResponse(responseCode = "404", description = "Rapport non trouvé"),
            @ApiResponse(responseCode = "500", description = "Erreur interne du serveur")
    })
    public ResponseEntity<String> deleteRapport(@PathVariable Long id) {
        try {
            rapportService.deleteRapport(id);
            return ResponseEntity.ok("Rapport supprimé avec succès");
        } catch (NoSuchElementException e) {
            log.error("Erreur : Rapport non trouvé : {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body("Rapport non trouvé");
        } catch (Exception e) {
            log.error("Erreur lors de la suppression du rapport : {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Erreur lors de la suppression du rapport");
        }
    }


//    @GetMapping("/generateReportByBeneficiary/{beneficiaryId}")
//    @Operation(summary = "Générer un rapport PDF pour les rapports d'un bénéficiaire",
//            description = "Génère un fichier PDF contenant les informations des rapports pour un bénéficiaire donné.")
//    @ApiResponses(value = {
//            @ApiResponse(responseCode = "200", description = "Rapport généré avec succès"),
//            @ApiResponse(responseCode = "404", description = "Bénéficiaire ou rapports non trouvés"),
//            @ApiResponse(responseCode = "500", description = "Erreur lors de la génération du rapport")
//    })
//    public ResponseEntity<byte[]> generateReportByBeneficiary(
//            @PathVariable Long beneficiaryId,
//            @RequestParam String reportType,
//            @RequestParam String language) {
//        try {
//            // Générer le fichier PDF à partir des données des rapports
//            File pdfFile = rapportService.generateReportByBeneficiary(beneficiaryId, reportType, language);
//
//            // Lire le contenu du fichier PDF
//            byte[] fileContent = Files.readAllBytes(pdfFile.toPath());
//
//            // Retourner le fichier PDF
//            return ResponseEntity.ok()
//                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=rapport_" + beneficiaryId + ".pdf")
//                    .contentType(MediaType.APPLICATION_PDF)
//                    .body(fileContent);
//        } catch (EntityNotFoundException e) {
//            log.error("Bénéficiaire ou rapports non trouvés pour l'identifiant: {}", beneficiaryId);
//            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
//        } catch (Exception e) {
//            log.error("Erreur lors de la génération du rapport pour le bénéficiaire {}: {}", beneficiaryId, e.getMessage());
//            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
//        }
//    }

//    @GetMapping("/generate")
//    public ResponseEntity<byte[]> generateReport(@RequestParam Long beneficiaryId,
//                                                 @RequestParam String reportType,
//                                                 @RequestParam String language) throws JRException, IOException {
//        // Générer le rapport et obtenir le fichier PDF
//        File pdfFile = rapportService.generateReport(beneficiaryId, reportType, language);
//
//        // Lire le fichier PDF en bytes
//        byte[] fileContent = Files.readAllBytes(pdfFile.toPath());
//
//        // Retourner le fichier PDF dans la réponse avec un en-tête approprié
//        return ResponseEntity.ok()
//                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=report.pdf")
//                .contentType(MediaType.APPLICATION_PDF)
//                .body(fileContent);
//    }

//    @GetMapping("/generate")
//    //http://localhost:8001/rapport/generate?beneficiaryId=129&rapportId=29&reportType=handicap&language=fr
//    //http://localhost:8001/rapport/generate?beneficiaryId=128&rapportId=38&reportType=handicap&language=fr
//    public ResponseEntity<byte[]> generateReport(@RequestParam Long beneficiaryId,
//                                                 @RequestParam Long rapportId,
//                                                 @RequestParam String reportType,
//                                                 @RequestParam String language) throws JRException, IOException, TechnicalException {
//        // Générer le rapport et obtenir le fichier PDF
//        File pdfFile = rapportService.generateReport(beneficiaryId, rapportId, reportType, language);
//
//        // Lire le fichier PDF en bytes
//        byte[] fileContent = Files.readAllBytes(pdfFile.toPath());
//
//        // Retourner le fichier PDF dans la réponse avec un en-tête approprié
//        return ResponseEntity.ok()
//                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=report.pdf")
//                .contentType(MediaType.APPLICATION_PDF)
//                .body(fileContent);
//    }

    @GetMapping("/generate")
    public ResponseEntity<List<byte[]>> generateReport(@RequestParam Long beneficiaryId,
                                                       @RequestParam Long rapportId,
                                                       @RequestParam String reportType) throws JRException, IOException, TechnicalException, URISyntaxException {
        // Générer les rapports pour tous les modèles disponibles
        List<File> pdfFiles = rapportService.generateReport(beneficiaryId, rapportId, reportType);

        // Convertir les fichiers PDF en byte[]
        List<byte[]> fileContents = new ArrayList<>();
        for (File pdfFile : pdfFiles) {
            fileContents.add(Files.readAllBytes(pdfFile.toPath()));
        }

        // Retourner les fichiers PDF dans la réponse
        return ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_PDF)
                .body(fileContents);
    }


//    @PostMapping("/validate-report")
//    public ResponseEntity<String> validateStatus(@RequestParam Long beneficiaryId,
//                                                 @RequestParam Long rapportId,
//                                                 @RequestParam String reportType,
//                                                 @RequestParam String language) {
//        log.debug("Debugging the parameters: beneficiaryId={}, rapportId={}, reportType={}, language={}",
//                beneficiaryId, rapportId, reportType, language);
//        try {
//            rapportService.validateReport(beneficiaryId, rapportId, reportType, language);
//            return ResponseEntity.ok("Rapport status updated successfully.");
//        } catch (IllegalStateException | IllegalArgumentException e) {
//            return ResponseEntity.badRequest().body("Validation error: " + e.getMessage());
//        } catch (Exception e) {
//            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("An error occurred: " + e.getMessage());
//        }
//    }

    @PostMapping("/validate")
    //http://localhost:8001/rapport/validate?rapportId=43&reportType=handicap&language=fr
    public ResponseEntity<String> validateStatus(@RequestParam Long rapportId,
                                                 @RequestParam String reportType,
                                                 @RequestParam String language) {
        log.debug("Debugging the parameters: rapportId={}, reportType={}, language={}",
                rapportId, reportType, language);
        try {
            rapportService.validateReport(rapportId, reportType, language);
            return ResponseEntity.ok("Rapport status updated successfully.");
        } catch (IllegalStateException | IllegalArgumentException e) {
            return ResponseEntity.badRequest().body("Validation error: " + e.getMessage());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("An error occurred: " + e.getMessage());
        }
    }


    @PutMapping(value = "/update/{id}", consumes = {MediaType.APPLICATION_JSON_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE})
    @Operation(summary = "Mettre à jour un Rapport", description = "Met à jour les informations d'un rapport existant", tags = {"Rapports"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Rapport mis à jour avec succès",
                    content = @Content(schema = @Schema(implementation = AddedRapportResponse.class))),
            @ApiResponse(responseCode = "404", description = "Rapport non trouvé"),
            @ApiResponse(responseCode = "500", description = "Erreur interne du serveur")
    })
    public ResponseEntity<AddedRapportResponse> updateReport(
            @PathVariable Long id,
            @Valid @ModelAttribute RapportDto rapportDto) {
        logUserInfo("updateRapport", "ID: " + id);
        try {
            AddedRapportResponse response = rapportService.updateReport(id, rapportDto);
            return new ResponseEntity<>(response, HttpStatus.OK);
        } catch (NoSuchElementException e) {
            log.error("Erreur : Rapport non trouvé : {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
        } catch (Exception e) {
            log.error("Erreur lors de la mise à jour du rapport : {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    @PostMapping(value = "/complete-rapport/{id}", headers = "Accept=application/json")
    //http://localhost:8001/rapport/complete-rapport/38
    @Operation(summary = "Complete rapport", description = "Complete rapport", tags = {"Rapports"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = RapportDto.class)))),
            @ApiResponse(responseCode = "204", description = "No content found"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<Long> rapportToComplete(
            @PathVariable Long id,
            @RequestParam(required = false) Long idStatutTarget,
            @RequestParam String rqComplete) {

        logUserInfo("rapportToComplete", String.valueOf(id));

        try {
            Long newStatutId = rapportService.rapportToComplete(id, idStatutTarget, rqComplete);
            log.info("End resource rapportToComplete By Id {}", id);
            return ResponseEntity.ok(newStatutId);
        } catch (Exception e) {
            log.error("Error occurred while completing rapport with Id {}: {}", id, e.getMessage());
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

//    @GetMapping("/view-report/{rapportId}")
//    public ResponseEntity<byte[]> viewReport(@PathVariable Long rapportId)
//            throws JRException, IOException, TechnicalException {
//
//        log.info("Début de la visualisation du rapport PDF pour le rapport {}", rapportId);
//
//        // Appel au service pour générer le PDF
//        File pdfFile = rapportService.viewReport(rapportId, "pdf", "fr");
//
//        if (!pdfFile.exists()) {
//            throw new FileNotFoundException("Le rapport PDF n'a pas pu être généré.");
//        }
//
//        // Lire le fichier PDF en bytes
//        byte[] pdfBytes = Files.readAllBytes(pdfFile.toPath());
//        String fileName = "rapport_" + rapportId + ".pdf";
//
//        log.info("Rapport PDF généré avec succès pour le rapport {}", rapportId);
//
//        return ResponseEntity.ok()
//                .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=" + fileName) // Affichage direct
//                .contentType(MediaType.APPLICATION_PDF)
//                .body(pdfBytes);
//    }


    @PostMapping(value = "/notify-assistant/{id}", headers = "Accept=application/json")
    @Operation(summary = "Notify assistant", description = "Notify assistant about required actions on Kafalat report", tags = {"Rapports"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation"),
            @ApiResponse(responseCode = "204", description = "No content found"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<Void> notifyAssistant(@PathVariable Long id) {
        logUserInfo("notifyAssistant", String.valueOf(id));

        try {
            rapportService.notifyAssistant(id);
            log.info("End resource notifyAssistant By Id {}", id);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            log.error("Error occurred while notifying assistant for rapport with Id {}: {}", id, e.getMessage());
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/beneficiaries/{id}/donors")
    public ResponseEntity<List<DonorInfoDto>> getDonorsForBeneficiary(@PathVariable Long id) {
        List<DonorInfoDto> donors = rapportService.findDonorsForBeneficiary(id);
        return ResponseEntity.ok(donors);
    }

    //@GetMapping("/view-report-with-donor/{rapportId}/{donorId}")
//    //http://localhost:8001/rapport/view-report-with-donor/120/12
//    public ResponseEntity<byte[]> viewReportWithDonor(@PathVariable Long rapportId, @PathVariable Long donorId)
//            throws JRException, IOException, TechnicalException {
//
//        log.info("Début de la visualisation du rapport PDF pour le rapport {} avec le donateur {}", rapportId, donorId);
//
//        // Appel au service pour générer le PDF
//        File pdfFile = rapportService.viewReportWithDonor(rapportId, donorId, "pdf", "fr");
//
//        if (!pdfFile.exists()) {
//            throw new FileNotFoundException("Le rapport PDF n'a pas pu être généré.");
//        }
//
//        // Lire le fichier PDF en bytes
//        byte[] pdfBytes = Files.readAllBytes(pdfFile.toPath());
//        String fileName = "rapport_" + rapportId + ".pdf";
//
//        log.info("Rapport PDF généré avec succès pour le rapport {} avec le donateur {}", rapportId, donorId);
//
//        return ResponseEntity.ok()
//                .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=" + fileName) // Affichage direct
//                .contentType(MediaType.APPLICATION_PDF)
//                .body(pdfBytes);
//    }


    @GetMapping("/view-report/{rapportId}/{language}")
    public ResponseEntity<byte[]> viewReport(@PathVariable Long rapportId, @PathVariable String language)
            throws JRException, IOException, TechnicalException {

        log.info("Début de la visualisation du rapport PDF pour le rapport {} avec langue {}", rapportId, language);

        File pdfFile = rapportService.viewReport(rapportId, "pdf", language);

        if (!pdfFile.exists()) {
            throw new FileNotFoundException("Le rapport PDF n'a pas pu être généré.");
        }

        byte[] pdfBytes = Files.readAllBytes(pdfFile.toPath());
        String fileName = "rapport_" + rapportId + "_" + language + ".pdf";

        log.info("Rapport PDF généré avec succès pour le rapport {}", rapportId);

        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=" + fileName)
                .contentType(MediaType.APPLICATION_PDF)
                .body(pdfBytes);
    }

    @GetMapping("/view-report-with-donor/{rapportId}/{donorId}/{language}")
    public ResponseEntity<byte[]> viewReportWithDonor(@PathVariable Long rapportId,
                                                      @PathVariable Long donorId,
                                                      @PathVariable String language)
            throws JRException, IOException, TechnicalException {

        log.info("Début de la visualisation du rapport PDF pour le rapport {} avec le donateur {} en {}",
                rapportId, donorId, language);

        File pdfFile = rapportService.viewReportWithDonor(rapportId, donorId, "pdf", language);

        if (!pdfFile.exists()) {
            throw new FileNotFoundException("Le rapport PDF n'a pas pu être généré.");
        }

        byte[] pdfBytes = Files.readAllBytes(pdfFile.toPath());
        String fileName = "rapport_" + rapportId + "_" + language + ".pdf";

        log.info("Rapport PDF généré avec succès pour le rapport {} avec le donateur {}", rapportId, donorId);

        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=" + fileName)
                .contentType(MediaType.APPLICATION_PDF)
                .body(pdfBytes);
    }

}
