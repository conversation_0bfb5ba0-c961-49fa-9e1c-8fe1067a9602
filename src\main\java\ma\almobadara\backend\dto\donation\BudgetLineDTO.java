package ma.almobadara.backend.dto.donation;

import lombok.*;
import ma.almobadara.backend.dto.referentiel.CurrencyDTO;
import ma.almobadara.backend.dto.referentiel.ServiceDTO;
import ma.almobadara.backend.dto.referentiel.TypePriseEnChargeDTO;
import ma.almobadara.backend.dto.referentiel.TypeProductNatureDTO;
import ma.almobadara.backend.dto.service.ServicesDTO;
import ma.almobadara.backend.model.administration.ServiceCollectEps;
import ma.almobadara.backend.model.service.Services;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class BudgetLineDTO {
    private Long id;
    private String code;
    private Double amount;
    private Double amountByBeneficiary;
    private String comment;
    private Double valueCurrency;
    private Boolean enableCurrency;
    private CurrencyDTO currency;
    private TypePriseEnChargeDTO typePriseEnCharge;
    private LocalDateTime createdAt;
    private String type;
    private String status;
    private String codeDonation;
    private String fullNameDonor;
    private Long idDonation;
    private Date receptionDateDonation;
    private ServicesDTO service;
    private Long serviceCollectEpsId;
    private Double montantReserve;
    private LocalDateTime executionDate;
    private String aideComplementaireName;
    private Date executionDateBudgetLine;
    private Boolean makeItAvailable;

    private List<Long> productCategory;  // List of product category IDs
    private List<TypeProductNatureDTO> typeProductNatures;
    private Boolean natureBudgetLine;

    // we will add a list of ids of typeProducts




}
