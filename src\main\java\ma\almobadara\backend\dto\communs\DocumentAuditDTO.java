package ma.almobadara.backend.dto.communs;

import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class DocumentAuditDTO {

    private String Objet;
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date DateDocument;
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date DateExpiration;
    private String Commentaire;
    private String NomFichier;
    private String TypeDocument;
}
