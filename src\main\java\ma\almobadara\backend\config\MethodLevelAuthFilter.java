package ma.almobadara.backend.config;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerExecutionChain;
import org.springframework.web.servlet.HandlerMapping;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import java.io.IOException;
import java.lang.reflect.Method;
import java.util.Collections;

/**
 * Filter that handles method-level authentication routing.
 * Checks for @AzureAuth annotation on controller methods and routes
 * authentication to the appropriate JWT decoder.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MethodLevelAuthFilter extends OncePerRequestFilter {

    private final JwtDecoder auth0JwtDecoder;
    private final JwtDecoder azureJwtDecoder;
    private final RequestMappingHandlerMapping requestMappingHandlerMapping;
    private final CustomJwtAuthenticationConverter customJwtAuthenticationConverter;

    @Override
    protected void doFilterInternal(
            HttpServletRequest request,
            HttpServletResponse response,
            FilterChain filterChain
    ) throws ServletException, IOException {

        String path = request.getRequestURI();

        // Only apply this filter to /mobile/ paths that are not login or reset-password endpoints
        if (path.startsWith("/mobile/") && !path.matches(".*/login.*") && !path.matches(".*/reset-password.*")) {
            String authHeader = request.getHeader("Authorization");

            if (!StringUtils.hasText(authHeader) || !authHeader.startsWith("Bearer ")) {
                log.error("Invalid or missing Authorization header for mobile endpoint");
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                response.getWriter().write("Unauthorized: Valid Authorization header is required");
                return;
            }

            String token = authHeader.substring(7); // Remove "Bearer " prefix
            log.debug("Processing token for mobile endpoint: {}", path);

            // Determine which decoder to use based on method annotation
            boolean useAzureAuth = shouldUseAzureAuth(request);

            try {
                if (useAzureAuth) {
                    log.debug("Using Azure authentication for endpoint: {}", path);
                    var jwt = azureJwtDecoder.decode(token);

                    // Use the custom JWT authentication converter for Azure tokens
                    var authToken = customJwtAuthenticationConverter.getJwtAuthenticationConverter().convert(jwt);
                    SecurityContextHolder.getContext().setAuthentication(authToken);
                    log.debug("Successfully authenticated request with Azure token");
                } else {
                    log.debug("Using Auth0 authentication for endpoint: {}", path);
                    var jwt = auth0JwtDecoder.decode(token);

                    // Create authentication token with the subject from the JWT for Auth0
                    UsernamePasswordAuthenticationToken authToken = new UsernamePasswordAuthenticationToken(
                        jwt.getSubject(),
                        null,
                        Collections.emptyList()
                    );

                    SecurityContextHolder.getContext().setAuthentication(authToken);
                    log.debug("Successfully authenticated request with Auth0 token");
                }
            } catch (Exception e) {
                log.error("JWT token validation failed: {}", e.getMessage());
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                response.getWriter().write("Unauthorized: Invalid token");
                return;
            }
        }

        filterChain.doFilter(request, response);
    }

    /**
     * Determines if the current request should use Azure authentication
     * by checking for the @AzureAuth annotation on the target method.
     */
    private boolean shouldUseAzureAuth(HttpServletRequest request) {
        try {
            HandlerExecutionChain handlerChain = requestMappingHandlerMapping.getHandler(request);
            if (handlerChain != null && handlerChain.getHandler() instanceof HandlerMethod handlerMethod) {
                Method method = handlerMethod.getMethod();
                boolean hasAzureAuth = method.isAnnotationPresent(AzureAuth.class);
                log.debug("Method {} has @AzureAuth annotation: {}", method.getName(), hasAzureAuth);
                return hasAzureAuth;
            }
        } catch (Exception e) {
            log.warn("Could not determine handler method for request: {}", e.getMessage());
        }

        // Default to Auth0 for mobile endpoints
        return false;
    }
}
