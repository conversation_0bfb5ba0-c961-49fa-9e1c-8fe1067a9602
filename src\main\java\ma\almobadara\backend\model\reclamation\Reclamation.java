package ma.almobadara.backend.model.reclamation;

import jakarta.persistence.*;
import lombok.*;
import ma.almobadara.backend.enumeration.ReclamationStatus;
import ma.almobadara.backend.model.donor.Donor;
import org.hibernate.annotations.CreationTimestamp;

import java.time.LocalDateTime;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Reclamation {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false)
    private String title;

    @Column(columnDefinition = "TEXT")
    private String description;

    @CreationTimestamp
    @Column(name = "created_at", updatable = false)
    private LocalDateTime createdAt;

    @ManyToOne
    @JoinColumn(name = "donor_id")
    private Donor donor;

    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private ReclamationStatus status = ReclamationStatus.ENVOYE; // Default status is ENVOYE

    @Column(name = "response", columnDefinition = "TEXT")
    private String response;

    @Column(name = "responded_by")
    private String respondedBy;

    @Column(name = "responded_at")
    private LocalDateTime respondedAt;
}
