package ma.almobadara.backend.dto.beneficiary;

import lombok.*;
import ma.almobadara.backend.dto.administration.SousZoneDTO;
import ma.almobadara.backend.dto.administration.TagDTO;
import ma.almobadara.backend.dto.referentiel.AllergiesDTO;
import ma.almobadara.backend.dto.referentiel.CityDTO;
import ma.almobadara.backend.dto.referentiel.DiseasesDTO;
import ma.almobadara.backend.dto.takenInCharge.TakenInChargeBeneficiaryDTO;

import java.time.Instant;
import java.util.List;
import java.util.Set;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
@ToString
public class BeneficiaryDTO {

	private Long id;
	private String code;
	private Boolean oldBeneficiary;
	private String codeBeneficiary;
	private String accountingCode;
	private String addedYear;
	private Instant createdAt;
	private Boolean independent;
	private Long familyId;
	private String addressFamily;
	private String addressFamilyAr;
	private String phoneNumberFamily;
	private Long zoneFamilyId;
	private String zoneFamilyName;
	private Long sousZoneFamilyId;
	private String sousZoneFamilyName;
	private CityDTO cityFamily;
	private String accommodationtypeFamily;
	private String accommodationNatureFamily;
	private String tutorName;
	private Boolean epsResident;
	private Boolean archived;
	private List<AllergiesDTO> allergies;
	private List<DiseasesDTO> diseases;
	private List<NoteBeneficiaryDTO> notes;
	private List<DocumentBeneficiaryDTO> documents;
	private Set<ScholarshipBeneficiaryDTO> scholarshipBeneficiaries;
	private Set<EpsResidentDTO> epsResidents;
	private Set<DiseaseTreatmentDTO> diseaseTreatments;
	private Set<BeneficiaryHandicapDto> handicapped;
	private Set<BeneficiaryServiceDTO> beneficiaryServices;
	private Set<EducationDTO> educations;
	private PersonDTO person;
	private GlassesDto glasses;
	private List<TakenInChargeBeneficiaryDTO> takenInChargeBeneficiaries;
	private SmallZoneDTO zone;
	private SousZoneDTO sousZone;
	private BeneficiaryStatutDTO beneficiaryStatut;
	private String statut;
	private String remarqueFr;
	private String remarqueEn;
	private String remarqueAr;
	private String rqComplete ;
	private String rqReject;
	private String statusBeneficiaryAdHoc;
	private String comment;
	private List<TagDTO> tags;
	private String coordinates;



}
