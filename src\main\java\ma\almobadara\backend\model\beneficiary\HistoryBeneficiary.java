package ma.almobadara.backend.model.beneficiary;

import jakarta.persistence.*;
import lombok.*;
import ma.almobadara.backend.model.administration.CacheAdUser;
import org.hibernate.annotations.CreationTimestamp;

import java.time.LocalDateTime;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class HistoryBeneficiary {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    protected Long id;
    private String title;
    private String description;
    private String createdBy;
    @CreationTimestamp
    private LocalDateTime createdAt;  // i shold add the attribute createdBy of the history
    @ManyToOne
    @JoinColumn(name = "beneficiaryId")
    private Beneficiary beneficiary;
}
