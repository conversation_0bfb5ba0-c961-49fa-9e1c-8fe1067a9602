package ma.almobadara.backend.model.donor;

import jakarta.persistence.*;
import lombok.*;
import ma.almobadara.backend.model.communs.Note;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@IdClass(NoteDonorId.class)
public class NoteDonor {

    @Id
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "donor_id")
    private Donor donor;

    @Id
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "note_id")
    private Note note;


    public String toDtoString() {
        return "{" +
                "\"date\": \"" + note.getCreatedDate().getDate() + "\"," +
                "\"objet\": \"" + note.getObjet() + "\"," +
                "\"prise_par\": \"" + note.getCreatedBy().getFirstName()+" "+note.getCreatedBy().getFirstName()+ "\"," +
                "\"commentaire\": \"" + note.getContent() + "\"," +
                "\"donateur_code\": \"" + donor.getCode() + "\"," +
                "\"donateur_id\": \"" + donor.getId() + "\"" +
                "}";
    }
}
