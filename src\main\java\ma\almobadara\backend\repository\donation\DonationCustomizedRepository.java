package ma.almobadara.backend.repository.donation;

import ma.almobadara.backend.dto.referentiel.CanalDonationDTO;
import ma.almobadara.backend.dto.donation.DonationDTO;
import ma.almobadara.backend.model.donation.Donation;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface DonationCustomizedRepository {

    Page<Donation> filterDonation(Pageable pageable, DonationDTO donation, String donorName, String columnName, String sortType, List<CanalDonationDTO> canalDonationDTOList);
}

