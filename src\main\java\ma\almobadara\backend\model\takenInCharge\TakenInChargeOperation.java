package ma.almobadara.backend.model.takenInCharge;

import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.UpdateTimestamp;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.Map;

import static ma.almobadara.backend.util.strings.HandleSpecialChars.escapeSpecialChars;

@Entity
@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
@DynamicUpdate
@ToString
public class TakenInChargeOperation {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private double amount;
    private double managementFees;
    private Date planningDate;
    private boolean reserved;
    private Date executionDate;
    private Date closureDate;
    private String comment;
    @Column(unique = true)
    private String code;
    @ManyToOne
    @JoinColumn(name = "taken_in_charge_donor_id", nullable = false)
    private TakenInChargeDonor takenInChargeDonor;
    @UpdateTimestamp
    private LocalDateTime createdAt;
    private String status ;

    public String getAudit(Map<String, String> params) {
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String createdAt = dateFormat.format(new Date());
        String planning = dateFormat.format(this.planningDate);
        String execution = executionDate != null ? dateFormat.format(this.executionDate) : "-";
        String closure = closureDate != null ? dateFormat.format(this.closureDate) : "-";

        StringBuilder auditJson = new StringBuilder("{")
                .append("\"Code de Kafalat\": \"").append(escapeSpecialChars(params.getOrDefault("takeInChargeCode", "-"))).append("\",")
                .append("\"Code de Donateur\": \"").append(escapeSpecialChars(params.getOrDefault("donorCode", "-"))).append("\",")
                .append("\"Code de Bénéficiaire\": \"").append(escapeSpecialChars(params.getOrDefault("beneficiary", "-"))).append("\",")
                .append("\"Service\": \"").append(escapeSpecialChars(params.getOrDefault("service", "-"))).append("\",")
                .append("\"Date de début de la Kafalat\": \"").append(escapeSpecialChars(createdAt)).append("\",")
                .append("\"Montant\": \"").append(escapeSpecialChars(String.valueOf(amount))).append("\",")
                .append("\"Frais de gestion\": \"").append(escapeSpecialChars(String.valueOf(managementFees))).append("\",");

        if (params.containsKey("size")) {
            auditJson.append("\"Nombre de mois\": \"").append(escapeSpecialChars(params.get("size"))).append("\",");
        }

        auditJson.append("\"Date de début de la 1ère Opération\": \"").append(escapeSpecialChars(planning)).append("\",")
                .append("\"Date d'exécution\": \"").append(escapeSpecialChars(execution)).append("\",")
                .append("\"Date de clôture\": \"").append(escapeSpecialChars(closure)).append("\",")
                .append("\"Commentaire\": \"").append(escapeSpecialChars(comment)).append("\",")
                .append("\"Status\": \"").append(escapeSpecialChars(status)).append("\"")

                .append("}");

        return auditJson.toString();
    }



}
