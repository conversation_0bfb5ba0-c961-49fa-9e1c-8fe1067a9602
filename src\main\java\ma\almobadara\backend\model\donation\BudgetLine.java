package ma.almobadara.backend.model.donation;

import jakarta.persistence.*;
import lombok.*;
import ma.almobadara.backend.enumeration.BudgetLineStatus;
import ma.almobadara.backend.model.administration.ServiceCollectEps;
import ma.almobadara.backend.model.aideComplemenatire.AideComplementaire;
import ma.almobadara.backend.model.donor.Donor;
import ma.almobadara.backend.model.service.Services;
import ma.almobadara.backend.model.takenInCharge.TakenInChargeOperation;
import org.hibernate.annotations.CreationTimestamp;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Entity
@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class BudgetLine {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @Column(unique = true)
    private String code;
    private Double amount;
    private String comment;
    private Double valueCurrency;
    private Long currencyId;
    @CreationTimestamp
    private LocalDateTime createdAt;
    private LocalDateTime executionDate;
    private Long typePriseEnChargeId ;
    @ManyToOne
    @JoinColumn(name = "donation_id")
    private Donation donation;
    @Enumerated(EnumType.STRING) // Store as string in the database
    private BudgetLineStatus status; // Use enum for status instead of Boolean
    private String type;
    @ManyToOne
    @JoinColumn(name = "service_id")
    private Services service;

    @ManyToOne
    @JoinColumn(name = "service_collect_id")
    private ServiceCollectEps serviceCollectEps ;

    private Double amountByBeneficiary;
    @ManyToOne
    @JoinColumn(name = "aide_complementaire_id")
    private AideComplementaire aideComplementaire;
    @ManyToOne
    @JoinColumn(name = "taken_in_charge_operation_id")
    private TakenInChargeOperation takenInChargeOperation;
    private Double montantReserve;

    @Column(name = "products_category", length = 1000)
    private String productsCategory;

    private Boolean natureBudgetLine;


    // make a set for the products category from a list of Lng to a string
    public void setProductsCategory(List<Long> productsCategory) {
        if (productsCategory == null || productsCategory.isEmpty()) {
            this.productsCategory = null;
        } else {
            this.productsCategory = productsCategory.toString();
        }
    }

    // make a get for the products category from a string to a list of Long
    public List<Long> getProductsCategory() {
        if (productsCategory == null || productsCategory.isEmpty()) {
            return List.of();
        }

        // Remove brackets and split by comma
        String cleanedCategories = productsCategory.replaceAll("\\[|\\]", "").trim();

        return Arrays.stream(cleanedCategories.split(","))
                .map(String::trim) // Trim each value to remove any extra whitespace
                .map(Long::parseLong)
                .collect(Collectors.toList());
    }

}
