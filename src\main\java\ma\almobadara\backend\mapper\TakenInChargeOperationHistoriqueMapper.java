package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.takenInCharge.TakenInChargeOperationHistoriqueDTO;
import ma.almobadara.backend.model.takenInCharge.TakenInChargeOperationHistorique;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface TakenInChargeOperationHistoriqueMapper {

    TakenInChargeOperationHistoriqueDTO takenInChargeOperationHistoriqueToTakenInChargeOperationHistoriqueDTO(TakenInChargeOperationHistorique takenInChargeOperationHistorique);
}
