package ma.almobadara.backend.dto.aideComplemenatire;

import lombok.*;
import ma.almobadara.backend.dto.beneficiary.*;
import ma.almobadara.backend.dto.referentiel.AllergiesDTO;
import ma.almobadara.backend.dto.referentiel.DiseasesDTO;
import ma.almobadara.backend.dto.takenInCharge.TakenInChargeBeneficiaryDTO;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.Instant;
import java.util.Date;
import java.util.List;
import java.util.Set;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class BeneficiaryAideComplemenatireDTO {
    private Long id;
    private String code;
    private String firstName;
    private String lastName;
    private Long numberOfMembers;
    private SmallZoneDTO zone;
    private BeneficiaryStatutDTO beneficiaryStatut;
    private String statut;
    private Boolean statutValidation;
    private Boolean relatedToDonor;
    private String phoneNumber;
    private Date birthDate;
    private Boolean independent;
    private Double montantAbeneficier;
    private String nomCompletDuDonateur;
    private Double montantPreciserParDonateur;
    private Long donorId;
    private String type;

}
