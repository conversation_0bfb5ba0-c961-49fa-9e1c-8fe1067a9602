package ma.almobadara.backend.dto.donor;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;
import ma.almobadara.backend.dto.beneficiary.BeneficiaryDTO;
import ma.almobadara.backend.dto.beneficiary.BeneficiaryReleveDto;
import ma.almobadara.backend.dto.referentiel.CanalDonationDTO;
import ma.almobadara.backend.dto.service.ServicesDTO;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
@JsonIgnoreProperties(value = { "executionDate" }, allowGetters = true)
public class ReleveDonorDto {

    private Long id;
    private String code;
    private String name;
    private Double montantEntree;
    private Date receptionDate;
    private String type;
    private CanalDonationDTO canalDonation;
    private String typeDonationKafalat;
    private LocalDateTime dateExecution;
    private Double montantSortie;
    private ServicesDTO services;
    private Double totalEntree;
    private Double totalSortie;
    private List<BeneficiaryReleveDto> beneficiaries;


    public LocalDateTime getSortableDate() {
        // Prioriser receptionDate, sinon utiliser dateExecution
        if (receptionDate != null) {
            return receptionDate.toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime();  // Convertir Date en LocalDateTime
        } else if (dateExecution != null) {
            return dateExecution;
        }
        return null; // Aucun critère de date disponible
    }

}
