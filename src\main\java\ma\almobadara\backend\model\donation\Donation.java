package ma.almobadara.backend.model.donation;

import com.google.gson.Gson;
import jakarta.persistence.*;
import lombok.*;
import ma.almobadara.backend.dto.donor.DonorDTO;
import ma.almobadara.backend.dto.donor.DonorPhysicalDTO;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.model.donor.Donor;
import ma.almobadara.backend.service.donor.DonorService;
import org.hibernate.annotations.CreationTimestamp;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;

import static ma.almobadara.backend.util.strings.HandleSpecialChars.escapeSpecialChars;

@Entity
@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class Donation {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private Double value;
    private Date receptionDate;
    private boolean identifiedDonor;
    private String unidentifiedDonorName;
    private String comment;
    private String transactionNumber;
    private String type;
    private Double valueCurrency;
    private Boolean archived;
    @CreationTimestamp
    private LocalDateTime createdAt;
    private Long canalDonationId;
    private Long currencyId;
    @Column(unique = true)
    private String code;
    @ManyToOne
    @JoinColumn(name = "donor_id")
    private Donor donor;
    @OneToMany(mappedBy = "donation", cascade = CascadeType.REMOVE)
    private List<DocumentDonation> documentDonations;
    @OneToMany(mappedBy = "donation", cascade = CascadeType.REMOVE)
    private List<DonationProductNature> donationProductNatures;
    @OneToMany(mappedBy = "donation", cascade = CascadeType.ALL)
    private List<BudgetLine> budgetLines;

    public String toAuditString(String currencyName, String canalDonationName, String donorName) throws TechnicalException {
        Gson gson = new Gson();
        SimpleDateFormat formatter = new SimpleDateFormat("dd-MM-yyyy");
        String date = formatter.format(receptionDate);
        StringBuilder budgetLinesData = new StringBuilder("");
        Map<String, String> nestedObject = new HashMap<>();

        if (budgetLines != null) {
            for (BudgetLine budgetLine : budgetLines) {
                if (budgetLine.getService() != null) {
                    nestedObject.put("Service "+ ( budgetLine.getId() == null ? "" :budgetLine.getId()) +": "+ budgetLine.getService().getName(),"Prix : " + budgetLine.getAmount());
                } else {
                    nestedObject.put("Service : Non identifie","Prix : " + budgetLine.getAmount());
                }
            }
        }
        String nombreDe = "Nombre des Lignes de Budget";

        Map<String, Object> jsonMap = new HashMap<>();
        jsonMap.put("Code", code);
        jsonMap.put("Commentaire", escapeSpecialChars(comment));
        jsonMap.put("Canal de Donation", escapeSpecialChars(canalDonationName));
        jsonMap.put("Nom du donateur", donorName != null ? donorName : "-");

        //OBject inside keyToValue
        jsonMap.put(nombreDe, nestedObject);

        jsonMap.put("Type Donation", (type != null ? type : "-"));
        jsonMap.put("Valeur de la donation", (value != null ? value + " Dh" : "-"));
        jsonMap.put("Equivalent", (valueCurrency == null || currencyName == null) ? "-" : valueCurrency + " " + currencyName);
        jsonMap.put("Date Reception", date);

        return gson.toJson(jsonMap);
    }
}
