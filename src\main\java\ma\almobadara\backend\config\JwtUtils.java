package ma.almobadara.backend.config;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.JwtException;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.io.Decoders;
import io.jsonwebtoken.security.Keys;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.security.Key;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

@Component
@RequiredArgsConstructor
public class JwtUtils {

    @Value("${jwt.secret}")
    private String jwtSecret;

    @Value("${jwt.expiration}")
    private long jwtExpiration;

    @Value("${jwt.refresh-token.expiration}")
    private long refreshExpiration;

    public String generateToken(String username) {
        return generateToken(new HashMap<>(), username, jwtExpiration);
    }

    public String generateRefreshToken(String username) {
        return generateToken(new HashMap<>(), username, refreshExpiration);
    }

    public String generateToken(Map<String, Object> extraClaims, String username, long expirationMillis) {
        return Jwts.builder()
                .setClaims(extraClaims)
                .setSubject(username)
                .setIssuedAt(new Date())
                .setExpiration(new Date(System.currentTimeMillis() + expirationMillis))
                .claim("tokenType", expirationMillis == jwtExpiration ? "ACCESS" : "REFRESH")
                .signWith(getSigningKey(), SignatureAlgorithm.HS256)
                .compact();
    }

    private Key getSigningKey() {
        byte[] keyBytes = Decoders.BASE64.decode(jwtSecret);
        return Keys.hmacShaKeyFor(keyBytes);
    }

    public boolean isTokenValid(String token) {
        try {
            // Parse the token once and get the claims
            var claims = Jwts.parserBuilder()
                    .setSigningKey(getSigningKey())
                    .build()
                    .parseClaimsJws(token)
                    .getBody();

            // Get the expiration date from claims
            Date expiration = claims.getExpiration();

            // Get current date
            Date now = new Date();

            // Check if token is expired
            return !expiration.before(now);
        } catch (JwtException e) {
            return false;
        }
    }

    public boolean isValidAccessToken(String token) {
        try {
            // Parse the token once and get the claims
            var claims = Jwts.parserBuilder()
                    .setSigningKey(getSigningKey())
                    .build()
                    .parseClaimsJws(token)
                    .getBody();

            // Get the expiration date from claims
            Date expiration = claims.getExpiration();

            // Get current date
            Date now = new Date();

            // Get token type
            String tokenType = claims.get("tokenType", String.class);

            // Check if token is expired and is an access token
            return !expiration.before(now) && "ACCESS".equals(tokenType);
        } catch (JwtException e) {
            return false;
        }
    }

    public String extractEmail(String token) {
        return Jwts.parserBuilder()
                .setSigningKey(getSigningKey())
                .build()
                .parseClaimsJws(token)
                .getBody()
                .getSubject();
    }
}