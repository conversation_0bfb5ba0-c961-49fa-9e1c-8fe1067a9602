package ma.almobadara.backend.controller.mobile;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.mobile.DocumentRenewMobileDTO;
import ma.almobadara.backend.service.mobile.DocumentMobileService;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import static ma.almobadara.backend.service.administration.CacheAdUserService.logUserInfo;

@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping("/mobile/document")
@CrossOrigin(origins = "*")
public class DocumentMobileController {

    private final DocumentMobileService documentMobileService;

    /**
     * Get documents that need to be renewed for mobile with pagination
     * @param page Page number (defaults to 0)
     * @param zoneId Zone ID to filter by (required)
     * @return Page of DocumentRenewMobileDTO
     */
    @GetMapping("/renew")
    public ResponseEntity<Page<DocumentRenewMobileDTO>> getToRenewDocuments(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(required = true) Long zoneId) {
        
        logUserInfo("getToRenewDocumentsForMobile", "zoneId: " + zoneId + ", page: " + page);
        
        try {
            Page<DocumentRenewMobileDTO> documents = documentMobileService.getToRenewDocumentsForMobile(page, zoneId);
            log.info("End resource getToRenewDocumentsForMobile. Retrieved documents: {}, OK", documents.getTotalElements());
            return new ResponseEntity<>(documents, HttpStatus.OK);
        } catch (Exception e) {
            log.error("End resource getToRenewDocumentsForMobile. KO: {}", e.getMessage());
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
