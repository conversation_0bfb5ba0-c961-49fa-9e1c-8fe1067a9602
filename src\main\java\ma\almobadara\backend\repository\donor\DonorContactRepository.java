package ma.almobadara.backend.repository.donor;

import ma.almobadara.backend.model.donor.DonorContact;
import ma.almobadara.backend.model.donor.DonorMoral;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface DonorContactRepository extends JpaRepository<DonorContact, Long> {

    List<DonorContact> findByDonor(DonorMoral donor);
    Optional<DonorContact> findByEmail(String email);

    @Query("SELECT c FROM DonorContact c WHERE c.donor = :donor AND c.email IS NOT NULL")
    List<DonorContact> findByDonorAndEmailIsNotNull(@Param("donor") DonorMoral donor);

}
