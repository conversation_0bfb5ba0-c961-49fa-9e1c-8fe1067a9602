package ma.almobadara.backend.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.oauth2.OAuth2ResourceServerConfiguration;
import ma.almobadara.backend.properties.Auth0Properties;
import ma.almobadara.backend.repository.administration.CacheAdUserRepository;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.oauth2.core.DelegatingOAuth2TokenValidator;
import org.springframework.security.oauth2.core.OAuth2Error;
import org.springframework.security.oauth2.core.OAuth2TokenValidator;
import org.springframework.security.oauth2.core.OAuth2TokenValidatorResult;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.jwt.JwtValidators;
import org.springframework.security.oauth2.jwt.NimbusJwtDecoder;

@Slf4j
@Configuration
@RequiredArgsConstructor
public class JwtDecoderConfig {

    private final OAuth2ResourceServerConfiguration oAuth2ResourceServerConfiguration;
    private final Auth0Properties auth0Properties;
    private final CacheAdUserRepository cacheAdUserRepository;

    @Bean
    public JwtDecoder azureJwtDecoder() {
        log.debug("Configuring Azure JWT Decoder");
        OAuth2TokenValidator<Jwt> audienceValidator = new SecurityConfig.AudienceValidator(oAuth2ResourceServerConfiguration.getClientId(), cacheAdUserRepository);
        OAuth2TokenValidator<Jwt> withIssuer = JwtValidators.createDefaultWithIssuer(oAuth2ResourceServerConfiguration.getIssuerUri());
        OAuth2TokenValidator<Jwt> withAudience = new DelegatingOAuth2TokenValidator<>(withIssuer, audienceValidator);

        NimbusJwtDecoder jwtDecoder = NimbusJwtDecoder.withJwkSetUri(oAuth2ResourceServerConfiguration.getJwkSetUri()).build();
        jwtDecoder.setJwtValidator(withAudience);

        return jwtDecoder;
    }

    @Bean
    public JwtDecoder auth0JwtDecoder() {
        String expectedIssuer = auth0Properties.getIssuerUri();
        log.debug("Configuring Auth0 JWT Decoder with expected issuer: {}", expectedIssuer);
        
        OAuth2TokenValidator<Jwt> withIssuer = new OAuth2TokenValidator<Jwt>() {
            @Override
            public OAuth2TokenValidatorResult validate(Jwt jwt) {
                String receivedIssuer = jwt.getIssuer().toString();
                // Remove trailing slash from received issuer for comparison
                String normalizedReceivedIssuer = receivedIssuer.endsWith("/") 
                    ? receivedIssuer.substring(0, receivedIssuer.length() - 1) 
                    : receivedIssuer;
                
                log.debug("Validating Auth0 JWT - Expected issuer: {}, Received issuer: {}, Normalized received issuer: {}", 
                    expectedIssuer, receivedIssuer, normalizedReceivedIssuer);
                
                if (expectedIssuer.equals(normalizedReceivedIssuer)) {
                    return OAuth2TokenValidatorResult.success();
                }
                
                OAuth2Error error = new OAuth2Error("invalid_token", "The iss claim is not valid", null);
                return OAuth2TokenValidatorResult.failure(error);
            }
        };
        
        OAuth2TokenValidator<Jwt> audienceValidator = new SecurityConfig.Auth0AudienceValidator(auth0Properties.getAudience());
        OAuth2TokenValidator<Jwt> withAudience = new DelegatingOAuth2TokenValidator<>(withIssuer, audienceValidator);

        NimbusJwtDecoder jwtDecoder = NimbusJwtDecoder.withJwkSetUri(auth0Properties.getJwkSetUri()).build();
        jwtDecoder.setJwtValidator(jwt -> {
            log.debug("Full JWT claims: {}", jwt.getClaims());
            
            OAuth2TokenValidatorResult result = withAudience.validate(jwt);
            if (result.hasErrors()) {
                log.error("Auth0 JWT validation failed. Errors: {}", result.getErrors());
            } else {
                log.debug("Auth0 JWT validation successful");
            }
            return result;
        });

        return jwtDecoder;
    }
} 