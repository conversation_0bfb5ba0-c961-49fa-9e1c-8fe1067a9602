package ma.almobadara.backend.repository.aideComplemenatire;



import ma.almobadara.backend.model.aideComplemenatire.AideComplementaire;
import ma.almobadara.backend.model.donation.BudgetLine;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface AideComplementaireRepository extends JpaRepository<AideComplementaire, Long> {
    Optional<AideComplementaire> findByCode(String code);
    @Query("SELECT a FROM AideComplementaire a WHERE a.id = :id")
    Optional<AideComplementaire> findByIdWithQuery(@Param("id") Long id);


    @Query("SELECT a FROM AideComplementaire a WHERE LOWER(a.service.name) LIKE LOWER(CONCAT('%', :query, '%')) OR LOWER(a.commentaire) LIKE LOWER(CONCAT('%', :query, '%')) OR LOWER(a.slogan) LIKE LOWER(CONCAT('%', :query, '%')) OR LOWER(a.name) LIKE LOWER(CONCAT('%', :query, '%'))      ")
    Page<AideComplementaire> searchAideComplementaire(@Param("query") String query, Pageable pageable);

    @Query("SELECT a.statut, COUNT(a) FROM AideComplementaire a GROUP BY a.statut")
    List<Object[]> getAideByStatus();

    @Query("SELECT s.name, count(a) FROM AideComplementaire a JOIN a.service s GROUP BY s.name")
    List<Object[]> getAideAmountByService();

    @Query(value = """
    SELECT
        TO_CHAR(created_at, 'YYYY-MM') AS month,
        COUNT(*) AS count
    FROM aide_complementaire
    WHERE created_at >= CURRENT_DATE - INTERVAL '12 months'
    GROUP BY TO_CHAR(created_at, 'YYYY-MM')
    ORDER BY month ASC
    """, nativeQuery = true)
    List<Object[]> getAideByDate();
}

