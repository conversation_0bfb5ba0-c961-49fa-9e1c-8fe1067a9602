package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.donation.BudgetLineDTO;
import ma.almobadara.backend.dto.donation.BudgetLineForAideComplementaireDTO;
import ma.almobadara.backend.enumeration.BudgetLineStatus;
import ma.almobadara.backend.model.donation.BudgetLine;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;

import java.util.List;

@Mapper(componentModel = "spring")
public interface BudgetLineMapper {

    // Mapping for BudgetLine -> BudgetLineDTO
    @Mapping(source = "currencyId", target = "currency.id")
    @Mapping(target = "enableCurrency", expression = "java(budgetLine.getValueCurrency() != null)")
    @Mapping(target = "status", source = "status", qualifiedByName = "statusToString")
    @Mapping(target = "aideComplementaireName", source = "aideComplementaire.name")
    BudgetLineDTO budgetLineToBudgetLineDTO(BudgetLine budgetLine);


    @Mapping(target = "status", source = "status", qualifiedByName = "statusToString")
    BudgetLineForAideComplementaireDTO budgetLineToBudgetLineAideComplementaireDTO(BudgetLine budgetLine);

    // Mapping for BudgetLineDTO -> BudgetLine
    @Mapping(source = "currency.id", target = "currencyId")
    @Mapping(target = "status", source = "status", qualifiedByName = "stringToStatus")
    BudgetLine budgetLineDTOToBudgetLine(BudgetLineDTO budgetLineDTO);

    // Mapping a list of BudgetLines -> BudgetLineDTOs
    List<BudgetLineDTO> budgetLinesToBudgetLineDTOs(List<BudgetLine> budgetLines);

    // Mapping a list of BudgetLineDTOs -> BudgetLines
    List<BudgetLine> budgetLineDTOsToBudgetLines(List<BudgetLineDTO> budgetLineDTOs);

    // Update existing BudgetLine from BudgetLineDTO
    @Mapping(source = "currency.id", target = "currencyId")
    @Mapping(target = "status", source = "status", qualifiedByName = "stringToStatus")
    @Mapping(target = "executionDate", ignore = true)
    void updateBudgetLineFromDTO(BudgetLineDTO budgetLineDTO, @MappingTarget BudgetLine budgetLine);

@Named("statusToString")
    default String statusToString(BudgetLineStatus status) {
        return status != null ? status.name() : null;
    }
    @Named("stringToStatus")
    default BudgetLineStatus stringToStatus(String status) {
        return status != null ? BudgetLineStatus.valueOf(status) : BudgetLineStatus.DISPONIBLE;
    }

}
