package ma.almobadara.backend.dto.referentiel;

import lombok.*;
import org.springframework.hateoas.RepresentationModel;

import java.io.Serializable;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class FamilyDocumentTypeDTO extends RepresentationModel<FamilyDocumentTypeDTO> implements Serializable {

	private static final long serialVersionUID = -7308151301901051816L;

	private Long id;

	private String code;
	private String name;
	private String nameAr;
	private String nameEn;

	private String folderName;

}
