package ma.almobadara.backend.controller.donor;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.donor.CorrespondenceDto;
import ma.almobadara.backend.service.donor.CorrespondenceService;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping("/correspondences")
public class CorrespondenceController {

    private final CorrespondenceService correspondenceService;

    @PostMapping(value = "", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    @Operation(summary = "Create a Correspondence ", tags = {"correspondence"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(schema = @Schema(implementation = CorrespondenceDto.class)))})
    public ResponseEntity<CorrespondenceDto> addCorrespondence(@ModelAttribute CorrespondenceDto correspondenceDto) {
        CorrespondenceDto addedCorrespondence = correspondenceService.addCorrespondence(correspondenceDto);
        return new ResponseEntity<>(addedCorrespondence, HttpStatus.CREATED);
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get All Correspondences By Entity", description = "get all Correspondences byEntityId", tags = {"correspondence"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = CorrespondenceDto.class))))})
    public ResponseEntity<List<CorrespondenceDto>> getCorrespondenceByIdDonor(@PathVariable Long id) {

        List<CorrespondenceDto> correspondenceDTOList = correspondenceService.getCorrespondenceByIdDonor(id);
        if (correspondenceDTOList != null) {
            return new ResponseEntity<>(correspondenceDTOList, new HttpHeaders(), HttpStatus.OK);
        } else {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
    }

    @GetMapping("")
    public ResponseEntity<List<CorrespondenceDto>> getAllCorrespondences() {
        List<CorrespondenceDto> correspondencesDto = correspondenceService.getAllCorrespondences();
        return new ResponseEntity<>(correspondencesDto, HttpStatus.OK);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete correspondence", description = "delete correspondence", tags = {"correspondence"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = CorrespondenceDto.class))))})
    public ResponseEntity<Void> deleteCorrespondence(@PathVariable Long id) {
        correspondenceService.deleteCorrespondence(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

}
