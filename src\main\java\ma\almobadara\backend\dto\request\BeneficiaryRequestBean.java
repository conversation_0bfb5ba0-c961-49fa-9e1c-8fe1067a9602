package ma.almobadara.backend.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import ma.almobadara.backend.dto.beneficiary.*;

import java.util.List;

@Getter
@Setter
@Builder
@AllArgsConstructor
public class BeneficiaryRequestBean {

    BeneficiaryDTO beneficiary;
    List<ServiceAndStatus> serviceAndStatuses;
    List<EducationDTO> educations;
    List<ScholarshipBeneficiaryDTO> scholarshipBeneficiaries;
    List<EpsResidentDTO> epsResidents;
    List<DiseaseTreatmentDTO> diseaseTreatments;
    List<DocumentBeneficiaryDTO> documents;
    List<NoteBeneficiaryDTO> notes;

}