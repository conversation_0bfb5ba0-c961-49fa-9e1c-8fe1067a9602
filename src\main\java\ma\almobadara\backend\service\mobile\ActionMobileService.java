package ma.almobadara.backend.service.mobile;

import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.clients.RefFeignClient;
import ma.almobadara.backend.dto.mobile.ActionMobileDTO;
import ma.almobadara.backend.dto.referentiel.ActionStatusDTO;
import ma.almobadara.backend.model.administration.Assistant;
import ma.almobadara.backend.model.administration.CacheAdUser;
import ma.almobadara.backend.model.communs.Action;
import ma.almobadara.backend.model.communs.CommentAction;
import ma.almobadara.backend.repository.administration.AssistantRepository;
import ma.almobadara.backend.repository.administration.CacheAdUserRepository;
import ma.almobadara.backend.repository.communs.ActionRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class ActionMobileService {

    private final ActionRepository actionRepository;
    private final AssistantRepository assistantRepository;
    private final CacheAdUserRepository cacheAdUserRepository;
    private final RefFeignClient refFeignClient;

    /**
     * Get all actions for mobile with a fixed page size of 6
     * @param page Optional page number
     * @return Page of ActionMobileDTO
     */
    public Page<ActionMobileDTO> getAllActionsForMobile(Optional<Integer> page) {
        log.debug("Start service getAllActionsForMobile");
        
        // Create pageable with fixed size of 6
        int pageNumber = page.orElse(0);
        int pageSize = 6;
        Sort.Direction sortDirection = Sort.Direction.DESC;
        String sortBy = "dateEntry";
        PageRequest pageRequest = PageRequest.of(pageNumber, pageSize, Sort.by(sortDirection, sortBy));
        
        // Get actions from repository
        Page<Action> actions = actionRepository.findAll(pageRequest);
        
        // Convert to mobile DTOs
        List<ActionMobileDTO> mobileDTOs = actions.getContent().stream()
                .map(this::convertToMobileDTO)
                .collect(Collectors.toList());
        
        log.debug("End service getAllActionsForMobile");
        return new PageImpl<>(mobileDTOs, pageRequest, actions.getTotalElements());
    }
    
    /**
     * Get actions for mobile filtered by user ID with a fixed page size of 6
     * @param userId The ID of the user
     * @param page Optional page number
     * @return Page of ActionMobileDTO
     */
    public Page<ActionMobileDTO> getActionsByUserId(Long userId, Optional<Integer> page) {
        log.debug("Start service getActionsByUserId for user ID: {}", userId);
        
        // Create pageable with fixed size of 6
        int pageNumber = page.orElse(0);
        int pageSize = 6;
        Sort.Direction sortDirection = Sort.Direction.DESC;
        String sortBy = "dateEntry";
        PageRequest pageRequest = PageRequest.of(pageNumber, pageSize, Sort.by(sortDirection, sortBy));
        
        // Get the user
        CacheAdUser user = cacheAdUserRepository.findById(userId)
                .orElseThrow(() -> new EntityNotFoundException("User not found with ID: " + userId));
        
        // Get actions by user (created by or affected to)
        List<Action> userActions = actionRepository.findByCreatedByOrAffectedTo(user, user);
        
        // Apply pagination manually since the repository method doesn't support it
        int start = (int) pageRequest.getOffset();
        int end = Math.min((start + pageRequest.getPageSize()), userActions.size());
        
        List<Action> pageContent = userActions.subList(start, end);
        
        // Convert to mobile DTOs
        List<ActionMobileDTO> mobileDTOs = pageContent.stream()
                .map(this::convertToMobileDTO)
                .collect(Collectors.toList());
        
        log.debug("End service getActionsByUserId, found {} actions", userActions.size());
        return new PageImpl<>(mobileDTOs, pageRequest, userActions.size());
    }
    
    /**
     * Get actions for mobile filtered by assistant ID with a fixed page size of 6
     * @param assistantId The ID of the assistant
     * @param page Optional page number
     * @return Page of ActionMobileDTO
     */
    public Page<ActionMobileDTO> getActionsByAssistantId(Long assistantId, Optional<Integer> page) {
        log.debug("Start service getActionsByAssistantId for assistant ID: {}", assistantId);
        
        // Create pageable with fixed size of 6
        int pageNumber = page.orElse(0);
        int pageSize = 6;
        Sort.Direction sortDirection = Sort.Direction.DESC;
        String sortBy = "dateEntry";
        PageRequest pageRequest = PageRequest.of(pageNumber, pageSize, Sort.by(sortDirection, sortBy));
        
        // Get the assistant to find their user
        Assistant assistant = assistantRepository.findById(assistantId)
                .orElseThrow(() -> new EntityNotFoundException("Assistant not found with ID: " + assistantId));
        
        // Check if assistant has a user
        if (assistant.getCacheAdUser() == null) {
            log.warn("Assistant with ID {} has no associated user", assistantId);
            return new PageImpl<>(Collections.emptyList(), pageRequest, 0);
        }
        
        CacheAdUser user = assistant.getCacheAdUser();
        log.debug("Found user ID {} for assistant ID {}", user.getId(), assistantId);
        
        // Get actions by user (created by or affected to)
        List<Action> userActions = actionRepository.findByCreatedByOrAffectedTo(user, user);
        
        // Apply pagination manually since the repository method doesn't support it
        int start = (int) pageRequest.getOffset();
        int end = Math.min((start + pageRequest.getPageSize()), userActions.size());
        
        List<Action> pageContent = userActions.subList(start, end);
        
        // Convert to mobile DTOs
        List<ActionMobileDTO> mobileDTOs = pageContent.stream()
                .map(this::convertToMobileDTO)
                .collect(Collectors.toList());
        
        log.debug("End service getActionsByAssistantId, found {} actions", userActions.size());
        return new PageImpl<>(mobileDTOs, pageRequest, userActions.size());
    }
    
    /**
     * Convert Action entity to ActionMobileDTO with only the required fields
     */
    private ActionMobileDTO convertToMobileDTO(Action action) {
        ActionMobileDTO dto = new ActionMobileDTO();
        
        // Set ID and object
        dto.setId(action.getId());
        dto.setObject(action.getSubject());
        
        // Set limit date
        dto.setLimitDate(action.getDeadline());
        
        // Get the status name from the reference service
        try {
            ActionStatusDTO statusDTO = refFeignClient.getActionStatus(action.getStatus());
            dto.setStatus(statusDTO != null ? statusDTO.getName() : "Unknown");
        } catch (Exception e) {
            log.error("Error getting status for action {}: {}", action.getId(), e.getMessage());
            dto.setStatus("Unknown");
        }
        
        // Set affected by (person the action is assigned to)
        if (action.getAffectedTo() != null) {
            String firstName = action.getAffectedTo().getFirstName() != null ? action.getAffectedTo().getFirstName() : "";
            String lastName = action.getAffectedTo().getLastName() != null ? action.getAffectedTo().getLastName() : "";
            dto.setAffectedBy(firstName + " " + lastName);
        } else {
            dto.setAffectedBy("Not assigned");
        }
        
        // Get the latest comment as description
        if (action.getComments() != null && !action.getComments().isEmpty()) {
            CommentAction latestComment = action.getComments().stream()
                    .max(Comparator.comparing(CommentAction::getId))
                    .orElse(null);
            
            if (latestComment != null) {
                dto.setDescription(latestComment.getContent());
            }
        }
        
        return dto;
    }
}
