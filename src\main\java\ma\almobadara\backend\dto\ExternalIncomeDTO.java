package ma.almobadara.backend.dto;

import lombok.*;
import ma.almobadara.backend.dto.family.FamilyMemberDTO;
import ma.almobadara.backend.dto.referentiel.IncomeSourceDTO;
import org.springframework.hateoas.RepresentationModel;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class ExternalIncomeDTO extends RepresentationModel<ExternalIncomeDTO> implements Serializable {

	private static final long serialVersionUID = -6412492047869797428L;

	private Long id;

	private Double amount;

	private int periodicity;

	private String comment;

	private FamilyMemberDTO familyMember;

	private IncomeSourceDTO incomeSource;

	private Date startDate;

	private Date endDate;

}
