package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.beneficiary.RapportDto;
import ma.almobadara.backend.dto.beneficiary.RapportPictureDTO;
import ma.almobadara.backend.model.beneficiary.Rapport;
import ma.almobadara.backend.model.beneficiary.RapportPicture;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.stream.Collectors;
@Mapper(componentModel = "spring")
public interface RapportMapper {

    RapportMapper INSTANCE = Mappers.getMapper(RapportMapper.class);

    @Mapping(source = "donor.id", target = "donorId")
    @Mapping(source = "beneficiary.id", target = "beneficiaryId")
    @Mapping(source = "family.id", target = "familyId")
    @Mapping(source = "familyRelationshipId", target = "familyRelationship.id")
    @Mapping(source = "cityId", target = "city.id")
    @Mapping(source = "schoolLevelId", target = "schoolLevel.id")
    @Mapping(source = "professionId", target = "profession.id")
    @Mapping(source = "accommodationTypeId", target = "accommodationType.id")
    @Mapping(source = "pictures", target = "pictures")
    RapportDto toDto(Rapport rapport);

    @Mapping(target = "donor", ignore = true) // Pour éviter une boucle infinie ou un conflit
    @Mapping(target = "beneficiary", ignore = true)
    @Mapping(target = "family", ignore = true)
    @Mapping(source = "city.id", target = "cityId")
    @Mapping(source = "familyRelationship.id", target = "familyRelationshipId")
    @Mapping(source = "schoolLevel.id", target = "schoolLevelId")
    @Mapping(source = "profession.id", target = "professionId")
    @Mapping(source = "accommodationType.id", target = "accommodationTypeId")
    @Mapping(source = "pictures", target = "pictures", qualifiedByName = "mapPictures")
    Rapport toEntity(RapportDto rapportDto);

    List<RapportDto> toDtoList(List<Rapport> rapports);

    List<Rapport> toEntityList(List<RapportDto> rapportDtos);

    @Named("mapPictures")
    default List<RapportPicture> mapPictures(List<RapportPictureDTO> pictureDTOs) {
        return pictureDTOs == null ? null : pictureDTOs.stream()
                .map(dto -> RapportPicture.builder()
                        .id(dto.getId())
                        .url(dto.getUrl())
                        .description(dto.getDescription())
                        .descriptionAng(dto.getDescriptionAng())
                        .descriptionAr(dto.getDescriptionAr())
                        .build())
                .collect(Collectors.toList());
    }

}
