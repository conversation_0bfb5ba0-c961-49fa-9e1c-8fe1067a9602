package ma.almobadara.backend.translation;


import org.json.JSONObject;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

@Service
public class TranslationService {

    private static final String MYMEMORY_URL = "https://api.mymemory.translated.net/get";

    public Map<String, String> translate(String text, String sourceLang) throws Exception {
        Map<String, String> translations = new HashMap<>();

        // Define target languages
        Map<String, String> targetLanguages = Map.of(
                "fr", "fr",
                "en", "en",
                "ar", "ar"
        );

        for (Map.Entry<String, String> entry : targetLanguages.entrySet()) {
            String targetLanguage = entry.getValue();

            if (!targetLanguage.equals(sourceLang)) {
                String translatedText = translateText(text, sourceLang, targetLanguage);
                translations.put(targetLanguage, translatedText);
            } else {
                translations.put(targetLanguage, text); // Return the original text for source language
            }
        }

        return translations;
    }

    private String translateText(String text, String sourceLang, String targetLang) throws Exception {
        String urlString = String.format("%s?q=%s&langpair=%s|%s",
                MYMEMORY_URL,
                URLEncoder.encode(text, StandardCharsets.UTF_8),
                sourceLang,
                targetLang);
        URL url = new URL(urlString);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("GET");

        BufferedReader br = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8));
        StringBuilder response = new StringBuilder();
        String responseLine;
        while ((responseLine = br.readLine()) != null) {
            response.append(responseLine.trim());
        }

        return extractTranslation(response.toString());
    }

    private String extractTranslation(String jsonResponse) {
        JSONObject jsonObject = new JSONObject(jsonResponse);
        return jsonObject.getJSONObject("responseData").getString("translatedText");
    }
}
