package ma.almobadara.backend.util.strings;

public class HandleSpecialChars {
    public static String escapeSpecialChars(String input) {
        if (input == null) return "-";


        return input.replace("\\", "\\\\")
                .replace("\"", "\\\"")
                .replace("\n", "\\n")
                .replace("\r", "\\r")
                .replace("\t", "\\t")
                .replace("\b", "\\b")
                .replace("\f", "\\f");
    }
}
