package ma.almobadara.backend.service.administration;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.administration.TagDTO;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.model.administration.TypeTag;
import ma.almobadara.backend.repository.administration.TaggableRepository;
import ma.almobadara.backend.repository.administration.TypeTagRepositoy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
public class TypeTagService {
    private final TypeTagRepositoy typeTagRepository;
    
    public TypeTag createTypeTag(TypeTag typeTag) throws TechnicalException {
        log.info("Creating a new type tag");
        return typeTagRepository.save(typeTag);
    }

    public List<TypeTag> getAllTypeTags() {
        log.info("Retrieving all type tags");
        List<TypeTag> typeTags = typeTagRepository.findAll();
        for (TypeTag typeTag : typeTags) {
            if (typeTag.getTags() != null) {
                typeTag.setTags(null); // Avoid circular reference
            }
        }
        return typeTags;
    }




    public List<TypeTag> getTypeTagList() {
        log.info("Retrieving all type tags");
        return typeTagRepository.findAll();
    }
    
    public Optional<TypeTag> getTypeTagById(Long typeTagId) {
        log.info("Retrieving type tag by id: {}", typeTagId);
        return typeTagRepository.findById(typeTagId);
    }
    
    public void deleteTypeTag(Long typeTagId) {
        log.info("Deleting type tag with id: {}", typeTagId);
        typeTagRepository.deleteById(typeTagId);
    }
}
