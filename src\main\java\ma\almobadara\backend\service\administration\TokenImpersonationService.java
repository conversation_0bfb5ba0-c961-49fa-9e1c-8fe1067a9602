package ma.almobadara.backend.service.administration;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.administration.CacheAdUserDTO;
import ma.almobadara.backend.dto.administration.ImpersonationTokenDTO;
import ma.almobadara.backend.enumeration.RoleCode;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.exceptions.UserNotFoundException;
import ma.almobadara.backend.model.administration.CacheAdUser;
import ma.almobadara.backend.repository.administration.CacheAdUserRepository;
import ma.almobadara.backend.service.SecurityService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.crypto.SecretKey;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
public class TokenImpersonationService {

    private final CacheAdUserService cacheAdUserService;
    private final CacheAdUserRepository cacheAdUserRepository;
    private final SecurityService securityService;

    @Value("${impersonation.token.secret:defaultSecretKeyForImpersonationTokenSigningThatShouldBeAtLeast32Bytes}")
    private String secretKey;

    // No expiration time for impersonation tokens

    public ImpersonationTokenDTO generateImpersonationToken(Long targetUserId) throws TechnicalException {
        log.debug("Start service generateImpersonationToken for targetUserId: {}", targetUserId);

        String currentUserAzureId = securityService.getAuthenticatedAzureId();
        if (currentUserAzureId == null) {
            log.error("No authenticated user found.");
            return null;
        }

        CacheAdUser currentUser = cacheAdUserRepository.findByAzureDirectoryIdAndIsDeletedIsFalse(currentUserAzureId);
        if (currentUser == null) {
            log.error("Current user not found in database.");
            throw new UserNotFoundException("Current user not found in database", 404);
        }

        if (currentUser.getRole() == null ||
            !currentUser.getRole().getCode().equals(RoleCode.ADMIN.getCode())) {
            log.error("User does not have permission to impersonate.");
            throw new TechnicalException("User does not have permission to impersonate");
        }

        // Get the target user to impersonate
        Optional<CacheAdUser> targetUserOpt = cacheAdUserRepository.findById(targetUserId);
        if (targetUserOpt.isEmpty() || targetUserOpt.get().isDeleted()) {
            log.error("Target user not found or is deleted.");
            throw new UserNotFoundException("Target user not found", 404);
        }

        // Generate the token without expiration
        SecretKey key = Keys.hmacShaKeyFor(secretKey.getBytes(StandardCharsets.UTF_8));
        String token = Jwts.builder()
                .setSubject(currentUser.getAzureDirectoryId())
                .claim("originUserId", currentUser.getId())
                .claim("impersonatedUserId", targetUserId)
                .setIssuedAt(new Date())
                .signWith(key, SignatureAlgorithm.HS256)
                .compact();

        // Create and return the DTO
        ImpersonationTokenDTO tokenDTO = ImpersonationTokenDTO.builder()
                .token(token)
                .originUserId(currentUser.getId())
                .impersonatedUserId(targetUserId)
                .originUserName(currentUser.getFirstName()+" "+currentUser.getLastName())
                .build();

        log.debug("End service generateImpersonationToken for targetUserId: {}", targetUserId);
        return tokenDTO;
    }

    public Long validateImpersonationToken(String token) {
        if (token == null || token.isEmpty()) {
            return null;
        }

        try {
            SecretKey key = Keys.hmacShaKeyFor(secretKey.getBytes(StandardCharsets.UTF_8));
            Claims claims = Jwts.parserBuilder()
                    .setSigningKey(key)
                    .build()
                    .parseClaimsJws(token)
                    .getBody();

            return claims.get("impersonatedUserId", Long.class);
        } catch (Exception e) {
            log.warn("Invalid impersonation token: {}", e.getMessage());
            return null;
        }
    }

    public CacheAdUserDTO getImpersonatedUser(String token) {
        Long impersonatedUserId = validateImpersonationToken(token);
        if (impersonatedUserId == null) {
            return null;
        }

        try {
            return cacheAdUserService.loadUserWithRolePrivileges(impersonatedUserId);
        } catch (Exception e) {
            log.error("Error loading impersonated user: {}", e.getMessage());
            return null;
        }
    }
}
