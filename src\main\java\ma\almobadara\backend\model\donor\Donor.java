package ma.almobadara.backend.model.donor;

import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.SuperBuilder;
import ma.almobadara.backend.dto.referentiel.ActionStatusDTO;
import ma.almobadara.backend.dto.referentiel.CityDTO;
import ma.almobadara.backend.dto.referentiel.DonorStatusDTO;
import ma.almobadara.backend.model.aideComplemenatire.AideComplementaireDonorBeneficiary;
import ma.almobadara.backend.model.donation.Donation;
import ma.almobadara.backend.model.takenInCharge.TakenInChargeDonor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.DynamicUpdate;

import java.time.LocalDateTime;
import java.util.List;

import static ma.almobadara.backend.util.strings.HandleSpecialChars.escapeSpecialChars;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Inheritance(strategy = InheritanceType.JOINED)
@DynamicUpdate
@SuperBuilder
@ToString
 public class Donor {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private Double balance;
    private String identityCode;
    private String address;
    private String addressAr;
    private Long donorStatusId;
    @Transient
    private DonorStatusDTO status;
    private Long anonymeId;
    @Transient
    private ActionStatusDTO actionStatus;
    private Long actionStatusId;
    private Long cityId;
    @Transient
    private CityDTO city;
    @CreationTimestamp
    private LocalDateTime createdAt;
    private LocalDateTime deletedAt ;
    @Column(unique = true)
    private String code;
    private String comment;
    private String label;

    private String firstDonationYear;
    @OneToMany(targetEntity = NoteDonor.class, mappedBy = "donor",cascade = CascadeType.REMOVE)
    private List<NoteDonor> notes;
    @OneToMany(mappedBy = "donor")
    private List<DocumentDonor> documentsDonors;
    @OneToMany(targetEntity = TakenInChargeDonor.class, mappedBy = "donor")
    private List<TakenInChargeDonor> takenInChargeDonors;
    @OneToMany(mappedBy = "donor", cascade = CascadeType.ALL)
    private List<Donation> donations;
    @OneToMany(mappedBy = "donor", cascade = CascadeType.ALL)
    private List<Correspondence> correspondences;
    @OneToMany(mappedBy = "donor", cascade = CascadeType.ALL)
    private List<AideComplementaireDonorBeneficiary> aideComplementaireDonorBeneficiaries;

    public String toDtoString(String type, String nom) {
        return "{"
                + "\"Nom\": \"" + escapeSpecialChars(nom) + "\","
                + "\"Code\": \"" + escapeSpecialChars(code) + "\","
                + "\"Type\": \"" + escapeSpecialChars(type) + "\""
                + "}";
    }



}
