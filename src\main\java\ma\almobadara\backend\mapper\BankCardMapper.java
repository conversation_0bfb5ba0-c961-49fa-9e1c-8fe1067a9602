package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.beneficiary.BankCardDTO;
import ma.almobadara.backend.dto.beneficiary.PersonDTO;
import ma.almobadara.backend.model.beneficiary.BankCard;
import ma.almobadara.backend.model.beneficiary.Person;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface BankCardMapper {


	@Mapping(source = "cardTypeId", target = "cardType.id")
	BankCardDTO bankCardToBankCardDTO(BankCard bankCard);

	Iterable<BankCardDTO> bankCardToBankCardDTO(Iterable<BankCard> bankCards);

	@Mapping(source = "cardType.id", target = "cardTypeId")
	BankCard bankCardDTOToBankCard(BankCardDTO bankCardDTO);

	Iterable<BankCard> bankCardDTOToBankCard(Iterable<BankCardDTO> bankCardDTOS);

	@Mapping(target = "city", ignore = true)
	@Mapping(target = "bankCards", ignore = true)
	@Mapping(target = "beneficiary", ignore = true)
	@Mapping(target = "familyMember", ignore = true)
	PersonDTO personToPersonDTO(Person person);

}
