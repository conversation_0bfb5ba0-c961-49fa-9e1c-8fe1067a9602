package ma.almobadara.backend.service.mobile;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.communs.DocumentRenewDTO;
import ma.almobadara.backend.dto.mobile.DocumentRenewMobileDTO;
import ma.almobadara.backend.service.communs.DocumentService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class DocumentMobileService {

    private final DocumentService documentService;

    /**
     * Get documents that need to be renewed for mobile with a fixed page size of 6
     * @param page Optional page number
     * @param zoneId Zone ID to filter by (required)
     * @return Page of DocumentRenewMobileDTO
     */
    public Page<DocumentRenewMobileDTO> getToRenewDocumentsForMobile(int page, Long zoneId) {
        log.debug("Start service getToRenewDocumentsForMobile for zone ID: {}, page: {}", zoneId, page);
        
        // Fixed page size of 6 for mobile
        int size = 6;
        
        // Use the existing service to get documents that need to be renewed
        // We only pass zoneId and leave other search parameters as null
        Page<DocumentRenewDTO> documentRenewDTOs = documentService.getToRenewDocuments(
                page, size, zoneId, null, null, null, null);
        
        // Convert to mobile DTOs
        List<DocumentRenewMobileDTO> mobileDTOs = documentRenewDTOs.getContent().stream()
                .map(this::convertToMobileDTO)
                .collect(Collectors.toList());
        
        log.debug("End service getToRenewDocumentsForMobile, found {} documents", documentRenewDTOs.getTotalElements());
        return new PageImpl<>(mobileDTOs, PageRequest.of(page, size), documentRenewDTOs.getTotalElements());
    }
    
    /**
     * Convert DocumentRenewDTO to DocumentRenewMobileDTO
     */
    private DocumentRenewMobileDTO convertToMobileDTO(DocumentRenewDTO dto) {
        return DocumentRenewMobileDTO.builder()
                .id(dto.getId())
                .nomEntity(dto.getNomEntity())
                .idEntity(dto.getIdEntity())
                .module(dto.getModule())
                .codeEntity(dto.getCodeEntity())
                .labelDocument(dto.getLabelDocument())
                .typeDocument(dto.getTypeDocument())
                .expiryDate(dto.getExpiryDate())
                .expiryDateString(dto.getExpiryDateString())
                .build();
    }
}
