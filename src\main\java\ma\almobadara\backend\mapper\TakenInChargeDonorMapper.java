package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.donor.DonorDTO;
import ma.almobadara.backend.dto.takenInCharge.TakenInChargeDonorDTO;
import ma.almobadara.backend.dto.takenInCharge.TakenInChargeOperationDTO;
import ma.almobadara.backend.model.donor.Donor;
import ma.almobadara.backend.model.takenInCharge.TakenInChargeDonor;
import ma.almobadara.backend.model.takenInCharge.TakenInChargeOperation;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface TakenInChargeDonorMapper {

	@Mapping(target = "takenInCharge", ignore = true)
	TakenInChargeDonorDTO takenInChargeDonorToTakenInChargeDonorDTO(TakenInChargeDonor takenInChargeDonor);

	Iterable<TakenInChargeDonorDTO> takenInChargeDonorToTakenInChargeDonorDTO(Iterable<TakenInChargeDonor> takenInChargeDonor);

	TakenInChargeDonor takenInChargeDonorDTOToTakenInChargeDonor(TakenInChargeDonorDTO takenInChargeDonorDTO);

	Iterable<TakenInChargeDonor> takenInChargeDonorDTOToTakenInChargeDonor(Iterable<TakenInChargeDonorDTO> takenInChargeDonorDTO);

	@Mapping(target = "takenInChargeDonor", ignore = true)
	TakenInChargeOperationDTO takenInChargeOperationDTO(TakenInChargeOperation takenInChargeOperation);

	@Mapping(target = "notes", ignore = true)
	@Mapping(target = "documentDonors", ignore = true)
	@Mapping(target = "donations", ignore = true)
	@Mapping(target = "city", ignore = true)
	@Mapping(target = "status", ignore = true)
	@Mapping(target = "takenInChargeDonors", ignore = true)
	DonorDTO donorDTO(Donor donor);


}
