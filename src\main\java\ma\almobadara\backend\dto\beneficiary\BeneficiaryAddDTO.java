package ma.almobadara.backend.dto.beneficiary;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import ma.almobadara.backend.dto.administration.TagDTO;
import ma.almobadara.backend.dto.request.ServiceAndStatus;
import org.springframework.web.multipart.MultipartFile;

import java.time.Instant;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@Builder
@ToString
public class BeneficiaryAddDTO {

    private Long id;
    private String code;
    private String accountingCode;
    private String codeBeneficiary;
    private Boolean oldBeneficiary;
    private String addedYear;
    private Instant createdAt;
    private Boolean independent;
    private Boolean epsResident;
    private Boolean archived;
    private List<EpsResidentDTO> epsResidents;
    private List<ServiceAndStatus> beneficiaryServices;
    private Long personId;
    private String pictureUrl;
    private String pictureBase64;
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private MultipartFile picture;
    private String firstName;
    private String lastName;
    private String firstNameAr;
    private String lastNameAr;
    private String sex;
    private String email;
    private String phoneNumber;
    private String address;
    private String addressAr;
    private Date birthDate;
    private Long cityId;
    private String identityCode;
    private Long typeIdentityId;
    private Long professionId;
    private Long accommodationTypeId;
    private Long accommodationNatureId;
    private Long categoryBeneficiaryId;
    private Long typeKafalatId;
    private Long sourceBeneficiaryId;
    private String sourceBeneficiaryComment;
    private List<Long> typePriseEnChargeIds;
    private Long beneficiaryStatusId;
    private Long zoneId;
    private Long sousZoneId;
    private String remarqueFr;
    private String remarqueEn;
    private String remarqueAr;

    private List<TagDTO> tags;


}
