package ma.almobadara.backend.repository.beneficiary;

import ma.almobadara.backend.model.beneficiary.BeneficiaryAdHocGroup;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface BeneficiaryAdHocGroupRepository extends JpaRepository<BeneficiaryAdHocGroup, Long> {

    long countByIsDeletedIsNullOrIsDeletedFalse();

    Optional<BeneficiaryAdHocGroup> findTopByOrderByCodeDesc();
    boolean existsByCode(String code);

    Optional<BeneficiaryAdHocGroup> findByCode(String code);

    @Query("SELECT b FROM BeneficiaryAdHocGroup b JOIN b.beneficiaries ben WHERE ben.id = :beneficiaryId")
    List<BeneficiaryAdHocGroup> findByBeneficiaryId(@Param("beneficiaryId") Long beneficiaryId);


}
