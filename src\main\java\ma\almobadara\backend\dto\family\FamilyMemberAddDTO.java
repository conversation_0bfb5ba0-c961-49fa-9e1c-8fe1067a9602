package ma.almobadara.backend.dto.family;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import ma.almobadara.backend.dto.administration.TagDTO;
import ma.almobadara.backend.dto.beneficiary.BeneficiaryPieceJointe;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@Builder
public class FamilyMemberAddDTO {

    private Long id;
    private Long newTutorId;
    private boolean hasNewTutor;
    private Date newTutorStartDate;
    private Date newTutorEndDate;
    private String generalComment;

    private Long familyId;

    private String code;

    private boolean tutor;

    private Date tutorStartDate;

    private Date tutorEndDate;

    private Long familyRelationshipId;

    private Long personId;

    private String firstName;

    private String lastName;

    private String firstNameAr;

    private String lastNameAr;

    private String sex;

    private String email;

    private String phoneNumber;

    private String address;

    private String addressAr;

    private Date birthDate;

    private boolean deceased;
    private boolean educated;
    private String  schoolLevelType;

    private Date deathDate;

    private String deathReason;

    private String identityCode;

    private Long schoolLevelId;

    private Long cityId;

    private Long typeIdentityId;

    private Long professionId;

    private Long deathReasonId;

    private String schoolName;


    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private MultipartFile picture;


    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private FamilyPieceJointeDto familyPieceJointe;
    private Long cinId;
    private Long etatCivilId;

    private Long accommodationTypeId;
    private Long accommodationNatureId;
    private Long zoneId;
    private Long sousZoneId;

    private List<TagDTO> tags;

}
