package ma.almobadara.backend.model.takenInCharge;

import com.google.gson.Gson;
import jakarta.persistence.*;
import lombok.*;
import ma.almobadara.backend.model.beneficiary.Beneficiary;
import ma.almobadara.backend.model.donor.Donor;
import ma.almobadara.backend.model.service.Services;
import org.hibernate.annotations.CreationTimestamp;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static ma.almobadara.backend.util.strings.HandleSpecialChars.escapeSpecialChars;

@Entity
@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
@ToString
public class TakenInCharge {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @Column(unique = true)
    private String code;
    private String type;
    private Date startDate;
    private Date endDate;
    @ManyToOne
    @JoinColumn(name = "service_id")
    private Services service;
    private String status;
    @OneToMany(mappedBy = "takenInCharge")
    private List<TakenInChargeDonor> takenInChargeDonors;
    @OneToMany(mappedBy = "takenInCharge")
    private List<TakenInChargeBeneficiary> takenInChargeBeneficiaries;
    @OneToMany(targetEntity = NoteTakenInCharge.class, mappedBy = "takenInCharge")
    private List<NoteTakenInCharge> notes;
    @CreationTimestamp
    private LocalDateTime createdAt;
    private String clotureMotif;
    private Long clotureMotifTypeId;

    public String getAuditAjout(String donorName, String beneficiaryName, String serviceName){
        SimpleDateFormat formatter = new SimpleDateFormat("dd-MM-yyyy");
        String debutDate = (startDate!=null?formatter.format(startDate):"-");
        Gson gson = new Gson();
        Map<String,String> jsonMap = new LinkedHashMap<>();
        jsonMap.put("Donateur Name",(escapeSpecialChars(donorName)));
        jsonMap.put("Beneficiaire",(escapeSpecialChars(beneficiaryName)));
        jsonMap.put("Service",escapeSpecialChars(serviceName));
        jsonMap.put("Date de début",debutDate);
        jsonMap.put("Commentaire",type);

        return gson.toJson(jsonMap);
    }

    public String getAuditModification(String donorName, String beneficiaryName, String serviceName,String commentaire,String codeKafala){
        SimpleDateFormat formatter = new SimpleDateFormat("dd-MM-yyyy");
        String debutDate = (startDate!=null?formatter.format(startDate):"-");
        Gson gson = new Gson();
        Map<String,String> jsonMap = new LinkedHashMap<>();
        jsonMap.put("Code",(escapeSpecialChars(codeKafala)));
        jsonMap.put("Donateur Name",(escapeSpecialChars(donorName)));
        jsonMap.put("Beneficiaire",(escapeSpecialChars(beneficiaryName)));
        jsonMap.put("Service",serviceName!=null?escapeSpecialChars(serviceName):escapeSpecialChars(service.getName()));
        jsonMap.put("Date de début",debutDate);
        jsonMap.put("Commentaire",commentaire);

        return gson.toJson(jsonMap);
    }

    public String getAuditCloture(String clotureType,String donorName){
        SimpleDateFormat formatter = new SimpleDateFormat("dd-MM-yyyy");
        String debutDate = (startDate!=null?formatter.format(startDate):"-");
        Gson gson = new Gson();
        Map<String,String> jsonMap = new LinkedHashMap<>();
        jsonMap.put("Code",(escapeSpecialChars(code)));
        jsonMap.put("Donateur nom",(escapeSpecialChars(donorName)));
        jsonMap.put("Beneficiaire",(escapeSpecialChars(takenInChargeBeneficiaries.get(0).getBeneficiary().getPerson().getFirstName()+" "+takenInChargeBeneficiaries.get(0).getBeneficiary().getPerson().getLastName())));
        jsonMap.put("Service",escapeSpecialChars(service.getName()));
        jsonMap.put("Type de motif",escapeSpecialChars(clotureType));
        jsonMap.put("Motif de fermeture",escapeSpecialChars(clotureMotif));
        jsonMap.put("Date de début",debutDate);
        jsonMap.put("Commentaire",type);

        return gson.toJson(jsonMap);
    }
}

