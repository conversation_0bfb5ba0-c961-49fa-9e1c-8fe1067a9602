package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.beneficiary.AgendaRapportDto;
import ma.almobadara.backend.dto.beneficiary.AgendaRapportResponseDto;
import ma.almobadara.backend.dto.beneficiary.BeneficiaryResponseDTO;
import ma.almobadara.backend.model.beneficiary.AgendaRapport;
import ma.almobadara.backend.model.beneficiary.Beneficiary;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "spring")
public interface AgendaRapportMapper {

    AgendaRapportMapper INSTANCE = Mappers.getMapper(AgendaRapportMapper.class);

    @Mapping(source = "beneficiary.id", target = "beneficiaryId")
    @Mapping(source = "rapport.id", target = "rapportId")
    @Mapping(source = "rapport.detailComplete", target = "detailComplete")
    @Mapping(source = "modifiedAt", target = "modifiedAt")
    //@Mapping(source = "rapport.numberRapport", target = "numberRapport")
    @Mapping(source = "numberRapport", target = "numberRapport")
    AgendaRapportDto toDto(AgendaRapport agendaRapport);

    @Mapping(target = "beneficiary", ignore = true)
    @Mapping(target = "rapport", ignore = true)
    AgendaRapport toEntity(AgendaRapportDto agendaRapportDto);

    List<AgendaRapportDto> toDtoList(List<AgendaRapport> agendaRapports);

    List<AgendaRapport> toEntityList(List<AgendaRapportDto> agendaRapportDtos);

    @Mapping(source = "beneficiary.id", target = "beneficiaryId")
    @Mapping(source = "rapport.id", target = "rapportId")
    //@Mapping(source = "rapport.numberRapport", target = "numberRapport")
    @Mapping(source = "numberRapport", target = "numberRapport")
    @Mapping(source = "rapport.detailComplete", target = "detailComplete")
    @Mapping(source = "modifiedAt", target = "modifiedAt")
    @Mapping(source = "beneficiary", target = "beneficiary", qualifiedByName = "mapBeneficiary")
    AgendaRapportResponseDto toResponseDto(AgendaRapport agendaRapport);


    @Mapping(source = "beneficiary.id", target = "beneficiaryId")
    @Mapping(source = "rapport.id", target = "rapportId")
    @Mapping(source = "rapport.detailComplete", target = "detailComplete")
    @Mapping(source = "modifiedAt", target = "modifiedAt")
    @Mapping(source = "numberRapport", target = "numberRapport")
   // @Mapping(source = "rapport.numberRapport", target = "numberRapport")
    List<AgendaRapportResponseDto> toResponseDtoList(List<AgendaRapport> agendaRapports);

    @Named("mapBeneficiary")
    default BeneficiaryResponseDTO mapBeneficiary(Beneficiary beneficiary) {
        if (beneficiary == null) {
            return null;
        }
        return new BeneficiaryResponseDTO(
                beneficiary.getId(),
                beneficiary.getCode(),
                beneficiary.getPerson().getFirstName(),
                beneficiary.getPerson().getLastName(),
                beneficiary.getPerson().getFirstNameAr(),
                beneficiary.getPerson().getLastNameAr()
        );
    }

}
