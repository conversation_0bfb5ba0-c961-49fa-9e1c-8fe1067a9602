package ma.almobadara.backend.controller.beneficiary;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.config.HasAccessToModule;
import ma.almobadara.backend.dto.aideComplemenatire.AideComplementaireBeneficiareDto;
import ma.almobadara.backend.dto.beneficiary.*;
import ma.almobadara.backend.dto.exportentities.ExportFileDTO;
import ma.almobadara.backend.dto.getbeneficiary.GetListDTO;
import ma.almobadara.backend.dto.takenInCharge.GetBeneficiariesForTakeInchargeDTO;
import ma.almobadara.backend.enumeration.Functionality;
import ma.almobadara.backend.enumeration.Module;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.model.beneficiary.Beneficiary;
import ma.almobadara.backend.model.beneficiary.BeneficiaryAdHocGroup;
import ma.almobadara.backend.repository.beneficiary.BeneficiaryAdHocGroupRepository;
import ma.almobadara.backend.repository.beneficiary.BeneficiaryRepository;
import ma.almobadara.backend.service.beneficiary.AddedBeneficiaryResponse;
import ma.almobadara.backend.service.beneficiary.BeneficiaryService;
import net.sf.jasperreports.engine.JRException;
import org.checkerframework.checker.nullness.qual.RequiresNonNull;
import org.jetbrains.annotations.NotNull;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.*;

import static ma.almobadara.backend.service.administration.CacheAdUserService.logUserInfo;

//@RefreshScope
@RestController
@AllArgsConstructor
@Slf4j
@RequestMapping("/beneficiaries")
public class BeneficiaryController {

    private final BeneficiaryService beneficiaryService;

    @PostMapping(value = "", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    @PreAuthorize("@accessChecker.hasAnyAccess({'GERER_CANDIDATS:WRITE', 'GERER_BENEFICIAIRES_KAFALAT:WRITE'})")
    @Operation(summary = "Create a Beneficiary", description = "add a new beneficiary", tags = {"Beneficiaries"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = BeneficiaryAddDTO.class))))})
    public ResponseEntity<AddedBeneficiaryResponse> createBeneficiary(@ModelAttribute BeneficiaryAddDTO beneficiaryAddDTO) throws TechnicalException {

        logUserInfo("createBeneficiary", String.valueOf(beneficiaryAddDTO));

        var createdBeneficiary = beneficiaryService.addBeneficiary(beneficiaryAddDTO);

        log.info("End resource createBeneficiary with ID {}", createdBeneficiary.getId());
        return new ResponseEntity<>(createdBeneficiary, new HttpHeaders(), HttpStatus.OK);
    }

    @PostMapping(value = "sanitary", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    @Operation(summary = "addSanitaryToBeneficiary", description = "add sanitary to Beneficiary ", tags = {"Beneficiaries"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = BeneficiaryAddDTO.class))))})
    public ResponseEntity<AddedBeneficiaryResponse> addSanitaryToBeneficiary(@ModelAttribute BeneficiarySanitary beneficiarySanitary) {
        logUserInfo("addSanitaryToBeneficiary", String.valueOf(beneficiarySanitary));

        // Vérifier si la liste diseaseTreatments est null
        if (beneficiarySanitary.getDiseaseTreatments() == null) {
            // Gérer le cas où la liste est null, par exemple en la remplaçant par une liste vide
            beneficiarySanitary.setDiseaseTreatments(new ArrayList<>());
        }

        var createBeneficiaryWithDiseaseTreatment = beneficiaryService.addSanitaryToBeneficiary(beneficiarySanitary);

        log.info("End resource createBeneficiary with ID {}", beneficiarySanitary.getDiseaseTreatments().size());
        return new ResponseEntity<>(createBeneficiaryWithDiseaseTreatment, new HttpHeaders(), HttpStatus.OK);
    }

    @PostMapping(value = "education", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    @Operation(summary = "addSanitaryToBeneficiary", description = "add sanitary to Beneficiary ", tags = {"Beneficiaries"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = BeneficiaryAddDTO.class))))})
    public ResponseEntity<AddedBeneficiaryResponse> addEducationToBeneficiary(@ModelAttribute BeneficiaryEducation beneficiaryEducation) {

        logUserInfo("addSanitaryToBeneficiary", String.valueOf(beneficiaryEducation));

        var createBeneficiaryWithEducation = beneficiaryService.addEducationToBeneficiary(beneficiaryEducation);

        log.info("End resource createBeneficiaryWithEducation with ID {}", beneficiaryEducation.getBeneficiaryId());
        return new ResponseEntity<>(createBeneficiaryWithEducation, new HttpHeaders(), HttpStatus.OK);
    }

    @PostMapping(value = "make-it-not-educated/{id}", headers = "Accept=application/json")
    @Operation(summary = "Make beneficiary not educated", description = "Make beneficiary not educated", tags = {"Beneficiaries"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = BeneficiaryDTO.class))))})
    public ResponseEntity<Object> makeBeneficiaryNotEducated(@PathVariable Long id) {

        logUserInfo("makeBeneficiaryNotEducated", String.valueOf(id));

        HttpStatus status = HttpStatus.NO_CONTENT;
        try {
            beneficiaryService.makeBeneficiaryNotEducated(id);
            log.info("End resource makeBeneficiaryNotEducated By Id {}", id);
        } catch (Exception e) {
            status = HttpStatus.INTERNAL_SERVER_ERROR;
            log.error("Error occurred while making beneficiary not educated with Id {}: {}", id, e.getMessage());
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(e);
        }

        return new ResponseEntity<>(status);
    }



    @PostMapping(value = "/addPieceJointe", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    public ResponseEntity<AddedBeneficiaryResponse> addPieceJointeToBeneficiary(@ModelAttribute BeneficiaryPieceJointe beneficiaryPieceJointe) {
        try {
            AddedBeneficiaryResponse response = beneficiaryService.addPieceJointeToBeneficiary(beneficiaryPieceJointe);
            return new ResponseEntity<>(response, HttpStatus.OK);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }



    @Operation(summary = "Find All Beneficiaries", description = "Find all beneficiaries", tags = {"Beneficiaries"})
    @PreAuthorize("@accessChecker.hasAccess('GERER_BENEFICIAIRES_KAFALAT', 'READ')")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = GetListDTO.class))))})
    @GetMapping(value = "/list", produces = {"application/json"})
    public ResponseEntity<Page<GetListDTO>> getAllBeneficiaries(@RequestParam Optional<Integer> page,
                                                                @RequestParam Optional<String> criteria,
                                                                @RequestParam Optional<String> value1,
                                                                @RequestParam Optional<String> value2,
                                                                @RequestParam(required = false) final String searchByNom,
                                                                @RequestParam(required = false) final String lastNameAr,
                                                                @RequestParam(required = false) final Boolean searchByTypeBeneficiaire,
                                                                @RequestParam(required = false) final String searchByService,
                                                                @RequestParam(required = false) final String searchByStatut,
                                                                @RequestParam(required = false) final String searchByNumTel,
                                                                @RequestParam(required = false) final Boolean isCandidateInitial,
                                                                @RequestParam(required = false) final Boolean isValidateAssistant,
                                                                @RequestParam(required = false) final Boolean isValidateKafalat,
                                                                @RequestParam(required = false) final Boolean isOldBeneficiary,
                                                                @RequestParam(required = false) final Boolean isBeneficiaryRejete,
                                                                @RequestParam(required = false) final Boolean isBeneficiaryArchived,
                                                                @RequestParam(required = false) final Boolean isCandidateRejete,
                                                                @RequestParam(required = false) final Boolean isBeneficiaryWaiting,
                                                                @RequestParam(required = false) final Boolean isBeneficiaryActif,
                                                                @RequestParam(required = false) final Long searchByTagId,
                                                                @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") final Date minDate,
                                                                @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") final Date maxDate) {
        logUserInfo("getAllBeneficiaries");

        HttpStatus status = HttpStatus.OK;
        Page<GetListDTO> beneficiaryDTOS = null;
        try {
            beneficiaryDTOS = beneficiaryService.getAllBeneficiaries(criteria, value1, value2, searchByNom, lastNameAr,
                    searchByTypeBeneficiaire, searchByService, searchByStatut, searchByNumTel, minDate, maxDate, isCandidateInitial,
                    isValidateAssistant, isValidateKafalat, isOldBeneficiary, isBeneficiaryRejete, isBeneficiaryArchived, isCandidateRejete,
                    isBeneficiaryWaiting, isBeneficiaryActif,searchByTagId, page);
        } catch (Exception e) {
            status = HttpStatus.INTERNAL_SERVER_ERROR;
            log.error("Error occurred while fetching all beneficiaries: {}", e.getMessage());
        }

        log.info("End resource getAllBeneficiaries size {}", beneficiaryDTOS != null ? beneficiaryDTOS.getTotalElements() : 0);
        return new ResponseEntity<>(beneficiaryDTOS, new HttpHeaders(), status);
    }

    @Operation(summary = "Find All Candidates", description = "Find all candidates with specific statuses", tags = {"Beneficiaries"})
    @PreAuthorize("@accessChecker.hasAccess('GERER_CANDIDATS', 'READ')")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = GetListDTO.class))))})
    @GetMapping(value = "/candidates", produces = {"application/json"})
    public ResponseEntity<Page<GetListDTO>> getAllCandidates(@RequestParam Optional<Integer> page,
                                                             @RequestParam Optional<String> criteria,
                                                             @RequestParam Optional<String> value1,
                                                             @RequestParam Optional<String> value2,
                                                             @RequestParam(required = false) final String searchByNom,
                                                             @RequestParam(required = false) final String lastNameAr,
                                                             @RequestParam(required = false) final Boolean searchByTypeBeneficiaire,
                                                             @RequestParam(required = false) final String searchByService,
                                                             @RequestParam(required = false) final String searchByStatut,
                                                             @RequestParam(required = false) final String searchByNumTel,
                                                             @RequestParam(required = false) final Boolean isCandidateInitial,
                                                             @RequestParam(required = false) final Boolean isValidateAssistant,
                                                             @RequestParam(required = false) final Boolean isValidateKafalat,
                                                             @RequestParam(required = false) final Boolean isCandidate,
                                                             @RequestParam(required = false) final Boolean isOldBeneficiary,
                                                             @RequestParam(required = false) final Boolean isBeneficiaryRejete,
                                                             @RequestParam(required = false) final Boolean isBeneficiaryArchived,
                                                             @RequestParam(required = false) final Boolean isCandidateRejete,
                                                             @RequestParam(required = false) final Boolean isBeneficiaryWaiting,
                                                             @RequestParam(required = false) final Boolean isBeneficiaryActif,
                                                             @RequestParam(required = false) final Long searchByTagId,
                                                             @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") final Date minDate,
                                                             @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") final Date maxDate) {
        logUserInfo("getAllCandidates");

        HttpStatus status = HttpStatus.OK;
        Page<GetListDTO> candidateDTOS = null;
        try {
            candidateDTOS = beneficiaryService.getAllCandidates(criteria, value1, value2, searchByNom, lastNameAr, searchByTypeBeneficiaire,
                    searchByService, searchByStatut, searchByNumTel, minDate, isCandidateInitial, isValidateAssistant, isValidateKafalat,
                    isCandidate, maxDate, isOldBeneficiary, isBeneficiaryRejete, isBeneficiaryArchived, isCandidateRejete,
                    isBeneficiaryWaiting,searchByTagId, isBeneficiaryActif, page);
        } catch (Exception e) {
            status = HttpStatus.INTERNAL_SERVER_ERROR;
            log.error("Error occurred while fetching all candidates: {}", e.getMessage());
        }

        log.info("End resource getAllCandidates size {}", candidateDTOS != null ? candidateDTOS.getTotalElements() : 0);
        return new ResponseEntity<>(candidateDTOS, new HttpHeaders(), status);
    }


    @Operation(summary = "Find All Old Beneficiaries", description = "Find all Old beneficiaries", tags = {"Beneficiaries"})
    @PreAuthorize("@accessChecker.hasAccess('GERER_BENEFICIAIRES_ARCHIVES', 'READ')")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = GetListDTO.class))))})
    @GetMapping(value = "/old/beneficiary", produces = {"application/json"})
    public ResponseEntity<Page<GetListDTO>> getAllOldBeneficiaries(@RequestParam Optional<Integer> page,
                                                                   @RequestParam Optional<String> criteria,
                                                                   @RequestParam Optional<String> value1,
                                                                   @RequestParam Optional<String> value2,
                                                                   @RequestParam(required = false) final String searchByNom,
                                                                   @RequestParam(required = false) final String lastNameAr,
                                                                   @RequestParam(required = false) final Boolean searchByTypeBeneficiaire,
                                                                   @RequestParam(required = false) final String searchByService,
                                                                   @RequestParam(required = false) final String searchByStatut,
                                                                   @RequestParam(required = false) final String searchByNumTel,
                                                                   @RequestParam(required = false) final Boolean isCandidateInitial,
                                                                   @RequestParam(required = false) final Boolean isValidateAssistant,
                                                                   @RequestParam(required = false) final Boolean isValidateKafalat,
                                                                   @RequestParam(required = false) final Boolean isOldBeneficiary,
                                                                   @RequestParam(required = false) final Boolean isBeneficiaryRejete,
                                                                   @RequestParam(required = false) final Boolean isBeneficiaryArchived,
                                                                   @RequestParam(required = false) final Boolean isCandidateRejete,
                                                                   @RequestParam(required = false) final Boolean isBeneficiaryWaiting,
                                                                   @RequestParam(required = false) final Boolean isBeneficiaryActif,
                                                                   @RequestParam(required = false) final Long searchByTagId,
                                                                   @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") final Date minDate,
                                                                   @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") final Date maxDate) {
        logUserInfo("getAllOldBeneficiaries Archived");

        HttpStatus status = HttpStatus.OK;
        Page<GetListDTO> beneficiaryDTOS = null;
        try {
            beneficiaryDTOS = beneficiaryService.getAllOldBeneficiaries(criteria, value1, value2, searchByNom, lastNameAr,
                    searchByTypeBeneficiaire, searchByService, searchByStatut, searchByNumTel, minDate, maxDate, isCandidateInitial,
                    isValidateAssistant, isValidateKafalat, isOldBeneficiary, isBeneficiaryRejete, isBeneficiaryArchived, isCandidateRejete,
                    isBeneficiaryWaiting, isBeneficiaryActif,searchByTagId, page);
        } catch (Exception e) {
            status = HttpStatus.INTERNAL_SERVER_ERROR;
            log.error("Error occurred while fetching all beneficiaries Archived: {}", e.getMessage());
        }

        log.info("End resource getAllBeneficiariesArchived size {}", beneficiaryDTOS != null ? beneficiaryDTOS.getTotalElements() : 0);
        return new ResponseEntity<>(beneficiaryDTOS, new HttpHeaders(), status);
    }


    @Operation(summary = "Find beneficiary by ID", description = "Returns a single beneficiary", tags = {"Beneficiaries"})
    @HasAccessToModule(modules = {Module.BENEFICIARY}, functionalities = {Functionality.VIEW})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(schema = @Schema(implementation = BeneficiaryDTO.class))),
            @ApiResponse(responseCode = "404", description = "Beneficiary not found")})
    @GetMapping(value = "/{idBeneficiary}", produces = {"application/json"})
    public ResponseEntity<BeneficiaryDTO> getBeneficiaryById(@PathVariable Long idBeneficiary) {

        logUserInfo("deleteBeneficiary", String.valueOf(idBeneficiary));

        HttpStatus status = HttpStatus.OK;
        BeneficiaryDTO beneficiaryDTO = null;
        try {
            beneficiaryDTO = beneficiaryService.getBeneficiaryById(idBeneficiary);
            if (beneficiaryDTO == null) {
                status = HttpStatus.NOT_FOUND;
                log.warn("Beneficiary not found with ID {}", idBeneficiary);
            }
        } catch (TechnicalException e) {
            status = HttpStatus.INTERNAL_SERVER_ERROR;
            log.error("Error occurred while retrieving beneficiary with ID {}: {}", idBeneficiary, e.getMessage());
        }

        log.info("End resource getBeneficiaryById {}", idBeneficiary);

        return new ResponseEntity<>(beneficiaryDTO, new HttpHeaders(), status);
    }

    @DeleteMapping(value = "delete/{id}", headers = "Accept=application/json")
    @PreAuthorize("@accessChecker.hasAccess('GERER_CANDIDATS', 'DELETE')")
    @Operation(summary = "Delete beneficiary", description = "delete beneficiary", tags = {"Beneficiaries"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = BeneficiaryDTO.class))))})
    public ResponseEntity<Void> deleteBeneficiary(@PathVariable Long id, @RequestParam(required = false) String rqReject) {

        logUserInfo("deleteBeneficiary", String.valueOf(id));

        HttpStatus status = HttpStatus.NO_CONTENT;
        try {
            beneficiaryService.deleteBeneficiary(id, rqReject);
            log.info("End resource deleteBeneficiary By Id {}", id);
        } catch (Exception e) {
            status = HttpStatus.INTERNAL_SERVER_ERROR;
            log.error("Error occurred while deleting beneficiary with Id {}: {}", id, e.getMessage());
        }

        return new ResponseEntity<>(status);
    }

    @PutMapping(value = "/unArchive-beneficiary/{id}", headers = "Accept=application/json")
    @PreAuthorize("@accessChecker.hasAccess('GERER_BENEFICIAIRES_ARCHIVES', 'WRITE')")
    @Operation(summary = "unArchive beneficiary", description = "unArchive beneficiary", tags = {"Beneficiaries"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = BeneficiaryDTO.class))))})
    public ResponseEntity<Object> unArchiveBeneficiary(@PathVariable Long id) {

        logUserInfo("unArchiveBeneficiary", String.valueOf(id));

        HttpStatus status = HttpStatus.NO_CONTENT;
        try {
            beneficiaryService.unArchiveBeneficiary(id);
            log.info("End resource unArchive Beneficiary By Id {}", id);
        } catch (Exception e) {
            status = HttpStatus.INTERNAL_SERVER_ERROR;
            log.error("Error occurred while unArchiving beneficiary with Id {}: {}", id, e.getMessage());
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(e);
        }

        return new ResponseEntity<>(status);
    }

    @PostMapping(value = "/validate-beneficiary/{id}", headers = "Accept=application/json")
    @PreAuthorize("@accessChecker.hasAccess('GERER_CANDIDATS', 'VALIDATE')")
    @Operation(summary = "Validate beneficiary", description = "Validate beneficiary", tags = {"Beneficiaries"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = BeneficiaryDTO.class))))})
    public ResponseEntity<Object> beneficiaryValidation(@PathVariable Long id) {

        logUserInfo("beneficiaryValidation", String.valueOf(id));

        HttpStatus status = HttpStatus.NO_CONTENT;
        try {
            beneficiaryService.beneficiaryValidation(id);
            log.info("End resource beneficiaryValidation By Id {}", id);
        } catch (Exception e) {
            status = HttpStatus.INTERNAL_SERVER_ERROR;
            log.error("Error occurred while validating beneficiary with Id {}: {}", id, e.getMessage());
            Map<String, String> error = new HashMap<>();
            error.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(e);
        }

        return new ResponseEntity<>(status);
    }

    @PostMapping(value = "/complete-candidate/{id}", headers = "Accept=application/json")
    @PreAuthorize("@accessChecker.hasAccess('GERER_CANDIDATS', 'COMPLETE')")
    @Operation(summary = "Complete candidate", description = "Complete candidate", tags = {"Beneficiaries"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = BeneficiaryDTO.class)))),
            @ApiResponse(responseCode = "204", description = "No content found"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<Long> candidateToComplete(
            @PathVariable Long id,
            @RequestParam(required = false) Long idStatutTarget,
            @RequestParam String rqComplete) {

        logUserInfo("candidateToComplete", String.valueOf(id));

        try {
            Long newStatutId = beneficiaryService.candidateToComplete(id, idStatutTarget, rqComplete);
            log.info("End resource candidateToComplete By Id {}", id);
            return ResponseEntity.ok(newStatutId);
        } catch (Exception e) {
            log.error("Error occurred while completing candidate with Id {}: {}", id, e.getMessage());
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping(value = "/update-candidate/{id}", headers = "Accept=application/json")
    @PreAuthorize("@accessChecker.hasAccess('GERER_CANDIDATS', 'VALIDATE')")
    @Operation(summary = "Update beneficiary", description = "Update beneficiary", tags = {"Beneficiaries"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = BeneficiaryDTO.class)))),
            @ApiResponse(responseCode = "204", description = "No content found"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<Long> validateUpdateCandidate(
            @PathVariable Long id) {

        logUserInfo("validateUpdateCandidate", String.valueOf(id));

        try {
            Long newStatutId = beneficiaryService.validateUpdateCandidate(id);
            log.info("End resource validateUpdateCandidate By Id {}", id);
            return ResponseEntity.ok(newStatutId);
        } catch (Exception e) {
            log.error("Error occurred while updating beneficiary with Id {}: {}", id, e.getMessage());
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping(value = "/reject-beneficiary/{id}", headers = "Accept=application/json")
    @HasAccessToModule(modules = {Module.BENEFICIARY}, functionalities = {Functionality.UPDATE})
    @Operation(summary = "Reject beneficiary", description = "Reject beneficiary", tags = {"Beneficiaries"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = BeneficiaryDTO.class)))),
            @ApiResponse(responseCode = "204", description = "No content found"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<Long> rejectBeneficiary(
            @PathVariable Long id,
            @RequestParam String rqReject) {

        logUserInfo("rejectBeneficiary", String.valueOf(id));

        try {
            // Call the service method to handle the rejection
            Long newStatutId = beneficiaryService.rejectBeneficiary(id, rqReject);
            log.info("End resource rejectBeneficiary By Id {}", id);
            return ResponseEntity.ok(newStatutId);
        } catch (Exception e) {
            log.error("Error occurred while rejecting beneficiary with Id {}: {}", id, e.getMessage());
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }


    @GetMapping("/loadAideComplementaireForBeneficiare/{id}")
    public ResponseEntity<List<AideComplementaireBeneficiareDto>> getBeneficiariesAideComplementaire(
            @PathVariable Long id
    ) {
        logUserInfo("getBeneficiariesNotInKafalat");

        try {
            List<AideComplementaireBeneficiareDto> beneficiaryDTOS = beneficiaryService.getAideComplementaireForBeneficiare(id);

            return ResponseEntity.ok(beneficiaryDTOS);
        } catch (Exception e) {

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    @Operation(
            summary = "Find Beneficiaries that don't belong to Kafalat service",
            description = "Retrieve a paginated list of beneficiaries who are not part of the Kafalat service, with optional filters.",
            tags = {"SIG"}
    )
    @PreAuthorize("@accessChecker.hasAccess('GERER_BENEFICIAIRES_KAFALAT', 'READ')")
    @ApiResponses(value = {
            @ApiResponse(
                    responseCode = "200",
                    description = "Successful operation",
                    content = @Content(array = @ArraySchema(schema = @Schema(implementation = GetBeneficiariesForTakeInchargeDTO.class)))
            ),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @GetMapping(value = "/loadBeneficiariesForTakeInCharge", produces = {"application/json"})
    public ResponseEntity<Page<GetBeneficiariesForTakeInchargeDTO>> getBeneficiariesNotInKafalat(
            @RequestParam Optional<Integer> page,
            @RequestParam(required = false) Boolean independent,
            @RequestParam(required = false) String lastName,
            @RequestParam(required = false) Long status,
            @RequestParam(required = false) Long category
    ) {
        logUserInfo("getBeneficiariesNotInKafalat");

        try {
            Page<GetBeneficiariesForTakeInchargeDTO> beneficiaryDTOS = beneficiaryService.loadBeneficiariesForTakeInCharge(
                    page,
                    independent,
                    lastName,
                    status,
                    category
            );
            log.info("End resource Get Beneficiaries Not In Kafalat, OK");
            return ResponseEntity.ok(beneficiaryDTOS);
        } catch (Exception e) {
            log.error("End resource Get Beneficiaries Not In Kafalat, KO - {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }


    // Path: /beneficiaries/csv
    @GetMapping(value = "/csv", produces = {"application/json"})
    public ResponseEntity<ExportFileDTO> exportAllToCSV(@RequestParam(required = false) final String searchByNom,
                                                        @RequestParam(required = false) final String lastNameAr,
                                                        @RequestParam(required = false) final Boolean searchByTypeBeneficiaire,
                                                        @RequestParam(required = false) final String searchByService,
                                                        @RequestParam(required = false) final String searchByStatut,
                                                        @RequestParam(required = false) final String searchByNumTel,
                                                        @RequestParam(required = false) final Boolean isCandidateInitial,
                                                        @RequestParam(required = false) final Boolean isValidateAssistant,
                                                        @RequestParam(required = false) final Boolean isValidateKafalat,
                                                        @RequestParam(required = false) final Boolean isCandidate,
                                                        @RequestParam(required = false) final Boolean isOldBeneficiary,
                                                        @RequestParam(required = false) final Boolean isBeneficiaryRejete,
                                                        @RequestParam(required = false) final Boolean isBeneficiaryArchived,
                                                        @RequestParam(required = false) final Boolean isCandidateRejete,
                                                        @RequestParam(required = false) final Boolean isBeneficiaryWaiting,
                                                        @RequestParam(required = false) final Boolean isBeneficiaryActif,
                                                        @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") final Date minDate,
                                                        @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") final Date maxDate) {
        logUserInfo("exportAllToCSV");
        ExportFileDTO exportFileDTO;
        HttpStatus status;
        try {
            exportFileDTO = beneficiaryService.exportFileWithName(searchByNom, lastNameAr, searchByTypeBeneficiaire, searchByService, searchByStatut, searchByNumTel, minDate, maxDate,
                    isCandidateInitial, isValidateAssistant, isValidateKafalat, isCandidate, isOldBeneficiary,
                    isBeneficiaryRejete, isBeneficiaryArchived, isCandidateRejete, isBeneficiaryWaiting, isBeneficiaryActif);
            status = HttpStatus.OK;
            log.info("End resource exportAllToCSV, OK");
        } catch (Exception e) {
            exportFileDTO = null;
            status = HttpStatus.INTERNAL_SERVER_ERROR;
            log.error("End resource exportAllToCSV, KO - {}", e.getMessage());
            exportFileDTO = new ExportFileDTO();
        }
        return new ResponseEntity<>(exportFileDTO, new HttpHeaders(), status);
    }

    @PostMapping(value = "/addAdHocPerson", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    @PreAuthorize("@accessChecker.hasAccess('GERER_BENEFICIAIRES_AD_HOC', 'WRITE')")
    @Operation(summary = "Create an Ad-Hoc Beneficiary", description = "Add a new Ad-Hoc beneficiary", tags = {"Beneficiaries"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "Beneficiary successfully created", content = @Content(array = @ArraySchema(schema = @Schema(implementation = BeneficiaryAdHocPersonneDto.class)))),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "500", description = "Server error")})
    public ResponseEntity<AddedBeneficiaryResponse> ajouterBeneficiaryAdHocPerson(@ModelAttribute BeneficiaryAdHocPersonneDto dto) throws TechnicalException {

        logUserInfo("ajouterBeneficiaryAdHocPerson", String.valueOf(dto));

        var createdBeneficiary = beneficiaryService.ajouterBeneficiaryAdHocPerson(dto);

        log.info("End resource ajouterBeneficiaryAdHocPerson with ID {}", createdBeneficiary.getId());

        return new ResponseEntity<>(createdBeneficiary, new HttpHeaders(), HttpStatus.CREATED);
    }


    @PutMapping(value = "/updateAdHocPerson", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    @PreAuthorize("@accessChecker.hasAccess('GERER_BENEFICIAIRES_AD_HOC', 'WRITE')")
    @Operation(summary = "Update an Ad-Hoc Beneficiary", description = "Update an existing Ad-Hoc beneficiary", tags = {"Beneficiaries"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Beneficiary successfully updated", content = @Content(array = @ArraySchema(schema = @Schema(implementation = BeneficiaryAdHocPersonneDto.class)))),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "404", description = "Beneficiary not found"),
            @ApiResponse(responseCode = "500", description = "Server error")})
    public ResponseEntity<AddedBeneficiaryResponse> updateBeneficiaryAdHocPerson(@ModelAttribute BeneficiaryAdHocPersonneDto dto) throws TechnicalException {

        logUserInfo("updateBeneficiaryAdHocPerson", String.valueOf(dto));

        var updatedBeneficiary = beneficiaryService.updateBeneficiaryAdHocPerson(dto);

        log.info("End resource updateBeneficiaryAdHocPerson with ID {}", updatedBeneficiary.getId());

        return new ResponseEntity<>(updatedBeneficiary, new HttpHeaders(), HttpStatus.OK);
    }


    @Operation(summary = "Find All Beneficiaries", description = "Find all beneficiaries", tags = {"Beneficiaries"})
    @PreAuthorize("@accessChecker.hasAccess('GERER_BENEFICIAIRES_AD_HOC', 'READ')")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = GetListDTO.class))))})
        @GetMapping(value = "/all-beneficiary-ad-hoc", produces = {"application/json"})
    public ResponseEntity<Page<GetListDTO>> getAllBeneficiariesAdHocPersonne(@RequestParam Optional<Integer> page,
                                                                @RequestParam Optional<String> criteria,
                                                                @RequestParam Optional<String> value1,
                                                                @RequestParam Optional<String> value2,
                                                                @RequestParam(required = false) final String searchByNom,
                                                                @RequestParam(required = false) final String lastNameAr,
                                                                @RequestParam(required = false) final Boolean searchByTypeBeneficiaire,
                                                                @RequestParam(required = false) final String searchByStatut,
                                                                @RequestParam(required = false) final String searchByNumTel,
                                                                @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") final Date minDate,
                                                                @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") final Date maxDate) {
        logUserInfo("getAllBeneficiaries ad hoc");

        HttpStatus status = HttpStatus.OK;
        Page<GetListDTO> beneficiaryDTOS = null;
        try {
            beneficiaryDTOS = beneficiaryService.getAllBeneficiariesAdHocPersonne(criteria, value1, value2, searchByNom, lastNameAr,
                    searchByTypeBeneficiaire, searchByStatut, searchByNumTel, minDate, maxDate, page);
        } catch (Exception e) {
            status = HttpStatus.INTERNAL_SERVER_ERROR;
            log.error("Error occurred while fetching all beneficiaries ad-hoc: {}", e.getMessage());
        }

        log.info("End resource getAllBeneficiariesAdHoc size {}", beneficiaryDTOS != null ? beneficiaryDTOS.getTotalElements() : 0);
        return new ResponseEntity<>(beneficiaryDTOS, new HttpHeaders(), status);
    }



    @GetMapping("/generate")
    public ResponseEntity<byte[]> generateReport(@RequestParam Long beneficiaryId,
                                                 @RequestParam String reportType,
                                                 @RequestParam String language) throws JRException, IOException {
        // Générer le rapport et obtenir le fichier PDF
        File pdfFile = beneficiaryService.generateReport(beneficiaryId, reportType, language);

        // Lire le fichier PDF en bytes
        byte[] fileContent = Files.readAllBytes(pdfFile.toPath());

        // Retourner le fichier PDF dans la réponse avec un en-tête approprié
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=report.pdf")
                .contentType(MediaType.APPLICATION_PDF)
                .body(fileContent);
    }

    /**
     * Save coordinates for a beneficiary
     * 
     * @param id The ID of the beneficiary
     * @param coordinatesDTO The coordinates DTO containing the coordinates string
     * @return The updated beneficiary
     * @throws TechnicalException if the beneficiary is not found
     */
    @PostMapping("/{id}/coordinates")
    @Operation(summary = "Save Beneficiary Coordinates", description = "Save coordinates for a beneficiary", tags = {"Beneficiaries"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Coordinates successfully saved"),
            @ApiResponse(responseCode = "404", description = "Beneficiary not found"),
            @ApiResponse(responseCode = "500", description = "Server error")})
    public ResponseEntity<String> saveCoordinates(@PathVariable Long id,
                                                     @NotNull @RequestBody BeneficiaryCoordinatesDTO coordinatesDTO) throws TechnicalException {
        logUserInfo("saveCoordinates", "id: " + id);

        String updatedBeneficiary = beneficiaryService.saveCoordinates(id, coordinatesDTO);

        log.info("End resource saveCoordinates for beneficiary ID {}", id);

        return new ResponseEntity<>(updatedBeneficiary, HttpStatus.OK);
    }

}
