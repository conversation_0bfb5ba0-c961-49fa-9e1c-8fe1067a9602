package ma.almobadara.backend.repository.takenInCharge;

import ma.almobadara.backend.model.takenInCharge.TakenInChargeOperationHistorique;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

public interface TakenInChargeOperationHistoriqueRepository extends
        JpaRepository<TakenInChargeOperationHistorique, Long> {
    //findByOperationId
    List<TakenInChargeOperationHistorique> findByOperationId(Long operationId);

}
