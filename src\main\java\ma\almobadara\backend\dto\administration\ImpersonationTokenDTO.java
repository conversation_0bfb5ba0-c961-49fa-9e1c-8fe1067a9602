package ma.almobadara.backend.dto.administration;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ImpersonationTokenDTO {
    private String token;
    private Long originUserId;
    private Long impersonatedUserId;
    private String originUserName;
    // No expiration time
}
