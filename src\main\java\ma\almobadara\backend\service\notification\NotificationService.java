package ma.almobadara.backend.service.notification;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.model.administration.Assistant;
import ma.almobadara.backend.repository.administration.AssistantRepository;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * Service for sending push notifications to assistants
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class NotificationService {

    private final AssistantRepository assistantRepository;
    private final RestTemplate restTemplate;
    
    private static final String EXPO_PUSH_ENDPOINT = "https://exp.host/--/api/v2/push/send";

    /**
     * Sends a push notification to an assistant
     * @param assistantId The ID of the assistant
     * @param title The title of the notification
     * @param body The body of the notification
     * @return True if the notification was sent successfully, false otherwise
     * @throws TechnicalException If the assistant is not found or has no device token
     */
    public boolean sendNotification(Long assistantId, String title, String body) throws TechnicalException {
        log.debug("Start service send notification to assistant with id {}", assistantId);
        
        Assistant assistant = assistantRepository.findById(assistantId)
                .orElseThrow(() -> new TechnicalException("Assistant not found with ID: " + assistantId));
        
        if (assistant.getDevice_token() == null || assistant.getDevice_token().isEmpty()) {
            throw new TechnicalException("Assistant has no device token");
        }
        
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("to", assistant.getDevice_token());
            requestBody.put("title", title);
            requestBody.put("body", body);
            
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);
            
            ResponseEntity<Map> response = restTemplate.postForEntity(EXPO_PUSH_ENDPOINT, request, Map.class);
            
            log.debug("Notification sent to assistant with id {}, response: {}", assistantId, response);
            
            return response.getStatusCode().is2xxSuccessful();
        } catch (Exception e) {
            log.error("Error sending notification to assistant with id {}: {}", assistantId, e.getMessage());
            throw new TechnicalException("Error sending notification: " + e.getMessage());
        }
    }
    
    /**
     * Sends a push notification to an assistant by email
     * @param email The email of the assistant
     * @param title The title of the notification
     * @param body The body of the notification
     * @return True if the notification was sent successfully, false otherwise
     * @throws TechnicalException If the assistant is not found or has no device token
     */
    public boolean sendNotificationByEmail(String email, String title, String body) throws TechnicalException {
        log.debug("Start service send notification to assistant with email {}", email);
        
        Assistant assistant = assistantRepository.findByEmail(email)
                .orElseThrow(() -> new TechnicalException("Assistant not found with email: " + email));
        
        return sendNotification(assistant.getId(), title, body);
    }
}