package ma.almobadara.backend.dto.family;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@Builder
public class FamilyMemberAudit {
    private Long id;

    private Long familyId;

    private String code;

    private boolean tutor;

    private Date tutorStartDate;

    private Date tutorEndDate;

    private Long familyRelationshipId;

    private Long personId;

    private String firstName;

    private String lastName;

    private String firstNameAr;

    private String lastNameAr;

    private String sex;

    private String email;

    private String phoneNumber;

    private String address;

    private String addressAr;

    private Date birthDate;

    private boolean deceased;

    private Date deathDate;

    private String deathReason;

    private String identityCode;

    private String schoolLevel;

    private String city;

    private String typeIdentity;

    private String profession;

    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private MultipartFile picture;

}
