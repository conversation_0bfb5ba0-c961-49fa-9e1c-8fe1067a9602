package ma.almobadara.backend.dto.dashboard;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Map;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DonationStatsDTO {
    private Map<String, Double> donationByDonationType;
    private Map<String, Double> donationByChannel;
    private Map<String, Double> donationByService;
    private Map<String, Double> donationByMonth;
}
