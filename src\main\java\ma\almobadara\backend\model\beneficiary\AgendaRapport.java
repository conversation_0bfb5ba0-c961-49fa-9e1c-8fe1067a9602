package ma.almobadara.backend.model.beneficiary;

import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToOne;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Entity
@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class AgendaRapport extends BaseEntity{

    private String status;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date dateRapport;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date dateValidate;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date datePlanned;

    @OneToOne
    @JoinColumn(name = "beneficiary_id")
    private Beneficiary beneficiary;

    @OneToOne
    @JoinColumn(name = "rapport_id")
    private Rapport rapport;

    private String year;
    private Long numberRapport;

}
