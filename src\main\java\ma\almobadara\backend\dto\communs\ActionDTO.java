package ma.almobadara.backend.dto.communs;

import jakarta.validation.constraints.NotNull;
import lombok.*;
import ma.almobadara.backend.dto.administration.CacheAdUserDTO;
import ma.almobadara.backend.dto.referentiel.ActionStatusDTO;

import java.util.Date;
import java.util.List;

import static ma.almobadara.backend.util.constants.GlobalConstants.USER_ID_AFFECTED_BY_NOT_FOUND;
import static ma.almobadara.backend.util.constants.GlobalConstants.USER_ID_CREATED_BY_NOT_FOUND;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class ActionDTO {

    private Long id;
    private Date dateEntry;
    private Date deadline;
    private Date dateRealize;

    @NotNull(message = USER_ID_CREATED_BY_NOT_FOUND)
    private CacheAdUserDTO createdBy;
    private List<CommentActionDto> comments;
    private String subject;

    @NotNull(message = USER_ID_AFFECTED_BY_NOT_FOUND)
    private CacheAdUserDTO affectedTo;
    private ActionStatusDTO actionStatus;

}
