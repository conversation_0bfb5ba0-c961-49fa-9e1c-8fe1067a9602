package ma.almobadara.backend.util.page;

public class PageableDto {
    private int pageNumber;
    private int pageSize;
    private long offset;
    private boolean paged;
    private boolean unpaged;
    private SortDto sort;

    public PageableDto(int pageNumber, int pageSize, SortDto sort) {
        this.pageNumber = pageNumber;
        this.pageSize = pageSize;
        this.offset = (long) pageNumber * pageSize;
        this.paged = true;
        this.unpaged = false;
        this.sort = sort;
    }

    public int getPageNumber() { return pageNumber; }
    public void setPageNumber(int pageNumber) { this.pageNumber = pageNumber; }

    public int getPageSize() { return pageSize; }
    public void setPageSize(int pageSize) { this.pageSize = pageSize; }

    public long getOffset() { return offset; }
    public void setOffset(long offset) { this.offset = offset; }

    public boolean isPaged() { return paged; }
    public void setPaged(boolean paged) { this.paged = paged; }

    public boolean isUnpaged() { return unpaged; }
    public void setUnpaged(boolean unpaged) { this.unpaged = unpaged; }

    public SortDto getSort() { return sort; }
    public void setSort(SortDto sort) { this.sort = sort; }
}

