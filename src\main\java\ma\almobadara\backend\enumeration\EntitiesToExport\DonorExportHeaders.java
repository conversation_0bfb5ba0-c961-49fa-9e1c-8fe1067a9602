package ma.almobadara.backend.enumeration.EntitiesToExport;

import lombok.Getter;

@Getter
public enum DonorExportHeaders {
    CODE("Code"),
    DATE_CREATION("Date de création"),
    DONOR_TYPE("Type de Donateur"),
    FIRST_NAME("Prénom"),
    LAST_NAME("Nom"),
    COMPANY("Entreprise"),
    PHONE_NUMBER("Numéro de Téléphone"),
    EMAIL("Email"),
    SEX("Sexe"),
    ADDRESS("Adresse"),
    CITY("Ville"),
    REGION("Région"),
    COUNTRY("Pays"),
    STATUS("Statut");

    private final String headerName;

    DonorExportHeaders(String headerName) {
        this.headerName = headerName;
    }

}

