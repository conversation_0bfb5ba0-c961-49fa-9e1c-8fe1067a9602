package ma.almobadara.backend.dto.referentiel;

import lombok.*;
import org.springframework.hateoas.RepresentationModel;

import java.io.Serializable;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
@ToString
public class HonorDTO extends RepresentationModel<HonorDTO> implements Serializable {

	private static final long serialVersionUID = -2405540731083821913L;

	private Long id;

	private String code;
	private String name;
	private String nameAr;
	private String nameEn;

}
