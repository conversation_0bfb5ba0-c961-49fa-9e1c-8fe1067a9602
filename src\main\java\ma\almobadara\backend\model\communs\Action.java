package ma.almobadara.backend.model.communs;


import jakarta.persistence.*;
import lombok.*;
import ma.almobadara.backend.model.administration.CacheAdUser;
import org.springframework.format.annotation.DateTimeFormat;

import java.text.SimpleDateFormat;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static ma.almobadara.backend.util.strings.HandleSpecialChars.escapeSpecialChars;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Action {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date dateEntry;
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date deadline;
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date dateRealize;
    @OneToMany(mappedBy = "action", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<CommentAction> comments;
    private String subject;
    @ManyToOne
    @JoinColumn(name = "created_by_id")
    private CacheAdUser createdBy;
    @ManyToOne
    @JoinColumn(name = "affected_to_id")
    private CacheAdUser affectedTo;
    private Long status;

//    public void addComments(List<CommentAction> comments) {
//        this.comments = comments;
//        if (comments != null) {
//            for (CommentAction comment : comments) {
//                comment.setAction(this);
//            }
//        }
//    }

    public String getAudit(StringBuilder columnToAppend, String status) {
        SimpleDateFormat formatter = new SimpleDateFormat("dd/MM/yyyy");
        String dateEntryFormatted = formatter.format(dateEntry);
        String dateRealizeFormatted = (dateRealize != null) ? formatter.format(dateRealize) : "-";
        String dateLimiteFormatted = formatter.format(deadline);
        CommentAction maxComment = getComments().stream()
                .max(Comparator.comparingLong(CommentAction::getId)).orElse(null);
        return "{"
                + "\"Objet\": \"" + escapeSpecialChars(subject) + "\","
                + "\"Statut\": \"" + escapeSpecialChars(status) + "\","
                + "\"Date de création\": \"" + escapeSpecialChars(dateEntryFormatted) + "\","
                + "\"Date de réalisation\": \"" + escapeSpecialChars(dateRealizeFormatted) + "\","
                + "\"Date limite\": \"" + escapeSpecialChars(dateLimiteFormatted) + "\","
                + "\"Commentaire\": \"" + escapeSpecialChars(maxComment!=null ? maxComment.getContent():"-") + "\","
                + "\"Affecté à\": \""
                + (affectedTo != null ? escapeSpecialChars(affectedTo.getFirstName() + " " + affectedTo.getLastName()) : "-") + "\","
                + "\"Initié par\": \""
                + (createdBy != null ? escapeSpecialChars(createdBy.getFirstName() + " " + createdBy.getLastName()) : "-") + "\","
                + columnToAppend.toString()
                + "}";
    }


}
