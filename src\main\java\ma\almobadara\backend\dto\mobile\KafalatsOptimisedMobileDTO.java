package ma.almobadara.backend.dto.mobile;

import lombok.*;
import ma.almobadara.backend.dto.administration.ZoneDTOLight;
import ma.almobadara.backend.dto.beneficiary.SmallZoneDTO;
import ma.almobadara.backend.dto.service.ServicesDTO;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class KafalatsOptimisedMobileDTO {
    private Long id;
    private Long takenInChargeId;
    private String beneficiaryName;
    private String serviceName;
    private Date lastExecutionDate;
    private String serviceType;
    private String status;
    private Double sumReserved;
    private Double sumExecuted;
    private BeneficiaryMobileDto beneficiaryMobileDto;
    private Double sumPlanned;
    private Double donorBalance;
    private Boolean keepAnonymous;
    private List<OperationSummaryDTO> operations;

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class OperationSummaryDTO {
        private Long id;
        private String code;
        private Double amount;
        private String status;
        private Date executionDate;
        private Date plannedDate;
        private String type;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class BeneficiaryMobileDto {
        private Long id;
        private String name;
        private String type;
        private String email;
        private String phone;
        private Date birthDate;
        private String sex;
        private MultipartFile picture;
        private String pictureUrl;
        private String pictureBase64;
        private String profession;
        private SmallZoneDTO zoneDTOLight;
    }
}