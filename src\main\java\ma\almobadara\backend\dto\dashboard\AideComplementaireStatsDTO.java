package ma.almobadara.backend.dto.dashboard;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AideComplementaireStatsDTO {
    private Map<String, Long> aideByStatus;
    private Map<String, Long> aideAmountByService;
    private Map<String, Long> aideByDate;
}