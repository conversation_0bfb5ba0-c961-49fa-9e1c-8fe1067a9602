CREATE TABLE services (
                         id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
                         name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
                         code VA<PERSON>HAR(255) NOT NULL,
                         costs BIGINT,
                         priority BOOLEAN,
                         amount_per_beneficiary BIGINT,
                         statut_is_actif BOOLEAN,
                         service_category_id BIGINT,
                         service_category_type_id BIGINT,
                         created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
                         modified_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);


CREATE OR REPLACE FUNCTION update_modified_at_column()
RETURNS TRIGGER AS $$
BEGIN
   NEW.modified_at = CURRENT_TIMESTAMP;
RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER set_modified_at
    BEFORE UPDATE ON services
    FOR EACH ROW
    EXECUTE FUNCTION update_modified_at_column();
