package ma.almobadara.backend.dto.communs;

import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.util.Date;
import java.util.List;

import static ma.almobadara.backend.util.constants.GlobalConstants.USER_ID_AFFECTED_BY_NOT_FOUND;
import static ma.almobadara.backend.util.constants.GlobalConstants.USER_ID_CREATED_BY_NOT_FOUND;


@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class ActionAuditDTO {

    private Date DateDeCreation;
    private Date DateLimite;
    private Date DateDeRealisation;
    private String Objet;
    @NotNull(message = USER_ID_CREATED_BY_NOT_FOUND)
    private String InitierPar;
    @NotNull(message = USER_ID_AFFECTED_BY_NOT_FOUND)
    private String AffecterA;
    private String Statut;

}
