package ma.almobadara.backend.dto.mobile;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import org.springframework.web.multipart.MultipartFile;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class BeneficiaryMobileDTO {
    private Long id;
    private Boolean independent;
    private String fullName;       // Combined first and last name
    private String phoneNumber;
    private String email;
    private String zoneName;       // Name of the zone
    private String pictureUrl;
    private String pictureBase64;  // Base64 encoded image

    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private MultipartFile picture;
    private String coordinates;
}
