package ma.almobadara.backend.controller.donor;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.EntityNotFoundException;
import ma.almobadara.backend.config.Messages;
import ma.almobadara.backend.config.TestSecurityConfig;
import ma.almobadara.backend.dto.donor.*;
import ma.almobadara.backend.dto.exportentities.ExportFileDTO;
import ma.almobadara.backend.service.donor.DonorService;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@WebMvcTest(DonorController.class)
@ExtendWith(MockitoExtension.class)
@Import({TestSecurityConfig.class, Messages.class})
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
class DonorControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private DonorService donorService;

    ObjectMapper objectMapper = new ObjectMapper();

    // DELETE

    @Test
    @Order(1)
    void donorController_deleteDonor_Success() throws Exception {
        // Arrange
        Long donorId = 1L;
        doNothing().when(donorService).deleteDonor(donorId);

        mockMvc.perform(delete("/donors/delete/1")
                        .header("Accept", "application/json"))
                .andExpect(status().isOk())
                .andExpect(content().string("Donor has been successfully deleted."));

        verify(donorService, times(1)).deleteDonor(donorId);
    }

    @Test
    @Order(2)
    void donorController_deleteDonor_IllegalStateException() throws Exception {
        // Arrange
        Long donorId = 1L;
        doThrow(new IllegalStateException("Cannot delete donor")).when(donorService).deleteDonor(donorId);

        mockMvc.perform(delete("/donors/delete/1")
                        .header("Accept", "application/json"))
                .andExpect(status().isOk())
                .andExpect(content().string("Cannot delete donor"));

        verify(donorService, times(1)).deleteDonor(donorId);
    }

    @Test
    @Order(3)
    void donorController_deleteDonor_IllegalArgumentException() throws Exception {
        Long donorId = 1L;
        // Arrange
        doThrow(new IllegalArgumentException("Donor not found")).when(donorService).deleteDonor(donorId);

        // Act & Assert
        mockMvc.perform(delete("/donors/delete/1")
                        .header("Accept", "application/json"))
                .andExpect(status().isNotFound())
                .andExpect(content().string("Donor not found"));

        verify(donorService, times(1)).deleteDonor(donorId);
    }

    @Test
    @Order(4)
    void donorController_deleteDonor_GenericException() throws Exception {
        Long donorId = 1L;
        doThrow(new RuntimeException("Unexpected error")).when(donorService).deleteDonor(donorId);

        mockMvc.perform(delete("/donors/delete/1")
                        .header("Accept", "application/json"))
                .andExpect(status().isInternalServerError())
                .andExpect(content().string("An error occurred."));

        verify(donorService, times(1)).deleteDonor(donorId);
    }

    // Get By Id

    @Test
    @Order(5)
    void donorController_getDonorByID_NotFound() throws Exception {
        Long donorId = 1L;


        when(donorService.getDonorById(donorId)).thenThrow(new RuntimeException("Donor not found"));
        mockMvc.perform(get("/donors/1")
        .header("Accept", "application/json"))
                .andExpect(status().isNotFound()) ;

        verify(donorService, times(1)).getDonorById(donorId);

    }

    @Test
    @Order(6)
    void donorController_getDonorByID_Success() throws Exception {
        Long donorId = 1L;
        DonorDTO donorDTO = new DonorDTO();
        donorDTO.setId(donorId);
        donorDTO.setAddress("just for testing");
        String expectedJson = objectMapper.writeValueAsString(donorDTO);


        when(donorService.getDonorById(donorId)).thenReturn(donorDTO);

        mockMvc.perform(get("/donors/1")
                        .header("Accept", "application/json")).andExpect(status().isOk())
                .andExpect(content().json(expectedJson));

        verify(donorService, times(1)).getDonorById(donorId);

    }

    // Test Create Physical donor

    @Test
    @Order(7)
    void donorController_createDonorPhysical_Success() throws Exception {
        DonorPhysicalDTO donorPhysicalDTO = new DonorPhysicalDTO();
        donorPhysicalDTO.setFirstName("Just");
        donorPhysicalDTO.setLastName("just for testing");
        donorPhysicalDTO.setAddress("address");

        DonorDTO donorDTO = new DonorDTO();
        donorDTO.setId(1L);
        donorDTO.setAddress("just for testing");

        when(donorService.addDonorPhysique(any(DonorPhysicalDTO.class))).thenReturn(donorDTO);

        mockMvc.perform(post("/donors/physical/create")
                .param("firstName", donorPhysicalDTO.getFirstName())
                .param("lastName", donorPhysicalDTO.getLastName())
                .param("address", donorPhysicalDTO.getAddress())
                .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(donorDTO)));
        verify(donorService,times(1)).addDonorPhysique(any(DonorPhysicalDTO.class));
    }

    @Test
    @Order(8)
    void donorController_createDonorPhysical_InternalServerException() throws Exception {

        when(donorService.addDonorPhysique(any(DonorPhysicalDTO.class)))
                .thenThrow(new RuntimeException("Unexpected error"));


        mockMvc.perform(multipart("/donors/physical/create")
                        .param("firstName", "Hamza")
                        .param("lastName", "Nachid")
                        .param("email", "<EMAIL>")
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isInternalServerError());
        verify(donorService, times(1)).addDonorPhysique(any(DonorPhysicalDTO.class));
    }

    // Test Create Anonyme donor

    @Test
    @Order(9)
    void donorController_createDonorAnonyme_Success() throws Exception {
        DonorAnonymeDTO donorAnonymeDTO = new DonorAnonymeDTO();
        donorAnonymeDTO.setName("Just a name");
        donorAnonymeDTO.setDescription("Just a description");
        donorAnonymeDTO.setAddress("just for testing");

        DonorDTO donorDTO = new DonorDTO();
        donorDTO.setId(1L);
        donorDTO.setAddress("just for testing");

        when(donorService.addDonorAnonyme(any(DonorAnonymeDTO.class))).thenReturn(donorDTO);

        mockMvc.perform(post("/donors/anonymous/create")
                        .contentType(MediaType.APPLICATION_JSON_VALUE)
                        .content(objectMapper.writeValueAsString(donorAnonymeDTO)))
                .andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(donorDTO)));
        verify(donorService,times(1)).addDonorAnonyme(any(DonorAnonymeDTO.class));
    }

    @Test
    @Order(10)
    void donorController_createDonorAnonyme_InternalServerException() throws Exception {
        DonorAnonymeDTO donorAnonymeDTO = new DonorAnonymeDTO();
        when(donorService.addDonorAnonyme(any(DonorAnonymeDTO.class)))
                .thenThrow(new RuntimeException("Unexpected error"));


        mockMvc.perform(multipart("/donors/anonymous/create")
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .content(objectMapper.writeValueAsString(donorAnonymeDTO)))
                .andExpect(status().isInternalServerError());
        verify(donorService,times(1)).addDonorAnonyme(any(DonorAnonymeDTO.class));
    }

    // Test Create Moral donor

    @Test
    @Order(11)
    void donorController_createDonorMoral_InternalServerException() throws Exception {


        when(donorService.addDonorMoral(any(DonorMoralDTO.class)))
                .thenThrow(new RuntimeException("Unexpected error"));

        mockMvc.perform(multipart("/donors/moral/create")
                        .param("company", "Xelops")
                        .param("address", "just a address")
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isInternalServerError());
        verify(donorService).addDonorMoral(any(DonorMoralDTO.class));
    }

    @Test
    @Order(12)
    void donorController_createDonorMoral_Success() throws Exception {
        DonorMoralDTO  donorMoralDTO=new DonorMoralDTO();
        donorMoralDTO.setCompany("Xelops");
        donorMoralDTO.setAddress("just a address");

        DonorDTO donorDTO=new DonorDTO();
        donorDTO.setId(1L);
        donorDTO.setAddress("just a address");

        when(donorService.addDonorMoral(any(DonorMoralDTO.class))).thenReturn(donorDTO);

        mockMvc.perform(post("/donors/moral/create")
                        .param("company", donorMoralDTO.getCompany())
                        .param("address", donorMoralDTO.getAddress())
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(donorDTO)));
        verify(donorService,times(1)).addDonorMoral(any(DonorMoralDTO.class));
    }

    // Test Find Donors By Critreria

    @Test
    @Order(13)
    void donorController_findDonorsByCritreria_SuccessWithGetAll() throws Exception {
        List<DonorDTO> donorDTOList = Arrays.asList(
                DonorDTO.builder().id(1L).build(),
                DonorDTO.builder().id(2L).build()
        );

        Pageable pageable = PageRequest.of(0, 10);
        Page<DonorDTO> donorDTOPage = new PageImpl<>(donorDTOList, pageable, donorDTOList.size());


        when(donorService.getDonorsByCriteria(0,10,null,null,null,null,null,null,null,null,null,null,null,null,null)).thenReturn(
                donorDTOPage
        );

        mockMvc.perform(get("/donors/findByCriteria")
                        .header("Accept", "application/json")
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk())
                .andExpect(content()
                .json(objectMapper.writeValueAsString(donorDTOPage)));
        verify(donorService,times(1)).getDonorsByCriteria(
                0,10,null,null,null,null,null,null,null,null,null,null,null,null,null
        );

    }

    @Test
    @Order(14)
    void donorController_findDonorsByCritreria_InternalServerException() throws Exception {

        when(donorService.getDonorsByCriteria(0,10,null,null,null,null,null,null,null,null,null,null,null,null,2L)).thenThrow(
                new RuntimeException()
        );

        mockMvc.perform(get("/donors/findByCriteria")
                        .header("Accept", "application/json")
                        .param("searchByStatus","2")
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isInternalServerError());
        verify(donorService,times(1)).getDonorsByCriteria(
                0,10,null,null,null,null,null,null,null,null,null,null,null,null,2L
        );

    }

    @Test
    @Order(15)
    void donorController_findDonorsByCritreria_SuccessWithGetAllWithFilter() throws Exception {
        List<DonorDTO> donorDTOList = Arrays.asList(
                DonorDTO.builder().id(1L).build(),
                DonorDTO.builder().id(2L).build(),
                DonorDTO.builder().id(3L).build(),
                DonorDTO.builder().id(4L).build()
        );

        Pageable pageable = PageRequest.of(0, 4);
        Page<DonorDTO> donorDTOPage = new PageImpl<>(donorDTOList, pageable, donorDTOList.size());


        when(donorService.getDonorsByCriteria(
                0,
                4,
                "type",
                "nameFirst",
                "nameLast",
                "company",
                "anonymeDonor",
                "searchByDonorType",
                "searchByNom",
                "lastNameAr",
                "searchByPrenom",
                "searchByNum",
                "searchByEmail",
                2,
                2L)).thenReturn(
                donorDTOPage
        );

        mockMvc.perform(get("/donors/findByCriteria")
                        .header("Accept", "application/json")
                        .param("size","4")
                        .param("searchByStatus","2")
                        .param("typeDonor","type")
                        .param("firstName","nameFirst")
                        .param("lastName","nameLast")
                        .param("companyName","company")
                        .param("anonymeDonor","anonymeDonor")
                        .param("searchByDonorType","searchByDonorType")
                        .param("searchByNom","searchByNom")
                        .param("lastNameAr","lastNameAr")
                        .param("searchByPrenom","searchByPrenom")
                        .param("searchByPhoneNum","searchByNum")
                        .param("searchByEmail","searchByEmail")
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk())
                .andExpect(content()
                        .json(objectMapper.writeValueAsString(donorDTOPage)));
        verify(donorService, times(1)).getDonorsByCriteria(
                0,
                4,
                "type",
                "nameFirst",
                "nameLast",
                "company",
                "anonymeDonor",
                "searchByDonorType",
                "searchByNom",
                "lastNameAr",
                "searchByPrenom",
                "searchByNum",
                "searchByEmail",
                2,
                2L
        );

    }

    // Test export Donors To Csv

    @Test
    @Order(16)
    void donorController_exportToCsv_SuccessWithGetAll() throws Exception {
        ExportFileDTO exportFileDTO = new ExportFileDTO();
        exportFileDTO.setFileName("donors.csv");
        exportFileDTO.setFile64("donors.csv");

        when(donorService.exportFileWithName(
                null,
                null,
                null,
                null,
                null,
                null,
                2,
                2L
        )).thenReturn(exportFileDTO);

        mockMvc.perform(get("/donors/csv")
                        .header("Accept", "application/json")
                        .param("searchByStatus","2")
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk())
                .andExpect(content()
                        .json(objectMapper.writeValueAsString(exportFileDTO)));
        verify(donorService,times(1)).exportFileWithName(
                null,
                null,
                null,
                null,
                null,
                null,
                2,
                2L
        );

    }

    @Test
    @Order(17)
    void donorController_exportToCsv_InternalServerException() throws Exception {

        when(donorService.exportFileWithName(
                null,
                null,
                null,
                null,
                null,
                null,
                2,
                2L
        )).thenThrow(new RuntimeException());

        mockMvc.perform(get("/donors/csv")
                        .header("Accept", "application/json")
                        .param("searchByStatus","2")
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isInternalServerError());
        verify(donorService, times(1)).exportFileWithName(    null,
                null,
                null,
                null,
                null,
                null,
                2
        ,2L);
    }

    @Test
    @Order(18)
    void donorController_exportToCsv_SuccessWithFilter() throws Exception {
        ExportFileDTO exportFileDTO = new ExportFileDTO();
        exportFileDTO.setFileName("donors.csv");
        exportFileDTO.setFile64("donors.csv");

        when(donorService.exportFileWithName(
                "type",
                "nom",
                "nameAr",
                "prenom",
                "num",
                "email",
                2,
                2L
        )).thenReturn(exportFileDTO);

        mockMvc.perform(get("/donors/csv")
                        .header("Accept", "application/json")
                        .param("searchByDonorType","type")
                        .param("searchByNom","nom")
                        .param("searchByPrenom","prenom")
                        .param("searchByPhoneNum","num")
                        .param("searchByEmail","email")
                        .param("lastNameAr","nameAr")
                        .param("searchByStatus","2")
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk())
                .andExpect(content()
                        .json(objectMapper.writeValueAsString(exportFileDTO)));
        verify(donorService, times(1)).exportFileWithName("type",
                "nom",
                "nameAr",
                "prenom",
                "num",
                "email",
                2,
                2L);
    }

    // get releve_compte of donor id

    @Test
    @Order(19)
    void donorController_getReleveCompte_success() throws Exception {
        Long donorId = 1L;
        List<ReleveDonorDto> releveCompte = Arrays.asList(
                ReleveDonorDto.builder().id(1L).build(),
                ReleveDonorDto.builder().id(2L).build(),
                ReleveDonorDto.builder().id(3L).build()
        );

        when(donorService.releveCompte(donorId)).thenReturn(releveCompte);
        mockMvc.perform(get("/donors/1/releve-compte"))
                .andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(releveCompte)));
        verify(donorService,times(1)).releveCompte(donorId);
    }

    @Test
    @Order(20)
    void donorController_getReleveCompte_NotFound() throws Exception {
        Long donorId = 1L;

        when(donorService.releveCompte(donorId)).thenThrow(new EntityNotFoundException());

        mockMvc.perform(get("/donors/1/releve-compte"))
                .andExpect(status().isNotFound());
        verify(donorService,times(1)).releveCompte(donorId);
    }

    @Test
    @Order(21)
    void donorController_getReleveCompte_InternalServerException() throws Exception {
        Long donorId = 1L;

        when(donorService.releveCompte(donorId)).thenThrow(new RuntimeException());

        mockMvc.perform(get("/donors/1/releve-compte"))
                .andExpect(status().isInternalServerError());
        verify(donorService,times(1)).releveCompte(donorId);

    }

    //  get jornal operation for donor id

    @Test
    @Order(22)
    void donorController_getJournalOperation_success() throws Exception {
        Long donorId = 1L;
        List<JournalOperationDto> journalOperation = Arrays.asList(
                JournalOperationDto.builder().id(1L).build(),
                JournalOperationDto.builder().id(2L).build()
        );
        when(donorService.getJournalOperation(donorId)).thenReturn(journalOperation);
        mockMvc.perform(get("/donors/1/journal-operations"))
                .andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(journalOperation)));
        verify(donorService,times(1)).getJournalOperation(donorId);
    }

    @Test
    @Order(23)
    void donorController_getJournalOperation_notFound() throws Exception {
        Long donorId = 1L;
        when(donorService.getJournalOperation(donorId)).thenThrow(new EntityNotFoundException());
        mockMvc.perform(get("/donors/1/journal-operations"))
                .andExpect(status().isNotFound());
        verify(donorService,times(1)).getJournalOperation(donorId);
    }

    @Test
    @Order(24)
    void donorController_getJournalOperation_internalServerOperation() throws Exception {
        Long donorId = 1L;
        when(donorService.getJournalOperation(donorId)).thenThrow(new RuntimeException());
        mockMvc.perform(get("/donors/1/journal-operations"))
                .andExpect(status().isInternalServerError());
        verify(donorService,times(1)).getJournalOperation(donorId);
    }

    // get journal operations reserve for donor Id

    @Test
    @Order(25)
    void donorController_getJournalOperationReserve_success() throws Exception{
        Long donorId = 1L;
        List<JournalOperationReserveDto> journalOperationReserveDtos = Arrays.asList(
                JournalOperationReserveDto.builder().id(2L).build(),
                JournalOperationReserveDto.builder().id(1L).build()
        );

        when(donorService.getJournalOperationReserve(donorId)).thenReturn(journalOperationReserveDtos);

        mockMvc.perform(get("/donors/1/journal-operations-reserve"))
                .andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(journalOperationReserveDtos)));
        verify(donorService,times(1)).getJournalOperationReserve(donorId);

    }

    @Test
    @Order(26)
    void donorController_getJournalOperationReserve_NotFound() throws Exception{
        Long donorId = 1L;
        when(donorService.getJournalOperationReserve(donorId)).thenThrow(new EntityNotFoundException());

        mockMvc.perform(get("/donors/1/journal-operations-reserve"))
                .andExpect(status().isNotFound());
        verify(donorService,times(1)).getJournalOperationReserve(donorId);
    }

    @Test
    @Order(27)
    void donorController_getJournalOperationReserve_InternalServerException() throws Exception{
        Long donorId = 1L;
        when(donorService.getJournalOperationReserve(donorId)).thenThrow(new RuntimeException());

        mockMvc.perform(get("/donors/1/journal-operations-reserve"))
                .andExpect(status().isInternalServerError());
        verify(donorService,times(1)).getJournalOperationReserve(donorId);
    }

    // Get sold for donor Id

    @Test
    @Order(28)
    void donorController_getSolde_success() throws Exception {
        Long donorId = 1L;
        List<SoldeDto> soldeDtos = Arrays.asList(
                SoldeDto.builder().id(2L).build(),
                SoldeDto.builder().id(1L).build()
        );

        when(donorService.getSolde(donorId)).thenReturn(soldeDtos);

        mockMvc.perform(get("/donors/1/solde"))
                .andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(soldeDtos)));
        verify(donorService,times(1)).getSolde(donorId);
    }

    @Test
    @Order(28)
    void donorController_getSolde_notFound() throws Exception {
        Long donorId = 1L;

        when(donorService.getSolde(donorId)).thenThrow(new EntityNotFoundException());

        mockMvc.perform(get("/donors/1/solde"))
                .andExpect(status().isNotFound());
        verify(donorService,times(1)).getSolde(donorId);
    }

    @Test
    @Order(29)
    void donorController_getSolde_InternalServerError() throws Exception {
        Long donorId = 1L;

        when(donorService.getSolde(donorId)).thenThrow(new RuntimeException());

        mockMvc.perform(get("/donors/1/solde"))
                .andExpect(status().isInternalServerError());
        verify(donorService,times(1)).getSolde(donorId);
    }
}