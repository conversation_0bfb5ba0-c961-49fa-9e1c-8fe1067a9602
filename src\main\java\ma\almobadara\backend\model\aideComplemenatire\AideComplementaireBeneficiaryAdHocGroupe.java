package ma.almobadara.backend.model.aideComplemenatire;

import jakarta.persistence.*;
import lombok.*;
import ma.almobadara.backend.model.beneficiary.Beneficiary;
import ma.almobadara.backend.model.beneficiary.BeneficiaryAdHocGroup;
import ma.almobadara.backend.model.donor.Donor;



@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AideComplementaireBeneficiaryAdHocGroupe {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;


    @ManyToOne
    @JoinColumn(name = "aide_complementaire_id", nullable = false)
    private AideComplementaire aideComplementaire;

    @ManyToOne
    @JoinColumn(name = "beneficiary_ad_hoc_group_id", nullable = false)
    private BeneficiaryAdHocGroup beneficiaryAdHocGroup;

    @ManyToOne
    @JoinColumn(name = "beneficiary_id", nullable = true)
    private Beneficiary beneficiary;

    private Double montantAffecter;
    private Boolean statutValidation;

    private Long numberOfMembersBenefiting;

}
