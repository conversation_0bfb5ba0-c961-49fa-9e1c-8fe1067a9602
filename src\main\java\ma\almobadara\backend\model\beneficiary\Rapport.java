package ma.almobadara.backend.model.beneficiary;

import jakarta.persistence.*;
import lombok.*;
import ma.almobadara.backend.model.donor.Donor;
import ma.almobadara.backend.model.family.Family;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Entity
@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class Rapport extends BaseEntity{

    private Boolean archived;
    @Column(unique = true, nullable = false)
    private String codeRapport;
    private Long numberRapport;
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date dateRapport;
    private String reference;
    private String release;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date dateRapportValidate;
    private String detailComplete;

    @ManyToOne
    @JoinColumn(name = "donor_id", nullable = true)
    private Donor donor;
    private String donorFirstName;
    private String donorLastName;
    private String donorFirstNameAr;
    private String donorLastNameAr;

    @ManyToOne
    @JoinColumn(name = "beneficiary_id", nullable = false)
    private Beneficiary beneficiary;
    private Long personBeneficiaryId;
    private String beneficiaryCode;
    private String beneficiaryFirstName;
    private String beneficiaryLastName;
    private String beneficiaryFirstNameAr;
    private String beneficiaryLastNameAr;
    private String beneficiaryAddress;
    private String beneficiaryAddressAr;
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date beneficiaryBirthDate;
    private Long cityId;
    private Long schoolLevelId;
    private String schoolName;
    private Double result;

    @ManyToOne
    @JoinColumn(name = "family_id", nullable = true)
    private Family family;
    private Long personFamilyId;
    private Long familyRelationshipId;
    private Long numberOfFamilyMember;
    private String tutorFirstName;
    private String tutorLastName;
    private String tutorFirstNameAr;
    private String tutorLastNameAr;
    private Long professionId;
    private String phoneNumber;
    private Long accommodationTypeId;

    private String activityEducationalFr;
    private String activityEducationalEn;
    private String activityEducationalAr;
    private String socialServiceFr;
    private String socialServiceEn;
    private String socialServiceAr;
    private String recommendationFr;
    private String recommendationEn;
    private String recommendationAr;

    @OneToMany(mappedBy = "rapport", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<RapportPicture> pictures;

}
