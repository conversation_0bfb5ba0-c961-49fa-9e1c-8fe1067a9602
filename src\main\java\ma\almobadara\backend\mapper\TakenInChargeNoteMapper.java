package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.takenInCharge.NoteTakenInChargeDTO;
import ma.almobadara.backend.dto.takenInCharge.TakenInChargeDTO;
import ma.almobadara.backend.model.takenInCharge.NoteTakenInCharge;
import ma.almobadara.backend.model.takenInCharge.TakenInCharge;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface TakenInChargeNoteMapper {

	NoteTakenInChargeDTO takenInChargeNoteToTakenInChargeNoteDTO(NoteTakenInCharge takenInChargeNote);

	Iterable<NoteTakenInChargeDTO> takenInChargeNoteToTakenInChargeNoteDTO(Iterable<NoteTakenInCharge> takenInChargeNote);

	NoteTakenInCharge takenInChargeNoteDTOToTakenInChargeNote(NoteTakenInChargeDTO takenInChargeNoteDTO);

	Iterable<NoteTakenInCharge> takenInChargeNoteDTOToTakenInChargeNote(Iterable<NoteTakenInChargeDTO> takenInChargeNoteDTO);

	@Mapping(target = "services", ignore = true)
	@Mapping(target = "status", ignore = true)
	@Mapping(target = "takenInChargeDonors", ignore = true)
	@Mapping(target = "takenInChargeBeneficiaries", ignore = true)
	TakenInChargeDTO takenInChargeToTakenInChargeDTO(TakenInCharge takenInCharge);
}
