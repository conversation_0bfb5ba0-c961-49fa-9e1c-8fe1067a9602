package ma.almobadara.backend.dto.referentiel;

import lombok.*;

import java.io.Serializable;
import java.util.Objects;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class AllergiesDTO  implements Serializable {

	private Long id;
	private String code;
	private String name;
	private String nameAr;
	private String nameEn;
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}

	public String getNameAr() {
		return nameAr;
	}
	public void setNameAr(String nameAr) {
		this.nameAr = nameAr;
	}
	public String getNameEn() {
		return nameEn;
	}
	public void setNameEn(String nameEn) {
		this.nameEn = nameEn;
	}
	@Override
	public boolean equals(Object o) {
		if (this == o) {
			return true;
		}
		if (!(o instanceof AllergiesDTO allergiesDTO)) {
			return false;
		}

        if (this.id == null) {
			return false;
		}
		return Objects.equals(this.id, allergiesDTO.id);
	}

	@Override
	public int hashCode() {
		return Objects.hash(this.id);
	}

	// prettier-ignore
	@Override
	public String toString() {
		return "MetAllergiesDTO{" +
				"id=" + getId() +
				", name='" + getName() + "'" +
				"}";
	}

}
