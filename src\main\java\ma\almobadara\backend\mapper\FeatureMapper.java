package ma.almobadara.backend.mapper;
import ma.almobadara.backend.dto.administration.FeatureDTO;
import ma.almobadara.backend.model.administration.Feature;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
@Mapper(componentModel = "spring", uses = { ModuleMapper.class })
public interface FeatureMapper {

    FeatureDTO toDto(Feature feature);

    Feature toEntity(FeatureDTO featureDTO);

    @Mapping(target = "id", ignore = true)
    void updateFeatureFromDto(FeatureDTO featureDTO, @MappingTarget Feature feature);
}

