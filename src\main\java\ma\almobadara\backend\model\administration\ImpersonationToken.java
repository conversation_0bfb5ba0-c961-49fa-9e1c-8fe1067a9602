package ma.almobadara.backend.model.administration;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ImpersonationToken {
    private String token;
    private Long originUserId;
    private Long impersonatedUserId;
    private LocalDateTime createdAt;
    private LocalDateTime expiresAt;
}
