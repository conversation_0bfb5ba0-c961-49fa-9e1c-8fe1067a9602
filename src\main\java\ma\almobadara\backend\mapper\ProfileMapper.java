package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.administration.ProfileDTO;
import ma.almobadara.backend.model.administration.Profile;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "spring")
public interface ProfileMapper {

    ProfileMapper INSTANCE = Mappers.getMapper(ProfileMapper.class);

    Profile toEntity(ProfileDTO profileDTO);

    @Mapping(source = "moduleFunctionalities", target = "moduleFunctionalities")
    @Mapping(source = "id", target = "id")
    ProfileDTO toDTO(Profile profile);

    List<ProfileDTO> toDTOList(List<Profile> profiles);
}


