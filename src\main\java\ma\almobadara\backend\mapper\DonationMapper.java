package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.donation.DonationAuditDto;
import ma.almobadara.backend.dto.donation.DonationDTO;
import ma.almobadara.backend.dto.donation.DonationProductNatureDTO;
import ma.almobadara.backend.dto.donor.DonorDTO;
import ma.almobadara.backend.model.donation.Donation;
import ma.almobadara.backend.model.donation.DonationProductNature;
import ma.almobadara.backend.model.donor.Donor;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

@Mapper(componentModel = "spring")
public interface DonationMapper {

	@Mapping(source = "currencyId", target = "currency.id")
	@Mapping(source = "canalDonationId", target = "canalDonation.id")
	DonationDTO donationToDonationDTO(Donation donation);

	@Named("mapWithoutNesting")
	@Mapping(target = "donationProductNatures", ignore = true)
	@Mapping(target = "documentDonations", ignore = true)
	@Mapping(source = "canalDonationId", target = "canalDonation.id")
	@Mapping(source = "currencyId", target = "currency.id")
	DonationDTO donationToDonationDTOForList(Donation donation);

	@Mapping(target = "notes", ignore = true)
	@Mapping(target = "documentDonors", ignore = true)
	@Mapping(target = "donations", ignore = true) // Break recursion here
	@Mapping(target = "status", ignore = true)
	@Mapping(target = "takenInChargeDonors", ignore = true)
	@Mapping(source = "cityId", target = "city.id")
	DonorDTO donorDTO(Donor donor);

	@Mapping(source = "canalDonation.id", target = "canalDonationId")
	@Mapping(source = "currency.id", target = "currencyId")
	@Mapping(target = "budgetLines", ignore = true)  // Ignore the budgetLine field
	Donation donationDTOToDonation(DonationDTO donationDTO);

	Iterable<Donation> donationDTOToDonation(Iterable<DonationDTO> donationDTOS);

	@Mapping(target = "donation", ignore = true)
	@Mapping(source = "productNatureId", target = "productNature.id")
	@Mapping(source = "productUnitId", target = "productUnit.id")
	DonationProductNatureDTO donationProductNatureToDonationProductNatureDTO(DonationProductNature donationProductNature);

	@Mapping(source = "code", target = "Code")
	@Mapping(source = "value", target = "ValeurDeLaDonation")
	@Mapping(source = "receptionDate", target = "DateReception")
	@Mapping(source = "identifiedDonor", target = "DonateurIdentifie")
	@Mapping(source = "comment", target = "Commentaire")
	@Mapping(source = "valueCurrency", target = "Equivalent")
	@Mapping(source = "type", target = "TypeDonation")
	DonationAuditDto donationDtoToDonationAuditDto(DonationDTO donationDTO);
}
