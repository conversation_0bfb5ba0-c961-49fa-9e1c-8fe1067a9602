package ma.almobadara.backend.controller.administration;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.administration.TagDTO;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.model.administration.TypeTag;
import ma.almobadara.backend.service.administration.TagService;
import ma.almobadara.backend.service.administration.TypeTagService;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;
import java.util.Set;

@RestController
@RequestMapping("/tag")
@AllArgsConstructor
@Slf4j
public class TagController {

    private final TagService tagService;
    private final TypeTagService typeTagService;

    /**
     * Get all tags with pagination
     * @param page page number (default 0)
     * @param size page size (default 5)
     * @return a page of tag DTOs
     */
    @GetMapping
    public ResponseEntity<Page<TagDTO>> getAllTags(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size
    ) {
        log.info("Start resource getAllTags - page: {}, size: {}", page, size);
        Page<TagDTO> tagList = tagService.getAllTags(page, size);
        return ResponseEntity.ok(tagList);
    }

    /**
     * Get all tags as a list
     * @return a list of tag DTOs
     */
    @GetMapping("/list")
    public ResponseEntity<List<TagDTO>> getAll() {
        log.info("Start resource getAllTags as list");
        List<TagDTO> tagList = tagService.getTagList();
        return ResponseEntity.ok(tagList);
    }

    /**
     * Get a tag by its ID
     * @param id the tag ID
     * @return the tag DTO if found
     */
    @GetMapping("/{id}")
    public ResponseEntity<TagDTO> getTagById(@PathVariable Long id) {
        log.info("Start resource getTagById: {}", id);
        TagDTO tag = tagService.getTagById(id);
        if (tag == null) {
            return ResponseEntity.notFound().build();
        }
        return ResponseEntity.ok(tag);
    }

    /**
     * Get tags by type tag ID
     * @param typeTagId the type tag ID
     * @return a list of tag DTOs with the specified type
     */
    @GetMapping("/by-type/{typeTagId}")
    public ResponseEntity<List<TagDTO>> getTagsByTypeTagId(@PathVariable Long typeTagId) {
        log.info("Start resource getTagsByTypeTagId: {}", typeTagId);
        List<TagDTO> tags = tagService.getTagsByTypeTagId(typeTagId);
        return ResponseEntity.ok(tags);
    }

    /**
     * Create a new tag
     * @param tagDTO the tag data
     * @return the created tag DTO
     * @throws TechnicalException if a tag with the same name already exists
     */
    @PostMapping
    public ResponseEntity<TagDTO> createTag(@ModelAttribute TagDTO tagDTO) throws TechnicalException {
        log.info("Start resource createTag: {}", tagDTO.getName());
        TagDTO createdTag = tagService.saveTag(tagDTO);
        return new ResponseEntity<>(createdTag, HttpStatus.CREATED);
    }

    @GetMapping("/{taggableType}/tags")
    public ResponseEntity<Set<TagDTO>> getTagsByTaggableType(@PathVariable String taggableType) {
        log.info("Start resource getTagsByTaggableType: {}", taggableType);
        Set<TagDTO> tags = tagService.getTagsByTaggableType(taggableType);
        return ResponseEntity.ok(tags);
    }
    /**
     * Update an existing tag
     * @param id the tag ID
     * @param tagDTO the updated tag data
     * @return the updated tag DTO
     * @throws TechnicalException if the tag doesn't exist or there's a name conflict
     */
    @PutMapping("/{id}")
    public ResponseEntity<TagDTO> updateTag(@PathVariable Long id, @ModelAttribute TagDTO tagDTO) throws TechnicalException {
        log.info("Start resource updateTag: {}", id);
        tagDTO.setId(id); // Ensure the ID in the path is used
        TagDTO updatedTag = tagService.updateTag(tagDTO);
        return ResponseEntity.ok(updatedTag);
    }

    /**
     * Delete a tag by its ID
     * @param id the tag ID to delete
     * @return no content on success
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteTag(@PathVariable Long id) {
        log.info("Start resource deleteTag: {}", id);
        tagService.deleteTag(id);
        return ResponseEntity.noContent().build();
    }

    /**
     * Get all type tags
     * @return a list of type tags
     */
    @GetMapping("/types")
    public ResponseEntity<List<TypeTag>> getAllTypeTag() {
        log.info("Start resource getAllTypeTag");
        List<TypeTag> typeTagList = typeTagService.getTypeTagList();
        return ResponseEntity.ok(typeTagList);
    }

    /**
     * Get a type tag by its ID
     * @param id the type tag ID
     * @return the type tag if found
     */
    @GetMapping("/types/{id}")
    public ResponseEntity<TypeTag> getTypeTagById(@PathVariable Long id) {
        log.info("Start resource getTypeTagById: {}", id);
        Optional<TypeTag> typeTag = typeTagService.getTypeTagById(id);
        return typeTag.map(ResponseEntity::ok)
                .orElseGet(() -> ResponseEntity.notFound().build());
    }
}
