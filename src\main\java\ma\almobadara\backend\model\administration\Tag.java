package ma.almobadara.backend.model.administration;

import jakarta.persistence.*;
import lombok.*;

import java.util.List;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Tag {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String name;
    private String color;

    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "type_tag_id")
    private TypeTag typeTag;

    
    @OneToMany(mappedBy = "tag", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Taggable> taggables;
}
