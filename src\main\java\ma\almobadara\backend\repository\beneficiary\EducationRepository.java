package ma.almobadara.backend.repository.beneficiary;

import ma.almobadara.backend.model.beneficiary.Beneficiary;
import ma.almobadara.backend.model.beneficiary.Education;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EducationRepository extends JpaRepository<Education, Long> {

    List<Education> findByBeneficiaryId(Long id);

    List<Education> findByBeneficiary(Beneficiary beneficiary);

}
