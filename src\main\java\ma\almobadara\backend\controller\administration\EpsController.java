package ma.almobadara.backend.controller.administration;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.CityWithRegionAndCountryDTO;
import ma.almobadara.backend.dto.administration.*;
import ma.almobadara.backend.dto.beneficiary.EpsBeneficiaryDTO;
import ma.almobadara.backend.dto.administration.EpsDto;
import ma.almobadara.backend.dto.administration.ZoneDTO;
import ma.almobadara.backend.dto.donor.DonorDTO;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.mapper.EpsMapper;
import ma.almobadara.backend.model.administration.Eps;
import ma.almobadara.backend.model.administration.Zone;
import ma.almobadara.backend.repository.administration.EpsRepository;
import ma.almobadara.backend.service.administration.EpsService;
import org.springframework.data.domain.Page;
import ma.almobadara.backend.service.administration.ZoneService;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import static ma.almobadara.backend.service.administration.CacheAdUserService.logUserInfo;
import ma.almobadara.backend.dto.administration.FicheEpsDTO;
import ma.almobadara.backend.dto.beneficiary.EpsBeneficiaryDTO;
import ma.almobadara.backend.service.administration.EpsService;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/eps")
@AllArgsConstructor
@Slf4j
public class EpsController {
    private final EpsService epsService;

    @GetMapping
    public ResponseEntity<Page<EpsListDTO>> getAllEps(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "5") int size,
            @RequestParam(required = false) final String searchByName,
            @RequestParam(required = false) final String searchByNameAr,
            @RequestParam(required = false) final String searchByStatus,
            @RequestParam(required = false) final String searchByCity,
            @RequestParam(required = false) final Long searchByMinBenificiary,
            @RequestParam(required = false) final Long searchByMaxBenificiary,
            @RequestParam(required = false) final Long searchByTagId
    ) {
        log.info("Start resource getAllZones");
        Page<EpsListDTO> epsList = epsService.getAllEps(page, size, searchByName, searchByNameAr, searchByStatus,searchByMinBenificiary, searchByMaxBenificiary, searchByCity,searchByTagId);
        return ResponseEntity.ok(epsList);
    }

    @GetMapping("/list")
    public ResponseEntity<List<EpsListDTO>> getAllZonesList(
    ) {
        log.info("Start resource getAllZones");
        List<EpsListDTO> epsList = epsService.getAllEpsList();
        return ResponseEntity.ok(epsList);
    }

    @PostMapping
    public ResponseEntity<EpsDto> createEps(@ModelAttribute EpsDto epsDto) throws TechnicalException {
        log.info("Start resource createEps with  name: {}",  epsDto.getName());
        EpsDto createdEps = epsService.createEps(epsDto);
        log.info("end resource createEps with  name: {}", epsDto.getName());
        return ResponseEntity.status(HttpStatus.CREATED).body(createdEps);
    }
    @GetMapping("/{id}")
    public ResponseEntity<EpsDto> getAddEps(@PathVariable Long id) {
        logUserInfo("getAddEps", String.valueOf(id));

        try {
            EpsDto epsDto = epsService.FindEpsByIdForAdd(id);
            log.info("End resource getAddEps : {}. Retrieved Eps: {}, OK", id, epsDto);
            return new ResponseEntity<>(epsDto, HttpStatus.OK);

        } catch (Exception e) {
            log.error("End resource getAddEps : {}. KO: {}", id, e.getMessage());

            // Return an empty EpsDto or a custom error EpsDto with appropriate error information
            EpsDto errorEpsDto = new EpsDto();
            errorEpsDto.setError("Eps Not Found");

            // Return the error EpsDto with a NOT_FOUND status
            return new ResponseEntity<>(errorEpsDto, HttpStatus.NOT_FOUND);
        }
    }

    @GetMapping("/{epsId}/fiche")
    public ResponseEntity<FicheEpsDTO> loadFicheEps(@PathVariable Long epsId) {
        log.info("Start resource loadFicheEps with epsId: {}", epsId);
        FicheEpsDTO ficheEpsDTO = epsService.loadFicheEps(epsId);
        if (ficheEpsDTO != null) {
            return ResponseEntity.ok(ficheEpsDTO);
        } else {
            log.warn("Eps not found with id: {}", epsId);
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        }
    }

    @GetMapping("/cities")
    public ResponseEntity<List<CityWithRegionAndCountryDTO>> getAllCities() {
        log.info("Start resource getAllCities");
        List<CityWithRegionAndCountryDTO> cities = epsService.getCityOfMorroco();
        return ResponseEntity.ok(cities);
    }

    @GetMapping("/{epsId}/beneficiaires")
    public ResponseEntity<List<EpsBeneficiaryDTO>> getBeneficiaries(@PathVariable Long epsId) {
        log.info("Fetching beneficiaries for EPS ID: {}", epsId);
        List<EpsBeneficiaryDTO> beneficiaries = epsService.getBeneficiariesByEps(epsId);

        if (beneficiaries.isEmpty()) {
            return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
        }

        return ResponseEntity.ok(beneficiaries);
    }

    @DeleteMapping("/{id}")
    // we shoudl send the error catched by the service
    public ResponseEntity<Object> deleteEps(@PathVariable Long id) {
        log.info("Start resource deleteEps with id: {}", id);
        try {
            boolean isDeleted = epsService.deleteEps(id);
        } catch (TechnicalException e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of("error", "Technical error", "message", e.getMessage()));
        }
        log.info("End resource deleteZone with id: {}", id);
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }

    //loadActifEps

    @GetMapping("/actif")
    public ResponseEntity<List<EpsLightDTO>> loadActifEps() {
        log.info("Start resource loadActifEps");
        List<EpsLightDTO> epsDtoList = epsService.loadActifEps();
        return ResponseEntity.ok(epsDtoList);
    }

    @PutMapping("/{epsId}/status")
    public ResponseEntity<Object> changeEpsStatus(
            @PathVariable Long epsId,
            @RequestParam boolean status) {
        log.info("Start resource changeEpsStatus with epsId: {}", epsId);
        try {
            EpsListDTO updatedEps = epsService.changeEpsStatus(epsId, status);
            log.info("End resource changeEpsStatus with epsId: {}", epsId);
            return ResponseEntity.ok(updatedEps);
        } catch (TechnicalException e) {
            log.error("Error changing status for eps with id {}: {}", epsId, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Technical error", "message", e.getMessage()));
        }
    }

}
