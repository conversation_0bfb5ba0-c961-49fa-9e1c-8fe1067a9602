package ma.almobadara.backend.dto.family;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@Builder
public class FamilyMemberAuditDTO {

    private String Code;

    private boolean Tuteur;

    private Date TuteurDateDebut;

    private Date TuteurDateFin;

    private String LienDeParente;

    private String Prenom;

    private String Nom;

    private String PrenomArabe;

    private String NomArabe;

    private Date DateDeNaissance;

    private String Telephone;

    private String TypeIdentite;

    private String NumIdentite;

    private String NiveauScolaire;

    private String Profession;

    private String Adresse;

    private String AdresseArabe;

    private boolean Deceder;

    private Date DateDuDeces;

    private String RaisonDuDeces;
}
