package ma.almobadara.backend.repository.communs;

import ma.almobadara.backend.model.donor.DocumentDonor;
import ma.almobadara.backend.model.donor.Donor;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Repository
public interface DocumentDonorRepository extends JpaRepository<DocumentDonor,Long> {

    Iterable<DocumentDonor> findByDonor(Donor donor);

    List<DocumentDonor> findByDonorId(Long donorId);

    Optional<DocumentDonor> findByDonorIdAndDocumentId(Long donorId, Long documentId);

    List<DocumentDonor> findByDocumentId(Long documentId);

    @Modifying
    @Transactional

    @Query("UPDATE DocumentDonor d SET d.donor.id = :newDonorId WHERE d.donor.id = :oldDonorId")
    void updateDonorId(@Param("oldDonorId") Long oldDonorId, @Param("newDonorId") Long newDonorId);
}
