package ma.almobadara.backend;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableScheduling;

@EnableFeignClients
@EnableCaching
@SpringBootApplication
@EnableScheduling
public class SigBackendApiApplication {

    public static void main(String[] args) {
        SpringApplication.run(SigBackendApiApplication.class, args);
    }

}
