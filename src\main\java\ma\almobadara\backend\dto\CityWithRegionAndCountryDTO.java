package ma.almobadara.backend.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import ma.almobadara.backend.dto.referentiel.CountryDTO;
import ma.almobadara.backend.dto.referentiel.RegionDTO;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class CityWithRegionAndCountryDTO {

    private Long id;
    private String name;
    private RegionDTO region;
    private CountryDTO country;

}
