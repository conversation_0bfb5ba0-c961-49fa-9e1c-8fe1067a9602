package ma.almobadara.backend.model.donor;

import jakarta.persistence.*;
import lombok.*;
import ma.almobadara.backend.dto.referentiel.LanguageCommunicationDTO;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DonorPhysicalLanguageCommunication {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private Long languageCommunicationId;
    @Transient
    private LanguageCommunicationDTO languageCommunicationDTO;
    @ManyToOne
    @JoinColumn(name = "donor_physical_id")
    private DonorPhysical donorPhysical;

}
