package ma.almobadara.backend.controller.family;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.config.HasAccessToModule;
import ma.almobadara.backend.dto.family.*;
import ma.almobadara.backend.enumeration.Functionality;
import ma.almobadara.backend.enumeration.Module;
import ma.almobadara.backend.exceptions.FunctionalException;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.exceptions.TutorAlreadyExistsException;
import ma.almobadara.backend.model.family.TutorHistory;
import ma.almobadara.backend.service.family.FamilyMemberService;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static ma.almobadara.backend.service.administration.CacheAdUserService.logUserInfo;


//@RefreshScope
@RestController
@AllArgsConstructor
@Slf4j
@RequestMapping("/family-member")
public class FamilyMemberController {

    private final FamilyMemberService familyMemberService;
    @PostMapping(value = "", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    @Operation(summary = "Add a FamilyMember", description = "add a new family member", tags = {"family-member"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(schema = @Schema(implementation = FamilyMemberAddDTO.class)))})
    public ResponseEntity<AddedFamilyMemberResponse> addFamilyMember(@ModelAttribute FamilyMemberAddDTO familyMemberDTO) throws TechnicalException, FunctionalException, IOException {

        logUserInfo("addFamilyMember", String.valueOf(familyMemberDTO));

        AddedFamilyMemberResponse addedFamilyMember;

        addedFamilyMember = familyMemberService.addFamilyMember(familyMemberDTO);


        return new ResponseEntity<>(addedFamilyMember, new HttpHeaders(), HttpStatus.OK);
    }

    @PostMapping(value = "/members", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    @Operation(summary = "Add a FamilyMembers List", description = "add a new family member list", tags = {"family-member-list"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(schema = @Schema(implementation = FamilyMemberAddDTO.class)))})
    public ResponseEntity<Long> addListOfFamilyMembers(@ModelAttribute FamilyMemberListContainerDTO container) {
        List<FamilyMemberAddDTO> familyMembersDTOS = container.getFamilyMembers();

        logUserInfo("addListOfFamilyMembers");

        Long familyId;
        HttpStatus status;
        try {
            familyId = familyMemberService.addListOfFamilyMembers(familyMembersDTOS);
            status = HttpStatus.OK;
            log.info("End resource Add FamilyMember List, OK");
        } catch (TechnicalException e) {
            familyId = null;
            status = HttpStatus.INTERNAL_SERVER_ERROR;
            log.error("End resource Add FamilyMember List, KO: {}", e.getMessage());
        }

        return new ResponseEntity<>(familyId, new HttpHeaders(), status);
    }




}
