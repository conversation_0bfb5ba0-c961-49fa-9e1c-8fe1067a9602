package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.beneficiary.BeneficiaryDTO;
import ma.almobadara.backend.dto.beneficiary.BeneficiaryHandicapDto;
import ma.almobadara.backend.dto.beneficiary.PersonDTO;
import ma.almobadara.backend.dto.donor.DonorDTO;
import ma.almobadara.backend.dto.exportentities.TakenInChargeExportDTO;
import ma.almobadara.backend.dto.takenInCharge.*;
import ma.almobadara.backend.model.beneficiary.Beneficiary;
import ma.almobadara.backend.model.beneficiary.BeneficiaryHandicap;
import ma.almobadara.backend.model.beneficiary.Person;
import ma.almobadara.backend.model.takenInCharge.TakenInChargeBeneficiary;
import ma.almobadara.backend.model.donor.Donor;
import ma.almobadara.backend.model.takenInCharge.TakenInChargeDonor;
import ma.almobadara.backend.model.takenInCharge.DocumentTakenInCharge;
import ma.almobadara.backend.model.takenInCharge.TakenInCharge;
import ma.almobadara.backend.model.takenInCharge.NoteTakenInCharge;
import ma.almobadara.backend.model.takenInCharge.TakenInChargeOperation;
import org.mapstruct.IterableMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

@Mapper(componentModel = "spring")
public interface TakenInChargeMapper {


@Mapping(target = "serviceId", source = "service.id")
	TakenInChargeDTO takenInChargeToTakenInChargeDTO(TakenInCharge takenInCharge);

	@IterableMapping(qualifiedByName = "mapWithoutNesting")
	Iterable<TakenInChargeDTO> takenInChargeToTakenInChargeDTO(Iterable<TakenInCharge> takenInCharge);

	@Mapping(target = "service.id", source = "serviceId")
	TakenInCharge takenInChargeDTOToTakenInCharge(TakenInChargeDTO takenInChargeDTO);

	Iterable<TakenInCharge> takenInChargeDTOToTakenInCharge(Iterable<TakenInChargeDTO> takenInChargeDTO);

	@Named("mapWithoutNesting")
	@Mapping(target = "serviceId", source = "service.id")
	TakenInChargeDTO takenInChargeToTakenInChargeDTOForList(TakenInCharge takenInCharge);

	@Mapping(target = "takenInCharge", ignore = true)
	TakenInChargeDonorDTO takenInChargeDonorToTakenInChargeDonorDTO(TakenInChargeDonor takenInChargeDonor);

	@Mapping(target = "takenInCharge", ignore = true)
	TakenInChargeBeneficiaryDTO takenInChargeBeneficiaryToTakenInChargeBeneficiaryDTO(TakenInChargeBeneficiary takenInChargeBeneficiary);

	@Mapping(target = "notes", ignore = true)
	@Mapping(target = "documentDonors", ignore = true)
	@Mapping(target = "donations", ignore = true)
	@Mapping(target = "city", ignore = true)
	@Mapping(target = "status", ignore = true)
	@Mapping(target = "takenInChargeDonors", ignore = true)
	DonorDTO donorDTO(Donor donor);

	@Mapping(target = "notes", ignore = true)
	@Mapping(target = "documents", ignore = true)
	@Mapping(target = "scholarshipBeneficiaries", ignore = true)
	@Mapping(target = "epsResidents", ignore = true)
	@Mapping(target = "diseaseTreatments", ignore = true)
	@Mapping(target = "beneficiaryServices", ignore = true)
	@Mapping(target = "educations", ignore = true)
	@Mapping(target = "takenInChargeBeneficiaries", ignore = true)
	BeneficiaryDTO beneficiaryDTO(Beneficiary beneficiary);

	@Mapping(target = "city", ignore = true)
	@Mapping(target = "bankCards", ignore = true)
	@Mapping(target = "beneficiary", ignore = true)
	@Mapping(target = "familyMember", ignore = true)
	PersonDTO personDTO(Person person);

	@Mapping(target = "beneficiary",ignore = true)
	BeneficiaryHandicapDto beneficiaryHandicapToBeneficiaryHandicapDto(BeneficiaryHandicap beneficiaryHandicap);

	@Mapping(target = "takenInChargeDonor", ignore = true)
	TakenInChargeOperationDTO takenInChargeOperationDTO(TakenInChargeOperation takenInChargeOperation);

	NoteTakenInChargeDTO takenInChargeNoteToTakenInChargeNoteDTO(NoteTakenInCharge takenInChargeNote);

	DocumentTakenInCharge takenInChargeDocumentDTOToTakenInChargeDocument(DocumentTakenInChargeDTO takenInChargeDocumentDTO);

	@Mapping(source = "startDate", target = "DateDeDebut")
	@Mapping(source = "endDate", target = "DateDeFin")
	TakenInChargeAuditDto takenInChargeDtoToTakenInChargeAudit(TakenInChargeDTO takenInChargeDTO);

	@Mapping(source = "code", target = "code")
	@Mapping(source = "createdAt", target = "createdAt")
	TakenInChargeExportDTO takenInChargeToTakenInChargeExportDTO(TakenInCharge takenInCharge);

}
