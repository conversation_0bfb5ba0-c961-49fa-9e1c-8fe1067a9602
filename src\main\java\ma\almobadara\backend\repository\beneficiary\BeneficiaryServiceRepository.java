package ma.almobadara.backend.repository.beneficiary;


import ma.almobadara.backend.model.beneficiary.BeneficiaryService;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface BeneficiaryServiceRepository extends JpaRepository<BeneficiaryService, Long> {

    Iterable<BeneficiaryService> findByBeneficiaryId(Long id);
}
