package ma.almobadara.backend.dto.referentiel;

import lombok.*;

import java.util.HashSet;
import java.util.Objects;
import java.util.Set;
import java.io.Serializable;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class StatusDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	private Long id;

	private String code;
	private String name;
	private String nameAr;
	private String nameEn;

	private Set<CategoryDTO> categories = new HashSet<>();

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getNameAr() {
		return this.nameAr;
	}

	public void setNameAr(String nameAr) {
		this.nameAr = nameAr;
	}
	public String getNameEn() {
		return this.nameEn;
	}
	public void setNameEn(String nameEn) {
		this.nameEn = nameEn;
	}


	public Set<CategoryDTO> getCategories() {
		return categories;
	}

	public void setCategories(Set<CategoryDTO> categories) {
		this.categories = categories;
	}

	@Override
	public boolean equals(Object o) {
		if (this == o) {
			return true;
		}
		if (!(o instanceof StatusDTO parStatusDTO)) {
			return false;
		}

        if (this.id == null) {
			return false;
		}
		return Objects.equals(this.id, parStatusDTO.id);
	}

	@Override
	public int hashCode() {
		return Objects.hash(this.id);
	}

	// prettier-ignore
	@Override
	public String toString() {
		return "ParStatusDTO{" +
				"id=" + getId() +
				", name='" + getName() + "'" +
				", categories=" + getCategories() +
				"}";
	}

}
