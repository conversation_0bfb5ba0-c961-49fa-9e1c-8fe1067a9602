package ma.almobadara.backend.service.mobile;

import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.family.FamilyDTO;
import ma.almobadara.backend.dto.family.FamilyMemberDTO;
import ma.almobadara.backend.dto.family.KafalatDTO;
import ma.almobadara.backend.dto.mobile.FamilyMobileDTO;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.model.administration.Assistant;
import ma.almobadara.backend.model.beneficiary.Beneficiary;
import ma.almobadara.backend.model.family.Family;
import ma.almobadara.backend.model.family.FamilyMember;
import ma.almobadara.backend.repository.administration.AssistantRepository;
import ma.almobadara.backend.repository.beneficiary.BeneficiaryRepository;
import ma.almobadara.backend.repository.family.FamilyRepository;
import ma.almobadara.backend.service.family.FamilyService;
import ma.almobadara.backend.service.family.KafalatService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class FamilyMobileService {

    private final FamilyRepository familyRepository;
    private final AssistantRepository assistantRepository;
    private final FamilyService familyService;
    private final BeneficiaryRepository beneficiaryRepository;
    private final KafalatService kafalatService;

    /**
     * Get all families for mobile with a fixed page size of 6
     * @param page Optional page number
     * @return Page of FamilyMobileDTO
     */
    public Page<FamilyMobileDTO> getAllFamiliesForMobile(Optional<Integer> page) {
        log.debug("Start service getAllFamiliesForMobile");

        // Create pageable with fixed size of 6
        int pageNumber = page.orElse(0);
        int pageSize = 6;
        Sort.Direction sortDirection = Sort.Direction.DESC;
        String sortBy = "createdAt";
        PageRequest pageRequest = PageRequest.of(pageNumber, pageSize, Sort.by(sortDirection, sortBy));

        // Get families from repository
        Page<Family> families = familyRepository.findAll(pageRequest);

        // Convert to mobile DTOs
        List<FamilyMobileDTO> mobileDTOs = families.getContent().stream()
                .map(this::convertToMobileDTO)
                .collect(Collectors.toList());

        log.debug("End service getAllFamiliesForMobile");
        return new PageImpl<>(mobileDTOs, pageRequest, families.getTotalElements());
    }

    /**
     * Get families for mobile filtered by assistant ID with a fixed page size of 6
     * @param assistantId The ID of the assistant
     * @param page Optional page number
     * @return Page of FamilyMobileDTO
     */
    public Page<FamilyMobileDTO> getFamiliesByAssistantId(Long assistantId, Optional<Integer> page) {
        log.debug("Start service getFamiliesByAssistantId for assistant ID: {}", assistantId);

        // Create pageable with fixed size of 6
        int pageNumber = page.orElse(0);
        int pageSize = 6;
        Sort.Direction sortDirection = Sort.Direction.DESC;
        String sortBy = "createdAt";
        PageRequest pageRequest = PageRequest.of(pageNumber, pageSize, Sort.by(sortDirection, sortBy));

        // Get the assistant to find their zone
        Assistant assistant = assistantRepository.findById(assistantId)
                .orElseThrow(() -> new EntityNotFoundException("Assistant not found with ID: " + assistantId));

        // Check if assistant has a zone
        if (assistant.getZone() == null || assistant.getZone().getId() == null) {
            log.warn("Assistant with ID {} has no assigned zone", assistantId);
            return new PageImpl<>(Collections.emptyList(), pageRequest, 0);
        }

        Long zoneId = assistant.getZone().getId();
        log.debug("Found zone ID {} for assistant ID {}", zoneId, assistantId);

        // Get families by zone ID
        Page<Family> families = familyRepository.findFamilyByZoneId(pageRequest, zoneId);

        // Convert to mobile DTOs
        List<FamilyMobileDTO> mobileDTOs = families.getContent().stream()
                .map(this::convertToMobileDTO)
                .collect(Collectors.toList());

        log.debug("End service getFamiliesByAssistantId, found {} families", families.getTotalElements());
        return new PageImpl<>(mobileDTOs, pageRequest, families.getTotalElements());
    }

    /**
     * Convert Family entity to FamilyMobileDTO with only the required fields
     */
    private FamilyMobileDTO convertToMobileDTO(Family family) {
        FamilyMobileDTO dto = new FamilyMobileDTO();

        // Set ID and code
        dto.setId(family.getId());
        dto.setCode(family.getCode());

        // Set registration date
        dto.setRegistrationDate(family.getCreatedAt());

        // Set family name - using the first family member with relationship ID 1 (usually the head of family)
        String familyName = family.getFamilyMembers().stream()
                .filter(fm -> fm.getFamilyRelationshipId() != null && fm.getFamilyRelationshipId() == 1)
                .findFirst()
                .map(fm -> fm.getPerson().getLastName())
                .orElse("");
        dto.setFamilyName(familyName);

        // Set tutor name - find the family member marked as tutor
        String tutorName = family.getFamilyMembers().stream()
                .filter(FamilyMember::isTutor)
                .findFirst()
                .map(fm -> fm.getPerson().getFirstName() + " " + fm.getPerson().getLastName())
                .orElse("");
        dto.setTutorName(tutorName);

        // Set member count
        dto.setMemberCount(family.getFamilyMembers().size());

        return dto;
    }

    /**
     * Get a family by ID using the same DTO as the main service
     * @param id The ID of the family
     * @return FamilyDTO with full details
     * @throws TechnicalException if there's an error retrieving the family
     */
    public FamilyDTO getFamilyById(Long id) throws TechnicalException {
        log.debug("Start service getFamilyById for ID: {}", id);

        // Use the existing service to get the full family details
        FamilyDTO familyDTO = familyService.getFamilyById(id);

        if (familyDTO == null) {
            throw new EntityNotFoundException("Family not found with ID: " + id);
        }

        // Add kafalat information for each family member
        if (familyDTO.getFamilyMembers() != null) {
            for (FamilyMemberDTO familyMemberDTO : familyDTO.getFamilyMembers()) {
                if (familyMemberDTO.getPerson() != null && familyMemberDTO.getPerson().getId() != null) {
                    // Check if this person is a beneficiary
                    Optional<Beneficiary> optionalBeneficiary = beneficiaryRepository.findByPersonId(familyMemberDTO.getPerson().getId());
                    if (optionalBeneficiary.isPresent()) {
                        // Get kafalat information for this beneficiary
                        List<KafalatDTO> kafalats = kafalatService.getKafalatForBeneficiary(optionalBeneficiary.get().getId());
                        familyMemberDTO.setKafalats(kafalats);
                    }
                }
            }
        }

        log.debug("End service getFamilyById for ID: {}", id);
        return familyDTO;
    }
}
