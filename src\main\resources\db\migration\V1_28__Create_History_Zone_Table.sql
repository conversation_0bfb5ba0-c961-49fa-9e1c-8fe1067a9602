CREATE TABLE history_zone(
                                id BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
                                id_zone BIGINT NOT NULL,
                                id_assistant BIGINT NOT NULL,
                                date_affectation DATE NOT NULL,
                                date_fin_affectation DATE,
                                PRIMARY KEY (id),
                                CONSTRAINT fk_history_zone_id_zone FOREIGN KEY (id_zone) REFERENCES Zone(id),
                                CONSTRAINT fk_history_zone_id_assistant FOREIG<PERSON> KEY (id_assistant) REFERENCES assistant(id)
);
