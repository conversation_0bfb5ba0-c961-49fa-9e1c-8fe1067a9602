package ma.almobadara.backend.Audit.entities;

import jakarta.persistence.Entity;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.persistence.*;
import java.util.Date;
import java.util.Map;

@Setter
@Getter
@Entity
@Table(name = "AUDIT_TABLE")
public class AuditEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "AUDIT_ID")
    private Long id;

    @Column(name = "CANAL")
    private String canal;

    @Column(name = "APP_NAME")
    private String appName;

    @Column(name = "SESSION_ID")
    private String sessionId;

    @Column(name = "ACTIVITY")
    private String activity;

    @Column(name = "OPERATION")
    private String operation;

    @Column(name = "SUB_OPERATION")
    private String subOperation;

    @Column(name = "USER_NAME")
    private String user;

    @Column(name = "USER_IP")
    private String userIpAddress;

    @Column(name = "USER_AGENT")
    private String userAgent;

    @Column(name = "MESSAGE", length = 999)
    private String message;

    @Column(name = "PROCESS_ID")
    private Long processId;

    @Column(name = "PROCESS")
    private String process;

    @Column(name = "MACHINE")
    private String machine;

    @Column(name = "MACHINE_ADDRESS")
    private String machineAddress;

    @Column(name = "AUDIT_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    @Column(name = "INITIAL_VALUE", columnDefinition = "TEXT")
    private String initialValue;

    @Column(name = "NEW_VALUE", columnDefinition = "TEXT")
    private String newValue;

    private transient Map<String, Object> initialValueJson; // to store parsed initialValue JSON
    private transient Map<String, Object> newValueJson; // to store parsed newValue JSON

    // Getters and setters for initialValueJson and newValueJson fields
    public Map<String, Object> getInitialValueJson() {
        return initialValueJson;
    }

    public void setInitialValueJson(Map<String, Object> initialValueJson) {
        this.initialValueJson = initialValueJson;
    }

    public Map<String, Object> getNewValueJson() {
        return newValueJson;
    }

    public void setNewValueJson(Map<String, Object> newValueJson) {
        this.newValueJson = newValueJson;
    }


}
