package ma.almobadara.backend.model.administration;

import jakarta.persistence.*;
import lombok.*;


@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
public class ResponsableEps {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String pictureUrl;
    private String firstName;
    private String lastName;
    private String firstNameAr;
    private String lastNameAr;
    private String email;
    private String phoneNumber;
    private String identityCode;
    private Long typeIdentityId;
    private String comment;
    private Long functionContactId;
    private boolean isDeleted;
    @ManyToOne
    @JoinColumn(name = "eps_id")
    private Eps eps;

}
