package ma.almobadara.backend.repository.donation;

import ma.almobadara.backend.model.donation.DonationHistory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface DonationHistoryRepository extends JpaRepository<DonationHistory, Long> {

    List<DonationHistory> findByDonationId(Long donorId);

    @Modifying
    @Transactional

    @Query("UPDATE DonationHistory d SET d.donor.id = :newDonorId WHERE d.donor.id = :oldDonorId")
    void updateDonorId(@Param("oldDonorId") Long oldDonorId, @Param("newDonorId") Long newDonorId);


}
