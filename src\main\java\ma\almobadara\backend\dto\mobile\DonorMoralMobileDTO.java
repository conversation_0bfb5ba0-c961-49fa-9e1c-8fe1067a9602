package ma.almobadara.backend.dto.mobile;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;
import ma.almobadara.backend.dto.administration.CacheAdUserDTO;
import ma.almobadara.backend.dto.donor.DonorContactDTO;
import ma.almobadara.backend.dto.referentiel.ActivitySectorDTO;
import ma.almobadara.backend.dto.referentiel.ProfessionDTO;
import ma.almobadara.backend.dto.referentiel.TypeDonorMoralDTO;
import ma.almobadara.backend.dto.referentiel.TypeIdentityDTO;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@SuperBuilder
@ToString
@AllArgsConstructor
public class DonorMoralMobileDTO {

    private String company;

    private Long id;

    private String shortCompany;

    private String type = "Moral";

    private String logoUrl;

    private String identityCode;

    private Date registrationDate;

    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private MultipartFile logo;

    private String logo64;

    private List<DonorContactDTO> donorContacts;

    private ActivitySectorDTO activitySector;

    private TypeDonorMoralDTO typeDonorMoral;

    private Double totalDonations;

    private String address;
}
