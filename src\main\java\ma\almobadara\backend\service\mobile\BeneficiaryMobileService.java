package ma.almobadara.backend.service.mobile;

import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.beneficiary.BeneficiaryDTO;
import ma.almobadara.backend.dto.family.KafalatDTO;
import ma.almobadara.backend.dto.mobile.BeneficiaryMobileDTO;
import ma.almobadara.backend.dto.mobile.BeneficiaryMobileDetailDTO;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.mapper.BeneficiaryMapper;
import ma.almobadara.backend.model.administration.Assistant;
import ma.almobadara.backend.model.beneficiary.Beneficiary;
import ma.almobadara.backend.repository.administration.AssistantRepository;
import ma.almobadara.backend.repository.administration.TaggableRepository;
import ma.almobadara.backend.repository.beneficiary.BeneficiaryRepository;
import ma.almobadara.backend.service.beneficiary.BeneficiaryService;
import ma.almobadara.backend.service.donor.MinioService;
import ma.almobadara.backend.service.family.KafalatService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class BeneficiaryMobileService {

    private final BeneficiaryRepository beneficiaryRepository;
    private final TaggableRepository taggableRepository;
    private final MinioService minioService;
    private final AssistantRepository assistantRepository;
    private final BeneficiaryService beneficiaryService;
    private final BeneficiaryMapper beneficiaryMapper;
    private final KafalatService kafalatService;

    /**
     * Get all beneficiaries for mobile with a fixed page size of 6
     * @param page Optional page number
     * @return Page of BeneficiaryMobileDTO
     */
    public Page<BeneficiaryMobileDTO> getAllBeneficiariesForMobile(Optional<Integer> page) {
        log.debug("Start service getAllBeneficiariesForMobile");

        // Create pageable with fixed size of 6
        int pageNumber = page.orElse(0);
        int pageSize = 6;
        Sort.Direction sortDirection = Sort.Direction.DESC;
        String sortBy = "modifiedAt";
        PageRequest pageRequest = PageRequest.of(pageNumber, pageSize, Sort.by(sortDirection, sortBy));

        // Get active beneficiaries directly from repository
        // Using status IDs 6, 10, 13 which represent active beneficiaries based on existing code
        Page<Beneficiary> beneficiaries = beneficiaryRepository.findBeneficiaryByArchivedIsFalse(pageRequest);

        // Convert to mobile DTOs
        List<BeneficiaryMobileDTO> mobileDTOs = beneficiaries.getContent().stream()
                .map(this::convertToMobileDTO)
                .collect(Collectors.toList());

        log.debug("End service getAllBeneficiariesForMobile");
        return new PageImpl<>(mobileDTOs, pageRequest, beneficiaries.getTotalElements());
    }

    /**
     * Get beneficiaries for mobile filtered by assistant ID with a fixed page size of 6
     * @param assistantId The ID of the assistant
     * @param page Optional page number
     * @return Page of BeneficiaryMobileDTO
     */
    public Page<BeneficiaryMobileDTO> getBeneficiariesByAssistantId(Long assistantId, Optional<Integer>page) {
        log.debug("Start service getBeneficiariesByAssistantId for assistant ID: {}", assistantId);

        // Create pageable with fixed size of 6
        int pageNumber = page.orElse(0);
        int pageSize = 6;
        Sort.Direction sortDirection = Sort.Direction.DESC;
        String sortBy = "modifiedAt";
        PageRequest pageRequest = PageRequest.of(pageNumber, pageSize, Sort.by(sortDirection, sortBy));

        // Get the assistant to find their zone
        Assistant assistant = assistantRepository.findById(assistantId)
                .orElseThrow(() -> new EntityNotFoundException("Assistant not found with ID: " + assistantId));

        // Check if assistant has a zone
        if (assistant.getZone() == null || assistant.getZone().getId() == null) {
            log.warn("Assistant with ID {} has no assigned zone", assistantId);
            return new PageImpl<>(Collections.emptyList(), pageRequest, 0);
        }

        Long zoneId = assistant.getZone().getId();
        log.debug("Found zone ID {} for assistant ID {}", zoneId, assistantId);

        // Get beneficiaries by zone ID
        Page<Beneficiary> beneficiaries = beneficiaryRepository.findByZoneId(zoneId, pageRequest);

        // Convert to mobile DTOs
        List<BeneficiaryMobileDTO> mobileDTOs = beneficiaries.getContent().stream()
                .map(this::convertToMobileDTO)
                .collect(Collectors.toList());

        log.debug("End service getBeneficiariesByAssistantId, found {} beneficiaries", beneficiaries.getTotalElements());
        return new PageImpl<>(mobileDTOs, pageRequest, beneficiaries.getTotalElements());
    }

    /**
     * Convert Beneficiary entity to BeneficiaryMobileDTO with only the required fields
     */
    private BeneficiaryMobileDTO convertToMobileDTO(Beneficiary beneficiary) {
        BeneficiaryMobileDTO dto = new BeneficiaryMobileDTO();

        // Set ID and independent status
        dto.setId(beneficiary.getId());
        dto.setIndependent(beneficiary.getIndependent());

        // Set person properties (fullName, email, phone)
        if (beneficiary.getPerson() != null) {
            // Combine first and last name for fullName
            String firstName = beneficiary.getPerson().getFirstName() != null ? beneficiary.getPerson().getFirstName() : "";
            String lastName = beneficiary.getPerson().getLastName() != null ? beneficiary.getPerson().getLastName() : "";
            dto.setFullName(firstName + " " + lastName);

            // Set email and phone
            dto.setPhoneNumber(beneficiary.getPerson().getPhoneNumber());
            dto.setEmail(beneficiary.getPerson().getEmail());

            // Set picture URL and try to load base64 if needed
            dto.setPictureUrl(beneficiary.getPerson().getPictureUrl());

            dto.setCoordinates(beneficiary.getCoordinates());

            // Try to load picture if URL exists but base64 is empty
            if (beneficiary.getPerson().getPictureUrl() != null ) {
                try {
                    byte[] imageData = minioService.ReadFromMinIO(beneficiary.getPerson().getPictureUrl(), null);
                    String base64Image = Base64.getEncoder().encodeToString(imageData);
                    dto.setPictureBase64(base64Image);
                } catch (Exception ex) {
                    log.error("Error loading image for beneficiary {}: {}", beneficiary.getId(), ex.getMessage());
                }
            }
        }

        // Set zone name
        if (beneficiary.getZone() != null) {
            dto.setZoneName(beneficiary.getZone().getName());
        }

        return dto;
    }

    /**
     * Get a beneficiary by ID using the same DTO as the main service
     * @param id The ID of the beneficiary
     * @return BeneficiaryDTO with full details
     * @throws TechnicalException if there's an error retrieving the beneficiary
     */
    public BeneficiaryDTO getBeneficiaryById(Long id) throws TechnicalException {
        log.debug("Start service getBeneficiaryById for ID: {}", id);

        // Use the existing service to get the full beneficiary details
        BeneficiaryDTO beneficiaryDTO = beneficiaryService.getBeneficiaryById(id);

        if (beneficiaryDTO == null) {
            throw new EntityNotFoundException("Beneficiary not found with ID: " + id);
        }

        log.debug("End service getBeneficiaryById for ID: {}", id);
        return beneficiaryDTO;
    }

    /**
     * Get detailed beneficiary information for mobile
     * @param id The ID of the beneficiary
     * @return BeneficiaryMobileDetailDTO with comprehensive details
     * @throws TechnicalException if there's an error retrieving the beneficiary
     */
    public BeneficiaryMobileDetailDTO getBeneficiaryDetailById(Long id) throws TechnicalException {
        log.debug("Start service getBeneficiaryDetailById for ID: {}", id);

        // Get the full beneficiary details from the main service
        BeneficiaryDTO beneficiaryDTO = beneficiaryService.getBeneficiaryById(id);

        if (beneficiaryDTO == null) {
            throw new EntityNotFoundException("Beneficiary not found with ID: " + id);
        }

        // Convert to mobile detail DTO
        BeneficiaryMobileDetailDTO detailDTO = convertToMobileDetailDTO(beneficiaryDTO);

        // Get kafalat information
        List<KafalatDTO> kafalats = kafalatService.getKafalatForBeneficiary(id);
        detailDTO.setKafalats(kafalats);

        log.debug("End service getBeneficiaryDetailById for ID: {}", id);
        return detailDTO;
    }

    /**
     * Convert BeneficiaryDTO to BeneficiaryMobileDetailDTO
     */
    private BeneficiaryMobileDetailDTO convertToMobileDetailDTO(BeneficiaryDTO beneficiaryDTO) {
        BeneficiaryMobileDetailDTO detailDTO = new BeneficiaryMobileDetailDTO();

        // Set basic information
        detailDTO.setId(beneficiaryDTO.getId());
        detailDTO.setCode(beneficiaryDTO.getCode());
        detailDTO.setIndependent(beneficiaryDTO.getIndependent());
        detailDTO.setArchived(beneficiaryDTO.getArchived());

        // Set status
        if (beneficiaryDTO.getBeneficiaryStatut() != null) {
            detailDTO.setStatus(beneficiaryDTO.getBeneficiaryStatut().getNameStatut());
        } else {
            detailDTO.setStatus(beneficiaryDTO.getStatut());
        }

        // Set zone information
        detailDTO.setZone(beneficiaryDTO.getZone());

        // Set person information
        if (beneficiaryDTO.getPerson() != null) {
            // Set full name
            String firstName = beneficiaryDTO.getPerson().getFirstName() != null ? beneficiaryDTO.getPerson().getFirstName() : "";
            String lastName = beneficiaryDTO.getPerson().getLastName() != null ? beneficiaryDTO.getPerson().getLastName() : "";
            detailDTO.setFullName(firstName + " " + lastName);

            // Set contact information
            detailDTO.setPhoneNumber(beneficiaryDTO.getPerson().getPhoneNumber());
            detailDTO.setEmail(beneficiaryDTO.getPerson().getEmail());
            detailDTO.setAddress(beneficiaryDTO.getPerson().getAddress());

            // Set personal information
            detailDTO.setBirthDate(beneficiaryDTO.getPerson().getBirthDate());
            detailDTO.setSex(beneficiaryDTO.getPerson().getSex());

            // Set picture
            detailDTO.setPictureUrl(beneficiaryDTO.getPerson().getPictureUrl());
            detailDTO.setPictureBase64(beneficiaryDTO.getPerson().getPictureBase64());

            // Set professional information
            detailDTO.setProfession(beneficiaryDTO.getPerson().getProfession());

            // Set category and type information
            detailDTO.setCategory(beneficiaryDTO.getPerson().getCategoryBeneficiary());
            detailDTO.setKafalatType(beneficiaryDTO.getPerson().getTypeKafalat());
        }

        // Set current education information
        detailDTO.setEducated(beneficiaryDTO.getPerson() != null ? beneficiaryDTO.getPerson().isEducated() : false);
        detailDTO.setSchoolName(beneficiaryDTO.getPerson() != null ? beneficiaryDTO.getPerson().getSchoolName() : null);
        detailDTO.setCurrentSchoolLevel(beneficiaryDTO.getPerson() != null ? beneficiaryDTO.getPerson().getSchoolLevel() : null);
        detailDTO.setEducationType(beneficiaryDTO.getPerson() != null ? beneficiaryDTO.getPerson().getSchoolLevelType() : null);

        // Set education history
        detailDTO.setEducationHistory(beneficiaryDTO.getEducations());

        // Set scholarships
        detailDTO.setScholarships(beneficiaryDTO.getScholarshipBeneficiaries());

        // Set health information
        detailDTO.setAllergies(beneficiaryDTO.getAllergies());
        detailDTO.setDiseases(beneficiaryDTO.getDiseases());
        detailDTO.setGlasses(beneficiaryDTO.getGlasses());
        detailDTO.setHandicaps(beneficiaryDTO.getHandicapped());

        // Set chronic diseases with treatment information
        detailDTO.setChronicDiseases(beneficiaryDTO.getDiseaseTreatments());

        return detailDTO;
    }
}
