package ma.almobadara.backend.service.mobile;

import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.clients.RefFeignClient;
import ma.almobadara.backend.dto.donor.DonorDTO;
import ma.almobadara.backend.dto.donor.DonorContactDTO;
import ma.almobadara.backend.dto.donor.DonorMoralDTO;
import ma.almobadara.backend.dto.donor.DonorPhysicalDTO;
import ma.almobadara.backend.dto.administration.LoginDTO;
import ma.almobadara.backend.dto.mobile.DonorMoralMobileDTO;
import ma.almobadara.backend.dto.mobile.DonorPhysiqueMobileDTO;
import ma.almobadara.backend.dto.mobile.ResetPasswordDTO;
import ma.almobadara.backend.dto.referentiel.ActivitySectorDTO;
import ma.almobadara.backend.dto.referentiel.CanalCommunicationDTO;
import ma.almobadara.backend.dto.referentiel.DonorContactFunctionDTO;
import ma.almobadara.backend.dto.referentiel.LanguageCommunicationDTO;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.mapper.DonorMapper;
import ma.almobadara.backend.mapper.DonorMoralMapper;
import ma.almobadara.backend.mapper.DonorPhysicalMapper;
import ma.almobadara.backend.model.donation.Donation;
import ma.almobadara.backend.model.donor.*;
import ma.almobadara.backend.model.donor.DonorContact;
import ma.almobadara.backend.model.donor.DonorMoral;
import ma.almobadara.backend.model.donor.DonorPhysical;
import ma.almobadara.backend.repository.donor.DonorContactRepository;
import ma.almobadara.backend.repository.donor.DonorMoralRepository;
import ma.almobadara.backend.repository.donor.DonorPhysicalRepository;
import ma.almobadara.backend.repository.donor.DonorRepository;
import ma.almobadara.backend.service.donor.DonorService;
import ma.almobadara.backend.service.donor.MinioService;
import ma.almobadara.backend.util.times.TimeWatch;
import org.apache.commons.io.FilenameUtils;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@RefreshScope
@Service
@RequiredArgsConstructor
@Slf4j
public class DonorMobileService {

    private final DonorPhysicalRepository donorPhysicalRepository;
    private final DonorMoralRepository donorMoralRepository;
    private final DonorMoralMapper donorMoralMapper;
    private final DonorPhysicalMapper donorPhysicalMapper;
    private final RefFeignClient refFeignClient;
    private final DonorService donorService;
    private final DonorContactRepository donorContactRepository;
    private final MinioService minioService;
    private final DonorRepository donorRepository;

    public DonorPhysiqueMobileDTO loginDonorPhysique(LoginDTO loginDTO) {
        Optional<DonorPhysical> donorPhysicalOptional = donorPhysicalRepository.findByEmailAndPassword(loginDTO.getEmail(), loginDTO.getPassword());
        if (donorPhysicalOptional.isPresent()) {
            DonorPhysical donorPhysical = donorPhysicalOptional.get();
            return getDonorPhysiqueById(donorPhysical.getId());
        } else {
            throw new RuntimeException("Invalid email or password");
        }
    }

    public DonorPhysiqueMobileDTO getDonorPhysiqueById(Long id){
        DonorPhysical donorPhysical=donorPhysicalRepository.findById(id).orElseThrow();
        Date registrationDate = null;

        Donor donor = donorRepository.findById(donorPhysical.getId()).isPresent() ? donorRepository.findById(donorPhysical.getId()).get() : null;
        if (donor!=null){
            if (donor.getCreatedAt()!=null){
                registrationDate = Date.from(donor.getCreatedAt().toLocalDate().atStartOfDay(ZoneId.systemDefault()).toInstant());
            }
        }

        DonorPhysiqueMobileDTO dto=donorPhysicalMapper.donorPhysicalModelToMobileDto(donorPhysical);
        if (dto.getProfession().getId() != null) {
            dto.setId(id);
            dto.getProfession().setName(refFeignClient.getMetProfession(dto.getProfession().getId()).getName());
        }
        dto.setRegistrationDate(registrationDate);

        if (donorPhysical.getPictureUrl() != null) {
            try {
                byte[] imageData = minioService.ReadFromMinIO(donorPhysical.getPictureUrl(), null);
                String base64Image = Base64.getEncoder().encodeToString(imageData);
                // Add the data URL prefix to the base64 string
                dto.setPicture64("data:image/png;base64," + base64Image);
            } catch (TechnicalException ex) {
                ex.printStackTrace();
            }
        }
        double amount = donorPhysical.getDonations()
                .stream()
                .mapToDouble(Donation::getValue)
                .sum();
        dto.setTotalDonations(amount);
        dto.setName(donorPhysical.getFirstName()+" "+donorPhysical.getLastName());
        dto.setNameAr(donorPhysical.getFirstNameAr()+" "+donorPhysical.getLastNameAr());
        dto.setId(id);
        return dto;
    }
    public DonorMoralMobileDTO loginDonorMoral(LoginDTO loginDTO) {
        Optional<DonorContact> donorContact = donorContactRepository.findByEmail(loginDTO.getEmail());
        if (donorContact.isPresent()) {
            DonorContact donor = donorContact.get();

            if(donor.getDonor().getPassword().equals(loginDTO.getPassword())){
                return getDonorMoralById(donor.getDonor().getId());
            }else{
                throw new RuntimeException("Invalid email or password");
            }

        } else {
            throw new RuntimeException("Invalid email or password");
        }
    }

    public DonorMoralMobileDTO getDonorMoralById(Long id){
        DonorMoral donorMoral=donorMoralRepository.findById(id).orElseThrow();
        Donor donor = donorRepository.findById(donorMoral.getId()).isPresent() ? donorRepository.findById(donorMoral.getId()).get() : null;
        DonorMoralMobileDTO dto=donorMoralMapper.donorMoralModelToMobilDto(donorMoral);

        if (donor!=null){
            if (donor.getCreatedAt() != null) {
                dto.setRegistrationDate(Date.from(donor.getCreatedAt().toLocalDate().atStartOfDay(ZoneId.systemDefault()).toInstant()));
            } else {
                dto.setRegistrationDate(null);
            }
        }

        if (donorMoral.getLogoUrl() != null) {

            try {
                // Retrieve the image from MinIO
                byte[] imageData = minioService.ReadFromMinIO(donorMoral.getLogoUrl(),null);
                String base64Image = Base64.getEncoder().encodeToString(imageData);
                // Set the base64 string to the DonorPhysicalDTO object
                dto.setLogo64(base64Image);
            } catch (TechnicalException ex) {
                ex.printStackTrace();
            }
        }
        dto.getDonorContacts().stream()
                .filter(donorContactDTO -> !donorContactDTO.getCanalCommunications().isEmpty())
                .flatMap(donorContactDTO -> donorContactDTO.getCanalCommunications().stream())
                .filter(canal -> canal.getId() != null)
                .forEach(canal -> {
                    CanalCommunicationDTO canalCommunicationDTO = refFeignClient.getMetCanalCommunication(canal.getId());
                    canal.setName(canalCommunicationDTO.getName());
                });

        dto.getDonorContacts().stream()
                .filter(donorContactDTO -> !donorContactDTO.getLanguageCommunications().isEmpty())
                .flatMap(donorContactDTO -> donorContactDTO.getLanguageCommunications().stream())
                .filter(langue -> langue.getId() != null)
                .forEach(langue -> {
                    LanguageCommunicationDTO languageCommunicationDTO = refFeignClient.getParLanguageCommunication(langue.getId());
                    langue.setName(languageCommunicationDTO.getName());
                });

        if (dto.getActivitySector() != null && dto.getActivitySector().getId() != null) {
            ActivitySectorDTO updatedActivitySector = refFeignClient.getMetActivitySector(dto.getActivitySector().getId());
            dto.getActivitySector().setName(updatedActivitySector.getName());
        }
        double amount = donorMoral.getDonations()
                .stream()
                .mapToDouble(Donation::getValue)
                .sum();
        dto.setTotalDonations(amount);
        dto.setAddress(donorMoral.getAddress());
        dto.getDonorContacts().stream()
                .filter(donorContactDTO -> donorContactDTO.getDonorContactFunction() != null)
                .forEach(donorContactDTO -> {
                    DonorContactFunctionDTO donorContactFunction = donorContactDTO.getDonorContactFunction();
                    if (donorContactFunction.getId() != null) {
                        DonorContactFunctionDTO contactFunctionDTO = refFeignClient.getMetDonorContactFunction(donorContactFunction.getId());
                        donorContactFunction.setName(contactFunctionDTO.getName());
                    }
                });
        dto.setId(id);

        return dto;
    }
    @Transactional
    public DonorPhysicalDTO updateDonorPhysique(DonorPhysicalDTO donorPhysicalDTO) throws TechnicalException {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service updateDonorPhysique {}", donorPhysicalDTO);

        if (donorPhysicalDTO == null || donorPhysicalDTO.getId() == null) {
            throw new TechnicalException("Donor ID must not be null for update operation.");
        }

        // Retrieve the existing donor
        Optional<DonorPhysical> donorPhysicalOptional = donorPhysicalRepository.findById(donorPhysicalDTO.getId());
        DonorPhysical existingDonorPhysical = donorPhysicalOptional.orElseThrow(() -> new TechnicalException("Donor Physical not found"));

        // Update only non-null fields
        if (donorPhysicalDTO.getFirstName() != null) {
            existingDonorPhysical.setFirstName(donorPhysicalDTO.getFirstName());
        }
        if (donorPhysicalDTO.getLastName() != null) {
            existingDonorPhysical.setLastName(donorPhysicalDTO.getLastName());
        }
        if (donorPhysicalDTO.getFirstNameAr() != null) {
            existingDonorPhysical.setFirstNameAr(donorPhysicalDTO.getFirstNameAr());
        }
        if (donorPhysicalDTO.getLastNameAr() != null) {
            existingDonorPhysical.setLastNameAr(donorPhysicalDTO.getLastNameAr());
        }
        if (donorPhysicalDTO.getEmail() != null) {
            existingDonorPhysical.setEmail(donorPhysicalDTO.getEmail());
        }
        if (donorPhysicalDTO.getPhoneNumber() != null) {
            existingDonorPhysical.setPhoneNumber(donorPhysicalDTO.getPhoneNumber());
        }

        // Save updated donor
        DonorPhysical updatedDonor = donorPhysicalRepository.save(existingDonorPhysical);

        log.debug("End service updateDonorPhysique with code: {}, took {}", updatedDonor.getCode(), watch.toMS());
        return donorPhysicalMapper.donorPhysicalModelToDto(updatedDonor);
    }

    @Transactional
    public DonorMoralDTO updateDonorMoral(DonorMoralDTO donorMoralDTO) throws TechnicalException {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service updateDonorMoral {}", donorMoralDTO);

        if (donorMoralDTO == null || donorMoralDTO.getId() == null) {
            throw new TechnicalException("Donor ID must not be null for update operation.");
        }

        // Retrieve the existing donor
        Optional<DonorMoral> donorMoralOptional = donorMoralRepository.findById(donorMoralDTO.getId());
        DonorMoral existingDonorMoral = donorMoralOptional.orElseThrow(() -> new TechnicalException("Donor Moral not found"));

        // Update only non-null fields for company information
        if (donorMoralDTO.getCompany() != null) {
            existingDonorMoral.setCompany(donorMoralDTO.getCompany());
        }
        if (donorMoralDTO.getShortCompany() != null) {
            existingDonorMoral.setShortCompany(donorMoralDTO.getShortCompany());
        }
        if (donorMoralDTO.getAddress() != null) {
            existingDonorMoral.setAddress(donorMoralDTO.getAddress());
        }
        if (donorMoralDTO.getAddressAr() != null) {
            existingDonorMoral.setAddressAr(donorMoralDTO.getAddressAr());
        }
        if (donorMoralDTO.getActivitySector() != null && donorMoralDTO.getActivitySector().getId() != null) {
            existingDonorMoral.setActivitySectorId(donorMoralDTO.getActivitySector().getId());
        }
        if (donorMoralDTO.getTypeDonorMoral() != null && donorMoralDTO.getTypeDonorMoral().getId() != null) {
            existingDonorMoral.setTypeDonorMoralId(donorMoralDTO.getTypeDonorMoral().getId());
        }

        // Save updated donor
        DonorMoral updatedDonor = donorMoralRepository.save(existingDonorMoral);

        log.debug("End service updateDonorMoral with code: {}, took {}", updatedDonor.getCode(), watch.toMS());
        return donorMoralMapper.donorMoralModelToDto(updatedDonor);
    }

    @Transactional
    public DonorContactDTO updateDonorContact(DonorContactDTO donorContactDTO) throws TechnicalException {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service updateDonorContact {}", donorContactDTO);

        if (donorContactDTO == null || donorContactDTO.getId() == null) {
            throw new TechnicalException("Contact ID must not be null for update operation.");
        }

        // Retrieve the existing contact
        Optional<DonorContact> donorContactOptional = donorContactRepository.findById(donorContactDTO.getId());
        DonorContact existingDonorContact = donorContactOptional.orElseThrow(() -> new TechnicalException("Donor Contact not found"));

        // Update only non-null fields
        if (donorContactDTO.getFirstName() != null) {
            existingDonorContact.setFirstName(donorContactDTO.getFirstName());
        }
        if (donorContactDTO.getLastName() != null) {
            existingDonorContact.setLastName(donorContactDTO.getLastName());
        }
        if (donorContactDTO.getFirstNameAr() != null) {
            existingDonorContact.setFirstNameAr(donorContactDTO.getFirstNameAr());
        }
        if (donorContactDTO.getLastNameAr() != null) {
            existingDonorContact.setLastNameAr(donorContactDTO.getLastNameAr());
        }
        if (donorContactDTO.getEmail() != null) {
            existingDonorContact.setEmail(donorContactDTO.getEmail());
        }
        if (donorContactDTO.getPhoneNumber() != null) {
            existingDonorContact.setPhoneNumber(donorContactDTO.getPhoneNumber());
        }
        if (donorContactDTO.getSex() != null) {
            existingDonorContact.setSex(donorContactDTO.getSex());
        }
        if (donorContactDTO.getBirthDate() != null) {
            existingDonorContact.setBirthDate(donorContactDTO.getBirthDate());
        }
        if (donorContactDTO.getMainContact() != null) {
            existingDonorContact.setMainContact(donorContactDTO.getMainContact());
        }
        if (donorContactDTO.getDonorContactFunction() != null && donorContactDTO.getDonorContactFunction().getId() != null) {
            existingDonorContact.setDonorContactFunctionId(donorContactDTO.getDonorContactFunction().getId());
        }

        // Save updated contact
        DonorContact updatedContact = donorContactRepository.save(existingDonorContact);

        log.debug("End service updateDonorContact, took {}", watch.toMS());
        return convertToDTO(updatedContact);
    }

    private DonorContactDTO convertToDTO(DonorContact donorContact) {
        DonorContactDTO dto = DonorContactDTO.builder()
                .id(donorContact.getId())
                .firstName(donorContact.getFirstName())
                .lastName(donorContact.getLastName())
                .firstNameAr(donorContact.getFirstNameAr())
                .lastNameAr(donorContact.getLastNameAr())
                .email(donorContact.getEmail())
                .phoneNumber(donorContact.getPhoneNumber())
                .sex(donorContact.getSex())
                .birthDate(donorContact.getBirthDate())
                .mainContact(donorContact.getMainContact())
                .build();

        if (donorContact.getDonorContactFunctionId() != null) {
            DonorContactFunctionDTO functionDTO = refFeignClient.getMetDonorContactFunction(donorContact.getDonorContactFunctionId());
            dto.setDonorContactFunction(functionDTO);
        }

        return dto;
    }

    /**
     * Reset password for a donor (physical or moral)
     * 
     * @param resetPasswordDTO DTO containing email, current password, and new password
     * @return true if password was reset successfully, false otherwise
     * @throws TechnicalException if there's a technical error
     */
    @Transactional
    public boolean resetPassword(ResetPasswordDTO resetPasswordDTO) throws TechnicalException {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service resetPassword for email: {}", resetPasswordDTO.getEmail());

        // First, try to find a physical donor with the given email
        DonorPhysical donorPhysical = donorPhysicalRepository.findByEmail(resetPasswordDTO.getEmail());

        if (donorPhysical != null) {
            // Handle physical donor password reset
            // Verify current password
            if (!donorPhysical.getPassword().equals(resetPasswordDTO.getPassword())) {
                log.debug("Invalid password for physical donor with email: {}", resetPasswordDTO.getEmail());
                return false;
            }

            // Update password
            donorPhysical.setPassword(resetPasswordDTO.getNewPassword());
            donorPhysicalRepository.save(donorPhysical);

            log.debug("End service resetPassword for physical donor with email: {}, took {}", resetPasswordDTO.getEmail(), watch.toMS());
            return true;
        } else {
            // Try to find a donor contact with the given email
            Optional<DonorContact> donorContactOptional = donorContactRepository.findByEmail(resetPasswordDTO.getEmail());

            if (donorContactOptional.isPresent()) {
                // Handle moral donor password reset
                DonorContact donorContact = donorContactOptional.get();
                DonorMoral donorMoral = donorContact.getDonor();

                if (donorMoral == null) {
                    throw new TechnicalException("Donor contact exists but is not associated with a moral donor");
                }

                // Verify current password
                if (!donorMoral.getPassword().equals(resetPasswordDTO.getPassword())) {
                    log.debug("Invalid password for moral donor associated with contact email: {}", resetPasswordDTO.getEmail());
                    return false;
                }

                // Update password
                donorMoral.setPassword(resetPasswordDTO.getNewPassword());
                donorMoralRepository.save(donorMoral);

                log.debug("End service resetPassword for moral donor with contact email: {}, took {}", resetPasswordDTO.getEmail(), watch.toMS());
                return true;
            } else {
                // No donor found with the given email
                log.debug("No donor found with email: {}", resetPasswordDTO.getEmail());
                return false;
            }
        }
    }
}
