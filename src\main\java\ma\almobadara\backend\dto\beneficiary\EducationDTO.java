package ma.almobadara.backend.dto.beneficiary;


import lombok.*;
import ma.almobadara.backend.dto.CityWithRegionAndCountryDTO;
import ma.almobadara.backend.dto.referentiel.*;

import java.time.Instant;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class EducationDTO {


	private Long id;

	private String educationType;

	private String schoolName;

	private String schoolNameAr;

	private Boolean succeed;

	private Double mark;

	private Instant createdAt;

	private CityDTO city;

	private CityWithRegionAndCountryDTO info;


	private SchoolYearDTO schoolYear;

	private SchoolLevelDTO schoolLevel;

	private HonorDTO honor;

	private MajorDTO major;

	private EducationSystemTypeDTO educationSystemType;
	private String studentNumber;
	private String comment;

	private BeneficiaryDTO beneficiary;

}
