package ma.almobadara.backend.dto.dashboard;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * DTO pour les statistiques des EPS
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EPSStatsDTO {

    private Map<String, Long> epsByStatus;

    private Map<String, Long> epsByRegion;

    private Map<String, Long> serviceCollectionByStatus;

}