package ma.almobadara.backend.config;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Annotation to mark methods that should use Azure authentication
 * instead of the default Auth0 authentication for mobile endpoints.
 * 
 * When applied to a method in a mobile controller, this annotation
 * will override the default Auth0 authentication and use Azure JWT
 * token validation instead.
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface AzureAuth {
}
