package ma.almobadara.backend.repository.administration;

import ma.almobadara.backend.model.administration.Eps;
import ma.almobadara.backend.model.administration.ResponsableEps;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ResponsableEpsRepository extends JpaRepository<ResponsableEps, Long> {

    List<ResponsableEps> findByIsDeletedFalse();

    List<ResponsableEps> findByEpsAndIsDeletedFalse(Eps eps);
}
