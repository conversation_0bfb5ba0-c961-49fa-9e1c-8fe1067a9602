package ma.almobadara.backend.repository.donor;

import ma.almobadara.backend.model.donor.DonorMoral;
import ma.almobadara.backend.model.donor.DonorPhysical;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface DonorMoralRepository extends JpaRepository<DonorMoral, Long> {
    @Query("SELECT e FROM DonorMoral e WHERE LOWER(e.company) LIKE LOWER(CONCAT('%', :query, '%')) OR LOWER(e.shortCompany) LIKE LOWER(CONCAT('%', :query, '%'))")
    Page<DonorMoral> searchDonor(@Param("query") String query, Pageable pageable);

    @Query("Select count(e) from DonorMoral e where e.deletedAt is null")
    long countByDeletedAtIsNull();

    @Query("SELECT d FROM DonorMoral d WHERE d.password IS NOT NULL")
    List<DonorMoral> findByPasswordIsNotNull();
}
