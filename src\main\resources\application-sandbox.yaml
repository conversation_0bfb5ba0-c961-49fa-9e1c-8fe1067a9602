info:
  component: AlMobadara PMGMT Backend

server:
  port: 8002


springdoc:
  packagesToScan: ma.almobadara.pmgmt
  api-docs:
    path: /api-docs

management:
  endpoints:
    web:
      exposure:
        include: "*"

azure-enterprise-app-properties:
  tenant-id: 1299c058-f4c7-42c6-9a4e-20f2cdb65448
  client-id: 2e2e7b55-4c96-475c-81ec-5d45ad853b74
  client-secret: ****************************************
  service-root-beta: https://graph.microsoft.com/v1.0/

spring:
  datasource:
    url: ********************************************************************
    username: postgres
    password: admin
    driver-class-name: org.postgresql.Driver
  jpa:
    database: POSTGRESQL
    show-sql: true
    hibernate:
      ddl-auto: validate

  security:
    oauth2:
      resourceserver:
        jwt:
          jwk-set-uri: ${oauth2.resourceserver.jwk-set-uri}
          issuer-uri: ${oauth2.resourceserver.issuer-uri}

  servlet:
    multipart:
      enabled: true
      file-size-threshold: 2KB
      maxFileSize: 10MB
      maxRequestSize: 10MB
  jackson:
    serialization:
      FAIL_ON_EMPTY_BEANS: false
  cache:
    type: redis
  redis:
    host: localhost
    port: 6379

  flyway:
    enabled: true
    baseline-on-migrate: true
    url: ********************************************************************
    user: postgres
    password: admin
    locations: classpath:db/migration
  sql:
    init:
      platform: POSTGRESQL

oauth2:
  resourceserver:
    tenant-id: ${azure-enterprise-app-properties.tenant-id}
    client-id: ${azure-enterprise-app-properties.client-id}
    jwk-set-uri: https://login.microsoftonline.com/${oauth2.resourceserver.tenant-id}/discovery/v2.0/keys
    issuer-uri: https://sts.windows.net/${oauth2.resourceserver.tenant-id}/


minio:
  access:
    name: minioadmin
    secret: minioadmin
  default:
    folder: /
  url: http://127.0.0.1:9000
  bucket: documents
  beneficiariesFolder: beneficiary/
  familiesFolder: families/
  campagnesFolder: campagnes/
  membersFolder: members/
  donorsFolder: doners/
  donationsFolder: donations/
  takenInChargesFolder: supports/
  assistantFolder: assistants/
  profilePicture:
    folder: profil_picture
    abv: pdp
  reportsFolder: reports
  picturesFolder: pictures
  picture:
    abv: IMG


ref:
  uri: http://*************:8080/api
  username: user
  password: user
