package ma.almobadara.backend.controller.family;

import com.fasterxml.jackson.databind.ObjectMapper;
import ma.almobadara.backend.config.Messages;
import ma.almobadara.backend.config.TestSecurityConfig;
import ma.almobadara.backend.controller.beneficiary.BeneficiaryController;
import ma.almobadara.backend.dto.family.AddedFamilyMemberResponse;
import ma.almobadara.backend.dto.family.FamilyMemberAddDTO;
import ma.almobadara.backend.dto.family.FamilyMemberListContainerDTO;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.service.beneficiary.BeneficiaryService;
import ma.almobadara.backend.service.family.FamilyMemberService;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;


@WebMvcTest(FamilyMemberController.class)
@ExtendWith(MockitoExtension.class)
@Import({TestSecurityConfig.class, Messages.class})
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class FamilyMemberControllerTest {
    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private FamilyMemberService familyMemberService;

    ObjectMapper objectMapper = new ObjectMapper();

    @Test
    @Order(1)
    void FamilyMemberController_addFamilyMember_Success() throws Exception {
        AddedFamilyMemberResponse addedFamilyMemberResponse = AddedFamilyMemberResponse
                .builder()
                .familyId(1L)
                .addressFamily("address")
                .code("code")
                .cinId(1L)
                .educated(false)
                .personId(1L)
                .build();
        FamilyMemberAddDTO familyMember = FamilyMemberAddDTO.builder()
                .id(1L)
                .newTutorId(123L)
                .hasNewTutor(true)
                .newTutorStartDate(new Date())
                .newTutorEndDate(new Date())
                .generalComment("comment")
                .familyId(10L)
                .code("XYZ123")
                .tutor(true)
                .firstName("hamza")
                .lastName("nachid")
                .email("<EMAIL>")
                .birthDate(new Date())
                .deceased(false)
                .educated(true)
                .schoolLevelType("ensa")
                .deathDate(new Date())
                .identityCode("ID1234")
                .schoolLevelId(2L)
                .cityId(100L)
                .zoneId(1L)
                .build();

        when(familyMemberService.addFamilyMember(any(FamilyMemberAddDTO.class))).thenReturn(addedFamilyMemberResponse);

        mockMvc.perform(post("/family-member")
                        .flashAttr("familyMemberDTO", familyMember)
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(addedFamilyMemberResponse)));
    }

    @Test
    @Order(2)
    void FamilyMemberController_addListOfFamilyMembers_Success() throws Exception {
        long familyMemberId = 1L;
        List<FamilyMemberAddDTO> familyMembers = new ArrayList<>();
        familyMembers.add(FamilyMemberAddDTO.builder()
                .id(1L)
                .newTutorId(123L)
                .hasNewTutor(true)
                .newTutorStartDate(new Date())
                .newTutorEndDate(new Date())
                .generalComment("comment")
                .familyId(10L)
                .code("XYZ123")
                .tutor(true)
                .firstName("hamza")
                .lastName("nachid")
                .email("<EMAIL>")
                .birthDate(new Date())
                .deceased(false)
                .educated(true)
                .schoolLevelType("Ensa")
                .deathDate(new Date())
                .identityCode("ID1234")
                .schoolLevelId(2L)
                .cityId(100L)
                .zoneId(1L)
                .build());

        familyMembers.add(FamilyMemberAddDTO.builder()
                .id(2L)
                .newTutorId(124L)
                .hasNewTutor(false)
                .newTutorStartDate(new Date())
                .newTutorEndDate(new Date())
                .generalComment("comment")
                .familyId(11L)
                .code("ABC456")
                .tutor(false)
                .firstName("karimi")
                .lastName("amine")
                .email("<EMAIL>")
                .birthDate(new Date())
                .deceased(true)
                .educated(false)
                .schoolLevelType("Middle School")
                .deathDate(new Date())
                .identityCode("ID5678")
                .schoolLevelId(3L)
                .cityId(101L)
                .zoneId(2L)
                .build());
        FamilyMemberListContainerDTO familyMemberListContainerDTO=FamilyMemberListContainerDTO.builder().familyMembers(familyMembers).build();

        when(familyMemberService.addListOfFamilyMembers(any())).thenReturn(familyMemberId);

        mockMvc.perform(post("/family-member/members")
                        .flashAttr("container", familyMemberListContainerDTO)
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(familyMemberId)));
    }

    @Test
    @Order(3)
    void FamilyMemberController_addListOfFamilyMembers_InternalServerError() throws Exception {
        List<FamilyMemberAddDTO> familyMembers = new ArrayList<>();
        familyMembers.add(FamilyMemberAddDTO.builder()
                .id(1L)
                .newTutorId(123L)
                .hasNewTutor(true)
                .newTutorStartDate(new Date())
                .newTutorEndDate(new Date())
                .generalComment("comment")
                .familyId(10L)
                .code("XYZ123")
                .tutor(true)
                .firstName("hamza")
                .lastName("nachid")
                .email("<EMAIL>")
                .birthDate(new Date())
                .deceased(false)
                .educated(true)
                .schoolLevelType("Ensa")
                .deathDate(new Date())
                .identityCode("ID1234")
                .schoolLevelId(2L)
                .cityId(100L)
                .zoneId(1L)
                .build());

        familyMembers.add(FamilyMemberAddDTO.builder()
                .id(2L)
                .newTutorId(124L)
                .hasNewTutor(false)
                .newTutorStartDate(new Date())
                .newTutorEndDate(new Date())
                .generalComment("comment")
                .familyId(11L)
                .code("ABC456")
                .tutor(false)
                .firstName("karimi")
                .lastName("amine")
                .email("<EMAIL>")
                .birthDate(new Date())
                .deceased(true)
                .educated(false)
                .schoolLevelType("Middle School")
                .deathDate(new Date())
                .identityCode("ID5678")
                .schoolLevelId(3L)
                .cityId(101L)
                .zoneId(2L)
                .build());
        FamilyMemberListContainerDTO familyMemberListContainerDTO=FamilyMemberListContainerDTO.builder().familyMembers(familyMembers).build();

        doThrow(new TechnicalException("You can't add ")).when(familyMemberService).addListOfFamilyMembers(any());

        mockMvc.perform(post("/family-member/members")
                        .flashAttr("container", familyMemberListContainerDTO)
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isInternalServerError());
    }
}