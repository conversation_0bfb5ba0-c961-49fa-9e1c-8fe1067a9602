package ma.almobadara.backend.dto.mobile;

import lombok.*;

import java.time.Instant;
import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class FamilyMobileDTO {
    private Long id;
    private String code;
    private String familyName;      // Name of the family
    private String tutorName;       // Name of the tutor
    private Integer memberCount;    // Number of family members
    private Instant registrationDate;  // Date of inscription
}
