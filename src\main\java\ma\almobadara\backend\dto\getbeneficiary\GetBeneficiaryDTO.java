package ma.almobadara.backend.dto.getbeneficiary;


import lombok.*;
import ma.almobadara.backend.dto.beneficiary.*;
import ma.almobadara.backend.dto.referentiel.AllergiesDTO;
import ma.almobadara.backend.dto.referentiel.DiseasesDTO;
import org.springframework.hateoas.RepresentationModel;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Set;

@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class GetBeneficiaryDTO extends RepresentationModel<GetEducationDTO> implements Serializable {

	private static final long serialVersionUID = -5312180104203028749L;

	private Long id;

	private Boolean independent;

	private Boolean epsResident;

	private Boolean useGlasses;

	private String handicapCause;

	private Double handicapCost;

	private Boolean archived;

	//private  Boolean handicapped;

	private Long handicapTypeId;

	private String handicapType;

	private String pictureBase64;

	private String firstName;

	private String lastName;

	private String firstNameAr;

	private String lastNameAr;

	private String sex;

	private String email;

	private String phoneNumber;

	private String address;

	private String addressAr;

	private Date birthDate;

	private Long cityId;

	private String city;

	private String region;

	private String country;

	private String identityCode;

	private Long typeIdentityId;

	private String typeIdentity;

	private Long professionId;

	private Long accommodationTypeId;
	private Long accommodationNatureId;

	private Long typeKafalatId;
	private List<Long> typePriseEnChargeIds;

	private Long categoryBeneficiaryId;

	private String profession;

	//Get Beneficiary DTOs

	private Set<GetServiceDTO> services;

	private Set<GetEducationDTO> educations;

	//One to many relations

	private List<NoteBeneficiaryDTO> notes;

	private List<DocumentBeneficiaryDTO> documents;

	private Set<AllergiesDTO> allergies;

	private Set<DiseasesDTO> diseases;

	private Set<EpsResidentDTO> epsResidents;

	private Set<DiseaseTreatmentDTO> diseaseTreatments;

	private Set<ScholarshipBeneficiaryDTO> scholarshipBeneficiaries;

}
