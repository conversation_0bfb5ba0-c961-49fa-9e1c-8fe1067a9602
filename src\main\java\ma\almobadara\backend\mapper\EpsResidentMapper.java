package ma.almobadara.backend.mapper;

import ma.almobadara.backend.model.beneficiary.EpsResident;
import ma.almobadara.backend.dto.beneficiary.EpsResidentDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface EpsResidentMapper {


	@Mapping(source = "epsId", target = "eps.id")
	EpsResidentDTO epsResidentToEpsResidentDTO(EpsResident epsResident);

	Iterable<EpsResidentDTO> epsResidentToEpsResidentDTO(Iterable<EpsResident> epsResidents);


	@Mapping(source = "eps.id", target = "epsId")
	EpsResident epsResidentDTOToEpsResident(EpsResidentDTO epsResidentDTO);

	Iterable<EpsResident> epsResidentDTOToEpsResident(Iterable<EpsResidentDTO> epsResidentDTOS);
}
