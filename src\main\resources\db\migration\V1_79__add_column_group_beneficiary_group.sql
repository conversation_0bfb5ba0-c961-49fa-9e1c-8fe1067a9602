CREATE TABLE IF NOT EXISTS public.beneficiary_adhocgroup (
                                                             beneficiary_id BIGINT NOT NULL,
                                                             beneficiary_ad_hoc_group_id BIGINT NOT NULL,
                                                             PRIMARY KEY (beneficiary_id, beneficiary_ad_hoc_group_id),
    FOREIG<PERSON> KEY (beneficiary_id) REFERENCES public.beneficiary(id) ON DELETE CASCADE,
    FOREIGN KEY (beneficiary_ad_hoc_group_id) REFERENCES public.beneficiary_ad_hoc_group(id) ON DELETE CASCADE
    );

