package ma.almobadara.backend.dto.takenInCharge;

import lombok.*;
import ma.almobadara.backend.dto.service.ServicesDTO;

import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class OperationListDto {
    private Long id;
    private String code;
    private double amount;
    private double managementFees;
    private Date planningDate;
    private Date executionDate;
    private Date closureDate;
    private String comment;
    private Long donorId;
    private Long beneficiaryId;
    private Long takenInChargeId;
    private String donorName;
    private boolean reserved;
    private String type;
    private String beneficiaryName;
    private String statusOperation;
    private ServicesDTO services;
    private String status;

}

