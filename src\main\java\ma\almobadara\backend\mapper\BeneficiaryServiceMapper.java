package ma.almobadara.backend.mapper;

import ma.almobadara.backend.model.beneficiary.BeneficiaryService;
import ma.almobadara.backend.dto.beneficiary.BeneficiaryServiceDTO;
import ma.almobadara.backend.dto.getbeneficiary.GetServiceDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface BeneficiaryServiceMapper {

	@Mapping(source = "serviceId", target = "service.id")
	@Mapping(source = "statusId", target = "status.id")
	BeneficiaryServiceDTO beneficiaryServiceToBeneficiaryServiceDTO(BeneficiaryService beneficiaryService);

	Iterable<BeneficiaryServiceDTO> beneficiaryServiceToBeneficiaryServiceDTO(Iterable<BeneficiaryService> beneficiaryServices);

	@Mapping(source = "service.id", target = "serviceId")
	@Mapping(source = "status.id", target = "statusId")
	BeneficiaryService beneficiaryServiceDTOToBeneficiaryService(BeneficiaryServiceDTO beneficiaryServiceDTO);

	Iterable<BeneficiaryService> beneficiaryServiceDTOToBeneficiaryService(Iterable<BeneficiaryServiceDTO> beneficiaryServiceDTOS);

	GetServiceDTO beneficiaryServiceToGetServiceDTO(BeneficiaryService beneficiaryService);

	Iterable<GetServiceDTO> beneficiaryServiceToGetServiceDTO(Iterable<BeneficiaryService> beneficiaryServices);

}
