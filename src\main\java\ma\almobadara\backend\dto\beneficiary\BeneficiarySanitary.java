package ma.almobadara.backend.dto.beneficiary;

import com.google.gson.Gson;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import ma.almobadara.backend.model.beneficiary.BeneficiaryHandicap;
import ma.almobadara.backend.model.beneficiary.DiseaseTreatment;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Getter
@Setter
@Builder
@AllArgsConstructor
public class BeneficiarySanitary {

    Long beneficiaryId;
    GlassesDto glasses;
    List<DiseaseTreatmentBeneficiaryAddDto> diseaseTreatments;
    List<HandicapBeneficiaryAddDto> handicapped;
    List<AllergyBeneficiaryAddDto> beneficiaryAllergies;
    List<DiseaseBeneficiaryAddDto> beneficiaryDiseases;



}
