package ma.almobadara.backend.repository.reclamation;

import ma.almobadara.backend.model.reclamation.Reclamation;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ReclamationRepository extends JpaRepository<Reclamation, Long> {
    
    // Find all reclamations by donor ID
    List<Reclamation> findByDonorId(Long donorId);
    
    // Find all reclamations by donor ID with pagination
    Page<Reclamation> findByDonorId(Long donorId, Pageable pageable);
    
    // Find reclamations by title containing a keyword
    Page<Reclamation> findByTitleContainingIgnoreCase(String keyword, Pageable pageable);
}
