package ma.almobadara.backend.util.page;

import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

public class SortDtoTest {

    @Test
    void testConstructor() {
        SortDto sortDto = new SortDto("ASC", "property");
        assertThat(sortDto.getDirection()).isEqualTo("ASC");
        assertThat(sortDto.getProperty()).isEqualTo("property");
        assertThat(sortDto.isIgnoreCase()).isFalse();
        assertThat(sortDto.getNullHandling()).isEqualTo("NATIVE");
        assertThat(sortDto.isAscending()).isTrue();
        assertThat(sortDto.isDescending()).isFalse();
    }

    @Test
    void testConstructorWithDescending() {
        SortDto sortDto = new SortDto("DESC", "property");
        assertThat(sortDto.getDirection()).isEqualTo("DESC");
        assertThat(sortDto.getProperty()).isEqualTo("property");
        assertThat(sortDto.isIgnoreCase()).isFalse();
        assertThat(sortDto.getNullHandling()).isEqualTo("NATIVE");
        assertThat(sortDto.isAscending()).isFalse();
        assertThat(sortDto.isDescending()).isTrue();
    }

    @Test
    void testSetterMethods() {
        SortDto sortDto = new SortDto("ASC", "property");
        sortDto.setDirection("DESC");
        sortDto.setProperty("anotherProperty");
        sortDto.setIgnoreCase(true);
        sortDto.setNullHandling("NULLS_LAST");
        sortDto.setAscending(false);
        sortDto.setDescending(true);
        assertThat(sortDto.getDirection()).isEqualTo("DESC");
        assertThat(sortDto.getProperty()).isEqualTo("anotherProperty");
        assertThat(sortDto.isIgnoreCase()).isTrue();
        assertThat(sortDto.getNullHandling()).isEqualTo("NULLS_LAST");
        assertThat(sortDto.isAscending()).isFalse();
        assertThat(sortDto.isDescending()).isTrue();
    }

    @Test
    void testInvalidDirection() {
        SortDto sortDto = new SortDto("INVALID", "property");
        assertThat(sortDto.getDirection()).isEqualTo("INVALID");
        assertThat(sortDto.isAscending()).isFalse();
        assertThat(sortDto.isDescending()).isFalse();
    }

    @Test
    void testNullProperty() {
        SortDto sortDto = new SortDto("ASC", null);
        assertThat(sortDto.getProperty()).isNull();
    }

    @Test
    void testNullDirection() {
        SortDto sortDto = new SortDto(null, "property");
        assertThat(sortDto.getDirection()).isNull();
        assertThat(sortDto.isAscending()).isFalse();
        assertThat(sortDto.isDescending()).isFalse();
    }
}
