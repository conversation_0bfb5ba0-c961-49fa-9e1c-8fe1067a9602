package ma.almobadara.backend.dto.donor;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import ma.almobadara.backend.dto.administration.TagDTO;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class DonorAnonymeDTO extends DonorDTO {

    private Long id;
    private String name;

    private String description; // Description of the anonymous donor
    private String type = "Anonyme"; // e.g., "anonyme"

    private List<TagDTO> tags;

}
