package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.beneficiary.DocumentBeneficiaryDTO;
import ma.almobadara.backend.model.beneficiary.DocumentBeneficiary;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface BeneficiaryDocumentMapper {

	@Mapping(target = "beneficiaryId", ignore = true)
	@Mapping(source = "document.id", target = "document.id")
    DocumentBeneficiaryDTO beneficiaryDocumentToBeneficiaryDocumentDTO(DocumentBeneficiary document);

	@Mapping(target = "beneficiary", ignore = true)
	@Mapping(source = "document.id", target = "document.id")
	Iterable<DocumentBeneficiaryDTO> beneficiaryDocumentToBeneficiaryDocumentDTO(Iterable<DocumentBeneficiary> documents);

	DocumentBeneficiary beneficiaryDocumentDTOToBeneficiaryDocument(DocumentBeneficiaryDTO documentDTO);

	Iterable<DocumentBeneficiary> beneficiaryDocumentDTOToBeneficiaryDocument(Iterable<DocumentBeneficiaryDTO> documentDTOS);
}
