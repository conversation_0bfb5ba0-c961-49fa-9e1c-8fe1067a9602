package ma.almobadara.backend.dto.caisse;

import lombok.*;
import ma.almobadara.backend.dto.donation.DonationDTO;
import ma.almobadara.backend.dto.donor.DonorDTO;
import ma.almobadara.backend.dto.referentiel.CurrencyDTO;
import ma.almobadara.backend.dto.referentiel.TypePriseEnChargeDTO;

import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class BudgetLineRequest {

    private Long id;
    private String code;
    private Double amount;
    private String comment;
    private Double valueCurrency;
    private CurrencyDTO currency;
    private TypePriseEnChargeDTO typePriseEnCharge;
    private LocalDateTime createdAt;
    private Boolean executed;
    private String type;
    private DonationDTO donation;

}
