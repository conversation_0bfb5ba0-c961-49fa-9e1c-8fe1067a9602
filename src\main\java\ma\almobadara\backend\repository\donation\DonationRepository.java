package ma.almobadara.backend.repository.donation;

import ma.almobadara.backend.dto.dashboard.NombreDonateurByMonth;
import ma.almobadara.backend.dto.dashboard.TotalDonation;
import ma.almobadara.backend.model.beneficiary.Beneficiary;
import ma.almobadara.backend.model.donation.Donation;
import ma.almobadara.backend.model.donor.Donor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Repository
public interface DonationRepository extends JpaRepository<Donation, Long> {

    List<Donation> findByDonor(Donor donor);


    @Query("select sum(value) from Donation d where d.archived = false or d.archived is null")
    Double sumByArchivedIsFalseOrArchivedNull();


    @Modifying
    @Transactional
    @Query("UPDATE Donation d SET d.donor.id = :newDonorId WHERE d.donor.id = :oldDonorId")
    void updateDonorId(@Param("oldDonorId") Long oldDonorId, @Param("newDonorId") Long newDonorId);

    Donation findByCode(String code);
    List<Donation> findByDonorIdAndArchivedIsFalseOrArchivedNull(Long donorId);
    @Query(value = "SELECT \n" + "new ma.almobadara.backend.dto.dashboard.TotalDonation(" +
            "    EXTRACT(MONTH FROM d.receptionDate) AS month," +
            "    EXTRACT(YEAR FROM d.receptionDate) AS year," +
            "    SUM(CASE WHEN d.type = 'Financière' THEN d.value ELSE 0 END) AS financialDonations," +
            "    SUM(CASE WHEN d.type = 'Nature' THEN d.value ELSE 0 END)AS natureDonations, " +
            "SUM(d.value)AS totalDonations)" +
            "FROM \n" +
            "    Donation d \n" +
            "WHERE \n" +
            "     d.receptionDate >= :maxDate\n" +
            "GROUP BY \n" +
            "    EXTRACT(month FROM d.receptionDate),\n" +
            "    EXTRACT(year FROM d.receptionDate)\n" +
            "ORDER BY \n" +
            "    year DESC, month DESC")
    List<TotalDonation> getTotalDonationsByMonthAndType(LocalDateTime maxDate);

    Page<Donation> findAllByArchivedIsNullOrderByCreatedAtDesc(Pageable pageable);

    //findDonationsByDonorId
    List<Donation> findByDonorId(Long donorId);
    
    @Query(value = "SELECT d.type as donationType, SUM(d.value) as totalAmount " +
            "FROM Donation d " +
            "WHERE d.archived = false OR d.archived IS NULL " +
            "GROUP BY d.type")
    List<Object[]> getDonationsByDonationType();

    @Query(value = "SELECT d.canal_donation_id, SUM(d.value) as totalAmount " +
            "FROM Donation d " +
            "WHERE d.archived = false OR d.archived IS NULL " +
            "GROUP BY d.canal_donation_id", nativeQuery = true)
    List<Object[]> getDonationsByChannel();
    
    @Query(value = "SELECT s.name as serviceName, SUM(bl.amount) as totalAmount " +
            "FROM budget_line bl " +
            "JOIN services s ON bl.service_id = s.id " +
            "JOIN donation d ON bl.donation_id = d.id " +
            "WHERE d.archived = false OR d.archived IS NULL " +
            "GROUP BY s.name", nativeQuery = true)
    List<Object[]> getDonationsByService();
    
    @Query(value = "SELECT TO_CHAR(d.reception_date, 'YYYY-MM') as month, SUM(d.value) as totalAmount " +
            "FROM Donation d " +
            "WHERE d.reception_date >= CURRENT_DATE - INTERVAL '12 months' " +
            "AND (d.archived = false OR d.archived IS NULL) " +
            "GROUP BY TO_CHAR(d.reception_date, 'YYYY-MM') " +
            "ORDER BY month ASC", nativeQuery = true)
    List<Object[]> getDonationsByMonth();
}
