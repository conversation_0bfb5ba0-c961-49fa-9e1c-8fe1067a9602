package ma.almobadara.backend.repository.communs;

import ma.almobadara.backend.model.donor.Donor;
import ma.almobadara.backend.model.donor.NoteDonor;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
public interface NoteDonorRepository extends JpaRepository<NoteDonor,Long> {

    Iterable<NoteDonor> findByDonor(Donor donor);

    List<NoteDonor> findByNoteId(Long id);

    List<NoteDonor> findByDonorId(Long donorId);


    @Modifying
    @Transactional

    @Query("UPDATE NoteDonor n SET n.donor.id = :newDonorId WHERE n.donor.id = :oldDonorId")
    void updateDonorId(@Param("oldDonorId") Long oldDonorId, @Param("newDonorId") Long newDonorId);

}
