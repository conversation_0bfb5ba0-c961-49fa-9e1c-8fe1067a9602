package ma.almobadara.backend.dto.dashboard;

import lombok.*;

import java.util.List;
import java.util.Map;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class MonthlyServiceDataDTO {

    private Integer month;
    private String monthName;
    private Integer year;
    private List<ActiveKafalatesByMonthAndTypeDTO> services;
    private Map<Integer, Long> cumulativeTotalActiveKafalatesByService;

}
