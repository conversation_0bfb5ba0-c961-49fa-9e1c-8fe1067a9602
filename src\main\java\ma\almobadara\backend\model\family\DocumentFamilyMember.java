package ma.almobadara.backend.model.family;


import jakarta.persistence.*;
import lombok.*;
import ma.almobadara.backend.model.communs.Document;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@IdClass(DocumentFamilyMemberId.class)
public class DocumentFamilyMember {

    @Id
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "family_member_id")
    private FamilyMember familyMember;

    @Id
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "document_id")
    private Document document;

}
