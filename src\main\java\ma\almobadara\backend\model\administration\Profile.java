package ma.almobadara.backend.model.administration;

import jakarta.persistence.*;
import lombok.*;
import ma.almobadara.backend.enumeration.Functionality;
import ma.almobadara.backend.enumeration.Module;

import java.util.*;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class Profile {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String nameProfile;
    private boolean isDeleted;
    @OneToMany(mappedBy = "profile")
    private Set<CacheAdUser> users;


    @ElementCollection
    @CollectionTable(name = "user_profile_module_functionality")
    @MapKeyEnumerated(EnumType.STRING)
    @Column(name = "functionality", columnDefinition = "smallint[] DEFAULT '{}'")
    private Map<Module, ArrayList<Functionality>> moduleFunctionalities;
}
