package ma.almobadara.backend.controller.mobile;

import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.mobile.ActionMobileDTO;
import ma.almobadara.backend.service.mobile.ActionMobileService;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;

import static ma.almobadara.backend.service.administration.CacheAdUserService.logUserInfo;

@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping("/mobile/action")
@CrossOrigin(origins = "*")
public class ActionMobileController {

    private final ActionMobileService actionMobileService;

    /**
     * Get all actions for mobile with pagination
     * @param page Optional page number (defaults to 0)
     * @return Page of ActionMobileDTO
     */
    @GetMapping("/all")
    public ResponseEntity<Page<ActionMobileDTO>> getAllActions(
            @RequestParam(required = false) Optional<Integer> page) {
        
        logUserInfo("getAllActionsForMobile", "page: " + page.orElse(0));
        
        try {
            Page<ActionMobileDTO> actions = actionMobileService.getAllActionsForMobile(page);
            log.info("End resource getAllActionsForMobile. Retrieved actions: {}, OK", actions.getTotalElements());
            return new ResponseEntity<>(actions, HttpStatus.OK);
        } catch (Exception e) {
            log.error("End resource getAllActionsForMobile. KO: {}", e.getMessage());
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    /**
     * Get actions for mobile filtered by user ID with pagination
     * @param userId The ID of the user
     * @param page Optional page number (defaults to 0)
     * @return Page of ActionMobileDTO
     */
    @GetMapping("/by-user/{userId}")
    public ResponseEntity<Page<ActionMobileDTO>> getActionsByUserId(
            @PathVariable Long userId,
            @RequestParam(required = false) Optional<Integer> page) {
        
        logUserInfo("getActionsByUserId", "userId: " + userId + ", page: " + page.orElse(0));
        
        try {
            Page<ActionMobileDTO> actions = actionMobileService.getActionsByUserId(userId, page);
            log.info("End resource getActionsByUserId. Retrieved actions: {}, OK", actions.getTotalElements());
            return new ResponseEntity<>(actions, HttpStatus.OK);
        } catch (EntityNotFoundException e) {
            log.error("End resource getActionsByUserId. KO: {}", e.getMessage());
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        } catch (Exception e) {
            log.error("End resource getActionsByUserId. KO: {}", e.getMessage());
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    /**
     * Get actions for mobile filtered by assistant ID with pagination
     * @param assistantId The ID of the assistant
     * @param page Optional page number (defaults to 0)
     * @return Page of ActionMobileDTO
     */
    @GetMapping("/by-assistant/{assistantId}")
    public ResponseEntity<Page<ActionMobileDTO>> getActionsByAssistantId(
            @PathVariable Long assistantId,
            @RequestParam(required = false) Optional<Integer> page) {
        
        logUserInfo("getActionsByAssistantId", "assistantId: " + assistantId + ", page: " + page.orElse(0));
        
        try {
            Page<ActionMobileDTO> actions = actionMobileService.getActionsByAssistantId(assistantId, page);
            log.info("End resource getActionsByAssistantId. Retrieved actions: {}, OK", actions.getTotalElements());
            return new ResponseEntity<>(actions, HttpStatus.OK);
        } catch (EntityNotFoundException e) {
            log.error("End resource getActionsByAssistantId. KO: {}", e.getMessage());
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        } catch (Exception e) {
            log.error("End resource getActionsByAssistantId. KO: {}", e.getMessage());
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
