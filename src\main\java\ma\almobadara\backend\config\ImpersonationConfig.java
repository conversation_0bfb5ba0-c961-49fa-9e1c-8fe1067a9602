package ma.almobadara.backend.config;

import ma.almobadara.backend.service.administration.CacheAdUserService;
import ma.almobadara.backend.service.administration.TokenImpersonationService;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

import jakarta.annotation.PostConstruct;

@Configuration
public class ImpersonationConfig {

    private final CacheAdUserService cacheAdUserService;
    private final TokenImpersonationService tokenImpersonationService;

    public ImpersonationConfig(
            CacheAdUserService cacheAdUserService,
            @Lazy TokenImpersonationService tokenImpersonationService) {
        this.cacheAdUserService = cacheAdUserService;
        this.tokenImpersonationService = tokenImpersonationService;
    }

    @PostConstruct
    public void init() {
        // Set up the circular dependency
        cacheAdUserService.setTokenImpersonationService(tokenImpersonationService);
    }
}
