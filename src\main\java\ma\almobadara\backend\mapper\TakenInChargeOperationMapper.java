package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.takenInCharge.TakenInChargeDonorDTO;
import ma.almobadara.backend.dto.takenInCharge.TakenInChargeOperationDTO;
import ma.almobadara.backend.model.takenInCharge.TakenInChargeDonor;
import ma.almobadara.backend.model.takenInCharge.TakenInChargeOperation;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface TakenInChargeOperationMapper {

	TakenInChargeOperationDTO takenInChargeOperationToTakenInChargeOperationDTO(TakenInChargeOperation takenInChargeOperation);

	Iterable<TakenInChargeOperationDTO> takenInChargeOperationToTakenInChargeOperationDTO(Iterable<TakenInChargeOperation> takenInChargeOperation);

	TakenInChargeOperation takenInChargeOperationDTOToTakenInChargeOperation(TakenInChargeOperationDTO takenInChargeOperationDTO);

	Iterable<TakenInChargeOperation> takenInChargeOperationDTOToTakenInChargeOperation(Iterable<TakenInChargeOperationDTO> takenInChargeOperationDTO);

	@Mapping(target = "donor", ignore = true)
	@Mapping(target = "takenInCharge", ignore = true)
	@Mapping(target = "takenInChargeOperations", ignore = true)
	TakenInChargeDonorDTO takenInChargeDonor(TakenInChargeDonor takenInChargeDonor);

}
