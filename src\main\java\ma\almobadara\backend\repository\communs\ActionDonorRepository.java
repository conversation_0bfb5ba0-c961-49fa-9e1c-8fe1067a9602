package ma.almobadara.backend.repository.communs;

import ma.almobadara.backend.model.donor.ActionDonor;
import ma.almobadara.backend.model.donor.Donor;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
public interface ActionDonorRepository extends JpaRepository<ActionDonor,Long> {

    Iterable<ActionDonor> findByDonor(Donor donor);

    List<ActionDonor> findByDonorId(Long donorId);

    List<ActionDonor> findByActionId(Long id);
    @Modifying
    @Transactional

    @Query("UPDATE ActionDonor n SET n.donor.id = :newDonorId WHERE n.donor.id = :oldDonorId")
    void updateDonorId(@Param("oldDonorId") Long oldDonorId, @Param("newDonorId") Long newDonorId);
}
