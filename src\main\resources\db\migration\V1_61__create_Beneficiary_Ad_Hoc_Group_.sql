-- Création de la table BeneficiaryAdHocGroup
CREATE TABLE beneficiary_ad_hoc_group (
                                          id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
                                          code VARCHAR(255) UNIQUE NOT NULL,
                                          name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
                                          city_id BIGINT,
                                          full_name_contact <PERSON><PERSON>HAR(255),
                                          phone_number <PERSON><PERSON><PERSON><PERSON>(50),
                                          comment TEXT,
                                          status VARCHAR(50),
                                          created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                                          updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                                          is_deleted BOOLEAN DEFAULT FALSE
);

-- Création de la table de jointure pour la relation ManyToMany entre aide_complementaire et beneficiary_ad_hoc_group
CREATE TABLE aide_complementaire_beneficiary_ad_hoc_group (
                                                              aide_complementaire_id BIGINT NOT NULL,
                                                              beneficiary_ad_hoc_group_id BIGINT NOT NULL,  -- Correction ici
                                                              PRIMARY KEY (aide_complementaire_id, beneficiary_ad_hoc_group_id),
                                                              FOREI<PERSON><PERSON> KEY (aide_complementaire_id) REFERENCES aide_complementaire(id) ON DELETE CASCADE,
                                                              FOREIGN KEY (beneficiary_ad_hoc_group_id) REFERENCES beneficiary_ad_hoc_group(id) ON DELETE CASCADE
);



-- Index pour améliorer les performances des recherches par code
CREATE INDEX idx_beneficiary_ad_hoc_group_code ON beneficiary_ad_hoc_group(code);

