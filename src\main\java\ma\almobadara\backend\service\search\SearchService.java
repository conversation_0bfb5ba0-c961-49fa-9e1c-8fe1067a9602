package ma.almobadara.backend.service.search;

import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import ma.almobadara.backend.config.SearchSpecification;
import ma.almobadara.backend.dto.SearchResultDTO;
import ma.almobadara.backend.model.administration.Eps;
import ma.almobadara.backend.model.administration.ServiceCollectEps;
import ma.almobadara.backend.model.aideComplemenatire.AideComplementaire;
import ma.almobadara.backend.model.beneficiary.Beneficiary;
import ma.almobadara.backend.model.donation.BudgetLine;
import ma.almobadara.backend.model.donation.Donation;
import ma.almobadara.backend.model.donor.Donor;
import ma.almobadara.backend.model.donor.DonorAnonyme;
import ma.almobadara.backend.model.donor.DonorMoral;
import ma.almobadara.backend.model.donor.DonorPhysical;
import ma.almobadara.backend.model.family.Family;
import ma.almobadara.backend.model.family.FamilyMember;
import ma.almobadara.backend.repository.administration.EpsRepository;
import ma.almobadara.backend.repository.administration.ServiceCollectEpsRepository;
import ma.almobadara.backend.repository.aideComplemenatire.AideComplementaireRepository;
import ma.almobadara.backend.repository.beneficiary.BeneficiaryRepository;
import ma.almobadara.backend.repository.donation.BudgetLineRepository;
import ma.almobadara.backend.repository.donation.DonationRepository;
import ma.almobadara.backend.repository.donor.DonorAnonymeRepository;
import ma.almobadara.backend.repository.donor.DonorMoralRepository;
import ma.almobadara.backend.repository.donor.DonorPhysicalRepository;
import ma.almobadara.backend.repository.donor.DonorRepository;
import ma.almobadara.backend.repository.family.FamilyMemberRepository;
import ma.almobadara.backend.repository.family.FamilyRepository;
import ma.almobadara.backend.repository.services.ServicesRepository;
import ma.almobadara.backend.service.beneficiary.BeneficiaryService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public class SearchService {
    private final DonorPhysicalRepository donorPhysicalRepository;
    private final DonorMoralRepository donorMoralRepository;
    private final DonorAnonymeRepository donorAnonymeRepository;
    private final BeneficiaryRepository beneficiaryRepository;
    private final EpsRepository epsRepository;
    private final ServiceCollectEpsRepository serviceCollectEpsRepository;
    private final FamilyMemberRepository familyMemberRepository;
    private final DonationRepository donationRepository;
    private final AideComplementaireRepository aideComplementaireRepository;
    private final BudgetLineRepository budgetLineRepository;
    private final EntityManager entityManager;

    public SearchService(DonorRepository donateurRepository, DonorPhysicalRepository donorPhysicalRepository, DonorMoralRepository donorMoralRepository, DonorAnonymeRepository donorAnonymeRepository, BeneficiaryRepository beneficiaryRepository, EpsRepository epsRepository, ServiceCollectEpsRepository serviceCollectEpsRepository, FamilyMemberRepository familyMemberRepository, DonationRepository donationRepository, AideComplementaireRepository aideComplementaireRepository, BudgetLineRepository budgetLineRepository, EntityManager entityManager) {
        this.donorPhysicalRepository = donorPhysicalRepository;
        this.donorMoralRepository = donorMoralRepository;
        this.donorAnonymeRepository = donorAnonymeRepository;
        this.beneficiaryRepository = beneficiaryRepository;
        this.epsRepository = epsRepository;
        this.serviceCollectEpsRepository = serviceCollectEpsRepository;
        this.familyMemberRepository = familyMemberRepository;
        this.donationRepository = donationRepository;
        this.aideComplementaireRepository = aideComplementaireRepository;
        this.budgetLineRepository = budgetLineRepository;
        this.entityManager=entityManager;
    }

    public List<SearchResultDTO> search(String query, int page, int size) {
        PageRequest pageable = PageRequest.of(page, size);
        List<SearchResultDTO> results = new ArrayList<>();

        // 🔹 Search in DonorPhysical Table
        Page<DonorPhysical> donorPhysicalList = donorPhysicalRepository.searchDonor(query, pageable);
        if (donorPhysicalList != null) {
            donorPhysicalList.forEach(e -> results.add(new SearchResultDTO("Donor", Map.of(
                    "id", e.getId(),
                    "firstName", e.getFirstName() != null ? e.getFirstName() : "-",
                    "lastName", e.getLastName() != null ? e.getLastName() : "-",
                    "email", e.getEmail() != null ? e.getEmail() : "-",
                    "phoneNumber", e.getPhoneNumber() != null ? e.getPhoneNumber() : "-",
                    "type", e.getType() != null ? e.getType() : "-"
            ))));
        }

        // 🔹 Search in DonorMoral Table
        Page<DonorMoral> donorDonorList = donorMoralRepository.searchDonor(query, pageable);
        if (donorDonorList != null) {
            donorDonorList.forEach(e -> results.add(new SearchResultDTO("Donor", Map.of(
                    "id", e.getId(),
                    "company", e.getCompany() != null ? e.getCompany() : "-",
                    "shortCompany", e.getShortCompany() != null ? e.getShortCompany() : "-",
                    "type", e.getType() != null ? e.getType() : "-"
            ))));
        }

        // 🔹 Search in DonorMoral Table
        Page<DonorAnonyme> donorAnonymes = donorAnonymeRepository.searchDonor(query, pageable);
        if (donorAnonymes != null) {
            donorAnonymes.forEach(e -> results.add(new SearchResultDTO("Donor", Map.of(
                    "id", e.getId(),
                    "name", e.getName() != null ? e.getName() : "-",
                    "description", e.getDescription() != null ? e.getDescription() : "-",
                    "type", e.getType() != null ? e.getType() : "-"
            ))));
        }

        // 🔹 Search in EPS Table
        Page<Eps> epsList = epsRepository.searchEps(query, pageable);
        if (epsList != null) {
            epsList.forEach(e -> results.add(new SearchResultDTO("Eps", Map.of(
                    "id", e.getId(),
                    "name", e.getName() != null ? e.getName() : "-",
                    "comment", e.getComment() != null ? e.getComment() : "-"
            ))));
        }

        // 🔹 Search in ServiceCollectEps Table
        Page<ServiceCollectEps> serviceCollectEps = serviceCollectEpsRepository.searchServiceCollectEps(query, pageable);
        if (epsList != null) {
            serviceCollectEps.forEach(e -> results.add(new SearchResultDTO("ServiceCollectEps", Map.of(
                    "id", e.getId(),
                    "name", e.getNom() != null ? e.getNom() : "-",
                    "commentaire", e.getCommentaire() != null ? e.getCommentaire() : "-",
                    "service", e.getService() != null ? e.getService().getName() : "-"
            ))));
        }

        // 🔹 Search in Beneficiary Table
        Page<Beneficiary> beneficiaries = beneficiaryRepository.searchBeneficiaries(query, pageable);
        if (beneficiaries != null) {
            beneficiaries.forEach(b -> results.add(new SearchResultDTO("Beneficiary", Map.of(
                    "id", b.getId(),
                    "firstName", b.getPerson() != null && b.getPerson().getFirstName() != null ? b.getPerson().getFirstName() : "-",
                    "lastName", b.getPerson() != null && b.getPerson().getLastName() != null ? b.getPerson().getLastName() : "-",
                    "phoneNumber", b.getPerson() != null && b.getPerson().getPhoneNumber() != null ? b.getPerson().getPhoneNumber() : "-",
                    "email", b.getPerson() != null && b.getPerson().getEmail() != null ? b.getPerson().getEmail() : "-",
                    "code", b.getCode() != null ? b.getCode() : "-"
            ))));
        }

        // 🔹 Search in Family Members Table
        Page<FamilyMember> families = familyMemberRepository.searchMembers(query, pageable);
        if (families != null) {
            families.forEach(f -> results.add(new SearchResultDTO("familyMember", Map.of(
                    "id", f.getId(),
                    "firstName", f.getPerson() != null && f.getPerson().getFirstName() != null ? f.getPerson().getFirstName() : "-",
                    "lastName", f.getPerson() != null && f.getPerson().getLastName() != null ? f.getPerson().getLastName() : "-",
                    "phoneNumber", f.getPerson() != null && f.getPerson().getPhoneNumber() != null ? f.getPerson().getPhoneNumber() : "-",
                    "email", f.getPerson() != null && f.getPerson().getEmail() != null ? f.getPerson().getEmail() : "-",
                    "code", f.getCode() != null ? f.getCode() : "-",
                    "isTutore", f.isTutor(),
                    "familyId", f.getFamily() != null ? f.getFamily().getId() : 0L
            ))));
        }

        // 🔹 Search in Budget Line (Donations)
        Page<BudgetLine> budgetLines = budgetLineRepository.searchDonation(query, pageable);
        if (budgetLines != null) {
            budgetLines.forEach(b -> results.add(new SearchResultDTO("donation", Map.of(
                    "id", b.getId(),
                    "code", b.getCode() != null ? b.getCode() : "-",
                    "service", b.getService() != null && b.getService().getName() != null ? b.getService().getName() : "-",
                    "donationCode", b.getDonation() != null && b.getDonation().getCode() != null ? b.getDonation().getCode() : "-",
                    "donationId", b.getDonation() != null ? b.getDonation().getId() : 0L
            ))));
        }

        // 🔹 Search in Aide Complementaire
        Page<AideComplementaire> aideComplementaires = aideComplementaireRepository.searchAideComplementaire(query, pageable);
        if (aideComplementaires != null) {
            aideComplementaires.forEach(a -> results.add(new SearchResultDTO("aideComplementaire", Map.of(
                    "id", a.getId(),
                    "name", a.getName() != null ? a.getName() : "-",
                    "service", a.getService() != null && a.getService().getName() != null ? a.getService().getName() : "-",
                    "slogan", a.getSlogan() != null ? a.getSlogan() : "-",
                    "comment", a.getCommentaire() != null ? a.getCommentaire() : "-"
            ))));
        }

        return results;
    }

}
