package ma.almobadara.backend.dto.family;

import lombok.*;
import ma.almobadara.backend.dto.administration.SousZoneDTO;
import ma.almobadara.backend.dto.administration.TagDTO;
import ma.almobadara.backend.dto.administration.ZoneDTO;
import ma.almobadara.backend.dto.referentiel.AccommodationNatureDTO;
import ma.almobadara.backend.dto.referentiel.AccommodationTypeDTO;
import ma.almobadara.backend.dto.referentiel.CityDTO;

import java.io.Serializable;
import java.time.Instant;
import java.util.List;

@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class FamilyDTO implements Serializable {

	protected Instant createdAt;

	private Long id;

	private String code;
	private String tutorName;
	private String tutorCIN ;

	private List<FamilyMemberDTO> familyMembers;

	private String addressFamily;
	private String addressFamilyAr;
	private String generalCommentFamily;
	private String phoneNumberFamily;
	private CityDTO city;
	private AccommodationTypeDTO accommodationType;
	private AccommodationNatureDTO accommodationNature;
	private ZoneDTO zoneDTO;
	private SousZoneDTO sousZoneDTO;

	private List<TagDTO> tags;
}
