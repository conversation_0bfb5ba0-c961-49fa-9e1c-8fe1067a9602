package ma.almobadara.backend.dto.donor;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import ma.almobadara.backend.dto.administration.CacheAdUserDTO;
import ma.almobadara.backend.dto.administration.TagDTO;
import ma.almobadara.backend.dto.referentiel.ActivitySectorDTO;
import ma.almobadara.backend.dto.referentiel.TypeDonorMoralDTO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Getter
@Setter
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class DonorMoralDTO extends DonorDTO {

	private String company;

	private String shortCompany;

	private String type = "Moral";
	private Long anonymeId;

	private CacheAdUserDTO createdBy;
	private String logoUrl;

	@JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
	private MultipartFile logo;

	private String logo64;

	private List<DonorContactDTO> donorContacts;

	private ActivitySectorDTO activitySector;

	private TypeDonorMoralDTO typeDonorMoral;

	private List<TagDTO> tags;

}
