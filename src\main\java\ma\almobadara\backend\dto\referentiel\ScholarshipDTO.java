package ma.almobadara.backend.dto.referentiel;

import lombok.*;
import org.springframework.hateoas.RepresentationModel;

import java.io.Serializable;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class ScholarshipDTO extends RepresentationModel<ScholarshipDTO> implements Serializable {

	private static final long serialVersionUID = 2941011348378156306L;

	private Long id;
	private String code;
	private String name;
	private String nameAr;
	private String nameEn;

}
