package ma.almobadara.backend.dto.aideComplemenatire;

import lombok.*;
import ma.almobadara.backend.model.service.Services;

import java.time.LocalDateTime;
import java.util.Date;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@Builder
public class AideComplementaireBeneficiareDto {
    private Long id;
    private String name;
    private Services services;
    private Double amount;
    private String statut;
    private LocalDateTime execuionDate;
}
