package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.donor.CorrespondenceDto;
import ma.almobadara.backend.model.donor.Correspondence;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface CorrespondenceMapper {

    @Mapping(source = "donor.id", target = "donorId")
    @Mapping(source = "canalCommunicationId", target = "canalCommunication.id")
    CorrespondenceDto correspondenceModelToDto(Correspondence correspondence);

    Iterable<CorrespondenceDto> correspondenceListModelToDto(Iterable<Correspondence> correspondences);

    @Mapping(source = "donorId", target = "donor.id")
    @Mapping(source = "canalCommunication.id", target = "canalCommunicationId")
    Correspondence correspondenceDtoModelToModel(CorrespondenceDto correspondenceDto);

    Iterable<Correspondence> correspondenceListDtoToModal(Iterable<CorrespondenceDto> correspondencesDto);

}
