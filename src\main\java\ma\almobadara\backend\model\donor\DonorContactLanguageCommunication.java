package ma.almobadara.backend.model.donor;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
public class DonorContactLanguageCommunication {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private Long languageCommunicationId;
    @ManyToOne
    @JoinColumn(name = "donor_contact_id")
    private DonorContact donorContact;

}
