CREATE TABLE rapport (
                         id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
                         archived <PERSON><PERSON><PERSON><PERSON><PERSON>,
                         code_rapport VARCHAR(255) UNIQUE NOT NULL,
                         number_rapport BIGINT,
                         date_rapport timestamp NULL,
                         reference VARCHAR(255),
                         release VARCHAR(255),

                         donor_id BIGINT NOT NULL,
                         donor_first_name <PERSON><PERSON><PERSON><PERSON>(255),
                         donor_last_name <PERSON><PERSON><PERSON><PERSON>(255),
                         donor_first_name_ar VA<PERSON>HA<PERSON>(255),
                         donor_last_name_ar VARCHAR(255),

                         beneficiary_id BIGINT NOT NULL,
                         person_beneficiary_id BIGINT,
                         beneficiary_code VA<PERSON>HAR(255),
                         beneficiary_first_name <PERSON><PERSON><PERSON><PERSON>(255),
                         beneficiary_last_name <PERSON><PERSON><PERSON><PERSON>(255),
                         beneficiary_first_name_ar VARCHA<PERSON>(255),
                         beneficiary_last_name_ar VARCHAR(255),
                         beneficiary_address VARCHAR(255),
                         beneficiary_address_ar VARCHAR(255),
                         beneficiary_birth_date timestamp NULL,
                         city_id BIGINT,
                         school_level_id BIGINT,
                         school_name VARCHA<PERSON>(255),
                         result DOUBLE PRECISION,

                         family_id BIGINT,
                         person_family_id BIGINT,
                         family_relationship_id BIGINT,
                         number_of_family_member BIGINT,
                         tutor_first_name VARCHAR(255),
                         tutor_last_name VARCHAR(255),
                         tutor_first_name_ar VARCHAR(255),
                         tutor_last_name_ar VARCHAR(255),
                         profession_id BIGINT,
                         phone_number VARCHAR(255),
                         accommodation_type_id BIGINT,

                         activity_educational_fr VARCHAR(500),
                         activity_educational_en VARCHAR(500),
                         activity_educational_ar VARCHAR(500),
                         social_service_fr VARCHAR(500),
                         social_service_en VARCHAR(500),
                         social_service_ar VARCHAR(500),
                         recommendation_fr VARCHAR(500),
                         recommendation_en VARCHAR(500),
                         recommendation_ar VARCHAR(500),

                         created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                         modified_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,

                         CONSTRAINT fk_donor FOREIGN KEY (donor_id)
                             REFERENCES donor(id) ON DELETE CASCADE,
                         CONSTRAINT fk_beneficiary FOREIGN KEY (beneficiary_id)
                             REFERENCES beneficiary(id) ON DELETE CASCADE,
                         CONSTRAINT fk_family FOREIGN KEY (family_id)
                             REFERENCES family(id) ON DELETE SET NULL
);

CREATE TABLE rapport_picture (
                                 id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
                                 url VARCHAR(500),
                                 description VARCHAR(500),
                                 rapport_id BIGINT NOT NULL,

                                 created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                                 modified_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,

                                 CONSTRAINT fk_rapport FOREIGN KEY (rapport_id)
                                     REFERENCES rapport(id) ON DELETE CASCADE
);
