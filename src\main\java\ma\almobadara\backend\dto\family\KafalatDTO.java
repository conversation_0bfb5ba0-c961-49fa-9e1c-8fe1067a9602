package ma.almobadara.backend.dto.family;

import lombok.*;

import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class KafalatDTO {
    
    private Long id;                  // ID of the TakenInCharge
    private String code;              // Code of the TakenInCharge
    private String serviceName;       // Name of the service
    private String status;            // Status of the TakenInCharge
    private String beneficiaryName;   // Name of the beneficiary
    private String donorName;         // Name of the donor
    private Date startDate;           // Start date of the TakenInCharge
    private Date endDate;             // End date of the TakenInCharge
    
    // Operation summaries
    private int operationCount;       // Total number of operations
    private double plannedAmount;     // Sum of planned operation amounts
    private double executedAmount;    // Sum of executed operation amounts
    private double closedAmount;      // Sum of closed operation amounts
}
