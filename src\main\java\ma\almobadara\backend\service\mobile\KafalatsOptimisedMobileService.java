package ma.almobadara.backend.service.mobile;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.mobile.KafalatsOptimisedMobileDTO;
import ma.almobadara.backend.dto.mobile.KafalatsOptimisedMobileDTO.OperationSummaryDTO;
import ma.almobadara.backend.dto.mobile.KafalatsOptimisedMobileDTO.BeneficiaryMobileDto;
import ma.almobadara.backend.dto.takenInCharge.TakenInChargeBeneficiaryDTO;
import ma.almobadara.backend.dto.takenInCharge.TakenInChargeDonorDTO;
import ma.almobadara.backend.dto.takenInCharge.TakenInChargeOperationDTO;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class KafalatsOptimisedMobileService {
    private final KafalatMobileService kafalatMobileService;

    public List<KafalatsOptimisedMobileDTO> getOptimisedKafalatsByDonorId(Long donorId) {
        List<TakenInChargeDonorDTO> fullKafalats = kafalatMobileService.getTakeInChargeByDonorId(donorId);

        return fullKafalats.stream().map(kafalat -> {
            List<OperationSummaryDTO> operations = kafalat.getTakenInChargeOperations().stream()
                .map(op -> mapToOperationSummary(op))
                .collect(Collectors.toList());
            BeneficiaryMobileDto beneficiaryMobileDto=mapToBeneficiarySummary(kafalat.getTakenInCharge().getTakenInChargeBeneficiaries().get(0));
            Double sumExecuted = kafalat.getTakenInChargeOperations().stream()
                    .filter(op -> "Exécuté".equals(op.getStatus())||"Clôturé".equals(op.getStatus()))
                    .map(op-> op.getAmount())
                    .filter(Objects::nonNull)
                    .reduce(0.0, Double::sum);

            Double sumPlanified = kafalat.getTakenInChargeOperations().stream()
                    .map(TakenInChargeOperationDTO::getAmount)
                    .filter(Objects::nonNull)
                    .reduce(0.0, Double::sum);

            Double sumReserved = kafalat.getTakenInChargeOperations().stream()
                    .filter(op -> "Réservé".equals(op.getStatus()))
                    .map(TakenInChargeOperationDTO::getAmount)
                    .filter(Objects::nonNull)
                    .reduce(0.0, Double::sum);


            return KafalatsOptimisedMobileDTO.builder()
                .id(kafalat.getId())
                    .sumReserved(sumReserved)
                    .sumExecuted(sumExecuted)
                    .beneficiaryMobileDto(beneficiaryMobileDto)
                    .sumPlanned(sumPlanified)
                .status(kafalat.getTakenInCharge().getStatus())
                .takenInChargeId(kafalat.getTakenInCharge().getId())
                    .lastExecutionDate(kafalat.getTakenInChargeOperations().stream()
                            .map(operation-> operation.getExecutionDate())
                            .filter(Objects::nonNull)
                            .max(Comparator.naturalOrder())
                            .orElse(null))
                .beneficiaryName(Optional.ofNullable(kafalat.getTakenInCharge())
                    .map(tc -> tc.getTakenInChargeBeneficiaries())
                    .filter(beneficiaries -> !beneficiaries.isEmpty())
                    .map(beneficiaries -> beneficiaries.get(0))
                    .map(tcb -> tcb.getBeneficiary())
                    .map(beneficiary -> beneficiary.getPerson())
                    .map(person -> person.getLastName())
                    .orElse(null))
                .serviceName(Optional.ofNullable(kafalat.getTakenInCharge())
                    .map(tc -> tc.getServices())
                    .map(services -> services.getName())
                    .orElse(null))
                .serviceType(Optional.ofNullable(kafalat.getTakenInCharge())
                    .map(tc -> tc.getServices())
                    .map(services -> services.getName())
                    .orElse(null))
                .donorBalance(kafalat.getDonorBalance())
                .keepAnonymous(kafalat.getKeepanonymous())
                .operations(operations)
                .build();
        }).collect(Collectors.toList());
    }

    private OperationSummaryDTO mapToOperationSummary(TakenInChargeOperationDTO operation) {
        return OperationSummaryDTO.builder()
            .id(operation.getId())
            .code(operation.getCode())
            .amount(operation.getAmount())
            .status(operation.getStatus())
                .executionDate(operation.getExecutionDate())
                .plannedDate(operation.getPlanningDate())
            .type(operation.getStatus())
            .build();
    }
    private BeneficiaryMobileDto mapToBeneficiarySummary(TakenInChargeBeneficiaryDTO beneficiary){
        if (beneficiary.getBeneficiary() == null || beneficiary.getBeneficiary().getPerson() == null) {
            return null; // Handle the case where beneficiary or person is null
        }
        beneficiary.getBeneficiary().getPerson().getPicture();
        return BeneficiaryMobileDto.builder()
                .id(beneficiary.getId())
                .sex(beneficiary.getBeneficiary().getPerson().getSex())
                .birthDate(beneficiary.getBeneficiary().getPerson().getBirthDate())
                .email(beneficiary.getBeneficiary().getPerson().getEmail())
                .phone(beneficiary.getBeneficiary().getPerson().getPhoneNumber())
                .name(beneficiary.getBeneficiary().getPerson().getLastName()+" "+beneficiary.getBeneficiary().getPerson().getFirstName())
                .zoneDTOLight(beneficiary.getBeneficiary().getZone())
                .picture(beneficiary.getBeneficiary().getPerson().getPicture())
                .pictureUrl(beneficiary.getBeneficiary().getPerson().getPictureUrl())
                .pictureBase64(beneficiary.getBeneficiary().getPerson().getPictureBase64())
                .profession(beneficiary.getBeneficiary().getPerson().getProfession()!=null?beneficiary.getBeneficiary().getPerson().getProfession().getName():null)
                .type(beneficiary.getBeneficiary().getIndependent()?"Indépendant":"Membre de famille")
                .build();
    }
}