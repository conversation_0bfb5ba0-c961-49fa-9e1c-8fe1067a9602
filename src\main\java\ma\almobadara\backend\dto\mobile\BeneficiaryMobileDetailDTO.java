package ma.almobadara.backend.dto.mobile;

import lombok.*;
import ma.almobadara.backend.dto.beneficiary.*;
import ma.almobadara.backend.dto.family.KafalatDTO;
import ma.almobadara.backend.dto.referentiel.*;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * Detailed DTO for beneficiary information in mobile applications
 */
@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class BeneficiaryMobileDetailDTO {
    
    // Basic information
    private Long id;
    private String code;
    private String fullName;
    private String status;
    private Boolean independent;
    private Boolean archived;
    
    // Contact information
    private String phoneNumber;
    private String email;
    private String address;
    
    // Personal information
    private Date birthDate;
    private String sex;
    private String pictureUrl;
    private String pictureBase64;
    
    // Professional information
    private ProfessionDTO profession;
    
    // Category and type information
    private CategoryBeneficiaryDTO category;
    private TypeKafalatDTO kafalatType;
    
    // Zone information
    private SmallZoneDTO zone;
    
    // Current education
    private Boolean educated;
    private String schoolName;
    private SchoolLevelDTO currentSchoolLevel;
    private String educationType;
    
    // Education history
    private Set<EducationDTO> educationHistory;
    
    // Scholarships
    private Set<ScholarshipBeneficiaryDTO> scholarships;
    
    // Health information
    private List<AllergiesDTO> allergies;
    private List<DiseasesDTO> diseases;
    private GlassesDto glasses;
    private Set<BeneficiaryHandicapDto> handicaps;
    
    // Chronic diseases with treatment information
    private Set<DiseaseTreatmentDTO> chronicDiseases;
    
    // Kafalat information
    private List<KafalatDTO> kafalats;
    private String coordinates;
}
