package ma.almobadara.backend.model.beneficiary;

import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToOne;
import lombok.*;
import ma.almobadara.backend.model.donor.Donor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Entity
@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class HistoryRapport extends BaseEntity{

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date dateCommunicated;
    private String communicatedBy;
    @OneToOne
    @JoinColumn(name = "beneficiary_id")
    private Beneficiary beneficiary;
    @OneToOne
    @JoinColumn(name = "rapport_id")
    private Rapport rapport;
    @OneToOne
    @JoinColumn(name = "donor_id")
    private Donor donor;
    @OneToOne
    @JoinColumn(name = "agenda_rapport_id")
    private AgendaRapport agendaRapport;

}
