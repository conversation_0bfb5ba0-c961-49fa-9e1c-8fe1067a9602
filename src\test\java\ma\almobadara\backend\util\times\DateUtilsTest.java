package ma.almobadara.backend.util.times;

import org.junit.jupiter.api.Test;
import static org.assertj.core.api.Assertions.*;

public class DateUtilsTest {

    @Test
    void getMonthName_validMonth_returnsCorrectMonthName() {
        assertThat(DateUtils.getMonthName(1)).isEqualTo("janvier");
        assertThat(DateUtils.getMonthName(2)).isEqualTo("février");
        assertThat(DateUtils.getMonthName(3)).isEqualTo("mars");
        assertThat(DateUtils.getMonthName(12)).isEqualTo("décembre");
    }

    @Test
    void getMonthName_invalidMonth_throwsArrayIndexOutOfBoundsException() {
        assertThatThrownBy(() -> DateUtils.getMonthName(0)).isInstanceOf(ArrayIndexOutOfBoundsException.class);
        assertThatThrownBy(() -> DateUtils.getMonthName(13)).isInstanceOf(ArrayIndexOutOfBoundsException.class);
    }

    @Test
    void getMonthName_nullMonth_throwsNoException() {
        // This test case is not applicable as the method doesn't handle null values.  It would require modification of the DateUtils class to handle nulls.
    }
}
