package ma.almobadara.backend.service.mobile;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.service.ServicesDTO;
import ma.almobadara.backend.dto.takenInCharge.TakenInChargeDTO;
import ma.almobadara.backend.dto.takenInCharge.TakenInChargeDonorDTO;
import ma.almobadara.backend.mapper.ServicesMapper;
import ma.almobadara.backend.mapper.TakenInChargeDonorMapper;
import ma.almobadara.backend.mapper.TakenInChargeMapper;
import ma.almobadara.backend.model.takenInCharge.TakenInCharge;
import ma.almobadara.backend.model.takenInCharge.TakenInChargeDonor;
import ma.almobadara.backend.repository.donor.DonorMoralRepository;
import ma.almobadara.backend.repository.donor.TakenInChargeDonorRepository;
import ma.almobadara.backend.repository.takenInCharge.TakenInChargeRepository;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@RefreshScope
@Service
@RequiredArgsConstructor
@Slf4j
public class KafalatMobileService {
    private final TakenInChargeDonorRepository takenInChargeDonorRepository;
    private final TakenInChargeDonorMapper takenInChargeDonorMapper;
    private final TakenInChargeRepository takenInChargeRepository;
    private final TakenInChargeMapper takenInChargeMapper;
    private final ServicesMapper servicesMapper;
    public List<TakenInChargeDonorDTO> getTakeInChargeByDonorId(Long id){
        List<TakenInChargeDonor> takenInChargeDonor=takenInChargeDonorRepository.findByDonorId(id);
        return takenInChargeDonor.stream().map(takenInChargeDonor1 -> {
            TakenInChargeDonorDTO takenInChargeDonorDTO=takenInChargeDonorMapper.takenInChargeDonorToTakenInChargeDonorDTO(takenInChargeDonor1);
            takenInChargeDonorDTO.setDonor(null);
            TakenInCharge takenInCharge=takenInChargeRepository.getTakenInChargeById(takenInChargeDonor1.getTakenInCharge().getId());
            TakenInChargeDTO takenInChargeDTO=takenInChargeMapper.takenInChargeToTakenInChargeDTO(takenInCharge);
            takenInChargeDonorDTO.setTakenInCharge(takenInChargeDTO);
            if(takenInChargeDonorDTO.getTakenInCharge()!=null  && takenInChargeDonorDTO.getTakenInCharge().getId()!=null) {
                Optional<TakenInCharge> takenInCharge1 = takenInChargeRepository.findById(takenInChargeDonorDTO.getTakenInCharge().getId());
                if (takenInCharge1.isPresent()) {
                    ServicesDTO servicesDTO = servicesMapper.toDto(takenInCharge1.get().getService());
                    takenInChargeDonorDTO.getTakenInCharge().setServices(servicesDTO);
                }
            }
            return  takenInChargeDonorDTO;
        }).toList();
    }
}
