package ma.almobadara.backend.repository.takenInCharge;

import ma.almobadara.backend.model.takenInCharge.DocumentTakenInCharge;
import ma.almobadara.backend.model.takenInCharge.TakenInCharge;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
@Repository
public interface DocumentTakenInChargeRepository extends JpaRepository<DocumentTakenInCharge, Long> {
    Iterable<DocumentTakenInCharge> findByTakenInCharge(TakenInCharge takenInCharge);

    Optional<DocumentTakenInCharge> findByTakenInChargeIdAndDocumentId(Long takenInChargeId, Long documentId);
    List<DocumentTakenInCharge> findByTakenInChargeId(Long  takenInChargeId  );
    List<DocumentTakenInCharge> findByDocumentId(Long documentId);

}
