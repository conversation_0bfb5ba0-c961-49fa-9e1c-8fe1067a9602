package ma.almobadara.backend.service.family;

import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.Audit.AuditApplicationService;
import ma.almobadara.backend.clients.RefFeignClient;
import ma.almobadara.backend.config.Messages;
import ma.almobadara.backend.dto.beneficiary.BeneficiaryPieceJointe;
import ma.almobadara.backend.dto.beneficiary.DocumentBeneficiaryAddDto;
import ma.almobadara.backend.dto.beneficiary.EducationDTO;
import ma.almobadara.backend.dto.communs.DocumentAndEntityDto;
import ma.almobadara.backend.dto.communs.DocumentDTO;
import ma.almobadara.backend.dto.family.*;
import ma.almobadara.backend.dto.referentiel.*;
import ma.almobadara.backend.exceptions.FunctionalException;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.mapper.*;
import ma.almobadara.backend.model.administration.SousZone;
import ma.almobadara.backend.model.administration.Zone;
import ma.almobadara.backend.model.beneficiary.Beneficiary;
import ma.almobadara.backend.model.beneficiary.DocumentBeneficiary;
import ma.almobadara.backend.model.beneficiary.Person;
import ma.almobadara.backend.model.communs.Document;
import ma.almobadara.backend.model.family.DocumentFamily;
import ma.almobadara.backend.model.family.Family;
import ma.almobadara.backend.model.family.FamilyMember;
import ma.almobadara.backend.model.family.TutorHistory;
import ma.almobadara.backend.repository.administration.SousZoneRepository;
import ma.almobadara.backend.repository.administration.ZoneRepository;
import ma.almobadara.backend.repository.beneficiary.DocumentBeneficiaryRepository;
import ma.almobadara.backend.repository.beneficiary.PersonRepository;
import ma.almobadara.backend.repository.communs.DocumentRepository;
import ma.almobadara.backend.repository.family.FamilyDocumentRepository;
import ma.almobadara.backend.repository.family.FamilyMemberRepository;
import ma.almobadara.backend.repository.family.FamilyRepository;
import ma.almobadara.backend.repository.family.TutorHistoryRepository;
import ma.almobadara.backend.service.ReferentialService;
import ma.almobadara.backend.service.beneficiary.AddedBeneficiaryResponse;
import ma.almobadara.backend.service.communs.DocumentService;
import ma.almobadara.backend.service.donor.MinioService;
import ma.almobadara.backend.util.times.TimeWatch;
import org.apache.commons.io.FilenameUtils;
import org.hibernate.Hibernate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static ma.almobadara.backend.Audit.ObjectConverter.convertObjectToJson;
import static ma.almobadara.backend.service.administration.CacheAdUserService.getUsernameFromJwt;
import static ma.almobadara.backend.util.constants.GlobalConstants.*;
import static ma.almobadara.backend.util.strings.HandleSpecialChars.escapeSpecialChars;


@Service
@RequiredArgsConstructor
@Slf4j
public class FamilyMemberService {
    private final FamilyRepository familyRepository;
    private final TutorHistoryRepository tutorHistoryRepository;
    private final TutorHistoryMapper tutorHistoryMapper;
    private final FamilyMemberRepository familyMemberRepository;
    private final FamilyService familyService;
    private final PersonRepository personRepository;
    private final MinioService minioService;
    private final FamilyMemberMapper familyMemberMapper;
    private final ReferentialService referentialService;
    private final AuditApplicationService auditApplicationService;
    private final Messages messages;
    private final RefFeignClient refFeignClient;
    private final DocumentMapper documentMapper;
    private final FamilyDocumentRepository familyDocumentRepository;
    private final DocumentRepository documentRepository;
    private final ZoneRepository zoneRepository;
    private final SousZoneRepository sousZoneRepository;
    private final SousZoneMapper sousZoneMapper;

    @Autowired
    private DocumentService documentService;


    @Value("${minio.familiesFolder}")
    private String familiesFolder;


    @Value("${minio.membersFolder}")
    private String membersFolder;

    @Value("${minio.profilePicture.folder}")
    private String folderPathPicture;

    @Value("${minio.profilePicture.abv}")
    private String abv;
    @Autowired
    private FamilyMemberDocumentMapper familyMemberDocumentMapper;
    @Autowired
    private ServicesMapper servicesMapper;

    @Transactional
    public AddedFamilyMemberResponse addFamilyMember(FamilyMemberAddDTO familyMemberDTO) throws TechnicalException, FunctionalException, IOException {
        TimeWatch watch = TimeWatch.start();
        Family familyOldForAuditObject = new Family();
        if (familyMemberDTO.getFamilyId() != null) {
            familyOldForAuditObject = familyService.findFamilyById(familyMemberDTO.getFamilyId());
        }
        String natureHeberg2 ="";
        String typeHeberg2="";
        if (familyOldForAuditObject.getAccommodationNatureId() != null) {
            AccommodationNatureDTO accommodationNature2 = referentialService.findAccommodationNatureById(familyOldForAuditObject.getAccommodationNatureId());
            natureHeberg2 = (accommodationNature2 != null) ? accommodationNature2.getName() : "";
        }
        if (familyOldForAuditObject.getAccommodationTypeId()!=null){
            AccommodationTypeDTO accommodationType2 = referentialService.findAccommodationTypeById(familyOldForAuditObject.getAccommodationTypeId());
            typeHeberg2 = (accommodationType2 != null) ? accommodationType2.getName() : "";
        }


        String cityName2 = "";
        if (familyOldForAuditObject.getCityId() != null) {
            cityName2 = referentialService.findCityById(familyOldForAuditObject.getCityId()).getName();
        }

        String familyOldForAudit = "";
        if (familyOldForAuditObject.getFamilyMembers() != null ){
            familyOldForAudit = familyOldForAuditObject.getAuditForModification(familyService.getFamilyName(familyOldForAuditObject), cityName2, natureHeberg2, typeHeberg2);
        }
        log.debug("Start service addFamilyMember {}", familyMemberDTO);
        Family family;
        Person person;
        Date oldDebutate = null;
        Date oldEndDate = null;
        Person existingPerson = new Person();
        FamilyMember existingFamilyMemberfordto = new FamilyMember();
        boolean isUpdate = familyMemberDTO.getId() != null;
        String initialValue = "";
        if (familyMemberDTO.getId() != null) {
            //for audit
            Optional<FamilyMember> familyMemberOptional = familyMemberRepository.findById(familyMemberDTO.getId());
            FamilyMember existingFamilyMember = familyMemberOptional.orElseThrow(() -> new TechnicalException("Family member not found")); // Get the FamilyMember object from Optional
            String profession = "-";
            if (existingFamilyMember.getPerson().getProfessionId() != null)
                profession = referentialService.findProfessionById(existingFamilyMember.getPerson().getProfessionId()).getName();

            SchoolLevelDTO education = new SchoolLevelDTO();
            if (existingFamilyMember.getPerson().getSchoolLevelId() != null)
                education = referentialService.findSchoolLevelById(existingFamilyMember.getPerson().getSchoolLevelId());
            CityDTO city = new CityDTO();
            if (existingFamilyMember.getPerson().getCityId() != null)
                city = referentialService.findCityById(existingFamilyMember.getPerson().getCityId());
            String adresse = existingFamilyMember.getPerson().getAddress() + ", " + city.getName();

            if (adresse.startsWith("null")) {
                adresse = adresse.replace("null,", "");
            }


            String education2 = "";
            if (education.getType() != null)
                education2 = education.getType().substring(0, 1).toUpperCase() + education.getType().substring(1).toLowerCase() + ", " + education.getName();
            initialValue = existingFamilyMember.getAuditInfos(adresse, profession, education2);
            existingPerson = existingFamilyMember.getPerson();

        }


        if (familyMemberDTO.getFamilyId() == null && familyMemberDTO.getId() == null) {
            family = familyService.createNewFamily(familyMemberDTO.getAddress(), familyMemberDTO.getAddressAr(), familyMemberDTO.getGeneralComment(), familyMemberDTO.getPhoneNumber(), familyMemberDTO.getCityId(), familyMemberDTO.getAccommodationTypeId(), familyMemberDTO.getAccommodationNatureId(), familyMemberDTO.getZoneId(), familyMemberDTO.getSousZoneId(),familyMemberDTO.getTags());
            AddedFamilyMemberResponse response = new AddedFamilyMemberResponse();
            response.setFamilyId(family.getId());
            response.setAddressFamily(family.getAddressFamily());
            response.setAddressFamilyAr(family.getAddressFamilyAr());
            response.setGeneralCommentFamily(family.getGeneralCommentFamily());
            response.setPhoneNumberFamily(family.getPhoneNumberFamily());
            response.setCityId(family.getCityId());

            //add piece jointe
            Map<String, Long> documentIds = addPieceJointeToFamily(familyMemberDTO.getFamilyPieceJointe(), family.getId(), null, null);

            if (documentIds.containsKey("cinId") && documentIds.get("cinId") != null) {
                response.setCinId(documentIds.get("cinId"));
            }

            if (documentIds.containsKey("etatCivilId") && documentIds.get("etatCivilId") != null) {
                response.setEtatCivilId(documentIds.get("etatCivilId"));
            }
            String cityName = "";
            String natureHebergName = "";
            String typeHebergName = "";
            if (familyMemberDTO.getCityId()!=null) cityName = referentialService.findCityById(familyMemberDTO.getCityId()).getName();
            if (familyMemberDTO.getCityId()!=null) natureHebergName = referentialService.findAccommodationNatureById(familyMemberDTO.getAccommodationNatureId()).getName();
            if (familyMemberDTO.getCityId()!=null) typeHebergName = referentialService.findAccommodationTypeById(familyMemberDTO.getAccommodationTypeId()).getName();
            String familyCommunAudit = family.getAuditForAjout(cityName,natureHebergName,typeHebergName,familyMemberDTO.getFamilyPieceJointe(),familyMemberDTO.getFamilyPieceJointe());

            auditApplicationService.audit("Ajout d'une Famille", getUsernameFromJwt(), "Add Family",
                    null, familyCommunAudit, FAMILLE, CREATE);
            return response;

        } else if (familyMemberDTO.getFamilyId() != null && familyMemberDTO.getId() == null && familyMemberDTO.getLastName() == null) {
            family = familyService.findFamilyById(familyMemberDTO.getFamilyId());

            Family oldFamilyInfos = family;
            family.setAddressFamily(familyMemberDTO.getAddress());
            family.setAddressFamilyAr(familyMemberDTO.getAddressAr());
            family.setGeneralCommentFamily(familyMemberDTO.getGeneralComment());
            family.setPhoneNumberFamily(familyMemberDTO.getPhoneNumber());
            family.setCityId(familyMemberDTO.getCityId());
            family.setAccommodationNatureId(familyMemberDTO.getAccommodationNatureId());
            family.setAccommodationTypeId(familyMemberDTO.getAccommodationTypeId());

            Zone zone = new Zone();
            SousZone sousZone = new SousZone();
            if (familyMemberDTO.getZoneId() != null) {
                zone = zoneRepository.findById(familyMemberDTO.getZoneId()).orElseThrow();
                family.setZone(zone);
                if (familyMemberDTO.getSousZoneId() != null) {
                    sousZone = sousZoneRepository.findById(familyMemberDTO.getSousZoneId()).orElseThrow();
                    family.setSousZone(sousZone);
                } else {
                    family.setSousZone(null);
                }
            }


            familyRepository.save(family);
            var accommodationNature = refFeignClient.getParAccommodationNature(family.getAccommodationNatureId());
            String natureHeberg = (accommodationNature != null) ? accommodationNature.getName() : "";
            var accommodationType = refFeignClient.getMetAccommodationType(family.getAccommodationTypeId());
            String typeHeberg = (accommodationType != null) ? accommodationType.getName() : "";
            String cityName = "";
            if (family.getCityId() != null) {
                cityName = referentialService.findCityById(family.getCityId()).getName();
            }

            String familyAudit = family.getAuditForModification(familyService.getFamilyName(family), cityName, natureHeberg, typeHeberg);


            auditApplicationService.audit("Modification Famille : " + family.getCode(), getUsernameFromJwt(), "Update Family",
                    familyOldForAudit, familyAudit, FAMILLE, UPDATE);

            AddedFamilyMemberResponse response = new AddedFamilyMemberResponse();
            response.setFamilyId(family.getId());
            response.setAddressFamily(family.getAddressFamily());
            response.setAddressFamilyAr(family.getAddressFamilyAr());
            response.setGeneralCommentFamily(family.getGeneralCommentFamily());
            response.setPhoneNumberFamily(family.getPhoneNumberFamily());
            response.setCityId(family.getCityId());
            //add piece jointe
            Map<String, Long> documentIds = addPieceJointeToFamily(familyMemberDTO.getFamilyPieceJointe(), family.getId(), familyMemberDTO.getCinId(), familyMemberDTO.getEtatCivilId());

            if (documentIds.containsKey("cinId") && documentIds.get("cinId") != null) {
                response.setCinId(documentIds.get("cinId"));
            }

            if (documentIds.containsKey("etatCivilId") && documentIds.get("etatCivilId") != null) {
                response.setEtatCivilId(documentIds.get("etatCivilId"));
            }
            return response;
        } else {
            family = familyService.findFamilyById(familyMemberDTO.getFamilyId());
        }
        if (familyMemberDTO.getId() != null) {
            personRepository.findById(familyMemberDTO.getPersonId()).orElseThrow(() -> new TechnicalException(PERSON_NOT_FOUND));
        }
        checkReferentialDataIntegrity(familyMemberDTO);
        person = familyMemberMapper.mapFamilyMemberAddDTOToPerson(familyMemberDTO);
        person.setId(familyMemberDTO.getPersonId());
        if (existingPerson.getId() != null) {
            person.setCategoryBeneficiaryId(existingPerson.getCategoryBeneficiaryId());
            person.setAccommodationTypeId(existingPerson.getAccommodationTypeId());
            person.setTypeKafalatId(existingPerson.getTypeKafalatId());
            person.setSourceBeneficiaryId(existingPerson.getSourceBeneficiaryId());
            person.setSourceBeneficiaryComment(existingPerson.getSourceBeneficiaryComment());
            person.setTypePriseEnChargeIdsList(existingPerson.getTypePriseEnChargeIdsList());
        }
        var familyMember = familyMemberMapper.mapFamilyMemberAddDTOToFamilyMember(familyMemberDTO);
        if (familyMemberDTO.getId() == null) {
            generateFamilyMemberCode(familyMember, family);
        } else {
            familyMember.setCode(familyMemberDTO.getCode());
        }
        // we should compare if the end date of the tutor is inferor of the actual date
        if (!familyMemberDTO.isTutor() || (familyMemberDTO.getTutorEndDate() != null && familyMemberDTO.getTutorEndDate().before(new Date()))) {
            oldDebutate = familyMember.getTutorStartDate();
            oldEndDate = familyMember.getTutorEndDate();
            familyMember.setTutorStartDate(null);
            familyMember.setTutorEndDate(null);
            familyMember.setTutor(false);
        }
        if (familyMemberDTO.isHasNewTutor()) {
            // we should find this member and set him as tutor
            FamilyMember tutor = family.getFamilyMembers().stream().filter(fm -> fm.getId().equals(familyMemberDTO.getNewTutorId())).findFirst().orElse(null);
            if (tutor != null) {
                tutor.setTutor(true);
                tutor.setTutorStartDate(familyMemberDTO.getNewTutorStartDate());
                tutor.setTutorEndDate(familyMemberDTO.getNewTutorEndDate());
                familyMemberRepository.save(tutor);
            }
        }
        saveMemberPicture(familyMemberDTO.getPicture(), person, family.getCode(), familyMember.getCode());
        person = personRepository.save(person);
        familyMember.setFamily(family);
        familyMember.setPerson(person);
        if (familyMemberDTO.getId() != null) {
            Optional<FamilyMember> familyMemberOptional = familyMemberRepository.findById(familyMemberDTO.getId());
            familyMemberOptional.ifPresent(member -> familyMember.setId(member.getId()));
        }
        if (!familyMemberDTO.isEducated()) {
            familyMember.getPerson().setSchoolLevelId(null);
            familyMember.getPerson().setSchoolName(null);

        }
        if (!familyMemberDTO.isDeceased() && (familyMember.getPerson().getDeathReasonId() != null || familyMember.getPerson().getDeathDate() != null || familyMember.getPerson().getDeathReason() != null)) {
            familyMember.getPerson().setDeathReasonId(null);
            familyMember.getPerson().setDeathDate(null);
            familyMember.getPerson().setDeathReason(null);
        }
        if (!familyMemberDTO.isDeceased()) {
            familyMember.getPerson().setDeathDate(null);
        }
        // we should add the sex to person depend on the family member relationShip
        if (familyMemberDTO.getFamilyRelationshipId() != null) {
            if (familyMemberDTO.getFamilyRelationshipId() == 1L || familyMemberDTO.getFamilyRelationshipId() == 3L || familyMemberDTO.getFamilyRelationshipId() == 5L || familyMemberDTO.getFamilyRelationshipId() == 7L) {
                familyMember.getPerson().setSex("Homme");
            } else if (familyMemberDTO.getFamilyRelationshipId() == 2L || familyMemberDTO.getFamilyRelationshipId() == 4L || familyMemberDTO.getFamilyRelationshipId() == 6L || familyMemberDTO.getFamilyRelationshipId() == 8L) {
                familyMember.getPerson().setSex("Femme");
            } else {
                familyMember.getPerson().setSex(null);
            }

        }
        familyMemberRepository.save(familyMember);
        if (isUpdate && (oldDebutate != null || oldEndDate != null)) {
            saveTutorHistory(oldDebutate, oldEndDate, familyMember);
        }


        AddedFamilyMemberResponse response = new AddedFamilyMemberResponse();
        response.setFamilyId(familyMember.getFamily().getId());
        response.setId(familyMember.getId());
        response.setPersonId(familyMember.getPerson().getId());
        response.setCode(familyMember.getCode());
        response.setTutor(familyMember.isTutor());
        response.setDeceased(familyMember.getPerson().isDeceased());
        response.setAddressFamily(family.getAddressFamily());
        response.setAddressFamilyAr(family.getAddressFamilyAr());
        response.setGeneralCommentFamily(family.getGeneralCommentFamily());
        response.setPhoneNumberFamily(family.getPhoneNumberFamily());
        response.setCityId(family.getCityId());
        response.setCinId(familyMemberDTO.getCinId());
        response.setEtatCivilId(familyMemberDTO.getEtatCivilId());

        //Audit
        String profession2 = "-";
        if (familyMemberDTO.getProfessionId() != null)
            profession2 = referentialService.findProfessionById(familyMemberDTO.getProfessionId()).getName();

        SchoolLevelDTO education2 = new SchoolLevelDTO();
        if (familyMemberDTO.getSchoolLevelId() != null)
            education2 = referentialService.findSchoolLevelById(familyMemberDTO.getSchoolLevelId());

        String educationTemp = "";
        if (education2.getType() != null) {
            educationTemp = education2.getType().substring(0, 1).toUpperCase() + education2.getType().substring(1).toLowerCase() + ", " + education2.getName();
        }

        CityDTO city = new CityDTO();
        if (familyMember.getPerson().getCityId() != null)
            city = referentialService.findCityById(familyMember.getPerson().getCityId());
        String adresse = familyMember.getPerson().getAddress() + ", " + city.getName();

        if (adresse.startsWith("null")) {
            adresse = adresse.replace("null,", "");
        }
        String newValue = familyMember.getAuditInfos(adresse, profession2, educationTemp);


        if (familyMemberDTO.getId() != null) {
            auditApplicationService.audit("Modification Membre Famille : " + familyMember.getCode(), getUsernameFromJwt(), "Update Family Member",
                    initialValue, newValue, FAMILLE, UPDATE);
        } else {
            auditApplicationService.audit("Ajout Membre Famille : " + familyMember.getCode(), getUsernameFromJwt(), "Add Family Member",
                    null, newValue, FAMILLE, CREATE);
        }

        log.debug("End service addFamilyMember, took {}", watch.toMS());
        return response;
    }


    public Map<String, Long> addPieceJointeToFamily(FamilyPieceJointeDto familyPieceJointe, Long familyId, Long cinId, Long etatCivilId) throws TechnicalException, IOException {
        Family family = familyRepository.findById(familyId)
                .orElseThrow(() -> new RuntimeException("family not found with id: " + familyId));

        Map<String, Long> documentIds = new HashMap<>();

        if (familyPieceJointe != null && familyPieceJointe.getCin() != null) {
            Long newCinId = addDocumentToFamily(family, familyPieceJointe.getCin(), "family", cinId, null);
            documentIds.put("cinId", newCinId);
        }

        if (familyPieceJointe != null && familyPieceJointe.getEtatCivilCertificate() != null) {
            Long newEtatCivilId = addDocumentToFamily(family, familyPieceJointe.getEtatCivilCertificate(), "family", null, etatCivilId);
            documentIds.put("etatCivilId", newEtatCivilId);
        }


        familyRepository.save(family);

        return documentIds;
    }


    private Long addDocumentToFamily(Family family, DocumentBeneficiaryAddDto documentDto, String entityType, Long cinId, Long etatCivilId) throws TechnicalException, IOException {

        Long documentTypeId = documentDto.getType().getId();


        if (documentDto.getType().getId().equals(3L) && cinId != null) {
            DocumentFamily documentFamily = familyDocumentRepository.findDocumentFamiliesByDocumentId(cinId).orElseThrow();
            familyDocumentRepository.delete(documentFamily);

            Document document = documentRepository.findById(cinId).orElseThrow();
            documentRepository.delete(document);
        }

        if (documentDto.getType().getId().equals(2L) && etatCivilId != null) {
            DocumentFamily documentFamily = familyDocumentRepository.findDocumentFamiliesByDocumentId(etatCivilId).orElseThrow();
            familyDocumentRepository.delete(documentFamily);

            Document document = documentRepository.findById(etatCivilId).orElseThrow();
            documentRepository.delete(document);
        }


        DocumentAndEntityDto documentAndEntityDto = new DocumentAndEntityDto();
        DocumentDTO documentDTO = new DocumentDTO();

        documentDTO.setLabel(documentDto.getLabel());
        documentDTO.setDocumentDate(documentDto.getDocumentDate());
        documentDTO.setExpiryDate(documentDto.getExpiryDate());
        documentDTO.setComment(documentDto.getComment());
        documentDTO.setFileUrl(documentDto.getFileUrl());
        documentDTO.setFileName(documentDto.getFileUrl());
        documentDTO.setFile(documentDto.getFile());
        documentDTO.setType(documentDto.getType());


        documentAndEntityDto.setDocumentDTO(documentDTO);
        documentAndEntityDto.setEntityId(family.getId());
        documentAndEntityDto.setEntityType(entityType);

        DocumentDTO newDocumentDTO = documentService.addDocument(documentAndEntityDto);


        DocumentFamily documentFamily = DocumentFamily.builder()
                .family(family)
                .document(documentMapper.documentToModelToModel(newDocumentDTO))
                .build();

        familyDocumentRepository.save(documentFamily);

        return documentFamily.getDocument().getId();
    }

    // funtion that will create and save the tutor history
    @Transactional
    public void saveTutorHistory(Date oldDebutate, Date oldEndDate, FamilyMember familyMember) {
        TutorHistory tutorHistory = new TutorHistory();
        tutorHistory.setFamilyMember(familyMember);
        tutorHistory.setDateDebut(oldDebutate);
        tutorHistory.setDateFin(oldEndDate);
        tutorHistoryRepository.save(tutorHistory);
    }

    public void saveMemberPicture(MultipartFile picture, Person person, String familyCode, String familyMemberCode) {
        if (picture != null) {
            var memberPath = familiesFolder + familyCode + "/" + membersFolder + person.getLastName().toUpperCase() + "-" + person.getFirstName().substring(0, 1).toUpperCase() + person.getFirstName().substring(1) + "_" + familyMemberCode;
            var dateTimeFormatter = DateTimeFormatter.BASIC_ISO_DATE;
            var instant = Instant.now();
            var fileName = person.getLastName().toUpperCase() + "-" + person.getFirstName().substring(0, 1).toUpperCase() + person.getFirstName().substring(1) + "_" + abv + "_" + instant.atZone(ZoneId.systemDefault()).toLocalDate().format(dateTimeFormatter) + "." + FilenameUtils.getExtension(picture.getOriginalFilename());
            minioService.WriteToMinIO(picture, memberPath + "/" + folderPathPicture + "/", fileName);
            person.setPictureUrl(memberPath + "/" + folderPathPicture + "/" + fileName);
        } else if (person.getId() != null) {
            Optional<Person> personOptional = personRepository.findById(person.getId());
            personOptional.ifPresent(value -> person.setPictureUrl(value.getPictureUrl()));
        }
    }

    private void checkReferentialDataIntegrity(FamilyMemberAddDTO familyMemberDTO) throws TechnicalException {
        if (familyMemberDTO.getCityId() != null) {
            referentialService.findCityById(familyMemberDTO.getCityId());
        }
        if (familyMemberDTO.getProfessionId() != null) {
            referentialService.findProfessionById(familyMemberDTO.getProfessionId());
        }
        if (familyMemberDTO.getTypeIdentityId() != null) {
            referentialService.findTypeIdentityById(familyMemberDTO.getTypeIdentityId());
        }
        if (familyMemberDTO.getSchoolLevelId() != null) {
            referentialService.findSchoolLevelById(familyMemberDTO.getSchoolLevelId());
        }
    }

    public void generateFamilyMemberCode(FamilyMember familyMember, Family family) throws TechnicalException {
        var code = "";
        long familyMemberCount = familyMemberRepository.countByFamilyId(family.getId());
        if (family.getCode() != null && familyMember.getCode() == null) {
            String zeros = "";
            if (familyMemberCount < 10) {
                zeros = "0";
            }
            // Generate the family member code based on the family count and family code
            code = M_CODE + zeros + (familyMemberCount + 1) + family.getCode();
        }
        // If the family member code already exists, validate and possibly update it
        else if (family.getCode() != null && !familyMember.getCode().substring(2).equals(family.getCode())) {
            code = familyMember.getCode().substring(0, 3) + familyMember.getFamily().getCode();
        }
        // If the family member code is already set, keep it unchanged
        else {
            code = familyMember.getCode();
        }

        // Set the generated code to the family member
        familyMember.setCode(code);
    }

    public Long addListOfFamilyMembers(List<FamilyMemberAddDTO> memberDTOS) throws TechnicalException {

        Long familyId = null;

        if (memberDTOS != null && !memberDTOS.isEmpty()) {
            for (FamilyMemberAddDTO memberDTO : memberDTOS) {
                var family = familyService.findFamilyById(memberDTO.getFamilyId());
                List<FamilyMember> familyMembers = familyMemberRepository.findAllByFamily(family);
                checkReferentialDataIntegrity(memberDTO);
                var person = familyMemberMapper.mapFamilyMemberAddDTOToPerson(memberDTO);
                person.setId(memberDTO.getPersonId());
                if (memberDTO.getFamilyRelationshipId() != null) {
                    if (memberDTO.getFamilyRelationshipId() == 1L || memberDTO.getFamilyRelationshipId() == 3L || memberDTO.getFamilyRelationshipId() == 5L || memberDTO.getFamilyRelationshipId() == 7L) {
                        person.setSex("Homme");
                    } else if (memberDTO.getFamilyRelationshipId() == 2L || memberDTO.getFamilyRelationshipId() == 4L || memberDTO.getFamilyRelationshipId() == 6L || memberDTO.getFamilyRelationshipId() == 8L) {
                        person.setSex("Femme");
                    } else {
                        person.setSex(null);
                    }
                }
                var familyMember = familyMemberMapper.mapFamilyMemberAddDTOToFamilyMember(memberDTO);
                familyMember.setFamily(family);
                generateFamilyMemberCode(familyMember, family);
                saveMemberPicture(memberDTO.getPicture(), person, family.getCode(), familyMember.getCode());
                family.setFamilyMembers(null);
                person = personRepository.save(person);
                familyMember.setPerson(person);
                //Save member
                familyMemberRepository.save(familyMember);

                familyMembers.add(familyMember);
                family.setFamilyMembers(familyMembers);

                familyId = memberDTO.getFamilyId();
                String profession = (person.getProfessionId()!=null?referentialService.findProfessionById(person.getProfessionId()).getName():"");
                String education = (person.getSchoolLevelId()!=null?referentialService.findSchoolLevelById(person.getSchoolLevelId()).getType()+", "+referentialService.findSchoolLevelById(person.getSchoolLevelId()).getName():"");
                String familyMemberAuditString = familyMember.getAuditInfos(person.getAddress(),profession,education);

                auditApplicationService.audit("Ajout Membre Famille : " + familyMember.getCode(), getUsernameFromJwt(), "Add Family Member",
                        null, familyMemberAuditString, FAMILLE, CREATE);
            }
        }
        return familyId;
    }

}
