package ma.almobadara.backend.model.family;

import com.google.gson.Gson;
import jakarta.persistence.*;
import lombok.*;
import ma.almobadara.backend.dto.family.FamilyPieceJointeDto;
import ma.almobadara.backend.model.administration.SousZone;
import ma.almobadara.backend.model.administration.Zone;
import ma.almobadara.backend.model.beneficiary.BaseEntity;

import java.text.SimpleDateFormat;
import java.util.*;

import static ma.almobadara.backend.util.strings.HandleSpecialChars.escapeSpecialChars;

@Entity
@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
@ToString
public class Family extends BaseEntity {

    @Column(unique = true)
    private String code;

    @OneToMany(mappedBy = "family", cascade = CascadeType.ALL)
    private List<FamilyMember> familyMembers;

    @Column(name = "address_family")
    private String addressFamily;

    @Column(name = "address_family_ar")
    private String addressFamilyAr;

    @Column(name = "general_comment_family", columnDefinition = "TEXT")
    private String generalCommentFamily;

    @Column(name = "phone_number_family")
    private String phoneNumberFamily;

    @Column(name = "city_id")
    private Long cityId;

    private Long accommodationTypeId;
    private Long accommodationNatureId;

    @ManyToOne
    @JoinColumn(name = "zone_id")
    private Zone zone;

    @ManyToOne
    @JoinColumn(name = "sous_zone_id")
    private SousZone sousZone;

    public String getAuditForModification(String familyName,String city, String natureHeberg, String typeHeberg){
        if (addressFamily == null) addressFamily = "";
        addressFamily = addressFamily.substring(0, 1).toUpperCase()+addressFamily.substring(1).toLowerCase();
        Gson gson = new Gson();
        Map<String, Object> jsonMap = new LinkedHashMap<>();

        jsonMap.put("Code", code);
        jsonMap.put("Family Name", escapeSpecialChars(familyName));
        jsonMap.put("Address du famille", escapeSpecialChars(addressFamily + " ," + city));
        jsonMap.put("Téléphone du famille", escapeSpecialChars(phoneNumberFamily));
        jsonMap.put("Zone", escapeSpecialChars(zone!=null ?zone.getName() :"-"));
        jsonMap.put("Nature d'hébergement", escapeSpecialChars(natureHeberg));
        jsonMap.put("Type d'hébergement", escapeSpecialChars(typeHeberg));
        jsonMap.put("Commentaire général", escapeSpecialChars(generalCommentFamily));

        String jsonString = gson.toJson(jsonMap);
        return jsonString;
    }
    public String getAuditForConsultation(String familyName,String city, String natureHeberg, String typeHeberg){
        if (addressFamily == null) addressFamily = "";
        addressFamily = addressFamily.substring(0, 1).toUpperCase()+addressFamily.substring(1).toLowerCase();
        Gson gson = new Gson();
        Map<String, String> nestedObject = new LinkedHashMap<>();
        if (!familyMembers.isEmpty()){
            for (FamilyMember familyMember : familyMembers){
                nestedObject.put("Membre "+familyMember.getId()+getRelationFromId(Math.toIntExact(familyMember.getFamilyRelationshipId()))+": "+familyMember.getPerson().getFirstName()+" "+familyMember.getPerson().getLastName() +(familyMember.isTutor()?" (Tuteur du famille)":""),"Code : "+familyMember.getCode());
            }
        }
        Map<String, Object> jsonMap = new LinkedHashMap<>();

        jsonMap.put("Code", code);
        jsonMap.put("Family Name", escapeSpecialChars(familyName));
        jsonMap.put("Family Members",nestedObject);
        jsonMap.put("Address du famille", escapeSpecialChars(addressFamily + " ," + city));
        jsonMap.put("Téléphone du famille", escapeSpecialChars(phoneNumberFamily));
        jsonMap.put("Zone", escapeSpecialChars(zone !=null ?zone.getName():"-"));
        jsonMap.put("Nature d'hébergement", escapeSpecialChars(natureHeberg));
        jsonMap.put("Type d'hébergement", escapeSpecialChars(typeHeberg));
        jsonMap.put("Commentaire général", escapeSpecialChars(generalCommentFamily));

        String jsonString = gson.toJson(jsonMap);
        return jsonString;
    }
    public String getAuditForAjout(String city, String natureHeberg, String typeHeberg, FamilyPieceJointeDto cin, FamilyPieceJointeDto etatCivil) {
        if (addressFamily == null) addressFamily = "";

        // Initialize date formatter
        SimpleDateFormat formatter = new SimpleDateFormat("dd-MM-yyyy");

        // Default values for dates in case of null
        String dateCin = "-";
        if (cin != null && cin.getCin() != null && cin.getCin().getExpiryDate() != null) {
            dateCin = formatter.format(cin.getCin().getExpiryDate());
        }

        String dateEtat = "-";  // Default value in case of null
        if (etatCivil != null && etatCivil.getEtatCivilCertificate() != null && etatCivil.getEtatCivilCertificate().getExpiryDate() != null) {
            dateEtat = formatter.format(etatCivil.getEtatCivilCertificate().getExpiryDate());
        }

        if (!addressFamily.isEmpty()) {
            addressFamily = addressFamily.substring(0, 1).toUpperCase() + addressFamily.substring(1).toLowerCase();
        }

        Gson gson = new Gson();
        Map<String, Object> jsonMap = new LinkedHashMap<>();

        jsonMap.put("Code", code);
        jsonMap.put("Address du famille", escapeSpecialChars(addressFamily + " ," + city));
        jsonMap.put("Téléphone du famille", escapeSpecialChars(phoneNumberFamily));
        jsonMap.put("Zone", escapeSpecialChars(zone != null ? zone.getName() : ""));
        jsonMap.put("Nature d'hébergement", escapeSpecialChars(natureHeberg));
        jsonMap.put("Type d'hébergement", escapeSpecialChars(typeHeberg));
        jsonMap.put("Commentaire général", escapeSpecialChars(generalCommentFamily));

        // Check if 'cin' and 'cin.getCin()' are not null
        if (cin != null && cin.getCin() != null) {
            jsonMap.put("Cin fichier: " + cin.getCin().getFileUrl(), "Date expiration : " + dateCin);
        } else {
            jsonMap.put("Cin fichier: N/A", "Date expiration : N/A");
        }

        // Check if 'etatCivil' and 'etatCivil.getEtatCivilCertificate()' are not null
        if (etatCivil != null && etatCivil.getEtatCivilCertificate() != null) {
            jsonMap.put("Etat civil fichier : " + etatCivil.getEtatCivilCertificate().getFileUrl(), "Date expiration : " + dateEtat);
        } else {
            jsonMap.put("Etat civil fichier : N/A", "Date expiration : N/A");
        }

        // Convert the map to JSON
        String jsonString = gson.toJson(jsonMap);
        return jsonString;
    }

    private String getRelationFromId(int id){

        switch(id){
            case 1:
                return "Père";
            case 2:
                return "Mère";
            case 3:
                return "Fils";
            case 4:
                return "Fille";
            case 5:
                return "Oncle";
            case 6:
                return "Tante";
            case 7:
                return "Grand-père";
            case 8:
                return "Grand-mère";
            default:
                return "Non défini";
        }
    }
}
