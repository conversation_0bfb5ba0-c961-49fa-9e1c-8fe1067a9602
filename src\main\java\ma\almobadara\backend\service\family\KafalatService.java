package ma.almobadara.backend.service.family;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.family.KafalatDTO;
import ma.almobadara.backend.model.beneficiary.Beneficiary;
import ma.almobadara.backend.model.donor.Donor;
import ma.almobadara.backend.model.donor.DonorAnonyme;
import ma.almobadara.backend.model.donor.DonorMoral;
import ma.almobadara.backend.model.donor.DonorPhysical;
import ma.almobadara.backend.model.takenInCharge.TakenInCharge;
import ma.almobadara.backend.model.takenInCharge.TakenInChargeBeneficiary;
import ma.almobadara.backend.model.takenInCharge.TakenInChargeDonor;
import ma.almobadara.backend.model.takenInCharge.TakenInChargeOperation;
import ma.almobadara.backend.repository.beneficiary.BeneficiaryRepository;
import ma.almobadara.backend.repository.donor.DonorRepository;
import ma.almobadara.backend.repository.donor.TakenInChargeDonorRepository;
import ma.almobadara.backend.repository.takenInCharge.TakenInChargeBeneficiaryRepository;
import ma.almobadara.backend.repository.takenInCharge.TakenInChargeRepository;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class KafalatService {

    private final TakenInChargeBeneficiaryRepository takenInChargeBeneficiaryRepository;
    private final TakenInChargeRepository takenInChargeRepository;
    private final BeneficiaryRepository beneficiaryRepository;
    private final DonorRepository donorRepository;
    private final TakenInChargeDonorRepository takenInChargeDonorRepository;

    /**
     * Get kafalat information for a beneficiary
     * @param beneficiaryId The ID of the beneficiary
     * @return List of KafalatDTO with kafalat information
     */
    public List<KafalatDTO> getKafalatForBeneficiary(Long beneficiaryId) {
        log.debug("Start service getKafalatForBeneficiary for beneficiary ID: {}", beneficiaryId);

        // Get the beneficiary
        Beneficiary beneficiary = beneficiaryRepository.findById(beneficiaryId)
                .orElse(null);

        if (beneficiary == null) {
            log.debug("Beneficiary not found with ID: {}", beneficiaryId);
            return new ArrayList<>();
        }

        // Get all TakenInChargeBeneficiary records for this beneficiary
        List<TakenInChargeBeneficiary> takenInChargeBeneficiaries =
                takenInChargeBeneficiaryRepository.findByBeneficiaryId(beneficiaryId);

        // Convert to KafalatDTO
        List<KafalatDTO> kafalatDTOs = takenInChargeBeneficiaries.stream()
                .map(this::convertToKafalatDTO)
                .collect(Collectors.toList());

        log.debug("End service getKafalatForBeneficiary, found {} kafalats", kafalatDTOs.size());
        return kafalatDTOs;
    }

    /**
     * Convert TakenInChargeBeneficiary to KafalatDTO
     */
    private KafalatDTO convertToKafalatDTO(TakenInChargeBeneficiary takenInChargeBeneficiary) {
        TakenInCharge takenInCharge = takenInChargeBeneficiary.getTakenInCharge();

        // Create the DTO
        KafalatDTO dto = new KafalatDTO();

        // Set basic information
        dto.setId(takenInCharge.getId());
        dto.setCode(takenInCharge.getCode());
        dto.setStatus(takenInCharge.getStatus());
        dto.setStartDate(takenInCharge.getStartDate());
        dto.setEndDate(takenInCharge.getEndDate());

        // Set service information
        if (takenInCharge.getService() != null) {
            dto.setServiceName(takenInCharge.getService().getName());
        }

        // Set beneficiary information
        if (takenInChargeBeneficiary.getBeneficiary() != null &&
            takenInChargeBeneficiary.getBeneficiary().getPerson() != null) {
            dto.setBeneficiaryName(
                takenInChargeBeneficiary.getBeneficiary().getPerson().getFirstName() + " " +
                takenInChargeBeneficiary.getBeneficiary().getPerson().getLastName()
            );
        }

        // Set donor information and calculate operation amounts
        if (takenInCharge.getTakenInChargeDonors() != null && !takenInCharge.getTakenInChargeDonors().isEmpty()) {
            TakenInChargeDonor takenInChargeDonor = takenInCharge.getTakenInChargeDonors().get(0);
            List<TakenInChargeDonor> takenInChargeDonor1=takenInChargeDonorRepository.findByTakenInChargeId(takenInChargeDonor.getId());
            // Fetch the donor from the repository to ensure we have the complete information
            if (takenInChargeDonor1.get(0).getDonor() != null && takenInChargeDonor1.get(0).getDonor().getId() != null) {
                Long donorId = takenInChargeDonor1.get(0).getDonor().getId();
                Donor donor = donorRepository.findById(donorId).orElse(null);

                if (donor != null) {
                    if(donor instanceof DonorPhysical donorPhysical)
                    dto.setDonorName(donorPhysical.getFirstName()+" "+donorPhysical.getLastName());
                    else if(donor instanceof DonorMoral donorMoral)
                        dto.setDonorName(donorMoral.getCompany());
                    else if(donor instanceof DonorAnonyme donorAnonyme){
                        dto.setDonorName(donorAnonyme.getName());
                    }
                } else {
                    dto.setDonorName("Unknown Donor");
                }
            }

            // Calculate operation amounts
            double plannedAmount = 0;
            double executedAmount = 0;
            double closedAmount = 0;
            int operationCount = 0;

            if (takenInChargeDonor.getTakenInChargeOperations() != null) {
                operationCount = takenInChargeDonor.getTakenInChargeOperations().size();

                for (TakenInChargeOperation operation : takenInChargeDonor.getTakenInChargeOperations()) {
                    if ("Planifié".equalsIgnoreCase(operation.getStatus())) {
                        plannedAmount += operation.getAmount();
                    } else if ("Exécuté".equalsIgnoreCase(operation.getStatus())) {
                        executedAmount += operation.getAmount();
                    } else if ("Clôturé".equalsIgnoreCase(operation.getStatus())) {
                        closedAmount += operation.getAmount();
                    }
                }
            }

            dto.setPlannedAmount(plannedAmount);
            dto.setExecutedAmount(executedAmount);
            dto.setClosedAmount(closedAmount);
            dto.setOperationCount(operationCount);
        }

        return dto;
    }
}
