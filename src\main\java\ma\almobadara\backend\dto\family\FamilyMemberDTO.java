package ma.almobadara.backend.dto.family;

import lombok.*;
import ma.almobadara.backend.dto.ExternalIncomeDTO;
import ma.almobadara.backend.dto.beneficiary.PersonDTO;
import ma.almobadara.backend.dto.referentiel.FamilyRelationshipDTO;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class FamilyMemberDTO {

	private Long id;

	private String code;

	private boolean tutor;
	private String generalComment;

	private Date tutorStartDate;

	private Date tutorEndDate;

	private FamilyRelationshipDTO familyRelationship;

	private PersonDTO person;

	private FamilyDTO family;

	private List<ExternalIncomeDTO> externalIncomes;
	private Boolean canBeUpdatedByAssistant;

	private List<KafalatDTO> kafalats;

}
