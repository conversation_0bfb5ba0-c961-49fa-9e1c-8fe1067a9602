package ma.almobadara.backend.model.beneficiary;

import lombok.Getter;

@Getter
public enum RapportStatus {
    RAPPORT_INITIAL(1L),
    RAPPORT_VALIDER_ASSISTANCE(2L),
    RAPPORT_VALIDER_KAFALAT(3L),
    RAPPORT_A_COMPLETER_PAR_ASSISTANCE(4L),
    RAPPORT_A_COMPLETER_PAR_KAFALAT(5L),
    RAPPORT_FINAL(6L),
    RAPPORT_A_PREPARE(7L),
    RAPPORT_PLANIFIER(8L);

    private final Long id;

    RapportStatus(Long id) {
        this.id = id;
    }

}
