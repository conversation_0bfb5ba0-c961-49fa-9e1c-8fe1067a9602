package ma.almobadara.backend.enumeration.EntitiesToExport;

import lombok.Getter;

@Getter
public enum DonationExportHeaders {
    CODE("Code"),
    DATE_CREATION("Date de Création"),
    AMOUNT("Montant (DH)"),
    TYPE_DONATION("Type de Donantion"),
    IDENTIFIED_DONOR("Donateur Identifié"),
    DONOR_NAME("Nom du Donateur"),
    TYPE_DONOR("Type de Donateur"),
    CANAL_DONATION("Canal de Dononation"),
    RECEPTION_DATE("Date de Réception"),
    COMMENT("Commentaire");

    private final String headerName;

    DonationExportHeaders(String headerName) {
        this.headerName = headerName;
    }

}

