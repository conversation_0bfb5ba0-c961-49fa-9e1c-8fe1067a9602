package ma.almobadara.backend.repository.beneficiary;

import ma.almobadara.backend.model.beneficiary.Person;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Date;

@Repository
public interface PersonRepository extends JpaRepository<Person, Long> {

    Person findByFirstNameAndLastNameAndBirthDate(String firstName, String lastName, Date birthDate);

}
