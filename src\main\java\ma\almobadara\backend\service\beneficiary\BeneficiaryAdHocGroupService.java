package ma.almobadara.backend.service.beneficiary;

import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityNotFoundException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.clients.RefFeignClient;
import ma.almobadara.backend.controller.referentiel.RefController;
import ma.almobadara.backend.dto.CityWithRegionAndCountryDTO;
import ma.almobadara.backend.dto.beneficiary.BeneficiaryAdHocCombineFilterDto;
import ma.almobadara.backend.dto.beneficiary.BeneficiaryAdHocCombinedDto;
import ma.almobadara.backend.dto.beneficiary.BeneficiaryAdHocGroupDto;
import ma.almobadara.backend.dto.beneficiary.BeneficiaryAffectedToGroupDto;
import ma.almobadara.backend.dto.getbeneficiary.AdHocGroupWithBeneficiariesDto;
import ma.almobadara.backend.dto.getbeneficiary.GetListDTO;
import ma.almobadara.backend.dto.referentiel.CityDTO;
import ma.almobadara.backend.dto.referentiel.ServiceDTO;
import ma.almobadara.backend.dto.referentiel.TypePriseEnChargeDTO;
import ma.almobadara.backend.enumeration.BeneficiaryStatus;
import ma.almobadara.backend.mapper.BeneficiaryAdHocGroupeMapper;
import ma.almobadara.backend.mapper.BeneficiaryMapper;
import ma.almobadara.backend.model.beneficiary.Beneficiary;
import ma.almobadara.backend.model.beneficiary.BeneficiaryAdHocGroup;
import ma.almobadara.backend.model.beneficiary.BeneficiaryStatut;
import ma.almobadara.backend.repository.beneficiary.BeneficiaryAdHocGroupRepository;
import ma.almobadara.backend.repository.beneficiary.BeneficiaryRepository;
import ma.almobadara.backend.repository.beneficiary.BeneficiaryYearCountRepository;
import org.springframework.data.domain.*;
import org.springframework.data.rest.webmvc.ResourceNotFoundException;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
@Slf4j
public class BeneficiaryAdHocGroupService {

    private final BeneficiaryAdHocGroupeMapper beneficiaryAdHocGroupeMapper ;
    private final BeneficiaryMapper beneficiaryMapper ;
    private final BeneficiaryAdHocGroupRepository beneficiaryAdHocGroupRepository;
    private final RefFeignClient refFeignClient;
    private final EntityManager entityManager;
    private final BeneficiaryYearCountRepository beneficiaryYearCountRepository;
    private final BeneficiaryRepository beneficiaryRepository;
    private final RefController refController;


    public BeneficiaryAdHocGroupDto addBeneficiaryAdHocGroup(BeneficiaryAdHocGroupDto beneficiaryAdHocGroupDto) {
        beneficiaryAdHocGroupDto.setCreatedAt(LocalDateTime.ofInstant(Instant.now(), ZoneId.systemDefault()));
        String code = generateCode(beneficiaryAdHocGroupDto.getCreatedAt());
        beneficiaryAdHocGroupDto.setCode(code);
        BeneficiaryAdHocGroup beneficiaryAdHocGroup = beneficiaryAdHocGroupeMapper.toEntity(beneficiaryAdHocGroupDto);

        if (beneficiaryAdHocGroupDto.getTypePriseEnChargeIds() != null && !beneficiaryAdHocGroupDto.getTypePriseEnChargeIds().isEmpty()) {
            getListPriseEncharge(beneficiaryAdHocGroupDto, beneficiaryAdHocGroup);
        }

        BeneficiaryStatut beneficiaryStatut = new BeneficiaryStatut(BeneficiaryStatus.BENEFICIARY_AD_HOC_GROUP.getId());
        beneficiaryAdHocGroup.setBeneficiaryStatut(beneficiaryStatut);
        //beneficiaryAdHocGroup.setBeneficiaryStatut(String.valueOf(beneficiaryStatut));
        BeneficiaryAdHocGroup savedBeneficiaryAdHocGroup = beneficiaryAdHocGroupRepository.save(beneficiaryAdHocGroup);
        return beneficiaryAdHocGroupeMapper.toDTO(savedBeneficiaryAdHocGroup);
    }

    private String generateCode(LocalDateTime createdAt) {
        String newCode;
        int nextCodeNumber;

        String creationYear = createdAt.format(DateTimeFormatter.ofPattern("yyyy"));

        Optional<String> lastCodeOpt = beneficiaryAdHocGroupRepository.findTopByOrderByCodeDesc()
                .map(BeneficiaryAdHocGroup::getCode);

        if (lastCodeOpt.isPresent() && lastCodeOpt.get().startsWith("8" + creationYear)) {
            String lastCode = lastCodeOpt.get();
            nextCodeNumber = Integer.parseInt(lastCode.substring(5)) + 1;
        } else {
            nextCodeNumber = 1;
        }

        do {
            newCode = String.format("8%s%05d", creationYear, nextCodeNumber);
            nextCodeNumber++;
        } while (beneficiaryAdHocGroupRepository.existsByCode(newCode));

        return newCode;
    }

    public Page<BeneficiaryAdHocCombinedDto> getAllBeneficiariesAdHocCombined(int page, int size, String searchByNom,String searchByStatus, String searchByTypeBeneficiaire) {

        List<Beneficiary> allBeneficiaries = beneficiaryRepository.findBeneficiariesWithoutAdHocGroup();
        List<BeneficiaryAdHocGroup> allAdHocGroups = beneficiaryAdHocGroupRepository.findAll();

        List<BeneficiaryAdHocCombinedDto> combinedDtos = new ArrayList<>();

        allBeneficiaries.forEach(beneficiary -> combinedDtos.add(mapBeneficiaryToCombinedDto(beneficiary)));
        allAdHocGroups.forEach(adHocGroup -> {
            BeneficiaryAdHocCombinedDto beneficiaryAdHocCombinedDto=mapAdHocGroupToCombinedDto(adHocGroup);
            beneficiaryAdHocCombinedDto.setNumberOfMembers(adHocGroup.getNumberOfMembers());
            beneficiaryAdHocCombinedDto.setFullNameContact(adHocGroup.getName());

             combinedDtos.add(beneficiaryAdHocCombinedDto);
        });

        List<BeneficiaryAdHocCombinedDto> filteredDtos = combinedDtos.stream()
                .filter(dto -> applyFilter(dto, searchByNom,searchByStatus,searchByTypeBeneficiaire))

                .collect(Collectors.toList());

        int totalElements = filteredDtos.size();

        // Define sorting by "modificatedTime" in descending order (you can adjust to ascending if needed)
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Order.desc("modifiedAt")));

        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), totalElements);

        if (start >= totalElements) {
            return new PageImpl<>(new ArrayList<>(), pageable, totalElements);
        }

        // Sort the list by modificatedTime before paginating (if not already sorted)
        filteredDtos.sort((dto1, dto2) -> dto2.getModifiedAt().compareTo(dto1.getModifiedAt())); // descending order

        List<BeneficiaryAdHocCombinedDto> pagedList = filteredDtos.subList(start, end);

        return new PageImpl<>(pagedList, pageable, totalElements);
    }

    private boolean applyFilter(BeneficiaryAdHocCombinedDto dto, String searchByNom, String searchByStatus, String searchByTypeBeneficiare) {
        // Filter by Nom (first name or last name)
        if (searchByNom != null && !searchByNom.isEmpty()) {
            searchByNom = searchByNom.toLowerCase();
            if ((dto.getFirstName() != null && !dto.getFirstName().toLowerCase().contains(searchByNom)) &&
                    (dto.getLastName() != null && !dto.getLastName().toLowerCase().contains(searchByNom))) {
                return false; // If the Nom does not match, return false
            }
        }

        // Filter by Status
        if (searchByStatus != null && !searchByStatus.isEmpty()) {
            if (dto.getStatus() == null || !dto.getStatus().equalsIgnoreCase(searchByStatus)) {
                return false; // If Status does not match, return false
            }
        }

        // Filter by Type Beneficiare
        if (searchByTypeBeneficiare != null && !searchByTypeBeneficiare.isEmpty()) {
            if (dto.getBeneficiaryStatut() == null || !dto.getBeneficiaryStatut().equalsIgnoreCase(searchByTypeBeneficiare)) {
                return false; // If Type Beneficiare does not match, return false
            }
        }

        // If all filters pass, return true
        return true;
    }

//    private boolean applyFilter(BeneficiaryAdHocCombinedDto dto, BeneficiaryAdHocCombineFilterDto filter) {
//        if (filter == null) return true;
//
//        boolean matches = true;
//
//        if (filter.getCode() != null) {
//            matches = matches && dto.getCode().equalsIgnoreCase(filter.getCode());
//        }
//        if (filter.getFirstName() != null) {
//            matches = matches && dto.getFirstName() != null && dto.getFirstName().equalsIgnoreCase(filter.getFirstName());
//        }
//        if (filter.getLastName() != null) {
//            matches = matches && dto.getLastName() != null && dto.getLastName().equalsIgnoreCase(filter.getLastName());
//        }
//        if (filter.getBeneficiaryStatut() != null) {
//            matches = matches && dto.getBeneficiaryStatut().equalsIgnoreCase(filter.getBeneficiaryStatut());
//        }
//        if (filter.getStatus() != null) {
//            matches = matches && dto.getStatus().equalsIgnoreCase(filter.getStatus());
//        }
//
//        return matches;
//    }
//
    private BeneficiaryAdHocCombinedDto mapBeneficiaryToCombinedDto(Beneficiary beneficiary) {
        BeneficiaryAdHocCombinedDto dto = new BeneficiaryAdHocCombinedDto();
        dto.setId(beneficiary.getId());
        dto.setCode(beneficiary.getCode());
        dto.setFirstName(beneficiary.getPerson().getFirstName());
        dto.setLastName(beneficiary.getPerson().getLastName());
        dto.setFirstNameAr(beneficiary.getPerson().getFirstNameAr());
        dto.setLastNameAr(beneficiary.getPerson().getLastNameAr());
        dto.setComment(beneficiary.getComment());
        dto.setStatus(beneficiary.getStatusBeneficiaryAdHoc());
        dto.setBeneficiaryStatut(beneficiary.getBeneficiaryStatut().getNameStatut());
        dto.setCreatedAt(beneficiary.getCreatedAt());
        dto.setModifiedAt(beneficiary.getModifiedAt());
        dto.setName(null);
        dto.setFullNameContact(null);
        dto.setPhoneNumber(null);
        dto.setCityId(null);

        return dto;
    }
    private BeneficiaryAdHocCombinedDto mapAdHocGroupToCombinedDto(BeneficiaryAdHocGroup adHocGroup) {
        BeneficiaryAdHocCombinedDto dto = new BeneficiaryAdHocCombinedDto();
        dto.setId(adHocGroup.getId());
        dto.setCode(adHocGroup.getCode());
        dto.setName(adHocGroup.getName());
        dto.setFullNameContact(adHocGroup.getFullNameContact());
        dto.setStatus(adHocGroup.getStatus());
        dto.setPhoneNumber(adHocGroup.getPhoneNumber());
        dto.setComment(adHocGroup.getComment());
        dto.setCityId(adHocGroup.getCityId());
        dto.setBeneficiaryStatut(adHocGroup.getBeneficiaryStatut().getNameStatut());
        dto.setModifiedAt(adHocGroup.getUpdatedAt().atZone(ZoneId.systemDefault()).toInstant());
        dto.setCreatedAt(adHocGroup.getCreatedAt().atZone(ZoneId.systemDefault()).toInstant());
        dto.setFirstName(null);
        dto.setLastName(null);
        dto.setFirstNameAr(null);
        dto.setLastNameAr(null);
        dto.setIdentityCode(null);
        dto.setTypeIdentityId(null);
        dto.setPersonId(null);

        return dto;
    }


    public void addBeneficiariesToAdHocGroup(Long groupId, BeneficiaryAffectedToGroupDto dto) {
        BeneficiaryAdHocGroup group = beneficiaryAdHocGroupRepository.findById(groupId)
                .orElseThrow(() -> new EntityNotFoundException("Groupe AdHoc introuvable"));

        List<Beneficiary> currentBeneficiaries = group.getBeneficiaries();

        List<Beneficiary> newBeneficiaries = beneficiaryRepository.findAllById(dto.getPersonneIdsList())
                .stream()
                .filter(beneficiary -> !currentBeneficiaries.contains(beneficiary))
                .collect(Collectors.toList());

        if (!newBeneficiaries.isEmpty()) {
            currentBeneficiaries.addAll(newBeneficiaries);
            group.setBeneficiaries(currentBeneficiaries);
            beneficiaryAdHocGroupRepository.save(group);
        } else {
            throw new IllegalArgumentException("Aucun nouveau bénéficiaire n'a été ajouté.");
        }
    }

    public AdHocGroupWithBeneficiariesDto getBeneficiariesByAdHocGroup(Long groupId) {
       BeneficiaryAdHocGroup groups = beneficiaryAdHocGroupRepository.findById(groupId)
                .orElseThrow(() -> new ResourceNotFoundException("Groupe non trouvé avec ID : " + groupId));

        Optional<BeneficiaryAdHocGroup> group = beneficiaryAdHocGroupRepository.findById(groupId);

        BeneficiaryAdHocGroupDto adHocGroupWithBeneficiariesDto = beneficiaryAdHocGroupeMapper.toDTO(group.get());
      //  CityDTO cityDTO = null;
        CityDTO cityDTO = null;

        if (adHocGroupWithBeneficiariesDto.getCityId()!= null) {
            CityDTO fullCityDTO = refFeignClient.getParCity(adHocGroupWithBeneficiariesDto.getCityId());
            adHocGroupWithBeneficiariesDto.setCity(fullCityDTO);

            CityWithRegionAndCountryDTO fullCountryDto = refController.getCityWithRegionAndCountry(adHocGroupWithBeneficiariesDto.getCityId()).getBody();
            adHocGroupWithBeneficiariesDto.setInfo(fullCountryDto);
        }

        if (groups.getTypePriseEnChargeIdsList() != null && !groups.getTypePriseEnChargeIdsList().isEmpty()) {
            String[] ids = groups.getTypePriseEnChargeIdsList().split(",");
            List<TypePriseEnChargeDTO> typePriseEnChargeDTOs = new ArrayList<>();
            List<Long> typePriseEnChargeIds = new ArrayList<>();
            for (String idStr : ids) {
                Long TpecId = Long.parseLong(idStr.trim());
                TypePriseEnChargeDTO typePriseEnChargeDTO = refFeignClient.getParTypePriseEnCharge(TpecId);
                if (typePriseEnChargeDTO != null) {
                    typePriseEnChargeDTOs.add(typePriseEnChargeDTO);
                    typePriseEnChargeIds.add(typePriseEnChargeDTO.getId());
                }
            }
            adHocGroupWithBeneficiariesDto.setTypePriseEnCharges(typePriseEnChargeDTOs);
            adHocGroupWithBeneficiariesDto.setTypePriseEnChargeIds(typePriseEnChargeIds);
        }

        List<GetListDTO> beneficiaries = groups.getBeneficiaries().stream()
                .map(this::mapToGetListDTO)
                .collect(Collectors.toList());

        return AdHocGroupWithBeneficiariesDto.builder()
                .groupId(adHocGroupWithBeneficiariesDto.getId())
                .groupCode(adHocGroupWithBeneficiariesDto.getCode())
                .groupName(adHocGroupWithBeneficiariesDto.getName())
                .groupCityId(adHocGroupWithBeneficiariesDto.getCityId())
                .groupFullNameContact(adHocGroupWithBeneficiariesDto.getFullNameContact())
                .groupPhoneNumber(adHocGroupWithBeneficiariesDto.getPhoneNumber())
                .groupComment(adHocGroupWithBeneficiariesDto.getComment())
                .groupStatus(adHocGroupWithBeneficiariesDto.getStatus())
                .numberOfMembers(adHocGroupWithBeneficiariesDto.getNumberOfMembers())
                .groupCreatedAt(adHocGroupWithBeneficiariesDto.getCreatedAt())
                .city(adHocGroupWithBeneficiariesDto.getCity())
                .info(adHocGroupWithBeneficiariesDto.getInfo())
                .typePriseEnCharges(adHocGroupWithBeneficiariesDto.getTypePriseEnCharges())
                .typePriseEnChargeIds(adHocGroupWithBeneficiariesDto.getTypePriseEnChargeIds())
                .beneficiaries(beneficiaries)
                .build();
    }

    private GetListDTO mapToGetListDTO(Beneficiary beneficiary) {
        return GetListDTO.builder()
                .id(beneficiary.getId())
                .code(beneficiary.getCode())
                .independent(beneficiary.getIndependent())
                .archived(beneficiary.getArchived())
                .firstName(beneficiary.getPerson().getFirstName())
                .lastName(beneficiary.getPerson().getLastName())
                .firstNameAr(beneficiary.getPerson().getFirstNameAr())
                .lastNameAr(beneficiary.getPerson().getLastNameAr())
                .sex(beneficiary.getPerson().getSex())
                .phoneNumber(beneficiary.getPerson().getPhoneNumber())
                .address(beneficiary.getPerson().getAddress())
                .addressAr(beneficiary.getPerson().getAddressAr())
                .birthDate(beneficiary.getPerson().getBirthDate())
                .cityId(beneficiary.getPerson().getCityId())
                .identityCode(beneficiary.getPerson().getIdentityCode())
                .typeIdentityId(beneficiary.getPerson().getTypeIdentityId())
                .pictureUrl(beneficiary.getPerson().getPictureUrl())
                .comment(beneficiary.getComment())
                .beneficiaryStatutId(beneficiary.getBeneficiaryStatut().getId())
                .build();
    }

    public List<GetListDTO> getAllBeneficiary() {
        List<Beneficiary> beneficiaries = beneficiaryRepository.findBeneficiaryByBeneficiaryStatutId();
        return beneficiaries.stream()
                .map(beneficiaryMapper::beneficiaryToGetListDTO)
                .collect(Collectors.toList());
    }

    public void deleteAdHocGroupIfEmpty(Long groupId) {
        BeneficiaryAdHocGroup group = beneficiaryAdHocGroupRepository.findById(groupId)
                .orElseThrow(() -> new EntityNotFoundException("AdHoc Group not found"));

        if (group.getBeneficiaries() != null && !group.getBeneficiaries().isEmpty()) {
            throw new IllegalStateException("Group cannot be deleted because it contains beneficiaries.");
        }

        group.setDeleted(true);
        beneficiaryAdHocGroupRepository.save(group);
    }

    public BeneficiaryAdHocGroupDto updateBeneficiaryAdHocGroup(Long id, BeneficiaryAdHocGroupDto beneficiaryAdHocGroupDto) {
        BeneficiaryAdHocGroup existingGroup = beneficiaryAdHocGroupRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Beneficiary Ad-Hoc Group not found with id: " + id));

        beneficiaryAdHocGroupDto.setCode(existingGroup.getCode());

        existingGroup.setName(beneficiaryAdHocGroupDto.getName());
        existingGroup.setCityId(beneficiaryAdHocGroupDto.getCityId());
        existingGroup.setFullNameContact(beneficiaryAdHocGroupDto.getFullNameContact());
        existingGroup.setPhoneNumber(beneficiaryAdHocGroupDto.getPhoneNumber());
        existingGroup.setComment(beneficiaryAdHocGroupDto.getComment());
        existingGroup.setStatus(beneficiaryAdHocGroupDto.getStatus());
        existingGroup.setUpdatedAt(LocalDateTime.now());
        getListPriseEncharge(beneficiaryAdHocGroupDto, existingGroup);

        BeneficiaryAdHocGroup updatedGroup = beneficiaryAdHocGroupRepository.save(existingGroup);

        return beneficiaryAdHocGroupeMapper.toDTO(updatedGroup);
    }

    private void getListPriseEncharge(BeneficiaryAdHocGroupDto beneficiaryAdHocGroupDto, BeneficiaryAdHocGroup existingGroup) {
        StringBuilder priseEnChargesString = new StringBuilder();
        int listSize = beneficiaryAdHocGroupDto.getTypePriseEnChargeIds().size();
        int count = 0;
        for (Long pr : beneficiaryAdHocGroupDto.getTypePriseEnChargeIds()) {
            ServiceDTO typePriseEnChargeDTO = refFeignClient.getMetService(pr);
            priseEnChargesString.append(typePriseEnChargeDTO.getId());
            count++;
            if (count < listSize) {
                priseEnChargesString.append(",");
            }
        }
        existingGroup.setTypePriseEnChargeIdsList(priseEnChargesString.toString());
    }

}
