package ma.almobadara.backend.dto.aideComplemenatire;

import jakarta.persistence.Column;
import lombok.*;
import ma.almobadara.backend.dto.administration.TagDTO;
import ma.almobadara.backend.dto.communs.DocumentDTO;
import ma.almobadara.backend.dto.referentiel.TypePriseEnChargeDTO;
import ma.almobadara.backend.model.communs.Document;
import ma.almobadara.backend.model.service.Services;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@Builder
public class AideComplementaireDTO {
    private Long id;
    private String name;
    private String code;
    private String statut;
    private String slogan;
    private Double montantPrevu;
    private Double montantExecuter;
    private Double montantPrevuParBeneficiary;
    private LocalDateTime datePlanification;
    private LocalDateTime dateExecution;
    private LocalDateTime createdAt;
    private LocalDateTime modifiedAt;
    private LocalDateTime dateDebut;
    private LocalDateTime dateFin;
    private String commentaire;
    private List<DonorAideComplemenatireDTO> donorAideComplemenatireDTOList;
    private List<DonorAideComplemenatireDTO> donorWithBeneficiariesAideComplemenatireDTOList;
    private List<BeneficiaryAideComplemenatireDTO> beneficiaryAideComplemenatireDTOList;
    private List<BeneficiaryAideComplemenatireDTO> beneficiaryWithDonorAideComplemenatireDTOList;
    private BigDecimal costs;
    private Boolean priority;
    private Boolean propositionSystem;
    private Double amountPerBeneficiary;
    private Long serviceId;
    private Long nbrBeneficiariesValidated;
    private Long nbrDonorsParticipating;
    private Services services;
    private Double montantCollecter;
    private Double montantTotalAffecter;
    private Double montantRestant;
    private DocumentDTO documentDto;
    private LocalDateTime dateCloture;

    private List<TagDTO> tags;
}
