package ma.almobadara.backend.dto.referentiel;

import lombok.*;
import org.springframework.hateoas.RepresentationModel;

import java.io.Serializable;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class ServiceDTO extends RepresentationModel<ServiceDTO> implements Serializable {

	private static final long serialVersionUID = 9002412228709814331L;

	private Long id;

	private String code;
	private String name;
	private String nameAr;
	private String nameEn;

	private CategoryDTO category;

}
