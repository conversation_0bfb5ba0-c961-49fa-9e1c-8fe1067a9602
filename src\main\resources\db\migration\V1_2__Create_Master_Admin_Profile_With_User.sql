-- Insert into Profile table with auto-generated ID
INSERT INTO Profile (name_profile,is_deleted)
VALUES ('Master Admin' , false);

-- Insert into user_profile_module_functionality table
INSERT INTO user_profile_module_functionality (profile_id, functionality, module_functionalities_key)
SELECT (SELECT id FROM Profile ORDER BY id DESC LIMIT 1), ARRAY[0,1,2,3], module_functionalities_key
FROM (VALUES
    ('DONOR'),
    ('BENEFICIARY'),
    ('FAMILLE'),
    ('DONATION'),
    ('TAKEINCHARGE'),
    ('USER')
    ) AS keys(module_functionalities_key);

ALTER TABLE cache_ad_user ALTER COLUMN id SET DEFAULT nextval('cache_ad_user_id_seq');

-- Insert into cache_ad_user table without specifying the id column
INSERT INTO cache_ad_user (is_deleted, mail, creation_date, profile_id)
VALUES (false, 'soumaya.hamza<PERSON><EMAIL>', CURRENT_DATE, (SELECT id FROM Profile ORDER BY id DESC LIMIT 1));
