package ma.almobadara.backend.model.family;

import jakarta.persistence.*;
import lombok.*;

import java.util.Date;

@Entity
@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class TutorHistory {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @ManyToOne
    @JoinColumn(name = "family_member_id", nullable = false)
    private FamilyMember familyMember; // Foreign key to FamilyMember

    private Date dateDebut; // Start date of the tutor relationship
    private Date dateFin;   // End date of the tutor relationship

}
