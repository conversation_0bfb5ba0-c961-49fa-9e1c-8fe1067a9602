package ma.almobadara.backend.controller.family;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.ExternalIncomeDTO;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.service.family.ExternalIncomeService;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.Map;

import static ma.almobadara.backend.service.administration.CacheAdUserService.logUserInfo;

@RefreshScope
@RestController
@AllArgsConstructor
@Slf4j
@RequestMapping("/externalIncomes")
public class ExternalIncomeController {

    private final ExternalIncomeService externalIncomeService;

    @PostMapping
    @Operation(summary = "Create an ExternalIncome", description = "add a new ExternalIncome", tags = {"external-income"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = ExternalIncomeDTO.class))))})
    public ResponseEntity<ExternalIncomeDTO> createExternalIncome(@RequestBody ExternalIncomeDTO externalIncomeFamilyDTO) {

        logUserInfo("createExternalIncome");

        ExternalIncomeDTO created;
        HttpStatus status;
        try {
            created = externalIncomeService.addExternalIncome(externalIncomeFamilyDTO);
            status = HttpStatus.OK;
            log.info("End resource Create ExternalIncome, OK");
        } catch (TechnicalException e) {
            created = null;
            status = HttpStatus.INTERNAL_SERVER_ERROR;
            log.error("End resource Create ExternalIncome, KO: {}", e.getMessage());
        }

        return new ResponseEntity<>(created, new HttpHeaders(), status);
    }

}
