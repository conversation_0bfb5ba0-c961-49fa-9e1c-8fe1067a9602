package ma.almobadara.backend.model.family;

import jakarta.persistence.*;
import lombok.*;
import ma.almobadara.backend.model.communs.Note;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@IdClass(NoteFamilyId.class)
public class NoteFamily {

    @Id
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "family_id")
    private Family family;

    @Id
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "note_id")
    private Note note;

}
