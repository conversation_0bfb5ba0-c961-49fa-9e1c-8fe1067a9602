package ma.almobadara.backend.controller.mobile;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.mobile.DonorPhysiqueMobileDTO;
import ma.almobadara.backend.dto.mobile.KafalatsOptimisedMobileDTO;
import ma.almobadara.backend.dto.takenInCharge.TakenInChargeDonorDTO;
import ma.almobadara.backend.service.mobile.KafalatMobileService;
import ma.almobadara.backend.service.mobile.KafalatsOptimisedMobileService;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static ma.almobadara.backend.service.administration.CacheAdUserService.logUserInfo;

@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping("/mobile/kafalat")
public class KafalatMobileController {

    private final  KafalatMobileService kafalatMobileService;
    private final KafalatsOptimisedMobileService kafalatsOptimisedMobileService;

    @GetMapping("/optimised-kafalat/{idDonor}")
    public ResponseEntity<List<KafalatsOptimisedMobileDTO>> getOptimisedKafalatsByDonorId(@PathVariable Long idDonor) {
        logUserInfo("getOptimisedKafalatsByDonorId", String.valueOf(idDonor));

        List<KafalatsOptimisedMobileDTO> dtos = null;
        HttpStatus status;
        try {
            dtos = kafalatsOptimisedMobileService.getOptimisedKafalatsByDonorId(idDonor);
            status = HttpStatus.OK;
            log.info("End resource getOptimisedKafalatsByDonorId : {}. Retrieved kafalats: {}, OK", idDonor, dtos);
        } catch (Exception e) {
            status = HttpStatus.NOT_FOUND;
            log.error("End resource getOptimisedKafalatsByDonorId : {}. KO: {}", idDonor, e.getMessage());
        }

        return new ResponseEntity<>(dtos, status);
    }

    @GetMapping("/kafalat-for-donateur/{idDonor}")
    public ResponseEntity<List<TakenInChargeDonorDTO>> getTakenInChargeByIdDonateur(@PathVariable Long idDonor) {

        logUserInfo("getDonorByID", String.valueOf(idDonor));

        List<TakenInChargeDonorDTO> dtos = null;
        HttpStatus status;
        try {
            dtos = kafalatMobileService.getTakeInChargeByDonorId(idDonor);
            status = HttpStatus.OK;
            log.info("End resource getDonorByID : {}. Retrieved donor: {}, OK", idDonor, dtos);
        } catch (Exception e) {
            status = HttpStatus.NOT_FOUND;
            log.error("End resource getDonorByID : {}. KO: {}", idDonor, e.getMessage());
        }

        return new ResponseEntity<>(dtos, status);
    }
}
