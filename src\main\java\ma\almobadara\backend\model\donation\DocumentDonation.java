package ma.almobadara.backend.model.donation;

import jakarta.persistence.*;
import lombok.*;
import ma.almobadara.backend.model.communs.Document;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@IdClass(DocumentDonationId.class)
public class DocumentDonation {

    @Id
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "donation_id")
    private Donation donation;

    @Id
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "document_id")
    private Document document;

}
