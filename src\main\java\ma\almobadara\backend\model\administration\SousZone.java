package ma.almobadara.backend.model.administration;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;

import java.time.LocalDateTime;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class SousZone {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @Column(unique = true)
    private String code;
    private String name;
    private String nameAr;
    private String detail;
    //creationDate
    @CreationTimestamp
    @Column(updatable = false)
    private LocalDateTime creationDate;
    @ManyToOne
    @JoinColumn(name = "zone_id")
    private Zone zone;
}
