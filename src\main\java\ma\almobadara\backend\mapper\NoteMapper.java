package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.communs.NoteAuditDTO;
import ma.almobadara.backend.dto.communs.NoteDTO;
import ma.almobadara.backend.model.communs.Note;
import ma.almobadara.backend.model.donor.NoteDonor;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface NoteMapper {

    NoteDTO noteModelToDto(Note donor);

	Iterable<NoteDTO> noteListModelToDto(Iterable<Note> donors);

	Note noteDtoModelToModel(NoteDTO noteDTO);

	Iterable<NoteDonor> noteListDtoToModal(Iterable<NoteDTO> donors);

	@Mapping(target = "CreerPar", ignore = true)
	@Mapping(target = "Objet", source = "objet")
	@Mapping(target = "Contenu", source = "content")
	@Mapping(target = "DateDeCreation", source = "createdDate")
	NoteAuditDTO noteToNoteAuditDto(Note note);

}
