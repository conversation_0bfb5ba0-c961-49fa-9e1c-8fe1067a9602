package ma.almobadara.backend.model.service;

import com.fasterxml.jackson.annotation.JsonManagedReference;
import jakarta.persistence.Entity;
import jakarta.persistence.*;
import lombok.*;
import ma.almobadara.backend.model.administration.Eps;
import ma.almobadara.backend.model.administration.ServiceCollectEps;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.List;


@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
public class Services {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "code", nullable = false)
    private String code;

    @Column(name = "costs")
    private BigDecimal costs;

    @Column(name = "priority")
    private Boolean priority;

    @Column(name = "proposition_system")
    private Boolean propositionSystem;

    @Column(name = "amount_per_beneficiary")
    private Long amountPerBeneficiary;

    @Column(name = "statut_is_actif")
    private Boolean statutIsActif;

    @Column(name = "service_category_id")
    private Long serviceCategoryId;

    @Column(name = "service_category_type_id")
    private Long serviceCategoryTypeId;

    @Column(columnDefinition = "TEXT")
    private String commentaire;

    @CreationTimestamp
    @Column(name = "created_at", updatable = false, nullable = false)
    private Instant createdAt;

    @UpdateTimestamp
    @Column(name = "modified_at")
    private Instant modifiedAt;

    private Boolean isDedicatedToEps;
    private String collectionType;
    private String distributionType;

    @ManyToOne
    @JoinColumn(name = "eps_id")
    private Eps eps;

    @OneToMany(mappedBy = "service")
    private List<ServiceCollectEps> serviceCollectEpsList;
}
