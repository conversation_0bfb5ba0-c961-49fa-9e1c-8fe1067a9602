package ma.almobadara.backend.repository.donor;

import ma.almobadara.backend.model.donor.DonorAnonyme;
import ma.almobadara.backend.model.donor.DonorMoral;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;
@Repository
public interface DonorAnonymeRepository  extends JpaRepository<DonorAnonyme, Long> {
    // find by name
    Optional<DonorAnonyme> findByName(String name);

    @Query("SELECT e FROM DonorAnonyme e WHERE LOWER(e.name) LIKE LOWER(CONCAT('%', :query, '%')) OR LOWER(e.description) LIKE LOWER(CONCAT('%', :query, '%'))")
    Page<DonorAnonyme> searchDonor(@Param("query") String query, Pageable pageable);

    @Query("Select count(e) from DonorAnonyme e where e.deletedAt is null")
    long countByDeletedAtIsNull();
}
