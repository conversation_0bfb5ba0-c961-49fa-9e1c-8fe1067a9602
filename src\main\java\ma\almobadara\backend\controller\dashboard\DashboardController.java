package ma.almobadara.backend.controller.dashboard;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.dashboard.*;
import ma.almobadara.backend.model.administration.Eps;
import ma.almobadara.backend.service.dashboard.DashboardService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping("/dashboard")
public class DashboardController {

    private final DashboardService dashboardService;


    @GetMapping("/active-kafalates")
    public List<MonthlyServiceDataDTO> getGroupedActiveKafalates() {
        return dashboardService.getGroupedActiveKafalatesByMonthAndType();
    }

    @GetMapping("/general-statistics")
    public ResponseEntity<GeneralDashboardDTO> getGeneralStatistics() {
        GeneralDashboardDTO result = dashboardService.getGeneralDashboardData();
        return ResponseEntity.ok(result);
    }

    @GetMapping("/donor-statistics")
    public ResponseEntity<DonorStatsDTO> getDonorStatistics() {
        DonorStatsDTO result = dashboardService.getDonorStats();
        return ResponseEntity.ok(result);
    }

    @GetMapping("/beneficiary-statistics")
    public ResponseEntity<BeneficiaryStatsDTO> getBeneficiaryStats() {
        BeneficiaryStatsDTO result = dashboardService.getBeneficiaryStatsDTO();
        return ResponseEntity.ok(result);
    }



    @GetMapping("/active-kafalates-without-taken-in-charge")
    public List<MonthlyServiceDataDTO> getActiveKafalatesByMonthAndTypeWithoutTakenInCharge() {
        return dashboardService.getActiveKafalatesByMonthAndTypeWithoutTakenInCharge();
    }

    @GetMapping("/total_active_orphans")
    public ResponseEntity<List<ActiveKafalatesByMonthAndTypeDTO>> getActiveKafalatesOrphansByMonthAndSex() {
        List<ActiveKafalatesByMonthAndTypeDTO> result = dashboardService.getActiveKafalatesOrphansByMonthAndSex();
        return ResponseEntity.ok(result);
    }

    @GetMapping("/total_active_orphans-city")
    public ResponseEntity<List<ActiveKafalatesByMonthAndCityDTO>> getActiveKafalatesOrphansByMonthAndCity() {
        List<ActiveKafalatesByMonthAndCityDTO> result = dashboardService.getActiveKafalatesOrphansByMonthAndCity();
        return ResponseEntity.ok(result);
    }

    @GetMapping("/total_active_orphans-city-region")
    public ResponseEntity<Map<String, List<ActiveKafalatesByMonthAndCityDTO>>> getActiveKafalatesOrphansByMonthAndCityAndRegion(){
        var result = dashboardService.getActiveKafalatesOrphansByMonthAndCityAndRegion();
        return ResponseEntity.ok(result);
    }

    @GetMapping("/total_active_orphans-age")
    public ResponseEntity<List<ActiveKafalatesByMonthAndAge>> getActiveKafalatesOrphansByMonthAndAge() {
        List<ActiveKafalatesByMonthAndAge> result = dashboardService.getActiveKafalatesOrphansByMonthAndAge();
        return ResponseEntity.ok(result);
    }

    @GetMapping("/number_donor")
    public List<MonthlyDonorsDataDto> getNumberDonorByMonth() {
        return dashboardService.getNumberDonorByMonth();
    }

    @GetMapping("/number_donor_sex")
    public ResponseEntity<List<NombreDonateurByMonth>> getNumberDonorActiveByMonthAndSex() {
        List<NombreDonateurByMonth> result = dashboardService.getNumberDonorActiveByMonthAndSex();
        return ResponseEntity.ok(result);
    }
    @GetMapping("/total_donation")
    public List<MonthlyTotalDonationDataDto> getTotalDonationsByMonthAndType() {
        return dashboardService.getTotalDonationsByMonthAndType();
    }

    @GetMapping("/number_donor_city")
    public ResponseEntity<List<DonorActive>> getDonorPhysicalActiveByMonthAndCity() {
        List<DonorActive> result = dashboardService.getDonorPhysicalActiveByMonthAndCity();
        return ResponseEntity.ok(result);
    }

    @GetMapping("/donor-physical-region")
    public ResponseEntity<Map<String, List<DonorActive>>> getActiveDonorPhysicalByMonthAndCityAndRegion(){
        var result = dashboardService.getActiveDonorPhysicalByMonthAndCityAndRegion();
        return ResponseEntity.ok(result);
    }


    @GetMapping("/donor_moral_city")
    public ResponseEntity<List<DonorActive>> getDonorMoralActiveByMonthAndCity() {
        List<DonorActive> result = dashboardService.getDonorMoralActiveByMonthAndCity();
        return ResponseEntity.ok(result);
    }

    @GetMapping("/donor-moral-region")
    public ResponseEntity<Map<String, List<DonorActive>>> getActiveDonorMoralByMonthAndCityAndRegion(){
        var result = dashboardService.getActiveDonorMoralByMonthAndCityAndRegion();
        return ResponseEntity.ok(result);
    }


    @GetMapping("/beneficiary_level")
    public List<BeneficiaryLevel> getBeneficiaryLevel() {
        return dashboardService.getBeneficiaryLevel();
    }
    
    @GetMapping("/donation-stats")
    public ResponseEntity<DonationStatsDTO> getDonationStats() {
        log.info("Start resource getDonationStats");
        DonationStatsDTO result = dashboardService.getDonationStats();
        log.info("End resource getDonationStats");
        return ResponseEntity.ok(result);
    }

    @GetMapping("/kafalat-stats")
    public ResponseEntity<KafalatStatsDTO> getKafalatStats() {
        log.info("Start resource getKafalatStats");
        KafalatStatsDTO result = dashboardService.getKafalatStats();
        log.info("End resource getKafalatStats");
        return ResponseEntity.ok(result);
    }

    @GetMapping("/eps-stats")
    public ResponseEntity<EPSStatsDTO> getEpsStats() {
        log.info("Start resource getKafalatStats");
        EPSStatsDTO result = dashboardService.getEPSStats();
        log.info("End resource getKafalatStats");
        return ResponseEntity.ok(result);
    }
    
    @GetMapping("/aide-complementaire-statistics")
    public ResponseEntity<AideComplementaireStatsDTO> getAideComplementaireStats() {
        log.info("Start resource getAideComplementaireStats");
        AideComplementaireStatsDTO result = dashboardService.getAideComplementaireStats();
        log.info("End resource getAideComplementaireStats");
        return ResponseEntity.ok(result);
    }

}
