package ma.almobadara.backend.config;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.Collections;

@Slf4j
@Component
@RequiredArgsConstructor
public class JwtAuthFilter extends OncePerRequestFilter {

    private final JwtDecoder auth0JwtDecoder;

    @Override
    protected void doFilterInternal(
            HttpServletRequest request,
            HttpServletResponse response,
            FilterChain filterChain
    ) throws ServletException, IOException {
        // Get the path
        String path = request.getRequestURI();

        // Only apply this filter to /mobile/ paths that are not login or reset-password endpoints
        if (path.startsWith("/mobile/") && !path.matches(".*/login.*") && !path.matches(".*/reset-password.*")) {
            String authHeader = request.getHeader("Authorization");

            if (!StringUtils.hasText(authHeader) || !authHeader.startsWith("Bearer ")) {
                log.error("Invalid or missing Authorization header for mobile endpoint");
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                response.getWriter().write("Unauthorized: Valid Authorization header is required");
                return;
            }

            String token = authHeader.substring(7); // Remove "Bearer " prefix
            log.debug("Processing token for mobile endpoint: {}", path);

            try {
                log.debug("Validating Auth0 token for mobile endpoint: {}", path);
                var jwt = auth0JwtDecoder.decode(token);
                
                // Create authentication token with the subject from the JWT
                UsernamePasswordAuthenticationToken authToken = new UsernamePasswordAuthenticationToken(
                    jwt.getSubject(),
                    null,
                    Collections.emptyList()
                );
                
                SecurityContextHolder.getContext().setAuthentication(authToken);
                log.debug("Successfully authenticated mobile request with Auth0 token");
            } catch (Exception e) {
                log.error("Auth0 token validation failed: {}", e.getMessage());
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                response.getWriter().write("Unauthorized: Invalid token");
                return;
            }
        }

        filterChain.doFilter(request, response);
    }
}
