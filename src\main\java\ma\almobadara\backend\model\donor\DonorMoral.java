package ma.almobadara.backend.model.donor;

import jakarta.persistence.Entity;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Transient;
import lombok.*;
import lombok.experimental.SuperBuilder;
import ma.almobadara.backend.dto.referentiel.ActivitySectorDTO;
import ma.almobadara.backend.dto.referentiel.TypeDonorMoralDTO;

import java.util.List;

import static ma.almobadara.backend.util.strings.HandleSpecialChars.escapeSpecialChars;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class DonorMoral extends Donor{

    private String company;
    private String shortCompany;
    private String type = "Moral";
    private String logoUrl;
    private String password;

    private Long activitySectorId;
    @Transient
    private ActivitySectorDTO activitySectorDTO;
    private Long typeDonorMoralId;
    @Transient
    private TypeDonorMoralDTO typeDonorMoralDTO;
    @OneToMany(targetEntity = DonorContact.class, mappedBy = "donor")
    private List<DonorContact> donorContacts;


    public String toDtoString() {
        return "{"
                + "\"Raison Sociale\": \"" + escapeSpecialChars(company) + "\","
                + "\"Identifiant Entreprise\": \"" + escapeSpecialChars(super.getIdentityCode()) + "\","
                + "\"Statut\": \"" + escapeSpecialChars(super.getStatus().getName()) + "\","
                + "\"Secteur d'Activité\": \""
                + (activitySectorDTO != null ? escapeSpecialChars(activitySectorDTO.getName()) : "-") + "\","
                + "\"Type Donneur Moral\": \""
                + (typeDonorMoralDTO != null ? escapeSpecialChars(typeDonorMoralDTO.getName()) : "-") + "\","
                + "\"Date de la Première Donation\": \"" + escapeSpecialChars(super.getFirstDonationYear()) + "\","
                + "\"Abréviation du Nom\": \"" + escapeSpecialChars(shortCompany) + "\","
                + "\"Adresse\": \"" + escapeSpecialChars(super.getAddress()) + "\","
                + "\"Adresse Arabe\": \"" + escapeSpecialChars(super.getAddressAr()) + "\","
                + "\"Commentaire\": \"" + escapeSpecialChars(super.getComment()) + "\","
                + "\"Code\": \"" + escapeSpecialChars(super.getCode()) + "\","
                + "\"Type\": \"" + escapeSpecialChars(type) + "\""
                + "}";
    }



}
