package ma.almobadara.backend.model.donor;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Objects;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class NoteDonorId implements Serializable {

    private Long donor;
    private Long note;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        NoteDonorId that = (NoteDonorId) o;
        return Objects.equals(donor, that.donor) && Objects.equals(note, that.note);
    }

    @Override
    public int hashCode() {
        return Objects.hash(donor, note);
    }

}
