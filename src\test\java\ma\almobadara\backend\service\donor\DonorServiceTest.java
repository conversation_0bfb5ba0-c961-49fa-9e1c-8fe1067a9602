package ma.almobadara.backend.service.donor;

import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityNotFoundException;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;
import ma.almobadara.backend.Audit.AuditApplicationService;
import ma.almobadara.backend.clients.RefFeignClient;
import ma.almobadara.backend.config.Messages;
import ma.almobadara.backend.controller.referentiel.RefController;
import ma.almobadara.backend.dto.CityWithRegionAndCountryDTO;
import ma.almobadara.backend.dto.donor.*;
import ma.almobadara.backend.dto.referentiel.*;
import ma.almobadara.backend.dto.takenInCharge.TakenInChargeDonorDTO;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.mapper.*;
import ma.almobadara.backend.model.communs.Document;
import ma.almobadara.backend.model.donation.Donation;
import ma.almobadara.backend.model.donor.*;
import ma.almobadara.backend.model.service.Services;
import ma.almobadara.backend.model.takenInCharge.TakenInCharge;
import ma.almobadara.backend.model.takenInCharge.TakenInChargeDonor;
import ma.almobadara.backend.repository.DonorYearCountRepository;
import ma.almobadara.backend.repository.administration.TagRepository;
import ma.almobadara.backend.repository.administration.TaggableRepository;
import ma.almobadara.backend.repository.aideComplemenatire.AideComplementaireDonorBeneficiaryRepository;
import ma.almobadara.backend.repository.beneficiary.HistoryRapportRepository;
import ma.almobadara.backend.repository.beneficiary.RapportRepository;
import ma.almobadara.backend.repository.communs.ActionDonorRepository;
import ma.almobadara.backend.repository.communs.DocumentDonorRepository;
import ma.almobadara.backend.repository.communs.NoteDonorRepository;
import ma.almobadara.backend.repository.donation.BudgetLineRepository;
import ma.almobadara.backend.repository.donation.DonationHistoryRepository;
import ma.almobadara.backend.repository.donation.DonationRepository;
import ma.almobadara.backend.repository.donor.*;
import ma.almobadara.backend.repository.services.ServicesRepository;
import ma.almobadara.backend.repository.takenInCharge.TakenInChargeOperationRepository;
import ma.almobadara.backend.repository.takenInCharge.TakenInChargeRepository;
import ma.almobadara.backend.service.communs.ExportService;
import ma.almobadara.backend.service.communs.NoteService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import static ma.almobadara.backend.util.constants.GlobalConstants.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class DonorServiceTest {


    @InjectMocks
    private DonorService donorService;

    // Mock only repositories
    @Mock private DonorRepository donorRepository;
    @Mock private DonationRepository donationRepository;
    @Mock private TakenInChargeDonorRepository takenInChargeDonorRepository;
    @Mock private TakenInChargeRepository takenInChargeRepository;
    @Mock private TakenInChargeOperationRepository takenInChargeOperationRepository;
    @Mock private AideComplementaireDonorBeneficiaryRepository aideComplementaireDonorBeneficiaryRepository;
    @Mock private DonorYearCountRepository donorYearCountRepository;
    @Mock private DonorPhysicalLanguageCommunicationRepository donorPhysicalLanguageCommunicationRepository;
    @Mock private DonorPhysicalRepository donorPhysicalRepository;
    @Mock private DonorPhysicalCanalCommunicationRepository donorPhysicalCanalCommunicationRepository;
    @Mock private DocumentDonorRepository documentDonorRepository;
    @Mock private MinioService minioService;
    @Mock
    private EntityManager entityManager;

    @Mock private DonorMoralRepository donorMoralRepository;
    @Mock private DonorContactRepository donorContactRepository;
    @Mock private NoteDonorContactRepository noteDonorContactRepository;
    @Mock private DonorContactCanalCommunicationRepository donorContactCanalCommunicationRepository;
    @Mock private DonorContactLanguageCommunicationRepository donorContactLanguageCommunicationRepository;
    @Mock private Messages messages;
    @Mock private RefFeignClient refFeignClient;
    @Mock private AuditApplicationService auditApplicationService;
    @Mock private ExportService exportService;
    @Mock private DonorAnonymeRepository donorAnonymeRepository;
    @Mock private ServicesRepository servicesRepository;
    @Mock private BudgetLineRepository budgetLineRepository;
    @Mock private RefController refController;

    private final DonorPhysicalMapper donorPhysicalMapper = new DonorPhysicalMapperImpl();
    private final DonorMoralMapper donorMoralMapper = new DonorMoralMapperImpl();
    private final DonorContactMapper donorContactMapper = new DonorContactMapperImpl();
    private final DonorAnonymeMapper donorAnonymeMapper = new DonorAnonymeMapperImpl();
    private final ServicesMapper servicesMapper = new ServicesMapperImpl();
    private final BeneficiaryMapper beneficiaryMapper = new BeneficiaryMapperImpl();

    @Mock private TaggableRepository taggableRepository;
    @Mock private TagRepository tagRepository;

    @Mock private CorrespondenceRepository correspondenceRepository;
    @Mock private NoteDonorRepository noteDonorRepository;
    @Mock private ActionDonorRepository actionDonorRepository;
    @Mock private DonationHistoryRepository donationHistoryRepository;
    @Mock private RapportRepository rapportRepository;
    @Mock private NoteService noteService;
    @Mock private HistoryRapportRepository historyRapportRepository;


    @BeforeEach
    void setUp() {
        donorService = new DonorService(
                // Repositories
                takenInChargeRepository, donorRepository,taggableRepository,tagRepository, donationRepository, takenInChargeDonorRepository,
                takenInChargeOperationRepository, aideComplementaireDonorBeneficiaryRepository,

                // Mappers
                donorPhysicalMapper, donorYearCountRepository, donorPhysicalLanguageCommunicationRepository,
                donorPhysicalRepository, donorPhysicalCanalCommunicationRepository,

                // Services & Storage
                documentDonorRepository, minioService,

                // More Mappers
                donorMoralMapper, refController, donorContactMapper,

                // Additional Repositories
                donorMoralRepository, donorContactRepository, noteDonorContactRepository,
                donorContactCanalCommunicationRepository, donorContactLanguageCommunicationRepository,

                // Utilities
                messages, refFeignClient, auditApplicationService, entityManager, exportService,

                // Other Repositories
                correspondenceRepository, noteDonorRepository, actionDonorRepository,
                donorAnonymeRepository, donorAnonymeMapper, servicesRepository, budgetLineRepository,
                servicesMapper, donationHistoryRepository, rapportRepository, noteService,
                historyRapportRepository, beneficiaryMapper
        );
    }


    // add Physique donor

    @Test
    void donorService_addDonorPhysique_SuccessAdd() throws TechnicalException {

        DonorPhysicalDTO physicalDTO=DonorPhysicalDTO
                .builder()
                .firstName("Hamza")
                .lastName("Nachid")
                .firstNameAr("HamzaAr")
                .lastNameAr("NachidAr")
                .email("<EMAIL>")
                .status(DonorStatusDTO.builder().id(1L).build())
                .phoneNumber("0707070707")
                .typeIdentity(TypeIdentityDTO.builder().id(1L).build())
                .identityCode("faa3E21S")
                .profession(ProfessionDTO.builder().id(11L).build())
                .sex("Homme")
                .code("DA2025XX22S")
                .address("add")
                .addressAr("addr")
                .city(CityDTO.builder().id(76446L).region(RegionDTO.builder().id(1884L).country(CountryDTO.builder().id(149L).build()).build()).build())
                .firstDonationYear("2025")
                .canalCommunications(List.of(CanalCommunicationDTO.builder().id(2L).code("met_canal_communication_Téléphone").name("Téléphone").build()))
                .languageCommunications(List.of(LanguageCommunicationDTO.builder().id(3L).code("par_language_communication_anglais").name("Anglais").build()))
                .build();
        DonorPhysical donorPhysical= donorPhysicalMapper.donorPhysicalDtoToModel(physicalDTO);
        donorPhysical.setId(1L);

        when(donorPhysicalRepository.save(any(DonorPhysical.class))).thenReturn(donorPhysical);
        when(refFeignClient.getMetCanalCommunication(anyLong())).thenReturn(CanalCommunicationDTO.builder().id(1L).name("test").build());
        when(refFeignClient.getParLanguageCommunication(anyLong())).thenReturn(LanguageCommunicationDTO.builder().id(1L).name("test").build());
        when(refFeignClient.getParTypeIdentity(anyLong())).thenReturn(TypeIdentityDTO.builder().id(1L).name("testName").build());
        when(refFeignClient.getMetProfession(anyLong())).thenReturn(ProfessionDTO.builder().id(1L).name("testName").build());
        when(refFeignClient.getParDonorStatus(anyLong())).thenReturn(DonorStatusDTO.builder().id(1L).name("testName").build());



        DonorDTO donorDTO= donorService.addDonorPhysique(physicalDTO);

        assertNotNull(donorDTO);
        assertEquals(1L, donorDTO.getId());
        assertEquals("faa3E21S",donorDTO.getIdentityCode());
        assertEquals("add",donorDTO.getAddress());
        assertEquals("addr",donorDTO.getAddressAr());
        assertEquals(physicalDTO.getCity(),donorDTO.getCity());
        assertNotNull(donorDTO);

        verify(donorPhysicalRepository,times(1)).save(any(DonorPhysical.class));
        verify(refFeignClient,times(1)).getMetCanalCommunication(anyLong());
        verify(refFeignClient,times(1)).getParLanguageCommunication(anyLong());
        verify(refFeignClient,times(1)).getParTypeIdentity(anyLong());
        verify(refFeignClient,times(1)).getMetProfession(anyLong());
        verify(refFeignClient,times(1)).getParDonorStatus(anyLong());
        verify(auditApplicationService, times(1)).audit(
                eq("Ajout d'nouveau donateur physique"),
                anyString(),
                eq("Add Physical Donor"),
                isNull(),
                anyString(),
                eq(DONATEUR),
                eq(CREATE)
        );
    }

    @Test
    void donorService_addDonorPhysique_SuccessUpdate() throws TechnicalException {

        DonorPhysicalDTO physicalDTO=DonorPhysicalDTO
                .builder()
                .id(2L)
                .firstName("Hamza")
                .lastName("Nachid")
                .firstNameAr("HamzaAr")
                .lastNameAr("NachidAr")
                .email("<EMAIL>")
                .status(DonorStatusDTO.builder().id(1L).build())
                .phoneNumber("0707070707")
                .typeIdentity(TypeIdentityDTO.builder().id(1L).build())
                .identityCode("faa3E21S")
                .profession(ProfessionDTO.builder().id(11L).build())
                .sex("Homme")
                .address("add")
                .addressAr("addr")
                .city(CityDTO.builder().id(76446L).region(RegionDTO.builder().id(1884L).country(CountryDTO.builder().id(149L).build()).build()).build())
                .firstDonationYear("2025")
                .canalCommunications(List.of(CanalCommunicationDTO.builder().id(2L).code("met_canal_communication_Téléphone").name("Téléphone").build()))
                .languageCommunications(List.of(LanguageCommunicationDTO.builder().id(3L).code("par_language_communication_anglais").name("Anglais").build()))
                .build();

        DonorPhysical donorPhysical= donorPhysicalMapper.donorPhysicalDtoToModel(physicalDTO);
        donorPhysical.setId(2L);

        when(donorPhysicalRepository.save(any(DonorPhysical.class))).thenReturn(donorPhysical);
        when(donorPhysicalRepository.findById(2L)).thenReturn(Optional.of(donorPhysical));
        when(donorRepository.findById(2L)).thenReturn(Optional.of(donorPhysical));
        when(refFeignClient.getMetCanalCommunication(anyLong())).thenReturn(CanalCommunicationDTO.builder().id(1L).name("test").build());
        when(refFeignClient.getParLanguageCommunication(anyLong())).thenReturn(LanguageCommunicationDTO.builder().id(1L).name("test").build());
        when(refFeignClient.getParTypeIdentity(anyLong())).thenReturn(TypeIdentityDTO.builder().id(1L).name("testName").build());
        when(refFeignClient.getMetProfession(anyLong())).thenReturn(ProfessionDTO.builder().id(1L).name("testName").build());
        when(refFeignClient.getParDonorStatus(anyLong())).thenReturn(DonorStatusDTO.builder().id(1L).name("testName").build());



        DonorDTO donorDTO= donorService.addDonorPhysique(physicalDTO);

        assertNotNull(donorDTO);
        assertEquals(2L, donorDTO.getId());
        assertEquals("faa3E21S",donorDTO.getIdentityCode());
        assertEquals("add",donorDTO.getAddress());
        assertEquals("addr",donorDTO.getAddressAr());
        assertEquals(physicalDTO.getCode(),donorDTO.getCode());
        assertEquals(physicalDTO.getCity(),donorDTO.getCity());
        assertNotNull(donorDTO);

        verify(donorPhysicalRepository,times(1)).save(any(DonorPhysical.class));
        verify(refFeignClient,times(2)).getMetCanalCommunication(anyLong());
        verify(refFeignClient,times(2)).getParLanguageCommunication(anyLong());
        verify(refFeignClient,times(2)).getParTypeIdentity(anyLong());
        verify(refFeignClient,times(2)).getMetProfession(anyLong());
        verify(refFeignClient,times(2)).getParDonorStatus(anyLong());
        verify(auditApplicationService, times(1)).audit(
                eq("Modification des informations du donateur physique"),
                anyString(), // getUsernameFromJwt() will return a dynamic value
                eq("Update Physical Donor"),
                anyString(),
                anyString(),
                eq(DONATEUR),
                eq(UPDATE)
        );

    }

    @Test
    void donorService_addDonorPhysique_nullDonorPhysique(){
        assertThrows(TechnicalException.class, () -> donorService.addDonorPhysique(null));
    }

    @Test
    void donorService_addDonorPysique_DonorNotFoundInUpdate(){

        DonorPhysicalDTO physicalDTO=DonorPhysicalDTO
                .builder()
                .id(2L)
                .firstName("Hamza")
                .lastName("Nachid")
                .firstNameAr("HamzaAr")
                .lastNameAr("NachidAr")
                .email("<EMAIL>")
                .status(DonorStatusDTO.builder().id(1L).build())
                .phoneNumber("0707070707")
                .typeIdentity(TypeIdentityDTO.builder().id(1L).build())
                .identityCode("faa3E21S")
                .profession(ProfessionDTO.builder().id(11L).build())
                .sex("Homme")
                .address("add")
                .addressAr("addr")
                .city(CityDTO.builder().id(76446L).region(RegionDTO.builder().id(1884L).country(CountryDTO.builder().id(149L).build()).build()).build())
                .firstDonationYear("2025")
                .canalCommunications(List.of(CanalCommunicationDTO.builder().id(2L).code("met_canal_communication_Téléphone").name("Téléphone").build()))
                .languageCommunications(List.of(LanguageCommunicationDTO.builder().id(3L).code("par_language_communication_anglais").name("Anglais").build()))
                .build();

        DonorPhysical donorPhysical= donorPhysicalMapper.donorPhysicalDtoToModel(physicalDTO);
        donorPhysical.setId(2L);

        when(donorPhysicalRepository.findById(2L)).thenReturn(Optional.empty());



        assertThrows(TechnicalException.class,()->donorService.addDonorPhysique(physicalDTO));

        verify(donorPhysicalRepository,times(1)).findById(2L);


    }

    // add Anonyme donor
    @Test
    void donorService_addDonorAnonyme_SuccessAdd(){
        DonorAnonymeDTO donorAnonymeDTO = DonorAnonymeDTO
                .builder()
                .name("Anonyme")
                .comment("Anonyme comment")
                .status(DonorStatusDTO.builder().id(1L).build())
                .build();
        DonorAnonyme donorAnonyme=donorAnonymeMapper.toModel(donorAnonymeDTO);
        donorAnonyme.setId(2L);
        donorAnonyme.setCode("anonyme code");
        when(donorAnonymeRepository.save(any(DonorAnonyme.class))).thenReturn(donorAnonyme);

        DonorDTO donorDTO=donorService.addDonorAnonyme(donorAnonymeDTO);
        System.out.println(donorDTO);
        assertNotNull(donorDTO);
        assertEquals(2L, donorDTO.getId());
        assertNotNull(donorDTO.getCode());

        verify(donorAnonymeRepository,times(1)).save(any(DonorAnonyme.class));
    }

    @Test
    void donorService_addDonorAnonyme_SuccessUpdate(){
        DonorAnonymeDTO donorAnonymeDTO = DonorAnonymeDTO
                .builder()
                .id(2L)
                .code("just a fake code")
                .name("Anonyme")
                .createdAt(LocalDateTime.now())
                .comment("Anonyme comment")
                .status(DonorStatusDTO.builder().id(1L).build())
                .build();

        DonorAnonyme donorAnonyme=donorAnonymeMapper.toModel(donorAnonymeDTO);

        when(donorAnonymeRepository.findById(2L)).thenReturn(Optional.of(donorAnonyme));
        when(donorAnonymeRepository.save(any(DonorAnonyme.class))).thenReturn(donorAnonyme);
        when(refFeignClient.getParDonorStatus(donorAnonymeDTO.getStatus().getId())).thenReturn(DonorStatusDTO.builder().id(1L).build());

        DonorDTO donorDTO=donorService.addDonorAnonyme(donorAnonymeDTO);

        assertNotNull(donorDTO);
        assertEquals(2L, donorDTO.getId());

        verify(donorAnonymeRepository,times(1)).save(any(DonorAnonyme.class));
        verify(donorAnonymeRepository,times(1)).findById(2L);
        verify(refFeignClient,times(1)).getParDonorStatus(donorAnonymeDTO.getStatus().getId());
    }

    @Test
    void donorService_addDonorAnonyme_NotFoundInUpdate(){
        DonorAnonymeDTO donorAnonymeDTO = DonorAnonymeDTO
                .builder()
                .id(2L)
                .build();

        when(donorAnonymeRepository.findById(2L)).thenThrow(new EntityNotFoundException("Donor not found"));

        assertThrows(EntityNotFoundException.class,()->donorService.addDonorAnonyme(donorAnonymeDTO));

        verify(donorAnonymeRepository,times(1)).findById(2L);
    }

    // add Moral donor

    @Test
    void donorService_addDonorMoral_SuccessAdd() throws TechnicalException {
        DonorMoralDTO donorMoralDto= DonorMoralDTO
                .builder()
                .company("test")
                .comment("test comment")
                .build();

        DonorMoral donor=donorMoralMapper.donorMoralDtoToModel(donorMoralDto);

        donor.setId(2L);


        when(donorMoralRepository.save(any(DonorMoral.class))).thenReturn(donor);

        DonorDTO donorMoralDTO=donorService.addDonorMoral(donorMoralDto);

        assertNotNull(donorMoralDTO);
        assertEquals(2L, donorMoralDTO.getId());

        verify(donorMoralRepository,times(1)).save(any(DonorMoral.class));
        verify(auditApplicationService, times(1)).audit(
                contains("Ajout d'un nouveau donateur moral: "+ donorMoralDto.getCompany()),
                anyString(),
                eq("Add Moral Donor"),
                isNull(),
                anyString(),
                eq(DONATEUR),
                eq(CREATE)
        );
    }

    @Test
    void donorService_addDonorMoral_SuccessUpdate() throws TechnicalException {
        DonorMoralDTO donorMoralDto= DonorMoralDTO
                .builder()
                .id(2L)
                .code("DM2025")
                .company("test")
                .activitySector(ActivitySectorDTO.builder().id(1L).name("test").build())
                .typeDonorMoral(TypeDonorMoralDTO.builder().id(2L).name("test").build())
                .donorContacts(List.of(DonorContactDTO.builder().id(3L).build()))
                .shortCompany("shortCompany")
                .comment("test comment")
                .build();

        DonorMoral donor=donorMoralMapper.donorMoralDtoToModel(donorMoralDto);


        Donor donor1=Donor.builder().build();


        when(donorMoralRepository.save(any(DonorMoral.class))).thenReturn(donor);
        when(donorMoralRepository.findById(2L)).thenReturn(Optional.of(donor));
        when(donorRepository.findById(2L)).thenReturn(Optional.of(donor1));
        when(refFeignClient.getMetActivitySector(1L)).thenReturn(ActivitySectorDTO.builder().id(1L).name("test").build());
        when(refFeignClient.getMetTypeDonorMoral(2L)).thenReturn(TypeDonorMoralDTO.builder().id(2L).name("test").build());



        DonorDTO donorMoralDTO=donorService.addDonorMoral(donorMoralDto);

        assertNotNull(donorMoralDTO);
        assertEquals(2L, donorMoralDTO.getId());
        assertEquals("DM2025", donorMoralDTO.getCode());
        verify(donorMoralRepository,times(1)).save(any(DonorMoral.class));

    }


    @Test
    void donorService_addDonorMoral_NotFoundInUpdate()  {
        DonorMoralDTO donorMoralDto= DonorMoralDTO
                .builder()
                .id(2L)
                .code("DM2025")
                .company("test")
                .activitySector(ActivitySectorDTO.builder().id(1L).name("test").build())
                .typeDonorMoral(TypeDonorMoralDTO.builder().id(2L).name("test").build())
                .donorContacts(List.of(DonorContactDTO.builder().id(3L).build()))
                .shortCompany("shortCompany")
                .comment("test comment")
                .build();

        DonorMoral donor=donorMoralMapper.donorMoralDtoToModel(donorMoralDto);

        when(donorMoralRepository.findById(2L)).thenReturn(Optional.empty());



        assertThrows(TechnicalException.class,()->donorService.addDonorMoral(donorMoralDto));

        verify(donorMoralRepository,times(1)).findById(2L);
    }


    // Test Get All

    @Test
    void donorService_getDonorsByCriteria_getAll() throws TechnicalException {
        Donor donor = new Donor();
        donor.setId(1L);
        donor.setCode("DM2025");
        Donor donor1 = new Donor();
        donor1.setId(2L);
        donor1.setCode("DM2022");

        Pageable pageable = PageRequest.of(0, 10);
        Page<Donor> donorPage = new PageImpl<>(List.of(donor,donor1), pageable, 1);

        when(donorRepository.findByDeletedAtIsNull(any(Pageable.class))).thenReturn(donorPage);


        Page<DonorDTO> result = donorService.getDonorsByCriteria(0, 10, null, null, null, null, null, null, null, null, null, null, null, null,null);

        assertNotNull(result);
        assertEquals(2, result.getTotalElements());

        verify(donorRepository,times(1)).findByDeletedAtIsNull(any(Pageable.class));
    }

    // DELETE TEST

    @Test
    void DonorService_deleteDonor_success() throws TechnicalException {
        // Arrange
        Donor donor1 = new Donor();
        donor1.setId(1L);
        donor1.setTakenInChargeDonors(List.of());
        donor1.setDonations(List.of());
        DonorPhysicalMapper donorPhysicalMapper = new DonorPhysicalMapperImpl();






        when(donorRepository.findById(1L)).then(invocation -> Optional.of(donor1));
        when(documentDonorRepository.findByDonor(any(Donor.class))).thenReturn(List.of());
        when(donorRepository.save(any(Donor.class))).then(invocation -> invocation.getArgument(0));
        doNothing().when(auditApplicationService)
                .audit(anyString(), anyString(), anyString(), anyString(), isNull(), eq(DONATEUR), eq(DELETE));

        // Acts

        donorService.deleteDonor(1L);

        // Assertions
        verify(donorRepository, times(1)).findById(1L);
        verify(donorRepository, times(1)).save((any(Donor.class)));
        verify(documentDonorRepository, times(1)).findByDonor((any(Donor.class)));
    }

    @Test
    void DonorService_deleteDonor_NotFount()  {

        when(donorRepository.findById(1L)).thenReturn(Optional.empty());

        // Asserts

        IllegalArgumentException exception= assertThrows(IllegalArgumentException.class,()->donorService.deleteDonor(1L));
        assertEquals("Donateur introuvable", exception.getMessage());
        verify(donorRepository, times(1)).findById(1L);
        verifyNoMoreInteractions(donorRepository);

    }


    @Test
    void DonorService_deleteDonor_DonorHasTakenInCharge()  {
        // Arrange
        Donor donor1 = new Donor();
        donor1.setId(1L);
        donor1.setTakenInChargeDonors(List.of(new TakenInChargeDonor()));
        donor1.setDonations(List.of());

        // Acts

        when(donorRepository.findById(1L)).thenReturn(Optional.of(donor1));

        // Asserts

        IllegalStateException exception= assertThrows(IllegalStateException.class,()->donorService.deleteDonor(1L));
        assertEquals("Ce donateur est lié à des services (Kafalat ou Aides Complémentaires) ou des donations. Veuillez les dissocier avant de le supprimer.", exception.getMessage());

        verify(donorRepository, times(1)).findById(1L);
        verifyNoMoreInteractions(donorRepository);

    }

    @Test
    void DonorService_deleteDonor_DonorHasDonation() {
        // Arrange
        Donor donor1 = new Donor();
        donor1.setId(1L);
        donor1.setTakenInChargeDonors(List.of());
        donor1.setDonations(List.of(new Donation()));

        // Acts

        when(donorRepository.findById(1L)).thenReturn(Optional.of(donor1));

        // Asserts

        IllegalStateException exception = assertThrows(IllegalStateException.class, () -> donorService.deleteDonor(1L));
        assertEquals("Ce donateur est lié à des services (Kafalat ou Aides Complémentaires) ou des donations. Veuillez les dissocier avant de le supprimer.", exception.getMessage());

        verify(donorRepository, times(1)).findById(1L);
        verifyNoMoreInteractions(donorRepository);

    }

    // GET By Donor By Id

    @Test
    void DonorService_getDonorById_DonorNotFound()  {
        // Arrange
        Donor donor1 = new Donor();
        donor1.setId(1L);
        donor1.setTakenInChargeDonors(List.of());
        donor1.setDonations(List.of(new Donation()));
        // Acts

        when(donorRepository.findById(1L)).thenReturn(Optional.empty());

        // Asserts

        assertThrows(TechnicalException.class,()->donorService.getDonorById(1L));

        verify(donorRepository, times(1)).findById(1L);
        verifyNoMoreInteractions(donorRepository);

    }

    @Test
    void DonorService_getDonorById_DonorHaveNoType()  {

        // Arrange
        Donor donor1 = new Donor();
        donor1.setId(1L);
        donor1.setTakenInChargeDonors(List.of());
        donor1.setDonations(List.of(new Donation()));
        // Acts

        when(donorRepository.findById(1L)).thenReturn(Optional.of(donor1));

        // Asserts

        assertThrows(TechnicalException.class,()->donorService.getDonorById(1L));

        verify(donorRepository, times(1)).findById(1L);
        verifyNoMoreInteractions(donorRepository);

    }

    @Test
    void DonorService_getDonorById_SuccessAnonyme() throws TechnicalException {
        DonorAnonyme anonyme=
                DonorAnonyme
                        .builder()
                        .id(1L)
                        .status(DonorStatusDTO.builder().id(1L).build())
                        .cityId(1L)
                        .donorStatusId(1L)
                        .build();

        when(donorRepository.findById(1L)).thenReturn(Optional.of(anonyme));
        when(refFeignClient.getParDonorStatus(1L)).thenReturn(DonorStatusDTO.builder().id(1L).name("test status").build());

        DonorDTO donor= donorService.getDonorById(1L);

        assertNotNull(donor);
        assertNotNull(donor.getStatus());
        assertEquals("test status", donor.getStatus().getName());

        verify(donorRepository, times(1)).findById(1L);
        verify(refFeignClient, times(1)).getParDonorStatus(1L);
    }

    @Test
    void DonorService_getDonorById_SuccessPhysique() throws TechnicalException {
        DonorPhysical anonyme=
                DonorPhysical
                        .builder()
                        .id(1L)
                        .code("DX2")
                        .status(DonorStatusDTO.builder().id(1L).build())
                        .typeIdentity(1L)
                        .professionId(1L)
                        .donorPhysicalCanalCommunication(Set.of(DonorPhysicalCanalCommunication.builder().id(1L).canalCommunicationId(1L).build()))
                        .donorPhysicalLanguageCommunications(Set.of(DonorPhysicalLanguageCommunication.builder().id(1L).languageCommunicationId(1L).build()))
                        .documentsDonors(List.of())
                        .cityId(1L)
                        .donorStatusId(1L)
                        .build();

        when(donorRepository.findById(1L)).thenReturn(Optional.of(anonyme));
        when(refController.getParDonorStatus(1L)).thenReturn(ResponseEntity.ok(DonorStatusDTO.builder().id(1L).name("actif").build()));
        when(refController.getCityWithRegionAndCountry(1L)).thenReturn(ResponseEntity.ok(CityWithRegionAndCountryDTO.builder().id(1L).build()));
        when(refController.getParCity(1L)).thenReturn(ResponseEntity.ok(CityDTO.builder().id(1L).build()));
        when(refController.getMetCanalCommunication(1L)).thenReturn(ResponseEntity.ok(CanalCommunicationDTO.builder().id(1L).build()));
        when(refController.getParLanguageCommunication(1L)).thenReturn(ResponseEntity.ok(LanguageCommunicationDTO.builder().id(1L).build()));
        when(refFeignClient.getMetCanalCommunication(anyLong())).thenReturn(CanalCommunicationDTO.builder().id(1L).name("test canal").build());
        when(refFeignClient.getParTypeIdentity(anyLong())).thenReturn(TypeIdentityDTO.builder().id(1L).name("test type identity").build());
        when(refFeignClient.getMetProfession(anyLong())).thenReturn(ProfessionDTO.builder().id(1L).name("test prof").build());
        when(refFeignClient.getParDonorStatus(anyLong())).thenReturn(DonorStatusDTO.builder().id(1L).name("actif").build());
        when(refFeignClient.getParLanguageCommunication(anyLong())).thenReturn(LanguageCommunicationDTO.builder().id(1L).name("test lang").build());
        when(donorRepository.findAvailableBalanceByDonorAndType(1L, "Kafalat")).thenReturn(1000.0);
        when(donorRepository.findAvailableBalanceByDonorAndType(1L, "Aides Complémentaires")).thenReturn(500.0);
        when(donorRepository.findAvailableBalanceByDonor(1L)).thenReturn(1500.0);
        when(donorRepository.findTotalDonatedByDonor(1L)).thenReturn(2000.0);
        when(donorRepository.findExecutedBalanceByDonor(1L)).thenReturn(1800.0);
        when(donorRepository.findReservedBalanceByDonor(1L)).thenReturn(200.0);


        DonorDTO donor= donorService.getDonorById(1L);

        assertNotNull(donor);
        assertNotNull(donor.getStatus());
        assertEquals("actif", donor.getStatus().getName());

        // Verify repository calls
        verify(donorRepository, times(1)).findById(1L);
        verify(donorRepository, times(1)).findAvailableBalanceByDonorAndType(1L, "Kafalat");
        verify(donorRepository, times(1)).findAvailableBalanceByDonorAndType(1L, "Aides Complémentaires");
        verify(donorRepository, times(1)).findAvailableBalanceByDonor(1L);
        verify(donorRepository, times(1)).findTotalDonatedByDonor(1L);
        verify(donorRepository, times(1)).findExecutedBalanceByDonor(1L);
        verify(donorRepository, times(1)).findReservedBalanceByDonor(1L);

        verify(refFeignClient, times(2)).getMetProfession(1L);
        verify(refFeignClient, times(1)).getMetCanalCommunication(anyLong());
        verify(refFeignClient, times(1)).getParTypeIdentity(anyLong());
        verify(refFeignClient, times(1)).getParDonorStatus(anyLong());
        verify(refFeignClient, times(1)).getParLanguageCommunication(anyLong());

        verify(refController, times(1)).getParDonorStatus(1L);
        verify(refController, times(1)).getCityWithRegionAndCountry(1L);
        verify(refController, times(1)).getParCity(1L);
        verify(refController, times(1)).getMetCanalCommunication(1L);
        verify(refController, times(1)).getParLanguageCommunication(1L);


    }

    @Test
    void DonorService_getDonorById_SuccessMoral() throws TechnicalException {
        DonorMoral donorMoral = DonorMoral
                .builder()
                .id(1L)
                .status(DonorStatusDTO.builder().id(1L).build())
                .donorStatusId(1L)
                .donorContacts(List.of(DonorContact.builder().donorContactCanalCommunications(List.of(DonorContactCanalCommunication.builder().id(1L).build())).donorContactLanguageCommunications(List.of(DonorContactLanguageCommunication.builder().id(1L).build())).id(1L).build()))
                .cityId(1L)
                .documentsDonors(List.of(DocumentDonor.builder().document(Document.builder().id(1L).fileName("file name").build()).build()))
                .status(DonorStatusDTO.builder().id(1L).build())
                .city(CityDTO.builder().id(1L).build())
                .build();


        when(donorRepository.findById(1L)).thenReturn(Optional.of(donorMoral));
        when(refController.getParDonorStatus(1L)).thenReturn(ResponseEntity.ok(DonorStatusDTO.builder().id(1L).name("actif").build()));
        when(refController.getCityWithRegionAndCountry(1L)).thenReturn(ResponseEntity.ok(CityWithRegionAndCountryDTO.builder().id(1L).build()));
        when(refController.getParCity(1L)).thenReturn(ResponseEntity.ok(CityDTO.builder().id(1L).build()));
        when(refFeignClient.getParDonorStatus(anyLong())).thenReturn(DonorStatusDTO.builder().id(1L).name("actif").build());

        DonorDTO donorDTO=donorService.getDonorById(1L);

        assertNotNull(donorDTO);
        assertNotNull(donorDTO.getStatus());
        assertEquals("actif", donorDTO.getStatus().getName());
        assertEquals(1L, donorDTO.getId());

        verify(donorRepository, times(1)).findById(1L);


        verify(refController, times(1)).getParDonorStatus(1L);
        verify(refController, times(1)).getCityWithRegionAndCountry(1L);
        verify(refController, times(1)).getParCity(1L);


        verify(refFeignClient, times(1)).getParDonorStatus(anyLong());


    }

}

