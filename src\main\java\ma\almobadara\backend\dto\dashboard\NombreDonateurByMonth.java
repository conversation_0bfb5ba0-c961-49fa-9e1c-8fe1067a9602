package ma.almobadara.backend.dto.dashboard;

import lombok.*;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class NombreDonateurByMonth {

    private Integer month;
    private Integer year;
    private Long physicalDonors;
    private Long moralDonors;
    private Long cityId;
    private String physicalSex;
    private String moralSex;
    private Double financialDonations;
    private Double natureDonations;
    private Double totalDonations;



    public NombreDonateurByMonth(Integer month, Integer year, Long physicalDonors, Long moralDonors) {
        this.month = month;
        this.year = year;
        this.physicalDonors = physicalDonors;
        this.moralDonors = moralDonors;
    }

    public NombreDonateurByMonth(Integer month, Integer year, Long physicalDonors, Long moralDonors, String physicalSex, String moralSex) {
        this.month = month;
        this.year = year;
        this.physicalDonors = physicalDonors;
        this.moralDonors = moralDonors;
        this.physicalSex = physicalSex;
        this.moralSex = moralSex;
    }

    public NombreDonateurByMonth(Integer month, Integer year, Double financialDonations, Double natureDonations, Double totalDonations) {
        this.month = month;
        this.year = year;
        this.financialDonations = financialDonations;
        this.natureDonations = natureDonations;
        this.totalDonations = totalDonations;
    }

    public NombreDonateurByMonth(Long cityId, Integer month, Integer year, Long physicalDonors) {
        this.cityId = cityId;
        this.month = month;
        this.year = year;
        this.physicalDonors = physicalDonors;
    }

    public NombreDonateurByMonth(Long cityId, Integer month, Integer year, Long physicalDonors, Long moralDonors) {
        this.cityId = cityId;
        this.month = month;
        this.year = year;
        this.physicalDonors = physicalDonors;
        this.moralDonors = moralDonors;
    }

}
