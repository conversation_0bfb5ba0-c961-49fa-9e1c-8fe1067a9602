package ma.almobadara.backend.enumeration;

import com.fasterxml.jackson.annotation.JsonValue;

/**
 * Enum representing the status of a reclamation.
 */
public enum ReclamationStatus {
    /**
     * The reclamation has been sent but not yet processed.
     */
    ENVOYE("envoyé"),

    /**
     * The reclamation has been resolved.
     */
    RESOLUE("résolue"),

    /**
     * The reclamation has been closed.
     */
    FERME("fermé");

    private final String label;

    ReclamationStatus(String label) {
        this.label = label;
    }

    /**
     * Returns the label of the status.
     * This method is annotated with @JsonValue to make <PERSON> serialize the enum as this string value.
     */
    @JsonValue
    public String getLabel() {
        return label;
    }
}