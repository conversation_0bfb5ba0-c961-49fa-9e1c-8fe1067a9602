package ma.almobadara.backend.model.beneficiary;

import jakarta.persistence.*;
import lombok.*;
import ma.almobadara.backend.model.communs.Action;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@IdClass(ActionBeneficiaryId.class)
public class ActionBeneficiary {

    @Id
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "beneficiary_id")
    private Beneficiary beneficiary;

    @Id
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "action_id")
    private Action action;

}
