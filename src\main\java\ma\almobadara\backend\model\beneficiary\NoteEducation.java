package ma.almobadara.backend.model.beneficiary;

import jakarta.persistence.*;
import lombok.*;
import ma.almobadara.backend.model.communs.Note;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@IdClass(NoteEducationId.class)
public class NoteEducation {

    @Id
    @ManyToOne
    @JoinColumn(name ="education_id")
    private Education education;

    @Id
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "note_id")
    private Note note;

}
