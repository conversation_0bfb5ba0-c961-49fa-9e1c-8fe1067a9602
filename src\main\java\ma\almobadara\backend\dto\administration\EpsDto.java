package ma.almobadara.backend.dto.administration;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import ma.almobadara.backend.model.administration.Zone;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Getter
@Setter
public class EpsDto {
    private Long id;
    private String name;
    private String nameAr;

    private String address;
    private String addressAr;
    private String beneficiaryType;
    private List<Long> ageGroupIds;
    private String code ;
    private String comment;

    private List<Long> cityIds;

    private Long capacity;
    private Date dateCreated;

    private String error;

    private List<TagDTO> tags;


}
