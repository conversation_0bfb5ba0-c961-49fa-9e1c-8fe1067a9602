package ma.almobadara.backend.controller.referentiel;


import feign.FeignException;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.clients.RefFeignClient;
import ma.almobadara.backend.dto.*;
import ma.almobadara.backend.dto.referentiel.*;
import ma.almobadara.backend.service.authRefrentiel.AuthenticationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@RestController
@Slf4j
@RequestMapping("/ref")
public class RefController {

    private final RefFeignClient refFeignClient;
    final String token;

    @Autowired
    public RefController(RefFeignClient refFeignClient, AuthenticationService authenticationService) {
        this.refFeignClient = refFeignClient;
        token = "Bearer " + authenticationService.getJwtToken();
    }


    @GetMapping("/currencies")
    ResponseEntity<List<CurrencyDTO>> getAllParCurrencies() {
        return ResponseEntity.ok(refFeignClient.getAllCurrencies());
    }

    @GetMapping("/currencies/{id}")
    public ResponseEntity<CurrencyDTO> getParCurrency(@PathVariable Long id) {
        return ResponseEntity.ok(refFeignClient.getParCurrency(id));
    }

    @GetMapping("/professions")
    ResponseEntity<List<ProfessionDTO>> getAllMetProfessions() {
        return ResponseEntity.ok(refFeignClient.getAllMetProfessions());
    }


    @GetMapping("/death-reasons")
    public ResponseEntity<List<DeathReasonDTO>> getAllMetDeathReasons() {
        return ResponseEntity.ok(refFeignClient.getAllMetDeathReasons());
    }

    @GetMapping("/death-reasons/{id}")
    public ResponseEntity<DeathReasonDTO> getMetDeathReason(@PathVariable Long id) {
        return ResponseEntity.ok(refFeignClient.getMetDeathReason(id));
    }

    @GetMapping("/education-system-types")
    public ResponseEntity<List<EducationSystemTypeDTO>> getAllConsEducationSystemTypes() {
        return ResponseEntity.ok(refFeignClient.getAllConsEducationSystemTypes());
    }

    @GetMapping("/education-system-types/{id}")
    public ResponseEntity<EducationSystemTypeDTO> getConsEducationSystemType(@PathVariable Long id) {
        return ResponseEntity.ok(refFeignClient.getConsEducationSystemType(id));
    }

    @GetMapping("/professions/{id}")
    public ResponseEntity<ProfessionDTO> getMetProfession(@PathVariable Long id) {
        return ResponseEntity.ok(refFeignClient.getMetProfession(id));
    }

    @GetMapping("/accommodation_types")
    ResponseEntity<List<AccommodationTypeDTO>> getAllMetAccommodationType() {
        return ResponseEntity.ok(refFeignClient.getAllMetAccommodationType());
    }

    @GetMapping("/accommodation_types/{id}")
    public ResponseEntity<AccommodationTypeDTO> getMetAccommodationType(@PathVariable Long id) {
        return ResponseEntity.ok(refFeignClient.getMetAccommodationType(id));
    }

    @GetMapping("/accommodation_natures")
    ResponseEntity<List<AccommodationNatureDTO>> getAllParAccommodationNature() {
        return ResponseEntity.ok(refFeignClient.getAllParAccommodationNature());
    }

    @GetMapping("/accommodation_natures/{id}")
    public ResponseEntity<AccommodationNatureDTO> getParAccommodationNature(@PathVariable Long id) {
        return ResponseEntity.ok(refFeignClient.getParAccommodationNature(id));
    }


    @GetMapping("/type-kafalat")
    ResponseEntity<List<TypeKafalatDTO>> getAllParTypeKafalat() {
        return ResponseEntity.ok(refFeignClient.getAllParTypeKafalat());
    }

    @GetMapping("/type-kafalat/{id}")
    public ResponseEntity<TypeKafalatDTO> getParTypeKafalat(@PathVariable Long id) {
        return ResponseEntity.ok(refFeignClient.getParTypeKafalat(id));
    }

    @GetMapping("/type-prise-en-charge")
    ResponseEntity<List<TypePriseEnChargeDTO>> getAllParTypePriseEnCharge() {
        return ResponseEntity.ok(refFeignClient.getAllParTypePriseEnCharge());
    }

    @GetMapping("/type-prise-en-charge/{id}")
    public ResponseEntity<TypePriseEnChargeDTO> getParTypePriseEnCharge(@PathVariable Long id) {
        return ResponseEntity.ok(refFeignClient.getParTypePriseEnCharge(id));
    }

    @GetMapping("/category-beneficiaries")
    public ResponseEntity<List<CategoryBeneficiaryDTO>> getAllCategoryBeneficiaries() {
        return ResponseEntity.ok(refFeignClient.getAllCategoryBeneficiaries());
    }

    @GetMapping("/cloture-motif-types")
    public ResponseEntity<List<ClotureMotifTypeDTO>> getAllConsClotureMotifTypes() {
        return ResponseEntity.ok(refFeignClient.getAllConsClotureMotifTypes());
    }

    // get cloture Motif Type by id
    @GetMapping("/cloture-motif-types/{id}")
    public ResponseEntity<ClotureMotifTypeDTO> getClotureMotifTypeById(@PathVariable Long id) {
        return ResponseEntity.ok(refFeignClient.getConsClotureMotifType(id));
    }

    @GetMapping("/category-beneficiaries/{id}")
    public ResponseEntity<CategoryBeneficiaryDTO> getCategoryBeneficiary(@PathVariable Long id) {
        return ResponseEntity.ok(refFeignClient.getCategoryBeneficiary(id));
    }


    @GetMapping("/statuses")
    ResponseEntity<List<StatusDTO>> getAllParStatuses() {
        return ResponseEntity.ok(refFeignClient.getAllParStatuses());
    }

    @GetMapping("/statuses/{id}")
    public ResponseEntity<StatusDTO> getParStatus(@PathVariable Long id) {
        return ResponseEntity.ok(refFeignClient.getParStatus(id));
    }

    @GetMapping("/donorStatuses")
    ResponseEntity<List<DonorStatusDTO>> getAllParDonorStatuses() {
        return ResponseEntity.ok(refFeignClient.getAllParDonorStatuses());
    }

    @GetMapping("/donor_statuses/{id}")
    public ResponseEntity<DonorStatusDTO> getParDonorStatus(@PathVariable Long id) {
        return ResponseEntity.ok(refFeignClient.getParDonorStatus(id));
    }

    @GetMapping("/action_statuses")
    ResponseEntity<List<ActionStatusDTO>> getAllActionStatuses() {
        List<ActionStatusDTO> allActionStatuses = refFeignClient.getAllActionStatuses();

        // Filtrer les actions avec les ID 1 et 2
        List<ActionStatusDTO> filteredActionStatuses = allActionStatuses.stream()
                .filter(action -> action.getId() != 1 && action.getId() != 2)
                .collect(Collectors.toList());

        return ResponseEntity.ok(filteredActionStatuses);
    }

    @GetMapping("/typeIdentities")
    ResponseEntity<List<TypeIdentityDTO>> getAllParTypeIdentities() {
        return ResponseEntity.ok(refFeignClient.getAllParTypeIdentities());
    }

    @GetMapping("/typeIdentities/{id}")
    public ResponseEntity<TypeIdentityDTO> getParTypeIdentity(@PathVariable Long id) {
        return ResponseEntity.ok(refFeignClient.getParTypeIdentity(id));
    }

    @GetMapping("/countries")
    ResponseEntity<List<CountryDTO>> getAllParCountries() {
        return ResponseEntity.ok(refFeignClient.getAllParCountries());
    }

    @GetMapping("/countries/{id}")
    ResponseEntity<CountryDTO> getParCountry(@PathVariable Long id) {
        return ResponseEntity.ok(refFeignClient.getParCountry(id));
    }

    @GetMapping("/regions")
    ResponseEntity<List<RegionDTO>> getAllParRegions() {
        return ResponseEntity.ok(refFeignClient.getAllParRegions());
    }

    @GetMapping("/regions/{id}")
    ResponseEntity<RegionDTO> getParRegion(@PathVariable Long id) {
        return ResponseEntity.ok(refFeignClient.getParRegion(id));
    }

    @GetMapping("/cities")
    ResponseEntity<List<CityDTO>> getAllCities() {
        return ResponseEntity.ok(refFeignClient.getAllParCities());
    }

    @GetMapping("/cities/{id}")
    public ResponseEntity<CityDTO> getParCity(@PathVariable Long id) {
        return ResponseEntity.ok(refFeignClient.getParCity(id));
    }

    @GetMapping("/canalCommunications")
    ResponseEntity<List<CanalCommunicationDTO>> getAllMetCanalCommunications() {
        return ResponseEntity.ok(refFeignClient.getAllMetCanalCommunications());
    }

    @GetMapping("/canal_communications/{id}")
    public ResponseEntity<CanalCommunicationDTO> getMetCanalCommunication(@PathVariable Long id) {
        return ResponseEntity.ok(refFeignClient.getMetCanalCommunication(id));
    }

    @GetMapping("/languageCommunications")
    ResponseEntity<List<LanguageCommunicationDTO>> getAllParLanguageCommunications() {
        return ResponseEntity.ok(refFeignClient.getAllParLanguageCommunications());
    }

    @GetMapping("/language_communications/{id}")
    public ResponseEntity<LanguageCommunicationDTO> getParLanguageCommunication(@PathVariable Long id) {
        return ResponseEntity.ok(refFeignClient.getParLanguageCommunication(id));
    }

    @GetMapping("/typeDocumentDonors")
    ResponseEntity<List<TypeDocumentDonorDTO>> getAllConsTypeDonorDocuments() {
        return ResponseEntity.ok(refFeignClient.getAllConsTypeDonorDocuments());
    }

    @GetMapping("/regions/country/{id}")
    public ResponseEntity<List<RegionDTO>> getRegionByCountry(@PathVariable Long id) {
        try {
            ResponseEntity<CountryDTO> countryResponse = getParCountry(id);
            if (countryResponse.getBody() != null) {
                ResponseEntity<List<RegionDTO>> regionsResponse = getAllParRegions();
                List<RegionDTO> filteredRegions = Objects.requireNonNull(regionsResponse.getBody())
                        .stream()
                        .filter(region -> Objects.equals(region.getCountry().getId(), id))
                        .toList();
                return ResponseEntity.ok(filteredRegions);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (FeignException feignException) {
            log.error("Error fetching regions by country ID: {}", feignException.getMessage(), feignException);
            return ResponseEntity.status(feignException.status()).body(null);
        }
    }

    @GetMapping("/cities/region/{id}")
    public ResponseEntity<List<CityDTO>> getCityByRegion(@PathVariable Long id) {
        try {
            var cities = getCityByRegionService(id);
            if (cities != null) {
                return ResponseEntity.ok(cities);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (FeignException feignException) {
            log.error("Error fetching cities by region {}", feignException.getMessage());
            return ResponseEntity.status(feignException.status()).body(null);
        }
    }


    @Cacheable(key = "#id", value = "citiesByRegion")
    public List<CityDTO> getCityByRegionService(Long id) {
        ResponseEntity<RegionDTO> regionResponse = getParRegion(id);
        if (regionResponse.getBody() != null) {
            ResponseEntity<List<CityDTO>> citiesResponse = getAllCities();
            return Objects.requireNonNull(citiesResponse.getBody())
                    .stream()
                    .filter(city -> Objects.equals(city.getRegion().getId(), id))
                    .toList();
        } else {
            return null;
        }

    }

    @GetMapping("/cities/region/country/{id}")
    public ResponseEntity<CityWithRegionAndCountryDTO> getCityWithRegionAndCountry(@PathVariable Long id) {
        try {
            ResponseEntity<CityDTO> cityResponse = getParCity(id);
            if (cityResponse.getBody() != null) {
                CityDTO city = cityResponse.getBody();
                ResponseEntity<RegionDTO> regionResponse = getParRegion(city.getRegion().getId());
                if (regionResponse.getBody() != null) {
                    RegionDTO region = regionResponse.getBody();
                    if (region.getCountry() != null) {
                        return ResponseEntity.ok(new CityWithRegionAndCountryDTO(
                                city.getId(),
                                city.getName(),
                                region,
                                region.getCountry()
                        ));
                    } else {
                        // Si le pays de la région est null, retournez une réponse not found
                        return ResponseEntity.notFound().build();
                    }
                } else {
                    // Si la réponse de la région est null, retournez une réponse not found
                    return ResponseEntity.notFound().build();
                }
            } else {
                // Si la réponse de la ville est null, retournez une réponse not found
                return ResponseEntity.notFound().build();
            }
        } catch (FeignException feignException) {
            log.error("Error fetching city with region and country by city ID: {}", feignException.getMessage(), feignException);
            return ResponseEntity.status(feignException.status()).body(null);
        }
    }

    // get cities of the country morroco

    @GetMapping("/cities/regions/countryId/{id}")
    public ResponseEntity<List<CityWithRegionAndCountryDTO>> getCitiesWithRegionAndCountry(@PathVariable Long id) {
        try {
            ResponseEntity<CountryDTO> countryResponse = getParCountry(id);
            if (countryResponse.getBody() != null) {
                ResponseEntity<List<RegionDTO>> regionsResponse = getAllParRegions();
                List<RegionDTO> countryRegions = Objects.requireNonNull(regionsResponse.getBody())
                        .stream()
                        .filter(region -> Objects.equals(region.getCountry().getId(), id))
                        .toList();

                List<Long> regionIds = countryRegions.stream()
                        .map(RegionDTO::getId)
                        .toList();

                ResponseEntity<List<CityDTO>> citiesResponse = getAllCities();
                List<CityDTO> filteredCities = Objects.requireNonNull(citiesResponse.getBody())
                        .stream()
                        .filter(city -> regionIds.contains(city.getRegion().getId()))
                        .toList();

                List<CityWithRegionAndCountryDTO> result = filteredCities.stream()
                        .map(city -> new CityWithRegionAndCountryDTO(
                                city.getId(),
                                city.getName(),
                                new RegionDTO(city.getRegion().getId(), city.getRegion().getCode(), city.getRegion().getName(), city.getRegion().getNameAr(), city.getRegion().getNameEn(), countryResponse.getBody()),
                                countryResponse.getBody()
                        ))
                        .toList();

                return ResponseEntity.ok(result);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (FeignException feignException) {
            log.error("Error fetching cities with region and country by country ID: {}", feignException.getMessage(), feignException);
            return ResponseEntity.status(feignException.status()).body(null);
        }
    }

    // get cities of the country morroco ***********
    @GetMapping("/cities/country/{id}")
    public ResponseEntity<List<CityWithRegionAndCountryDTO>> getCitiesByCountry(@PathVariable Long id) {
        try {
            ResponseEntity<CountryDTO> countryResponse = getParCountry(id);
            if (countryResponse.getBody() != null) {
                ResponseEntity<List<RegionDTO>> regionsResponse = getAllParRegions();
                List<RegionDTO> countryRegions = Objects.requireNonNull(regionsResponse.getBody())
                        .stream()
                        .filter(region -> Objects.equals(region.getCountry().getId(), id))
                        .toList();

                List<Long> regionIds = countryRegions.stream()
                        .map(RegionDTO::getId)
                        .toList();

                ResponseEntity<List<CityDTO>> citiesResponse = getAllCities();
                List<CityDTO> filteredCities = Objects.requireNonNull(citiesResponse.getBody())
                        .stream()
                        .filter(city -> regionIds.contains(city.getRegion().getId()))
                        .toList();

                List<CityWithRegionAndCountryDTO> result = filteredCities.stream()
                        .map(city -> new CityWithRegionAndCountryDTO(
                                city.getId(),
                                city.getName(),
                                new RegionDTO(city.getRegion().getId(), city.getRegion().getCode(), city.getRegion().getName(), city.getRegion().getNameAr(), city.getRegion().getNameEn(), countryResponse.getBody()),
                                countryResponse.getBody()
                        ))
                        .toList();

                return ResponseEntity.ok(result);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (FeignException feignException) {
            log.error("Error fetching cities by country ID: {}", feignException.getMessage(), feignException);
            return ResponseEntity.status(feignException.status()).body(null);
        }
    }


    @GetMapping("/activitySectors")
    ResponseEntity<List<ActivitySectorDTO>> getAllMetActivitySectors() {
        return ResponseEntity.ok(refFeignClient.getAllMetActivitySectors());
    }

    @GetMapping("/activitySectors/{id}")
    public ResponseEntity<ActivitySectorDTO> getMetActivitySector(@PathVariable Long id) {
        return ResponseEntity.ok(refFeignClient.getMetActivitySector(id));
    }

    @GetMapping("/typeDonorMorals")
    ResponseEntity<List<TypeDonorMoralDTO>> getAllMetTypeDonorMorals() {
        return ResponseEntity.ok(refFeignClient.getAllMetTypeDonorMorals());
    }

    @GetMapping("/typeDonorMorals/{id}")
    public ResponseEntity<TypeDonorMoralDTO> getMetTypeDonorMoral(@PathVariable Long id) {
        return ResponseEntity.ok(refFeignClient.getMetTypeDonorMoral(id));
    }

    @GetMapping("/donorContactFunctions")
    ResponseEntity<List<DonorContactFunctionDTO>> getAllMetDonorContactFunctions() {
        return ResponseEntity.ok(refFeignClient.getAllMetDonorContactFunctions());
    }

    @GetMapping("/donorContactFunctions/{id}")
    public ResponseEntity<DonorContactFunctionDTO> getMetDonorContactFunction(@PathVariable Long id) {
        return ResponseEntity.ok(refFeignClient.getMetDonorContactFunction(id));
    }

    @GetMapping("/type_donor_documents/{id}")
    public ResponseEntity<TypeDocumentDonorDTO> getConsTypeDonorDocument(@PathVariable Long id) {
        return ResponseEntity.ok(refFeignClient.getConsTypeDonorDocument(id));
    }

    @GetMapping("/schoolLevels")
    ResponseEntity<List<SchoolLevelDTO>> getAllParSchoolLevels() {
        return ResponseEntity.ok(refFeignClient.getAllParSchoolLevels());
    }

    @GetMapping("/schoolLevels/{id}")
    public ResponseEntity<SchoolLevelDTO> getParSchoolLevel(@PathVariable Long id) {
        return ResponseEntity.ok(refFeignClient.getParSchoolLevel(id));
    }

    @GetMapping("/familyRelationships")
    public ResponseEntity<List<FamilyRelationshipDTO>> getAllMetFamilyRelationships() {
        return ResponseEntity.ok(refFeignClient.getAllMetFamilyRelationships());
    }

    @GetMapping("/familyRelationships/{id}")
    public ResponseEntity<FamilyRelationshipDTO> getMetFamilyRelationship(@PathVariable Long id) {
        return ResponseEntity.ok(refFeignClient.getMetFamilyRelationship(id));
    }

    @GetMapping("/familyDocumentTypes")
    public ResponseEntity<List<FamilyDocumentTypeDTO>> getAllConsFamilyDocumentTypes() {
        return ResponseEntity.ok(refFeignClient.getAllConsFamilyDocumentTypes());
    }

    @GetMapping("/familyDocumentTypes/{id}")
    public ResponseEntity<FamilyDocumentTypeDTO> consFamilyDocumentTypeDTO(@PathVariable Long id) {
        return ResponseEntity.ok(refFeignClient.consFamilyDocumentTypeDTO(id));
    }

    @GetMapping("/card_types")
    public ResponseEntity<List<CardTypeDTO>> getAllConsCardTypes() {
        return ResponseEntity.ok(refFeignClient.getAllConsCardTypes());
    }

    @GetMapping("/card_types/{id}")
    public ResponseEntity<CardTypeDTO> getConsCardType(@PathVariable Long id) {
        return ResponseEntity.ok(refFeignClient.getConsCardType(id));
    }

    @GetMapping("/income_sources")
    public ResponseEntity<List<IncomeSourceDTO>> getAllMetIncomeSources() {
        return ResponseEntity.ok(refFeignClient.getAllMetIncomeSources());
    }

    @GetMapping("/income_sources/{id}")
    public ResponseEntity<IncomeSourceDTO> getMetIncomeSource(@PathVariable Long id) {
        return ResponseEntity.ok(refFeignClient.getMetIncomeSource(id));
    }

    @GetMapping("/services")
    ResponseEntity<List<ServiceDTO>> getAllMetServices() {
        return ResponseEntity.ok(refFeignClient.getAllMetServices());
    }

    @GetMapping("/services/{id}")
    public ResponseEntity<ServiceDTO> getMetService(@PathVariable Long id) {
        return ResponseEntity.ok(refFeignClient.getMetService(id));
    }

    @GetMapping("/categories")
    public ResponseEntity<List<CategoryDTO>> getAllMetCategories() {
        return ResponseEntity.ok(refFeignClient.getAllMetCategories());
    }

    @GetMapping("/eps")
    public ResponseEntity<List<EpsDTO>> getAllMetEps() {
        return ResponseEntity.ok(refFeignClient.getAllMetEps());
    }

    @GetMapping("/categories/{id}")
    ResponseEntity<CategoryDTO> getMetCategory(@PathVariable Long id) {
        return ResponseEntity.ok(refFeignClient.getMetCategory(id));
    }

    @GetMapping("/services/category/{id}")
    public ResponseEntity<List<ServiceDTO>> getServicesByCategory(@PathVariable Long id) {
        try {
            ResponseEntity<CategoryDTO> regionResponse = getMetCategory(id);
            if (regionResponse.getBody() != null) {
                ResponseEntity<List<ServiceDTO>> servicesResponse = getAllMetServices();
                List<ServiceDTO> filteredServices = Objects.requireNonNull(servicesResponse.getBody())
                        .stream()
                        .filter(service -> Objects.equals(service.getCategory().getId(), id))
                        .toList();
                return ResponseEntity.ok(filteredServices);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (FeignException feignException) {
            log.error("Error fetching cities by region ID: {}", feignException.getMessage(), feignException);
            return ResponseEntity.status(feignException.status()).body(null);
        }
    }

    @GetMapping("/statuses/category/{id}")
    public ResponseEntity<List<StatusDTO>> getStatusesByCategory(@PathVariable Long id) {
        try {
            ResponseEntity<List<StatusDTO>> statusesResponse = getAllParStatuses();
            List<StatusDTO> filteredStatuses = Objects.requireNonNull(statusesResponse.getBody())
                    .stream()
                    .filter(status -> status.getCategories().stream().anyMatch(category -> Objects.equals(category.getId(), id)))
                    .toList();

            return ResponseEntity.ok(filteredStatuses);
        } catch (FeignException feignException) {
            log.error("Error fetching statuses by category ID: {}", feignException.getMessage(), feignException);
            return ResponseEntity.status(feignException.status()).body(null);
        }
    }

    @GetMapping("/typePriseEnCharge/category/{id}")
    public ResponseEntity<List<TypePriseEnChargeDTO>> getTypePriseEnChargeByCategory(@PathVariable Long id) {
        try {
            ResponseEntity<CategoryDTO> regionResponse = getMetCategory(id);
            if (regionResponse.getBody() != null) {
                ResponseEntity<List<TypePriseEnChargeDTO>> typePriseEnChargeResponse = getAllParTypePriseEnCharge();
                List<TypePriseEnChargeDTO> filteredServices = Objects.requireNonNull(typePriseEnChargeResponse.getBody())
                        .stream()
                        .filter(typePriseEnCharge -> Objects.equals(typePriseEnCharge.getCategory().getId(), id))
                        .toList();
                return ResponseEntity.ok(filteredServices);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (FeignException feignException) {
            log.error("Error fetching cities by region ID: {}", feignException.getMessage(), feignException);
            return ResponseEntity.status(feignException.status()).body(null);
        }
    }

    @GetMapping("/serviceOrTypePriseEnCharge/category/{id}")
    public ResponseEntity<?> getServiceOrTypePriseEnChargeByCategory(@PathVariable Long id) {
        try {
            ResponseEntity<CategoryDTO> categoryResponse = getMetCategory(id);
            if (categoryResponse.getBody() == null) {
                return ResponseEntity.notFound().build();
            }

            if (id == 1) {
                ResponseEntity<List<ServiceDTO>> servicesResponse = getAllMetServices();
                List<ServiceDTO> filteredServices = Objects.requireNonNull(servicesResponse.getBody())
                        .stream()
                        .filter(service -> Objects.equals(service.getCategory().getId(), id))
                        .toList();
                return ResponseEntity.ok(filteredServices);

            } else if (id == 2) {
                ResponseEntity<List<TypePriseEnChargeDTO>> typePriseEnChargeResponse = getAllParTypePriseEnCharge();
                List<TypePriseEnChargeDTO> filteredTypePriseEnCharge = Objects.requireNonNull(typePriseEnChargeResponse.getBody())
                        .stream()
                        .filter(typePriseEnCharge -> Objects.equals(typePriseEnCharge.getCategory().getId(), id))
                        .toList();
                return ResponseEntity.ok(filteredTypePriseEnCharge);

            } else {
                return ResponseEntity.badRequest().body("Invalid category ID");
            }

        } catch (FeignException feignException) {
            log.error("Error fetching data by category ID: {}", feignException.getMessage(), feignException);
            return ResponseEntity.status(feignException.status()).body(null);
        }
    }

    @GetMapping("/diseases")
    public ResponseEntity<List<DiseasesDTO>> getAllMetDiseases() {
        return ResponseEntity.ok(refFeignClient.getAllMetDiseases());
    }

    @GetMapping("/allergies")
    public ResponseEntity<List<AllergiesDTO>> getAllMetAllergies() {
        return ResponseEntity.ok(refFeignClient.getAllMetAllergies());
    }

    @GetMapping("/handicapTypes")
    public ResponseEntity<List<HandicapTypeDTO>> getAllMetHandicapTypes() {
        return ResponseEntity.ok(refFeignClient.getAllMetHandicapTypes());
    }

    @GetMapping("/diseaseTreatmentTypes")
    public ResponseEntity<List<DiseaseTreatmentTypeDTO>> getAllMetDiseaseTreatmentTypes() {
        return ResponseEntity.ok(refFeignClient.getAllMetDiseaseTreatmentTypes());
    }

    @GetMapping("/honors")
    public ResponseEntity<List<HonorDTO>> getAllParHonors() {
        return ResponseEntity.ok(refFeignClient.getAllParHonors());
    }

    @GetMapping("/schoolYears")
    public ResponseEntity<List<SchoolYearDTO>> getAllParSchoolYears() {
        return ResponseEntity.ok(refFeignClient.getAllParSchoolYears());
    }

    @GetMapping("/majors")
    public ResponseEntity<List<MajorDTO>> getAllParMajors() {
        return ResponseEntity.ok(refFeignClient.getAllParMajors());
    }

    @GetMapping("/scholarships")
    public ResponseEntity<List<ScholarshipDTO>> getAllMetScholarships() {
        return ResponseEntity.ok(refFeignClient.getAllMetScholarships());
    }

    @GetMapping("/beneficiaryDocumentTypes")
    public ResponseEntity<List<BeneficiaryDocumentTypeDTO>> getAllConsBeneficiaryDocumentTypes() {
        return ResponseEntity.ok(refFeignClient.getAllConsBeneficiaryDocumentTypes());
    }

    @GetMapping("/canalDonations")
    public ResponseEntity<List<CanalDonationDTO>> getAllMetCanalDonations() {
        return ResponseEntity.ok(refFeignClient.getAllMetCanalDonations());
    }

    @GetMapping("/typeDocumentDonations")
    public ResponseEntity<List<TypeDonationDocumentDTO>> getAllConsTypeDonationDocuments() {
        return ResponseEntity.ok(refFeignClient.getAllConsTypeDonationDocuments());
    }

    @GetMapping("/type_donation_documents/{id}")
    ResponseEntity<TypeDonationDocumentDTO> getConsTypeDonationDocument(@PathVariable Long id) {
        return ResponseEntity.ok(refFeignClient.getConsTypeDonationDocument(id));
    }

    @GetMapping("/typeProductNatures")
    public ResponseEntity<List<TypeProductNatureDTO>> getAllConsTypeProductNatures() {
        return ResponseEntity.ok(refFeignClient.getAllConsTypeProductNatures());
    }

    @GetMapping("/type_product_natures/{id}")
    ResponseEntity<TypeProductNatureDTO> getConsTypeProductNature(@PathVariable Long id) {
        return ResponseEntity.ok(refFeignClient.getConsTypeProductNature(id));
    }

    @GetMapping("/productUnits")
    public ResponseEntity<List<ProductUnitDTO>> getAllParProductUnits() {
        return ResponseEntity.ok(refFeignClient.getAllParProductUnits());
    }

    @GetMapping("/productNatures")
    public ResponseEntity<List<ProductNatureDTO>> getAllMetProductNatures() {
        return ResponseEntity.ok(refFeignClient.getAllMetProductNatures());
    }

    @GetMapping("/productNatures/typeProductNature/{id}")
    public ResponseEntity<List<ProductNatureDTO>> getProductNatureByTypeProductNature(@PathVariable Long id) {
        try {
            ResponseEntity<TypeProductNatureDTO> regionResponse = getConsTypeProductNature(id);
            if (regionResponse.getBody() != null) {
                ResponseEntity<List<ProductNatureDTO>> productNatureResponse = getAllMetProductNatures();
                List<ProductNatureDTO> filteredProductNatures = Objects.requireNonNull(productNatureResponse.getBody())
                        .stream()
                        .filter(productNature -> Objects.equals(productNature.getTypeProductNature().getId(), id))
                        .toList();
                return ResponseEntity.ok(filteredProductNatures);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (FeignException feignException) {
            log.error("Error fetching cities by region ID: {}", feignException.getMessage(), feignException);
            return ResponseEntity.status(feignException.status()).body(null);
        }
    }

    @GetMapping("/source_beneficiary")
    ResponseEntity<List<SourceBeneficiaryDTO>> getAllSourceBeneficiary() {
        return ResponseEntity.ok(refFeignClient.getAllSourceBeneficiary());
    }

    @GetMapping("/source_beneficiary/{id}")
    public ResponseEntity<SourceBeneficiaryDTO> getSourceBeneficiary(@PathVariable Long id) {
        return ResponseEntity.ok(refFeignClient.getSourceBeneficiary(id));
    }
}


