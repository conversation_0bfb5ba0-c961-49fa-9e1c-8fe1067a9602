package ma.almobadara.backend.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.model.administration.CacheAdUser;
import ma.almobadara.backend.model.administration.RolePrivilege;
import ma.almobadara.backend.properties.Auth0Properties;
import ma.almobadara.backend.repository.administration.CacheAdUserRepository;
import ma.almobadara.backend.repository.administration.RolePrivilegeRepository;
import org.springframework.core.convert.converter.Converter;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationConverter;
import org.springframework.security.oauth2.server.resource.authentication.JwtGrantedAuthoritiesConverter;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class CustomJwtAuthenticationConverter {
    private final CacheAdUserRepository cacheAdUserRepository;
    private final RolePrivilegeRepository rolePrivilegeRepository;
    private final Auth0Properties auth0Properties;
    private final JwtGrantedAuthoritiesConverter defaultGrantedAuthoritiesConverter = new JwtGrantedAuthoritiesConverter();

    public Converter<Jwt, AbstractAuthenticationToken> getJwtAuthenticationConverter() {
        return new CustomJwtConverter();
    }

    private class CustomJwtConverter implements Converter<Jwt, AbstractAuthenticationToken> {
        @Override
        public AbstractAuthenticationToken convert(Jwt jwt) {
            Collection<GrantedAuthority> authorities = convertAuthorities(jwt);
            CacheAdUser principal = extractPrincipal(jwt);

            if (principal != null) {
                return new UsernamePasswordAuthenticationToken(principal, jwt, authorities);
            } else {
                // Fallback to default behavior if user not found
                return new UsernamePasswordAuthenticationToken(jwt.getSubject(), jwt, authorities);
            }
        }
    }

    private CacheAdUser extractPrincipal(Jwt jwt) {
        log.debug("Extracting principal from JWT - Issuer: {}, Subject: {}", jwt.getIssuer(), jwt.getSubject());

        // Check if it's an Auth0 token
        if (jwt.getIssuer().toString().equals(auth0Properties.getIssuerUri())) {
            log.debug("Extracting principal from Auth0 token");
            String email = jwt.getClaimAsString("email");
            if (email != null) {
                CacheAdUser user = cacheAdUserRepository.findByMail(email);
                if (user != null && !user.isDeleted()) {
                    log.debug("Found Auth0 user: {}", user.getMail());
                    return user;
                }
            }
        } else {
            // Otherwise, it's an Azure token
            log.debug("Extracting principal from Azure token");
            String azureId = jwt.getClaimAsString("oid");
            if (azureId != null && !azureId.isEmpty()) {
                CacheAdUser connectedUser = cacheAdUserRepository.findByAzureDirectoryIdAndIsDeletedIsFalse(azureId);
                if (connectedUser != null) {
                    log.debug("Found Azure user: {}", connectedUser.getMail());
                    return connectedUser;
                } else {
                    log.error("User not found for Azure ID: {}", azureId);
                }
            } else {
                log.error("Azure ID (oid) is missing from token");
            }
        }

        log.warn("No user found for JWT token");
        return null;
    }

    private Collection<GrantedAuthority> convertAuthorities(Jwt jwt) {
        log.debug("Converting JWT token authorities - Issuer: {}, Subject: {}", jwt.getIssuer(), jwt.getSubject());
        log.debug("JWT Claims: {}", jwt.getClaims());

        // Check if it's an Auth0 token
        if (jwt.getIssuer().toString().equals(auth0Properties.getIssuerUri())) {
            log.debug("Processing Auth0 token authorities");
            return convertAuth0Authorities(jwt);
        }
        // Otherwise, it's an Azure token
        log.debug("Processing Azure token authorities");
        return convertAzureAuthorities(jwt);
    }

    private Collection<GrantedAuthority> convertAuth0Authorities(Jwt jwt) {
        List<GrantedAuthority> authorities = new ArrayList<>();

        // Add default role for Auth0 users
        authorities.add(new SimpleGrantedAuthority("ROLE_USER"));
        log.debug("Added default ROLE_USER authority");

        // Get user and add role-based authorities
        String email = jwt.getClaimAsString("email");
        if (email != null) {
            CacheAdUser user = cacheAdUserRepository.findByMail(email);
            if (user != null && !user.isDeleted()) {
                // Add role-based authorities
                if (user.getRole() != null) {
                    authorities.add(new SimpleGrantedAuthority("ROLE_" + user.getRole().getCode()));

                    // Add role privileges as authorities
                    List<RolePrivilege> rolePrivileges = rolePrivilegeRepository.findByRole_Id(user.getRole().getId());
                    for (RolePrivilege privilege : rolePrivileges) {
                        String authority = privilege.getFeature().getCode() + ":" + privilege.getPrivilege().getCode();
                        authorities.add(new SimpleGrantedAuthority(authority));
                    }
                }
            }
        }

        log.debug("Final authorities for Auth0 token: {}", authorities);
        return authorities;
    }

    private Collection<GrantedAuthority> convertAzureAuthorities(Jwt jwt) {
        List<GrantedAuthority> authorities = new ArrayList<>();
        String azureId = jwt.getClaimAsString("oid");

        if (azureId != null && !azureId.isEmpty()) {
            CacheAdUser connectedUser = cacheAdUserRepository.findByAzureDirectoryIdAndIsDeletedIsFalse(azureId);
            if (connectedUser != null) {
                // Add role-based authorities
                if (connectedUser.getRole() != null) {
                    authorities.add(new SimpleGrantedAuthority("ROLE_" + connectedUser.getRole().getCode()));

                    // Add role privileges as authorities
                    List<RolePrivilege> rolePrivileges = rolePrivilegeRepository.findByRole_Id(connectedUser.getRole().getId());
                    for (RolePrivilege privilege : rolePrivileges) {
                        String authority = privilege.getFeature().getCode() + ":" + privilege.getPrivilege().getCode();
                        authorities.add(new SimpleGrantedAuthority(authority));
                    }
                }
            } else {
                log.error("User not found for Azure ID: {}", azureId);
            }
        } else {
            log.error("Azure ID (oid) is missing from token");
        }

        log.debug("Final authorities for Azure token: {}", authorities);
        return authorities;
    }
}
