package ma.almobadara.backend.model.beneficiary;


import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.SuperBuilder;

@Entity
@Getter
@Setter
@NoArgsConstructor
@SuperBuilder
@AllArgsConstructor
public class RapportPicture extends BaseEntity{

    private String url;
    private String description;
    private String descriptionAr;
    private String descriptionAng;

    @ManyToOne
    @JoinColumn(name = "rapport_id", nullable = false)
    private Rapport rapport;

}
