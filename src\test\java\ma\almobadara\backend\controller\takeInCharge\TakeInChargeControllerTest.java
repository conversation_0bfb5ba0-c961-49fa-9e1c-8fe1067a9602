package ma.almobadara.backend.controller.takeInCharge;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.EntityNotFoundException;
import ma.almobadara.backend.config.Messages;
import ma.almobadara.backend.config.TestSecurityConfig;
import ma.almobadara.backend.controller.TakenInCharge.TakenInChargeController;
import ma.almobadara.backend.dto.takenInCharge.*;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.model.takenInCharge.TakenInCharge;
import ma.almobadara.backend.model.takenInCharge.TakenInChargeOperation;
import ma.almobadara.backend.service.takenInCharge.TakenInChargeService;
import org.apache.poi.openxml4j.exceptions.InvalidOperationException;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;
import java.util.*;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@WebMvcTest(TakenInChargeController.class)
@ExtendWith(MockitoExtension.class)
@Import({TestSecurityConfig.class, Messages.class})
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class TakeInChargeControllerTest {
    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private TakenInChargeService takenInChargeService;

    ObjectMapper objectMapper = new ObjectMapper();

    @Test
    @Order(1)
    void TakenInChargeController_createTakenInCharge_Success() throws Exception {
        TakenInChargeDTO takenInCharge = TakenInChargeDTO.builder()
                .id(1L)
                .code("TIC-12345")
                .type("Medical Assistance")
                .serviceId(101L)
                .status("ACTIVE")
                .hasOperations(true)
                .clotureMotif("Completed successfully")
                .takenInChargeDonors(List.of(new TakenInChargeDonorDTO()))
                .takenInChargeBeneficiaries(List.of(new TakenInChargeBeneficiaryDTO()))
                .build();
        TakenInChargeDTO takenInCharge1 = TakenInChargeDTO.builder()
                .code("TIC-12345")
                .type("Medical Assistance")
                .serviceId(101L)
                .status("ACTIVE")
                .hasOperations(true)
                .clotureMotif("Completed successfully")
                .takenInChargeDonors(List.of(new TakenInChargeDonorDTO()))
                .takenInChargeBeneficiaries(List.of(new TakenInChargeBeneficiaryDTO()))
                .build();
        when(takenInChargeService.addTakenInCharge(any(TakenInChargeDTO.class))).thenReturn(takenInCharge);

        mockMvc.perform(post("/takenInCharges")
                        .flashAttr("takenInChargeDTO", takenInCharge1)
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(takenInCharge)));

        verify(takenInChargeService,times(1)).addTakenInCharge(any(TakenInChargeDTO.class));

    }

    @Test
    @Order(2)
    void TakenInChargeController_createTakenInCharge_BadRequest() throws Exception {
        TakenInChargeDTO takenInCharge1 = TakenInChargeDTO.builder()
                .code("TIC-12345")
                .type("Medical Assistance")
                .serviceId(101L)
                .status("ACTIVE")
                .hasOperations(true)
                .clotureMotif("Completed successfully")
                .takenInChargeDonors(List.of(new TakenInChargeDonorDTO()))
                .takenInChargeBeneficiaries(List.of(new TakenInChargeBeneficiaryDTO()))
                .build();
        doThrow(new IllegalStateException()).when(takenInChargeService).addTakenInCharge(any(TakenInChargeDTO.class));

        mockMvc.perform(post("/takenInCharges")
                        .flashAttr("takenInChargeDTO", takenInCharge1)
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isBadRequest());

        verify(takenInChargeService,times(1)).addTakenInCharge(any(TakenInChargeDTO.class));
    }

    @Test
    @Order(3)
    void TakenInChargeController_findAllTakenInChargesByCriteria_SuccessCase1() throws Exception {
        Pageable pageable = PageRequest.of(0, 10);
        Page<TakenInChargeDTO> takenInChargeDTOS= new PageImpl<>(List.of(),pageable,0);
        when(takenInChargeService.getAllTakenInChargesByCriteria(0,10,"hamza","test","test","test",1L,"test",null,null,null,null,null)).thenReturn(takenInChargeDTOS);

        mockMvc.perform(get("/takenInCharges/findAll")
                        .param("page","0")
                        .param("size","10")
                        .param("searchByNom","hamza")
                        .param("lastNameAr","test")
                        .param("searchByBeneficiaryAr","test")
                        .param("searchByBeneficiary","test")
                        .param("searchByService","1")
                        .param("searchByStatus","test"))
                .andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(takenInChargeDTOS)));
        verify(takenInChargeService,times(1)).getAllTakenInChargesByCriteria(0,10,"hamza","test","test","test",1L,"test",null,null,null,null,null);
    }
    @Test
    @Order(4)
    void TakenInChargeController_findAllTakenInChargesByCriteria_SuccessCase2() throws Exception {
        TakenInChargeDTO takenInChargeDTO=TakenInChargeDTO
                .builder()
                .id(1L)
                .code("TIC-12345")
                .type("Medical Assistance")
                .serviceId(101L)
                .status("ACTIVE")
                .hasOperations(true)
                .clotureMotif("Completed successfully")
                .build();
        Pageable pageable = PageRequest.of(0, 10);
        Page<TakenInChargeDTO> takenInChargeDTOS= new PageImpl<>(List.of(takenInChargeDTO),pageable,1);
        when(takenInChargeService.getAllTakenInChargesByCriteria(0,10,"hamza","test","test","test",1L,"test",null,null,null,null,null)).thenReturn(takenInChargeDTOS);

        mockMvc.perform(get("/takenInCharges/findAll")
                        .param("page","0")
                        .param("size","10")
                        .param("searchByNom","hamza")
                        .param("lastNameAr","test")
                        .param("searchByBeneficiaryAr","test")
                        .param("searchByBeneficiary","test")
                        .param("searchByService","1")
                        .param("searchByStatus","test"))
                .andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(takenInChargeDTOS)));
        verify(takenInChargeService,times(1)).getAllTakenInChargesByCriteria(0,10,"hamza","test","test","test",1L,"test",null,null,null,null,null);
    }

    @Test
    @Order(5)
    void TakenInChargeController_findAllTakenInChargesByCriteria_InternalServerError() throws Exception {

        doThrow(IllegalStateException.class).when(takenInChargeService).getAllTakenInChargesByCriteria(0,10,"hamza","test","test","test",1L,"test",null,null,null,null,null);

        mockMvc.perform(get("/takenInCharges/findAll")
                        .param("page","0")
                        .param("size","10")
                        .param("searchByNom","hamza")
                        .param("lastNameAr","test")
                        .param("searchByBeneficiaryAr","test")
                        .param("searchByBeneficiary","test")
                        .param("searchByService","1")
                        .param("searchByStatus","test"))
                .andExpect(status().isInternalServerError());

        verify(takenInChargeService,times(1)).getAllTakenInChargesByCriteria(0,10,"hamza","test","test","test",1L,"test",null,null,null,null,null);

    }

    @Test
    @Order(6)
    void TakeInChargeController_getTakenInChargeByID_SuccessCase() throws Exception {
        TakenInChargeDTO takenInChargeDTO=TakenInChargeDTO
                .builder()
                .id(1L)
                .code("TIC-12345")
                .type("Medical Assistance")
                .serviceId(101L)
                .status("ACTIVE")
                .hasOperations(true)
                .clotureMotif("Completed successfully")
                .build();
        Long id=1L;
        when(takenInChargeService.getTakenInChargeById(id)).thenReturn(takenInChargeDTO);

        mockMvc.perform(get("/takenInCharges/1")).andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(takenInChargeDTO)));
        verify(takenInChargeService,times(1)).getTakenInChargeById(id);

    }
    @Test
    @Order(7)
    void TakeInChargeController_getTakenInChargeByID_Exception() throws Exception {
        Long id=1L;
        doThrow(IllegalStateException.class).when(takenInChargeService).getTakenInChargeById(id);

        mockMvc.perform(get("/takenInCharges/1")).andExpect(status().isOk());
        verify(takenInChargeService,times(1)).getTakenInChargeById(id);

    }
    @Test
    @Order(8)
    void TakeInChargeController_planTakenInCharge_Success() throws Exception {
        TakenInChargeOperationDTO takenInChargeOperation=TakenInChargeOperationDTO
                .builder()
                .id(1L)
                .code("TIC-12345")
                .status("ACTIVE")
                .comment("comment")
                .build();
        List<TakenInChargeOperationDTO> takenInChargeOperationDTOS=List.of(takenInChargeOperation);

        when(takenInChargeService.planTakenInCharge(anyList())).thenReturn(takenInChargeOperationDTOS);

        mockMvc.perform(post("/takenInCharges/plan").content(objectMapper.writeValueAsString(takenInChargeOperationDTOS))
                .contentType(MediaType.APPLICATION_JSON)).andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(takenInChargeOperationDTOS)));

        verify(takenInChargeService,times(1)).planTakenInCharge(anyList());

    }

    @Test
    @Order(9)
    void TakeInChargeController_planTakenInCharge_InternalServerError() throws Exception {
        TakenInChargeOperationDTO takenInChargeOperation=TakenInChargeOperationDTO
                .builder()
                .id(1L)
                .code("TIC-12345")
                .status("ACTIVE")
                .comment("comment")
                .build();
        List<TakenInChargeOperationDTO> takenInChargeOperationDTOS=List.of(takenInChargeOperation);

        doThrow(IllegalStateException.class).when(takenInChargeService).planTakenInCharge(anyList());

        mockMvc.perform(post("/takenInCharges/plan").content(objectMapper.writeValueAsString(takenInChargeOperationDTOS))
                        .contentType(MediaType.APPLICATION_JSON)).andExpect(status().isInternalServerError());

        verify(takenInChargeService,times(1)).planTakenInCharge(anyList());

    }
    @Test
    @Order(10)
    void TakeInChargeController_updateOperation_Success() throws Exception {
        TakenInChargeOperationDTO takenInChargeOperation=TakenInChargeOperationDTO
                .builder()
                .id(1L)
                .code("TIC-12345")
                .status("ACTIVE")
                .comment("comment")
                .build();

        when(takenInChargeService.updateOperation(any(TakenInChargeOperationDTO.class))).thenReturn(takenInChargeOperation);

        mockMvc.perform(post("/takenInCharges/update").content(objectMapper.writeValueAsString(takenInChargeOperation))
                        .contentType(MediaType.APPLICATION_JSON)).andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(takenInChargeOperation)));
        verify(takenInChargeService,times(1)).updateOperation(any(TakenInChargeOperationDTO.class));

    }

    @Test
    @Order(11)
    void TakeInChargeController_updateOperation_InternalServerError() throws Exception {
        TakenInChargeOperationDTO takenInChargeOperation=TakenInChargeOperationDTO
                .builder()
                .id(1L)
                .code("TIC-12345")
                .status("ACTIVE")
                .comment("comment")
                .build();

        doThrow(IllegalStateException.class).when(takenInChargeService).updateOperation(any(TakenInChargeOperationDTO.class));

        mockMvc.perform(post("/takenInCharges/update").content(objectMapper.writeValueAsString(takenInChargeOperation))
                .contentType(MediaType.APPLICATION_JSON)).andExpect(status().isInternalServerError()) ;
        verify(takenInChargeService,times(1)).updateOperation(any(TakenInChargeOperationDTO.class));

    }

    @Test
    @Order(12)
    void TakeInChargeController_reserveOperation_Success() throws Exception {
        TakenInChargeOperationDTO takenInChargeOperation=TakenInChargeOperationDTO
                .builder()
                .id(1L)
                .code("TIC-12345")
                .status("ACTIVE")
                .comment("comment")
                .build();

        when(takenInChargeService.reserveOperation(any(TakenInChargeOperationDTO.class))).thenReturn(takenInChargeOperation);

        mockMvc.perform(post("/takenInCharges/reserve").content(objectMapper.writeValueAsString(takenInChargeOperation))
                        .contentType(MediaType.APPLICATION_JSON)).andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(takenInChargeOperation)));
        verify(takenInChargeService,times(1)).reserveOperation(any(TakenInChargeOperationDTO.class));

    }

    @Test
    @Order(13)
    void TakeInChargeController_reserveOperation_InternalServerError() throws Exception {
        TakenInChargeOperationDTO takenInChargeOperation=TakenInChargeOperationDTO
                .builder()
                .id(1L)
                .code("TIC-12345")
                .status("ACTIVE")
                .comment("comment")
                .build();

        doThrow(TechnicalException.class).when(takenInChargeService).reserveOperation(any(TakenInChargeOperationDTO.class));

        mockMvc.perform(post("/takenInCharges/reserve").content(objectMapper.writeValueAsString(takenInChargeOperation))
                .contentType(MediaType.APPLICATION_JSON)).andExpect(status().is5xxServerError()) ;

        verify(takenInChargeService,times(1)).reserveOperation(any(TakenInChargeOperationDTO.class));

    }
    @Test
    @Order(14)
    void TakeInChargeController_cancelReservationOperation_Success() throws Exception {
        TakenInChargeOperationDTO takenInChargeOperation=TakenInChargeOperationDTO
                .builder()
                .id(1L)
                .code("TIC-12345")
                .status("ACTIVE")
                .comment("comment")
                .build();

        when(takenInChargeService.cancelReservationOperation(any(TakenInChargeOperationDTO.class))).thenReturn(takenInChargeOperation);

        mockMvc.perform(post("/takenInCharges/cancelReservation").content(objectMapper.writeValueAsString(takenInChargeOperation))
                        .contentType(MediaType.APPLICATION_JSON)).andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(takenInChargeOperation)));
        verify(takenInChargeService,times(1)).cancelReservationOperation(any(TakenInChargeOperationDTO.class));

    }

    @Test
    @Order(15)
    void TakeInChargeController_cancelReservationOperation_InternalServerError() throws Exception {
        TakenInChargeOperationDTO takenInChargeOperation=TakenInChargeOperationDTO
                .builder()
                .id(1L)
                .code("TIC-12345")
                .status("ACTIVE")
                .comment("comment")
                .build();

        doThrow(IllegalStateException.class).when(takenInChargeService).cancelReservationOperation(any(TakenInChargeOperationDTO.class));

        mockMvc.perform(post("/takenInCharges/cancelReservation").content(objectMapper.writeValueAsString(takenInChargeOperation))
                .contentType(MediaType.APPLICATION_JSON)).andExpect(status().isInternalServerError()) ;
        verify(takenInChargeService,times(1)).cancelReservationOperation(any(TakenInChargeOperationDTO.class));

    }

    @Test
    @Order(16)
    void TakeInChargeController_cancelExecutionOperation_Success() throws Exception {
        TakenInChargeOperationDTO takenInChargeOperation=TakenInChargeOperationDTO
                .builder()
                .id(1L)
                .code("TIC-12345")
                .status("ACTIVE")
                .comment("comment")
                .build();

        when(takenInChargeService.cancelExecutionOperation(any(TakenInChargeOperationDTO.class))).thenReturn(takenInChargeOperation);

        mockMvc.perform(post("/takenInCharges/cancelExecution").content(objectMapper.writeValueAsString(takenInChargeOperation))
                        .contentType(MediaType.APPLICATION_JSON)).andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(takenInChargeOperation)));
        verify(takenInChargeService,times(1)).cancelExecutionOperation(any(TakenInChargeOperationDTO.class));

    }

    @Test
    @Order(17)
    void TakeInChargeController_cancelExecutionOperation_InternalServerError() throws Exception {
        TakenInChargeOperationDTO takenInChargeOperation=TakenInChargeOperationDTO
                .builder()
                .id(1L)
                .code("TIC-12345")
                .status("ACTIVE")
                .comment("comment")
                .build();

        doThrow(IllegalStateException.class).when(takenInChargeService).cancelExecutionOperation(any(TakenInChargeOperationDTO.class));

        mockMvc.perform(post("/takenInCharges/cancelExecution").content(objectMapper.writeValueAsString(takenInChargeOperation))
                .contentType(MediaType.APPLICATION_JSON)).andExpect(status().isInternalServerError()) ;
        verify(takenInChargeService,times(1)).cancelExecutionOperation(any(TakenInChargeOperationDTO.class));

    }

    @Test
    @Order(18)
    void TakeInChargeController_encloseOperation_Success() throws Exception {
        TakenInChargeOperationDTO takenInChargeOperation=TakenInChargeOperationDTO
                .builder()
                .id(1L)
                .code("TIC-12345")
                .status("ACTIVE")
                .comment("comment")
                .build();

        when(takenInChargeService.encloseOperation(any(TakenInChargeOperationDTO.class))).thenReturn(takenInChargeOperation);

        mockMvc.perform(post("/takenInCharges/enclose").content(objectMapper.writeValueAsString(takenInChargeOperation))
                        .contentType(MediaType.APPLICATION_JSON)).andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(takenInChargeOperation)));
        verify(takenInChargeService,times(1)).encloseOperation(any(TakenInChargeOperationDTO.class));

    }

    @Test
    @Order(19)
    void TakeInChargeController_encloseOperation_InternalServerError() throws Exception {
        TakenInChargeOperationDTO takenInChargeOperation=TakenInChargeOperationDTO
                .builder()
                .id(1L)
                .code("TIC-12345")
                .status("ACTIVE")
                .comment("comment")
                .build();

        doThrow(IllegalStateException.class).when(takenInChargeService).encloseOperation(any(TakenInChargeOperationDTO.class));

        mockMvc.perform(post("/takenInCharges/enclose").content(objectMapper.writeValueAsString(takenInChargeOperation))
                .contentType(MediaType.APPLICATION_JSON)).andExpect(status().isInternalServerError()) ;
        verify(takenInChargeService,times(1)).encloseOperation(any(TakenInChargeOperationDTO.class));

    }

    @Test
    @Order(21)
    void TakenInChargeController_deleteOperation_Success() throws Exception {
        Long idOperation=1L;
        doNothing().when(takenInChargeService).deleteOperation(idOperation);
        mockMvc.perform(delete("/takenInCharges/delete/1")).andExpect(status().isNoContent());
        verify(takenInChargeService,times(1)).deleteOperation(idOperation);

    }

    @Test
    @Order(22)
    void TakenInChargeController_closeTakenInCharge_Success() throws Exception {
        Long idOperation=1L;
        String comment="comment";
        Long motifTypeId=2L;

        doNothing().when(takenInChargeService).closeTakenInCharge(idOperation,comment,motifTypeId);

        mockMvc.perform(post("/takenInCharges/close/1").param("comment", comment)
                .param("motifTypeId", String.valueOf(motifTypeId)))
                .andExpect(status().isOk());
        verify(takenInChargeService,times(1)).closeTakenInCharge(idOperation,comment,motifTypeId);
    }
    @Test
    @Order(23)
    void TakenInChargeController_closeTakenInCharge_EntityNotFoundException() throws Exception {
        Long idOperation=1L;
        String comment="comment";
        Long motifTypeId=2L;

        doThrow(new EntityNotFoundException("Taken In Charge is not found")).when(takenInChargeService).closeTakenInCharge(idOperation,comment,motifTypeId);

        mockMvc.perform(post("/takenInCharges/close/1").param("comment", comment)
                        .param("motifTypeId", String.valueOf(motifTypeId)))
                .andExpect(status().isNotFound());
        verify(takenInChargeService,times(1)).closeTakenInCharge(idOperation,comment,motifTypeId);

    }
    @Test
    @Order(24)
    void TakenInChargeController_closeTakenInCharge_InvalidOperationException() throws Exception {
        Long idOperation=1L;
        String comment="comment";
        Long motifTypeId=2L;

        doThrow(new InvalidOperationException("This operation  is Invalid")).when(takenInChargeService).closeTakenInCharge(idOperation,comment,motifTypeId);

        mockMvc.perform(post("/takenInCharges/close/1").param("comment", comment)
                        .param("motifTypeId", String.valueOf(motifTypeId)))
                .andExpect(status().isBadRequest());
        verify(takenInChargeService,times(1)).closeTakenInCharge(idOperation,comment,motifTypeId);

    }
    @Test
    @Order(25)
    void TakenInChargeController_closeTakenInCharge_InternalServerError() throws Exception {
        Long idOperation=1L;
        String comment="comment";
        Long motifTypeId=2L;

        doThrow(new IllegalStateException("Something Wrong")).when(takenInChargeService).closeTakenInCharge(idOperation,comment,motifTypeId);

        mockMvc.perform(post("/takenInCharges/close/1").param("comment", comment)
                        .param("motifTypeId", String.valueOf(motifTypeId)))
                .andExpect(status().isInternalServerError());
        verify(takenInChargeService,times(1)).closeTakenInCharge(idOperation,comment,motifTypeId);

    }

    @Test
    @Order(26)
    void TakenInChargeController_executeOperation_Success() throws Exception {
        TakenInChargeOperationDTO takenInChargeOperation=TakenInChargeOperationDTO
                .builder()
                .id(1L)
                .code("TIC-12345")
                .status("ACTIVE")
                .comment("comment")
                .build();

        when(takenInChargeService.executeOperation(any(TakenInChargeOperationDTO.class))).thenReturn(takenInChargeOperation);

        mockMvc.perform(post("/takenInCharges/execute").content(objectMapper.writeValueAsString(takenInChargeOperation))
                        .contentType(MediaType.APPLICATION_JSON)).andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(takenInChargeOperation)));
        verify(takenInChargeService,times(1)).executeOperation(any(TakenInChargeOperationDTO.class));

    }

    @Test
    @Order(27)
    void TakeInChargeController_executeOperation_InternalServerError() throws Exception {
        TakenInChargeOperationDTO takenInChargeOperation=TakenInChargeOperationDTO
                .builder()
                .id(1L)
                .code("TIC-12345")
                .status("ACTIVE")
                .comment("comment")
                .build();

        doThrow(IllegalStateException.class).when(takenInChargeService).executeOperation(any(TakenInChargeOperationDTO.class));

        mockMvc.perform(post("/takenInCharges/execute").content(objectMapper.writeValueAsString(takenInChargeOperation))
                .contentType(MediaType.APPLICATION_JSON)).andExpect(status().isInternalServerError()) ;
        verify(takenInChargeService,times(1)).executeOperation(any(TakenInChargeOperationDTO.class));

    }

    @Test
    @Order(28)
    void TakeInChargeController_deleteTakenInCharge_Success() throws Exception {
        Long id=1L;

        doNothing().when(takenInChargeService).deleteTakenInCharge(id);

        mockMvc.perform(delete("/takenInCharges/1")).andExpect(status().isOk());
        verify(takenInChargeService,times(1)).deleteTakenInCharge(id);

    }
    @Test
    @Order(29)
    void TakeInChargeController_deleteTakenInCharge_IllegalStateException() throws Exception {
        Long id=1L;

        doThrow(new IllegalStateException("Something Wrong")).when(takenInChargeService).deleteTakenInCharge(id);

        mockMvc.perform(delete("/takenInCharges/1")).andExpect(status().isOk());
        verify(takenInChargeService,times(1)).deleteTakenInCharge(id);

    }
    @Test
    @Order(30)
    void TakeInChargeController_deleteTakenInCharge_IllegalArgumentException() throws Exception {
        Long id=1L;

        doThrow(new IllegalArgumentException("Something Wrong")).when(takenInChargeService).deleteTakenInCharge(id);

        mockMvc.perform(delete("/takenInCharges/1")).andExpect(status().isNotFound());
        verify(takenInChargeService,times(1)).deleteTakenInCharge(id);

    }
    @Test
    @Order(31)
    void TakeInChargeController_deleteTakenInCharge_InternalServerError() throws Exception {
        Long id=1L;

        doThrow(new TechnicalException("Something Wrong")).when(takenInChargeService).deleteTakenInCharge(id);

        mockMvc.perform(delete("/takenInCharges/1")).andExpect(status().isInternalServerError());
        verify(takenInChargeService,times(1)).deleteTakenInCharge(id);

    }

    @Test
    @Order(32)
    void TakeInChargeController_handleBatchOperation_Success() throws Exception {
        Map<String,Object> map=new HashMap<>();
        map.put("operationIds", List.of(1,2,3,4));
        map.put("actionType","comment");
        TakenInChargeOperationDTO takenInChargeOperation=TakenInChargeOperationDTO
                .builder()
                .id(1L)
                .code("TIC-12345")
                .status("ACTIVE")
                .comment("comment")
                .build();
        List<TakenInChargeOperationDTO> takenInChargeOperationDTOS=List.of(takenInChargeOperation);

        when(takenInChargeService.handleBatchOperation(anyList(),any())).thenReturn(takenInChargeOperationDTOS);

        mockMvc.perform(post("/takenInCharges/batch-action").content(objectMapper.writeValueAsString(map)).contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(takenInChargeOperationDTOS)));
        verify(takenInChargeService,times(1)).handleBatchOperation(anyList(),any());

    }

    @Test
    @Order(33)
    void TakeInChargeController_handleBatchOperation_InternalServerError() throws Exception {
        Map<String,Object> map=new HashMap<>();
        map.put("operationIds", List.of(1,2,3,4));
        map.put("actionType","comment");
        doThrow(new TechnicalException("Something Wrong")).when(takenInChargeService).handleBatchOperation(anyList(),any());

        mockMvc.perform(post("/takenInCharges/batch-action").content(objectMapper.writeValueAsString(map)).contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isInternalServerError());
        verify(takenInChargeService,times(1)).handleBatchOperation(anyList(),any());

    }


    @Test
    @Order(34)
    void TakenInChargeController_getAllOperations_Success() throws Exception {
        OperationListDto operationListDto=OperationListDto
                .builder()
                .id(1L)
                .code("TIC-12345")
                .status("ACTIVE")
                .comment("comment")
                .amount(20.20)
                .build();
        List<OperationListDto> operationListDtoS=List.of(operationListDto);
        Pageable pageable=PageRequest.of(0, 10);

        Page<OperationListDto> operationListDtos=new PageImpl<>(operationListDtoS,pageable,1);

        when(takenInChargeService.getAllOperations(0,20, "nameDonor", "Test", "Test","Test",
                "Test",1L,1L,null, null, null,
                null, null, null)).thenReturn(operationListDtos);

        mockMvc.perform(get("/takenInCharges/operations")
                        .param("searchByNameDonor","nameDonor")
                        .param("searchByNameDonorAr","Test")
                        .param("searchByBeneficiary","Test")
                        .param("searchByNameBeneficiaryAr","Test")
                        .param("searchByStatusOperation","Test")
                        .param("searchByServiceId","1")
                        .param("searchByStatusId","1"))
                .andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(operationListDtos)));
        verify(takenInChargeService,times(1)).getAllOperations(0,20, "nameDonor", "Test", "Test","Test",
                "Test",1L,1L,null, null, null,
                null, null, null);


    }

    @Test
    @Order(35)
    void TakenInChargeController_getOperationsByTakenInChargeId_Success() throws Exception {
        TakenInChargeOperationDTO takenInChargeOperation=TakenInChargeOperationDTO
                .builder()
                .id(1L)
                .code("TIC-12345")
                .status("ACTIVE")
                .comment("comment")
                .build();
        List<TakenInChargeOperationDTO> takenInChargeOperationDTOS=List.of(takenInChargeOperation);
        when(takenInChargeService.getOperationsByTakenInChargeId(anyLong())).thenReturn(takenInChargeOperationDTOS);

        mockMvc.perform(get("/takenInCharges/operations/1"))
                .andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(takenInChargeOperationDTOS)));
        verify(takenInChargeService,times(1)).getAllOperations(0,20, "nameDonor", "Test", "Test","Test",
                "Test",1L,1L,null, null, null,
                null, null, null);
    }

}
