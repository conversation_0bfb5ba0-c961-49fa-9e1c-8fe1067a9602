package ma.almobadara.backend.mapper;


import ma.almobadara.backend.model.takenInCharge.TakenInChargeBeneficiary;
import ma.almobadara.backend.dto.takenInCharge.TakenInChargeBeneficiaryDTO;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface TakenInChargeBeneficiaryMapper {

	TakenInChargeBeneficiaryDTO takenInChargeBeneficiaryToTakenInChargeBeneficiaryDTO(TakenInChargeBeneficiary takenInChargeBeneficiary);

	Iterable<TakenInChargeBeneficiaryDTO> takenInChargeBeneficiaryToTakenInChargeBeneficiaryDTO(Iterable<TakenInChargeBeneficiary> takenInChargeBeneficiary);

	TakenInChargeBeneficiary takenInChargeBeneficiaryDTOToTakenInChargeBeneficiary(TakenInChargeBeneficiaryDTO takenInChargeBeneficiaryDTO);

	Iterable<TakenInChargeBeneficiary> takenInChargeBeneficiaryDTOToTakenInChargeBeneficiary(Iterable<TakenInChargeBeneficiaryDTO> takenInChargeBeneficiaryDTO);

}
