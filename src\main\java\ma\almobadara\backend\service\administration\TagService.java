package ma.almobadara.backend.service.administration;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.administration.TagDTO;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.model.administration.Tag;
import ma.almobadara.backend.model.administration.Taggable;
import ma.almobadara.backend.model.administration.TypeTag;
import ma.almobadara.backend.repository.administration.TagRepository;
import ma.almobadara.backend.repository.administration.TaggableRepository;
import ma.almobadara.backend.repository.administration.TypeTagRepositoy;
import org.apache.poi.sl.draw.geom.GuideIf;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class TagService {
    private final TagRepository tagRepository;
    private final TypeTagRepositoy typeTagRepository;
    private final TaggableRepository taggableRepository;

    /**
     * Create a new tag
     * @param tagDTO the tag data transfer object
     * @return the created tag as DTO
     * @throws TechnicalException if a tag with the same name already exists
     */
    @Transactional
    public TagDTO saveTag(TagDTO tagDTO) throws TechnicalException {
        log.info("{} tag: {}", tagDTO.getId() == null ? "Creating" : "Updating", tagDTO.getName());

        Tag tag;

        if (tagDTO.getId() != null) {
            // Updating existing tag
            tag = tagRepository.findById(tagDTO.getId())
                    .orElseThrow(() -> new TechnicalException("Tag not found with ID: " + tagDTO.getId()));

            // Check if another tag with the same name exists
            Optional<Tag> existingTag = tagRepository.findByName(tagDTO.getName().trim(), tagDTO.getTypeTagId());
            if (existingTag.isPresent() && !existingTag.get().getId().equals(tagDTO.getId())) {
                throw new TechnicalException("Tag with the same name already exists");
            }

        } else {
            // Creating new tag - check for duplicate names
            Optional<Tag> existingTag = tagRepository.findByName(tagDTO.getName().trim(), tagDTO.getTypeTagId());
            if (existingTag.isPresent()) {
                throw new TechnicalException("Tag with the same name already exists");
            }
            tag = new Tag(); // New tag instance
        }


        // Set properties
        tag.setName(tagDTO.getName());
        tag.setColor(tagDTO.getColor());

        // Handle TypeTag association
        if (tagDTO.getTypeTagId() != null) {
            Optional<TypeTag> typeTagOptional = typeTagRepository.findById(tagDTO.getTypeTagId());
            typeTagOptional.ifPresent(tag::setTypeTag);
        } else if (tagDTO.getTypeTagName() != null && !tagDTO.getTypeTagName().trim().isEmpty()) {
            Optional<TypeTag> existingTypeTag = typeTagRepository.findByName(tagDTO.getTypeTagName().trim());

            if(existingTypeTag.isPresent()){
                throw new TechnicalException("Type Tag with the same name already exists");
            }
            TypeTag newTypeTag = new TypeTag();
            newTypeTag.setName(tagDTO.getTypeTagName().trim());
            TypeTag savedTypeTag = typeTagRepository.save(newTypeTag);
            tag.setTypeTag(savedTypeTag);
            tagDTO.setTypeTagId(savedTypeTag.getId());
        }

        // Save tag (create or update)
        Tag savedTag = tagRepository.save(tag);

        return convertToDTO(savedTag);
    }


    /**
     * Get all tags with pagination
     * @param page the page number
     * @param size the page size
     * @return a page of tag DTOs
     */
    public Page<TagDTO> getAllTags(int page, int size) {
        log.info("Retrieving all tags with pagination - page: {}, size: {}", page, size);
        Pageable pageable = PageRequest.of(page, size);
        Page<Tag> tagPage = tagRepository.findAll(pageable);
        return tagPage.map(this::convertToDTO);
    }

    /**
     * Get all tags as a list
     * @return a list of tag DTOs
     */
    public List<TagDTO> getTagList() {
        log.info("Retrieving all tags as a list");
        List<Tag> tags = tagRepository.findAll();
        return tags.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * Get a tag by its ID
     * @param tagId the tag ID
     * @return the tag DTO if found, or null
     */
    public TagDTO getTagById(Long tagId) {
        log.info("Retrieving tag by ID: {}", tagId);
        Optional<Tag> tagOptional = tagRepository.findById(tagId);
        return tagOptional.map(this::convertToDTO).orElse(null);
    }
    public Set<TagDTO> getTagsByTaggableType(String taggableType) {
        log.info("Retrieving tags by taggable type: {}", taggableType);
        List<Taggable> taggables = taggableRepository.findByTaggableType(taggableType);
        return taggables.stream()
                .map(Taggable::getTag)
                .map(this::convertToDTO)
                .collect(Collectors.toSet());
    }
    /**
     * Update an existing tag
     * @param tagDTO the tag data to update
     * @return the updated tag as DTO
     * @throws TechnicalException if the tag doesn't exist or name conflict
     */
    @Transactional
    public TagDTO updateTag(TagDTO tagDTO) throws TechnicalException {
        log.info("Updating tag with ID: {}", tagDTO.getId());

        if (tagDTO.getId() == null) {
            throw new TechnicalException("Tag ID must be provided for update");
        }

        Optional<Tag> existingTagOptional = tagRepository.findById(tagDTO.getId());
        if (existingTagOptional.isEmpty()) {
            throw new TechnicalException("Tag not found with ID: " + tagDTO.getId());
        }

        // Check for name conflicts with other tags
        Optional<Tag> tagWithSameName = tagRepository.findByName(tagDTO.getName().trim(), tagDTO.getTypeTagId());
        if (tagWithSameName.isPresent() && !tagWithSameName.get().getId().equals(tagDTO.getId())) {
            throw new TechnicalException("Another tag with the same name already exists");
        }

        Tag existingTag = existingTagOptional.get();
        existingTag.setName(tagDTO.getName());
        existingTag.setColor(tagDTO.getColor());

        // Handle TypeTag association
        if (tagDTO.getTypeTagId() != null) {
            // Try to find existing TypeTag
            Optional<TypeTag> typeTagOptional = typeTagRepository.findById(tagDTO.getTypeTagId());
            if (typeTagOptional.isPresent()) {
                existingTag.setTypeTag(typeTagOptional.get());
            }
        } else if (tagDTO.getTypeTagName() != null && !tagDTO.getTypeTagName().trim().isEmpty()) {
            // Create a new TypeTag if typeTagId is not provided but typeTagName is
            TypeTag newTypeTag = new TypeTag();
            newTypeTag.setName(tagDTO.getTypeTagName().trim());
            newTypeTag.setDescription("Auto-created type for tag: " + tagDTO.getName());

            // Save the new TypeTag
            TypeTag savedTypeTag = typeTagRepository.save(newTypeTag);
            existingTag.setTypeTag(savedTypeTag);

            // Update the DTO with the new TypeTag ID
            tagDTO.setTypeTagId(savedTypeTag.getId());
        } else {
            // If neither typeTagId nor typeTagName is provided, remove the association
            existingTag.setTypeTag(null);
        }

        Tag updatedTag = tagRepository.save(existingTag);
        return convertToDTO(updatedTag);
    }

    /**
     * Delete a tag by its ID
     * @param tagId the tag ID to delete
     */
    @Transactional
    public void deleteTag(Long tagId) {
        log.info("Deleting tag with ID: {}", tagId);
        tagRepository.deleteById(tagId);
    }

    /**
     * Get tags by type tag ID
     * @param typeTagId the type tag ID
     * @return a list of tag DTOs with the specified type
     */
    public List<TagDTO> getTagsByTypeTagId(Long typeTagId) {
        log.info("Retrieving tags by type tag ID: {}", typeTagId);
        // This would be more efficient with a custom repository method
        List<Tag> tags = tagRepository.findAll();
        return tags.stream()
                .filter(tag -> tag.getTypeTag() != null && tag.getTypeTag().getId().equals(typeTagId))
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * Convert a Tag entity to a TagDTO
     * @param tag the tag entity
     * @return the tag DTO
     */
    private TagDTO convertToDTO(Tag tag) {
        TagDTO dto = new TagDTO();
        dto.setId(tag.getId());
        dto.setName(tag.getName());
        dto.setColor(tag.getColor());

        if (tag.getTypeTag() != null) {
            dto.setTypeTagId(tag.getTypeTag().getId());
            dto.setTypeTagName(tag.getTypeTag().getName());
            dto.setReadOnly(tag.getTypeTag().getReadOnly());
        }

        return dto;
    }
}
