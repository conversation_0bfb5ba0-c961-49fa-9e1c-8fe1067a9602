package ma.almobadara.backend.service.communs;

import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.model.administration.CacheAdUser;
import ma.almobadara.backend.model.beneficiary.Beneficiary;
import ma.almobadara.backend.model.communs.Action;
import ma.almobadara.backend.model.communs.CommentAction;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import static ma.almobadara.backend.util.constants.GlobalConstants.*;

@Slf4j
@Service
public class MailSenderService {
    private final JavaMailSender mailSender;

    public MailSenderService(JavaMailSender mailSender) {
        this.mailSender = mailSender;
    }

    public void sendNewMail(String to, String subject, String body) {
        log.info("Sending email to: {}", to);
        MimeMessage message = mailSender.createMimeMessage();
        try {
            MimeMessageHelper helper = new MimeMessageHelper(message, true);
            helper.setTo(to);
            helper.setSubject(subject);
            helper.setText(body, true);
            mailSender.send(message);
        } catch (MessagingException e) {
            e.printStackTrace();
        }
        log.info("Email sent successfully to: {}", to);
    }

    // we should add the type of the enity of the action to generate the appropriate link
    public String generateLink(Long entityId, String entityType) {
        switch (entityType.toLowerCase()) {
            case DONOR:
                return "https://almobadara.xelops.ma/donors/fiche/" + entityId + "/action";
            case BENEFICIARY:
                return "https://almobadara.xelops.ma/beneficiaries/fiche/" + entityId + "/action";
            case FAMILY:
                return "https://almobadara.xelops.ma/families/fiche/" + entityId + "/action";
            case DONATION:
                return "https://almobadara.xelops.ma/donations/fiche/" + entityId + "/action";

            case TAKENINCHARGE:
                return "https://almobadara.xelops.ma/takenInCharges/fiche/" + entityId + "/action";
            default:
                return "";
        }
    }

    public String generateEmailContentOne(Action action, boolean isReminder, String entityType, Long entityId) {
        String message = "";
        String alert = "";
        String createdByFullName = action.getCreatedBy().getFirstName() + " " + action.getCreatedBy().getLastName();
        String actionSubject = action.getSubject();
        List<CommentAction> actionComments = action.getComments();
        LocalDateTime deadlineDateTime = LocalDateTime.ofInstant(action.getDeadline().toInstant(), ZoneId.systemDefault());

        // Format the date in French
        DateTimeFormatter frenchFormatter = DateTimeFormatter.ofPattern("EEEE d MMMM yyyy", Locale.FRENCH);
        String deadline = deadlineDateTime.format(frenchFormatter);
        String link = "";

        if (isReminder) {
            message = "Nous tenons à vous rappeler que le deadline pour votre action est prévue pour demain. " +
                    "Veuillez prendre les mesures nécessaires pour vous assurer que votre action est complétée à temps. " +
                    "Si vous avez des questions ou des préoccupations, n'hésitez pas à nous contacter. " +
                    "Merci de votre attention et de votre coopération.";
            alert = "Deadline pour l'action";

        }

        // Get the last comment if it exists
        String lastCommentContent = "";
        if (!actionComments.isEmpty()) {
            lastCommentContent = actionComments.get(actionComments.size() - 1).getContent();
        }

        return String.format(
                """
                                <html>
                                <head>
                                    <style>
                                        body {
                                            font-family: Arial, sans-serif;
                                            background-color: #f8f9fa;
                                            color: #333;
                                            margin: 0;
                                            padding: 0;
                                        }
                                        .email-container {
                                            max-width: 600px;
                                            margin: 20px auto;
                                            padding: 20px;
                                            background-color: #fff;
                                            border-radius: 5px;
                                            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
                                        }
                                        .email-heading {
                                            font-size: 24px;
                                            font-weight: bold;
                                            color: #007bff;
                                            margin-bottom: 20px;
                                        }
                                        .email-content {
                                            margin-bottom: 20px;
                                        }
                                        .email-content ul {
                                            list-style-type: none;
                                            padding: 0;
                                        }
                                        .email-content li {
                                            margin-bottom: 10px;
                                        }
                                        .email-signature {
                                            margin-top: 20px;
                                            font-size: 14px;
                                            color: #888;
                                        }
                                    </style>
                                </head>
                                <body>
                                    <div class="email-container">
                                        <p class="email-heading">Bonjour </p>
                                        <div class="email-content">
                                            <p style="color: red;"><strong>%s</strong></p>
                                            <p>%s</p>
                                            <p>Voici les détails de l'action :</p>
                                            <ul>
                                                <li><strong>Sujet :</strong> %s</li>
                                                <li><strong>Commentaire :</strong> %s</li>
                                                <li><strong>Créé par :</strong> %s</li>
                                                <li><strong>Affecté à :</strong> %s</li>
                                                <li><strong>Date limite :</strong> %s</li>
                                            </ul>
                                          
                                        </div>
                                        <p class="email-signature">Cordialement,<br>Association Almobadara</p>
                                    </div>
                                </body>
                                </html>
                        """,
                alert,
                message,
                actionSubject,
                lastCommentContent,
                createdByFullName,
                action.getAffectedTo().getFirstName() + " " + action.getAffectedTo().getLastName(),
                deadline
               // generateClickableLink(link)
        );
    }



    public void sendCompletionEmail(CacheAdUser assistant, Beneficiary beneficiary, String rqComplete, String subject, String recipientEmail, String serviceName, String username) {
        // Construire le corps de l'email
        String emailBody = String.format(
                "Bonjour,<br><br>" +
                        "Nous espérons que vous allez bien.<br><br>" +
                        "%s du service %s sollicite votre aide pour compléter les informations du bénéficiaire suivant :<br>" +
                        "<strong>%s %s</strong> - Code bénéficiaire : <strong>%s</strong>.<br><br>" +
                        "Informations à compléter :<br>" +
                        "<em>%s</em><br><br>" +
                        "Merci de bien vouloir compléter les informations nécessaires.<br><br>" +
                        "Bien cordialement,<br>" +
                        "L'équipe Almobadara",
                username,
                serviceName,
                beneficiary.getPerson().getFirstName(),
                beneficiary.getPerson().getLastName(),
                beneficiary.getCode(),
                rqComplete
        );

        // Envoyer l'email
        sendNewMail(recipientEmail, subject, emailBody);
    }


    public void sendCompletionEmailRapport(CacheAdUser assistant, Beneficiary beneficiary, String rqComplete, String subject, String recipientEmail, String serviceName, String username) {
        String emailBody = String.format(
                "Bonjour,<br><br>" +
                        "Nous espérons que vous allez bien.<br><br>" +
                        "%s du service %s sollicite votre aide pour compléter les informations de rapport du bénéficiaire suivant :<br>" +
                        "<strong>%s %s</strong> - Code bénéficiaire : <strong>%s</strong>.<br><br>" +
                        "Informations à compléter :<br>" +
                        "<em>%s</em><br><br>" +
                        "Merci de bien vouloir compléter les informations nécessaires.<br><br>" +
                        "Bien cordialement,<br>" +
                        "L'équipe Almobadara",
                username,
                serviceName,
                beneficiary.getPerson().getFirstName(),
                beneficiary.getPerson().getLastName(),
                beneficiary.getCode(),
                rqComplete
        );
        sendNewMail(recipientEmail, subject, emailBody);
    }


    public String buildNotificationEmailSubject(Beneficiary beneficiary) {
        return String.format("[AlMobadara] Action requise sur le Rapport Kafalat de %s %s",
                beneficiary.getPerson().getFirstName(),
                beneficiary.getPerson().getLastName());
    }

    public String buildNotificationEmailBody(String username, Beneficiary beneficiary, Date dateRapport) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("dd MMMM yyyy", java.util.Locale.FRENCH);
        String formattedDate = (dateRapport != null) ? dateFormat.format(dateRapport) : "Date non disponible";
        return String.format(
                "Bonjour <strong>%s</strong><br><br> " +
                        "Le rapport Kafalat <strong>%s</strong> du bénéficiaire <strong>%s %s</strong> nécessite votre intervention.<br>" +
                        "Merci de procéder aux actions requises pour faire avancer son état.<br><br>" +
                        "Cordialement,<br>" +
                        "L'équipe AlMobadara",
                username,
                formattedDate,
                beneficiary.getPerson().getFirstName(),
                beneficiary.getPerson().getLastName()
        );
    }



}
