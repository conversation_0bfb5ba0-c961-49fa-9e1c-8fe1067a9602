package ma.almobadara.backend.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "azure-enterprise-app-properties")
@Getter
@Setter
public class AzureEnterpriseApp {

    private String tenantId;

    private String clientId;

    private String clientSecret;

    private String serviceRootBeta;
}
