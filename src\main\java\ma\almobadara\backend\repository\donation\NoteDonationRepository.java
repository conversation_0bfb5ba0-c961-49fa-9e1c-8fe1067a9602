package ma.almobadara.backend.repository.donation;

import ma.almobadara.backend.model.donation.Donation;
import ma.almobadara.backend.model.donation.NoteDonation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface NoteDonationRepository  extends JpaRepository<NoteDonation, Long> {

    Iterable<NoteDonation> findByDonation(Donation donation);

    List<NoteDonation> findByNoteId(Long id);

    List<NoteDonation> findByDonationId(Long donationId);

}
