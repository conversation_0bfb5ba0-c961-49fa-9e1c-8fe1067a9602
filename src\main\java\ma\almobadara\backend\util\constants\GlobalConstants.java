package ma.almobadara.backend.util.constants;

public final class GlobalConstants {

    /**
     * code messages
     */

    public static final String CITY_NOT_FOUND = "error.city.not.found";
    public static final String PERSON_NOT_FOUND = "error.person.not.found";
    public static final String SCOOL_YEAR_NOT_FOUND = "error.schoolYear.not.found";
    public static final String BEBEFIACIARY_DOCUMENT_TYPE_NOT_FOUND = "error.beneficiaryDocumentType.notFound";
    public static final String SCOOL_LEVEL_NOT_FOUND = "error.schoolLevel.notFound";
    public static final String SCHOLARSHIP_NOT_FOUND = "error.scholarship.not.found";
    public static final String MAJOR_NOT_FOUND = "error.major.not.found";
    public static final String HONOR_NOT_FOUND = "error.honor.not.found";
    public static final String ALLERGY_NOT_FOUND = "error.allergy.not.found";
    public static final String DISEASE_NOT_FOUND = "error.disease.not.found";
    public static final String EPS_NOT_FOUND = "error.eps.not.found";
    public static final String HANDICAP_TYPE_NOT_FOUND = "error.handicapType.not.found";
    public static final String DISEASE_TREATMENT_TYPE_NOT_FOUND = "error.diseaseTreatmentType.not.found";
    public static final String EPS_RESIDENT_NOT_FOUND = "error.epsResident.not.found";
    public static final String SERVICE_NOT_FOUND = "error.service.not.found";
    public static final String ACCOMMODATION_NOT_FOUND = "error.accommodation.not.found";
    public static final String REGION_NOT_FOUND = "error.region.not.found";
    public static final String COUNTRY_NOT_FOUND = "error.country.not.found";
    public static final String STATUS_NOT_FOUND = "error.status.not.found";
    public static final String TYPE_IDENTITY_NOT_FOUND = "error.typeIdentity.not.found";
    public static final String BENEFICIARY_NOT_FOUND = "error.beneficiary.not.found";
    public static final String DONATION_NOT_FOUND = "error.donation.not.found";
    public static final String DONOR_NOT_FOUND = "error.donor.not.found";
    public static final String ACTIVITY_SECTOR_NOT_FOUND = "error.activitySector.not.found";
    public static final String DOCUMENT_DONOR_NOT_FOUND = "error.documentDonor.not.found";
    public static final String PROFESSION_NOT_FOUND = "error.profession.not.found";
    public static final String MET_DEATH_REASON_NOT_FOUND = "error.metDeathReason.not.found";
    public static final String EDUCATION_SYSTEM_TYPE_NOT_FOUND = "error.educationSystemType.not.found";
    public static final String CANAL_COMMUNICATION_NOT_FOUND = "error.canalCommunication.not.found";
    public static final String CARD_TYPE_NOT_FOUND = "error.cardType.not.found";
    public static final String SCHOLARSHIP_BENEFICIARY_NOT_FOUND = "error.scholarshipBeneficiary.not.found";
    public static final String EDUCATION_NOT_FOUND = "error.education.not.found";
    public static final String FAMILY_DOCUMENT_TYPE_NOT_FOUND = "error.familyDocumentType.not.found";
    public static final String INCOME_SOURCE_NOT_FOUND = "error.incomeSource.not.found";
    public static final String FAMILY_RELASHIONSHIP_NOT_FOUND = "error.familyRelationship.not.found";
    public static final String FAMILY_NOT_FOUND = "error.family.not.found";
    public static final String TAKEN_IN_CHARGE_OPERATION_NOT_FOUND = "error.takenInChargeOperation.not.found";
    public static final String DONOR_INSUFFICIENT_BALANCE = "error.donor.insufficient.balance";
    public static final String TYPE_DONOR_MORAL_NOT_FOUND = "error.typeDonorMoral.not.found";
    public static final String USER_ID_CREATED_BY_NOT_FOUND = "error.user.id.created.by.not.found";
    public static final String ENTITY_ID_NOT_FOUND = "error.entity.id.not.found";
    public static final String ENTITY_TYPE_NOT_FOUND = "error.entity.type.not.found";
    public static final String USER_ID_AFFECTED_BY_NOT_FOUND = "error.user.id.affected.by.not.found";
    public static final String ACTION_NULL = "error.entity.action.null";
    public static final String DOCUMENT_NULL = "error.entity.document.null";
    public static final String TAKEN_IN_CHARGE_BENEFICIARY_NOT_FOUND = "error.takenInChargeBeneficiary.not.found";

    public static final String More_THAN_ONE_TUTOR = "error.more.than.tutor";

    public static final String TAKEN_IN_CHARGE_DONOR_NOT_FOUND = "error.takenInChargeDonor.not.found";
    public static final String DONOR = "donor";
    public static final String BENEFICIARY = "beneficiary";
    public static final String DONATION = "donation";
    public static final String FAMILY = "family";
    public static final String TAKENINCHARGE = "takenincharge";
    public static final String CAMPAGNE = "campagne";
    public static final String TYPE_DONATION_NATURE = "Nature";
    public static final String TYPE_DONATION_FINNACIERE = "Financière";
    public static final String TYPE_DONATION = "typeDonation";
    public static final String TYPE_DONOR = "typeDonor";
    public static final String TYPE_DONOR_IDENTIFIE = "Identifié";
    public static final String TYPE_DONOR_NON_IDENTIFIE = "Non Identifié";
    public static final String DONATION_WITH_NO_DONOR = "DI";
    public static final String DONATION_WITH_DONOR = "D";
    public static final String TYPE_DONOR_PHYSIQUE = "Physique";
    public static final String TYPE_DONOR_MORAL = "Moral";
    public static final String TYPE_DONOR_ANONYME = "Anonyme";
    public static final String NULL_ENTITY = "error.entity.null";
    public static final String FAILED_TO_DELETE_FILE_FROM_MINIO = "Failed to delete file from MinIO";
    public static final String DONATION_PRODUCT_NOT_FOUND = "error.donation.product.not.found";
    public static final String ZEROS = "0";
    public static final String M_CODE = "M"; //M for member's code
    public static final String F_CODE = "F"; //M for family's code
    public static final String CRITERIA_NAMEBENEFICARY = "nameBeneficiary";
    public static final String CRITERIA_SERVICE = "service";
    public static final String CRITERIA_STATUS = "status";
    public static final String TAKEN_IN_CHARGE_NOT_FOUND = "Taken in charge not found";
    private static final String BASE_URI = "/problem";
    public static final String URI_VALIDATION_CONSTRAINT = BASE_URI + "/validation-constraint";
    public static String FAILED_TO_DOWNLOAD_FILE_FROM_MINIO = "Failed to download file from MinIO";
    public static String FAILED_TO_READ_OR_WRITE_FILE_TO_MINIO = "Failed to read or write file";
    public static String NO_DATA_TO_EXPORT = "No data to export";

    public static final String USER_ID_AUTHOR_BY_NOT_FOUND = "error.user.id.author.by.not.found";

    public static final String DONATEUR = "Donateur";
    public static final String SERVICE = "Service";
    public static final String BENEFICIAIRE = "Beneficiaire";
    public static final String DONATION2 = "Donation";
    public static final String FAMILLE = "Famille";
    public static final String KAFALAT = "Kafalat";
    public static final String ADMINISTRATION = "Administration";
    public static final String AIDE_COMPLEMENTAIRE = "Aide complementaire";
    public static final String PRISEENCHARGE = "Kafalat";
    public static final String DOCUMENT = "Document";
    public static final String EPS = "Eps";


    public static final String VIEW = "Recherche";
    public static final String CREATE = "Ajout";
    public static final String UPDATE = "Modification";
    public static final String EXPORT = "Exportation";
    public static final String DELETE = "Suppression";
    public static final String CONSULTATION = "Consultation"; 
    public static final String VALIDATION = "Cycle de Validation"; 
    public static final String TELECHARGEMENT = "Telechargement"; 

}
