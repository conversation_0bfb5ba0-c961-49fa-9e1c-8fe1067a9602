package ma.almobadara.backend.util.page;

import java.util.List;

public class PageDto<T> {
    private List<T> content;
    private PageableDto pageable;
    private boolean last;
    private boolean first;
    private int totalElements;
    private int totalPages;
    private int size;
    private int number;
    private int numberOfElements;
    private boolean empty;
    private SortDto sort;

    public PageDto(List<T> content, int page, int size, int totalElements, SortDto sort) {
        this.content = content;
        this.pageable = new PageableDto(page, size, sort);
        this.totalElements = totalElements;
        this.size = size;
        this.number = page;
        this.totalPages = (int) Math.ceil((double) totalElements / size);
        this.first = page == 0;
        this.last = page >= totalPages - 1;
        this.numberOfElements = content.size();
        this.empty = content.isEmpty();
        this.sort = sort;
    }

    // Getters et setters
    public List<T> getContent() { return content; }
    public void setContent(List<T> content) { this.content = content; }

    public PageableDto getPageable() { return pageable; }
    public void setPageable(PageableDto pageable) { this.pageable = pageable; }

    public boolean isLast() { return last; }
    public void setLast(boolean last) { this.last = last; }

    public boolean isFirst() { return first; }
    public void setFirst(boolean first) { this.first = first; }

    public int getTotalElements() { return totalElements; }
    public void setTotalElements(int totalElements) { this.totalElements = totalElements; }

    public int getTotalPages() { return totalPages; }
    public void setTotalPages(int totalPages) { this.totalPages = totalPages; }

    public int getSize() { return size; }
    public void setSize(int size) { this.size = size; }

    public int getNumber() { return number; }
    public void setNumber(int number) { this.number = number; }

    public int getNumberOfElements() { return numberOfElements; }
    public void setNumberOfElements(int numberOfElements) { this.numberOfElements = numberOfElements; }

    public boolean isEmpty() { return empty; }
    public void setEmpty(boolean empty) { this.empty = empty; }

    public SortDto getSort() { return sort; }
    public void setSort(SortDto sort) { this.sort = sort; }
}
