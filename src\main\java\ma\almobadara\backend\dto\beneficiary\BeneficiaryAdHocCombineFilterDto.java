package ma.almobadara.backend.dto.beneficiary;

import lombok.*;
import ma.almobadara.backend.model.aideComplemenatire.AideComplementaire;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class BeneficiaryAdHocCombineFilterDto {

    private String code;
    private String firstName;
    private String lastName;
    private String firstNameAr;
    private String lastNameAr;
    private String beneficiaryStatut;
    private String identityCode;
    private Long typeIdentityId;
    private String comment;
    private List<AideComplementaire> aideComplementaires;
    private List<Long> typePriseEnChargeIds;
    private String name;
    private Long cityId;
    private String fullNameContact;
    private String phoneNumber;
    private String status;

}
