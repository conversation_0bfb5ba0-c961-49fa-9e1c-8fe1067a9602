package ma.almobadara.backend.repository.donor;

import ma.almobadara.backend.model.administration.Eps;
import ma.almobadara.backend.model.donor.DonorPhysical;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface DonorPhysicalRepository extends JpaRepository<DonorPhysical,Long> {
    @Query("SELECT e FROM DonorPhysical e WHERE LOWER(e.firstName) LIKE LOWER(CONCAT('%', :query, '%')) OR LOWER(e.lastName) LIKE LOWER(CONCAT('%', :query, '%')) OR LOWER(e.email) LIKE LOWER(CONCAT('%', :query, '%')) OR LOWER(e.phoneNumber) LIKE LOWER(CONCAT('%', :query, '%'))")
    Page<DonorPhysical> searchDonor(@Param("query") String query, Pageable pageable);

    Optional<DonorPhysical> findByEmailAndPassword(String email, String password);

    @Query("Select count(e) from DonorPhysical e where e.deletedAt is null")
    long countByDeletedAtIsNull();

    DonorPhysical findByEmail(String email);

    @Query("SELECT d FROM DonorPhysical d WHERE d.password IS NOT NULL")
    List<DonorPhysical> findByPasswordIsNotNull();
}
