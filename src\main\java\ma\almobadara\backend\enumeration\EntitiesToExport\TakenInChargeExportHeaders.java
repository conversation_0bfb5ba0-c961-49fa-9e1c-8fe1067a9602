package ma.almobadara.backend.enumeration.EntitiesToExport;

import lombok.Getter;

@Getter
public enum TakenInChargeExportHeaders {
    CODE("Code"),
    DATE_CREATION("Date de creation"),
    DONOR_NAME("Nom du Donateur"),
    BENEFICIARY_NAME("Nom du Bénéficiaire"),
    SERVICE_NAME("Nom du Service"),
    STATUS("Statut"),
    START_DATE("Date de Début"),
    END_DATE("Date de Fin"),
    NUMBER_OF_OPERATIONS("Nombre d'opérations"),
    TOTAL_AMOUNT("Montant total (DH)"),
    NUMBER_OF_PLANNED_OPERATIONS("Nombre d'opérations planifiées"),
    NUMBER_OF_EXECUTED_OPERATIONS("Nombre d'opérations exécutées"),
    NUMBER_OF_CLOSED_OPERATIONS("Nombre d'opérations clôturées");

    private final String headerName;

    TakenInChargeExportHeaders(String headerName) {
        this.headerName = headerName;
    }

}

