package ma.almobadara.backend.dto.beneficiary;

import lombok.*;

import java.util.Date;

@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class HistoryRapportDto {

    private Long id;
    private Date dateCommunicated;
    private String communicatedBy;
    private Long beneficiaryId;
    private Long rapportId;
    private Long donorId;
    private Long agendaRapportId;
    private String donorName;
    private String codeRapport;
    private Long numberRapport;
    private String year;

}
