package ma.almobadara.backend.repository.administration;

import ma.almobadara.backend.model.administration.Profile;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ProfileRepository extends JpaRepository<Profile, Long> {
    @Query("SELECT p FROM Profile p WHERE p.nameProfile = :nameprofile and p.isDeleted = false")
    Optional<Profile> findByNameProfile(String nameprofile) ;

    // find All by deleted is false
    @Query("SELECT p FROM Profile p WHERE p.isDeleted = false")
    Page<Profile> findAllWithDeletedIsFalse(Pageable pageable);

    @Query("SELECT p FROM Profile p WHERE p.isDeleted = false")
    List<Profile> findAllByIsDeletedIsFalse();

    @Query(value = "SELECT DISTINCT p.* FROM profile p " +
            "JOIN user_profile_module_functionality upmf ON p.id = upmf.profile_id " +
            "WHERE p.is_deleted = false AND upmf.module_functionalities_key IN :modules " +
            "GROUP BY p.id " +
            "HAVING COUNT(DISTINCT CASE WHEN array_length(upmf.functionality, 1) > 0 THEN upmf.module_functionalities_key END) = :moduleCount",
            countQuery = "SELECT COUNT(DISTINCT p.id) FROM profile p " +
                    "JOIN user_profile_module_functionality upmf ON p.id = upmf.profile_id " +
                    "WHERE p.is_deleted = false AND upmf.module_functionalities_key IN :modules " +
                    "GROUP BY p.id " +
                    "HAVING COUNT(DISTINCT CASE WHEN array_length(upmf.functionality, 1) > 0 THEN upmf.module_functionalities_key END) = :moduleCount",
            nativeQuery = true)
    Page<Profile> findProfilesByModules(@Param("modules") List<String> modules, @Param("moduleCount") int moduleCount, Pageable pageable);

    @Query("SELECT p FROM Profile p WHERE lower(p.nameProfile) LIKE lower(concat('%', :nameProfile, '%')) AND p.isDeleted = false")
    Page<Profile> findProfilesByName(@Param("nameProfile") String nameProfile, Pageable pageable);


    @Query(value = "SELECT DISTINCT p.* FROM profile p " +
            "JOIN user_profile_module_functionality upmf ON p.id = upmf.profile_id " +
            "WHERE p.is_deleted = false AND upmf.module_functionalities_key IN :modules " +
            "AND LOWER(p.name_profile) LIKE LOWER(CONCAT('%', :nameProfile, '%')) " + // Perform case-insensitive search for nameProfile
            "GROUP BY p.id " +
            "HAVING COUNT(DISTINCT CASE WHEN array_length(upmf.functionality, 1) > 0 THEN upmf.module_functionalities_key END) = :moduleCount",
            countQuery = "SELECT COUNT(DISTINCT p.id) FROM profile p " +
                    "JOIN user_profile_module_functionality upmf ON p.id = upmf.profile_id " +
                    "WHERE p.is_deleted = false AND upmf.module_functionalities_key IN :modules " +
                    "AND LOWER(p.name_profile) LIKE LOWER(CONCAT('%', :nameProfile, '%')) " + // Perform case-insensitive search for nameProfile
                    "GROUP BY p.id " +
                    "HAVING COUNT(DISTINCT CASE WHEN array_length(upmf.functionality, 1) > 0 THEN upmf.module_functionalities_key END) = :moduleCount",
            nativeQuery = true)
    Page<Profile> findProfilesByModulesAndName(@Param("modules") List<String> modules, @Param("moduleCount") int moduleCount, @Param("nameProfile") String nameProfile, Pageable pageable);






}


