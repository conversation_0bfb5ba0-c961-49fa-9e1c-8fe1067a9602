package ma.almobadara.backend.model.beneficiary;

import jakarta.persistence.*;
import lombok.*;
import ma.almobadara.backend.model.takenInCharge.TakenInChargeBeneficiary;

import java.time.LocalDate;
import java.util.List;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BankCard extends BaseEntity{

    private String cardNumber;
    private String accountNumber;
    private LocalDate deliveryDate;
    private LocalDate expiryDate;
    private String status;
    @ManyToOne
    @JoinColumn(name = "person_id")
    private Person person;

    private Long cardTypeId;

    @ManyToMany
    @JoinTable(name = "taken_in_charge_beneficiary_bank_card",
            joinColumns = @JoinColumn(name = "bank_card_id"),
            inverseJoinColumns = @JoinColumn(name = "taken_in_charge_beneficiary_id"))
    private List<TakenInChargeBeneficiary> takenInChargeBeneficiaries;

}
