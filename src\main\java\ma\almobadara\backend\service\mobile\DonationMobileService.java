package ma.almobadara.backend.service.mobile;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.donor.ReleveDonorDto;
import ma.almobadara.backend.dto.mobile.ReleveDonorMobileDTO;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.service.donor.DonorService;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@RefreshScope
@Service
@RequiredArgsConstructor
@Slf4j
public class DonationMobileService {
    private final DonorService donorService;

    public List<ReleveDonorMobileDTO> getReleveForDonor(Long id) throws TechnicalException {
        List<ReleveDonorDto> releveDonorDTOs = donorService.releveCompte(id);

        return releveDonorDTOs.stream().map(dto -> {
            String direction = dto.getMontantEntree() != null ? "Entrée" : "Sortie";

            return ReleveDonorMobileDTO.builder()
                    .date(dto.getSortableDate())
                    .dateExecution(dto.getDateExecution())
                    .canal(dto.getCanalDonation() != null ? dto.getCanalDonation().getName() : null)
                    .amount(dto.getMontantEntree())
                    .executedAmount(dto.getMontantSortie())
                    .type(dto.getType())
                    .service(dto.getServices() != null ? dto.getServices().getName() : "Non Identifié")
                    .direction(direction)
                    .build();
        }).collect(Collectors.toList());
    }

}
