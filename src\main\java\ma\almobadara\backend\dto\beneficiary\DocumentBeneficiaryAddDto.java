package ma.almobadara.backend.dto.beneficiary;


import lombok.*;
import ma.almobadara.backend.dto.referentiel.TypeDocumentDonorDTO;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.multipart.MultipartFile;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static ma.almobadara.backend.util.strings.HandleSpecialChars.escapeSpecialChars;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@Builder
@ToString
public class DocumentBeneficiaryAddDto {

    private Long id;
    private String label;
    private Date documentDate;
    private Date expiryDate;
    private String comment;
    private MultipartFile file;
    private String file64;
    private String fileUrl;
    private String fileName;
    private TypeDocumentDonorDTO type;
    private String code;

    public Map<String, String> getAudit() {
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String date = dateFormat.format(this.expiryDate);

        Map<String, String> auditMap = new HashMap<>();
        auditMap.put("Date d'expiration", escapeSpecialChars(date));
        auditMap.put("Fichier", escapeSpecialChars(fileUrl));
        auditMap.put("Commentaire", escapeSpecialChars(comment));


        return auditMap;
    }

}
