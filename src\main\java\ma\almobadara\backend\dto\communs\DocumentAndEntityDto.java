package ma.almobadara.backend.dto.communs;

import jakarta.validation.constraints.NotNull;
import lombok.*;

import static ma.almobadara.backend.util.constants.GlobalConstants.*;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class DocumentAndEntityDto {

    @NotNull(message = DOCUMENT_NULL)
    private DocumentDTO documentDTO;

    @NotNull(message = ENTITY_ID_NOT_FOUND)
    private Long entityId;

    @NotNull(message = ENTITY_TYPE_NOT_FOUND)
    private String entityType;

}
