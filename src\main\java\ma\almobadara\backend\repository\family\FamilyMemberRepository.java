package ma.almobadara.backend.repository.family;

import ma.almobadara.backend.model.administration.Eps;
import ma.almobadara.backend.model.family.Family;
import ma.almobadara.backend.model.family.FamilyMember;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.Set;

@Repository
public interface FamilyMemberRepository extends JpaRepository<FamilyMember, Long> {
    @Query("SELECT f FROM FamilyMember f WHERE LOWER(f.person.firstName) LIKE LOWER(CONCAT('%', :query, '%')) OR LOWER(f.person.lastName) LIKE LOWER(CONCAT('%', :query, '%'))  OR LOWER(f.person.email) LIKE LOWER(CONCAT('%', :query, '%')) OR LOWER(f.person.phoneNumber) LIKE LOWER(CONCAT('%', :query, '%')) OR LOWER(f.code) LIKE LOWER(CONCAT('%', :query, '%'))")
    Page<FamilyMember> searchMembers(@Param("query") String query, Pageable pageable);
    List<FamilyMember> findAllByFamily(Family family);
    Optional<FamilyMember> findByPersonId(Long personId);
    List<FamilyMember> findByPersonIdIn(Set<Long> personIds);
    @Query("select fm from FamilyMember fm join fm.person p where p.identityCode = :firstIdentityCode")
    FamilyMember findByFirstIdentityCode(String firstIdentityCode);
    Long countByFamilyId(Long familyId);
}
