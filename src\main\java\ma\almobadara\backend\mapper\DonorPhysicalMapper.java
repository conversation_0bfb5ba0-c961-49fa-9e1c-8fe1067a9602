package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.beneficiary.BeneficiaryDTO;
import ma.almobadara.backend.dto.beneficiary.BeneficiaryHandicapDto;
import ma.almobadara.backend.dto.beneficiary.PersonDTO;
import ma.almobadara.backend.dto.communs.NoteDTO;
import ma.almobadara.backend.dto.donation.DonationDTO;
import ma.almobadara.backend.dto.donation.DonationProductNatureDTO;
import ma.almobadara.backend.dto.donor.DonorPhysicalAuditDTO;
import ma.almobadara.backend.dto.donor.DonorPhysicalDTO;
import ma.almobadara.backend.dto.mobile.DonorPhysiqueMobileDTO;
import ma.almobadara.backend.dto.referentiel.CanalCommunicationDTO;
import ma.almobadara.backend.dto.referentiel.LanguageCommunicationDTO;
import ma.almobadara.backend.dto.takenInCharge.*;
import ma.almobadara.backend.model.beneficiary.Beneficiary;
import ma.almobadara.backend.model.beneficiary.BeneficiaryHandicap;
import ma.almobadara.backend.model.beneficiary.Person;
import ma.almobadara.backend.model.donation.Donation;
import ma.almobadara.backend.model.donation.DonationProductNature;
import ma.almobadara.backend.model.donor.DonorPhysical;
import ma.almobadara.backend.model.donor.DonorPhysicalCanalCommunication;
import ma.almobadara.backend.model.donor.DonorPhysicalLanguageCommunication;
import ma.almobadara.backend.model.donor.NoteDonor;
import ma.almobadara.backend.model.takenInCharge.*;
import org.mapstruct.IterableMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.List;

@Mapper(componentModel = "spring")
public interface DonorPhysicalMapper {

	@Mapping(expression = "java( donationModelToDto(donor.getDonations()) )", target = "donations")
	@Mapping(source = "typeIdentity", target = "typeIdentity.id")
	@Mapping(source ="donorStatusId", target = "status.id")
	@Mapping(source ="cityId", target = "city.id")
	@Mapping(source = "professionId", target = "profession.id")
	@Mapping(source = "donorPhysicalCanalCommunication", target = "canalCommunications")
	@Mapping(source = "donorPhysicalLanguageCommunications", target = "languageCommunications")
	@Mapping(source = "documentsDonors", target = "documentDonors")
	DonorPhysicalDTO donorPhysicalModelToDto(DonorPhysical donor);

	@Mapping(source = "professionId", target = "profession.id")
	@Mapping(target = "typeIdentity" ,ignore = true)
	@Mapping(target = "registrationDate" ,ignore = true)
	DonorPhysiqueMobileDTO donorPhysicalModelToMobileDto(DonorPhysical donor);

	@IterableMapping(qualifiedByName = "mapWithoutNesting")
	Iterable<DonorPhysicalDTO> donorPhysicalListModelToDto(Iterable<DonorPhysical> donors);

	@Named("mapWithoutNesting")
	@Mapping(expression = "java( donationModelToDto(donor.getDonations()) )", target = "donations")
	@Mapping(target="takenInChargeDonors", ignore = true)
	@Mapping(target = "documentDonors", ignore = true)
	@Mapping(target = "notes", ignore = true)
	@Mapping(source = "typeIdentity", target = "typeIdentity.id")
	@Mapping(target = "canalCommunications", ignore = true)
	@Mapping(source = "professionId", target = "profession.id")
	@Mapping(source = "status", target = "status")
	@Mapping(source = "city", target = "city")
	DonorPhysicalDTO donorPhysicalModelToDtoForList(DonorPhysical donor);

	@Mapping(target = "beneficiary",ignore = true)
	BeneficiaryHandicapDto beneficiaryHandicapToBeneficiaryHandicapDto(BeneficiaryHandicap beneficiaryHandicap);

	@Mapping(target="takenInChargeDonors", ignore = true)
	@Mapping(target="documentDonors", ignore = true)
	@Mapping(target="notes", ignore = true)
	@Mapping(target = "typeIdentity", ignore=true)
	@Mapping(target = "canalCommunications",ignore = true)
//	@Mapping(target = "languageCommunications",ignore = true)
	@Mapping(source ="professionId", target = "profession.id")
	@Mapping(source ="donorStatusId", target = "status.id")
	@Mapping(source ="cityId", target = "city.id")
	@Mapping(source ="typeIdentity", target = "typeIdentity.id")
	DonorPhysicalDTO donorPhysicalModelToOneDTO(DonorPhysical donor);

	@Named("mapDonationForList")
	@Mapping(target = "donor", ignore = true)
	@Mapping(source = "canalDonationId", target = "canalDonation.id")
	DonationDTO donationModelToDto(Donation donation);

	List<DonationDTO> donationModelToDto(List<Donation> donation);

	@Mapping(source = "canalCommunicationId", target = "id")
	CanalCommunicationDTO donorPhysicalCanalCommunicationToCanalCommunicationDTO(DonorPhysicalCanalCommunication donorPhysicalCanalCommunication);

	Iterable<CanalCommunicationDTO> donorPhysicalCanalCommunicationToCanalCommunicationDTO(Iterable<DonorPhysicalCanalCommunication> donorPhysicalCanalCommunication);

	@Mapping(target = "id", ignore = true)
	@Mapping(source = "id", target = "canalCommunicationId")
	DonorPhysicalCanalCommunication canalCommunicationDTOToDonorPhysicalCanalCommunication(CanalCommunicationDTO canalCommunicationDTO);

	Iterable<DonorPhysicalCanalCommunication> canalCommunicationDTOToDonorPhysicalCanalCommunication(Iterable<CanalCommunicationDTO> canalCommunicationDTOS);

	@Mapping(source = "languageCommunicationId", target = "id")
	LanguageCommunicationDTO donorPhysicalCanalCommunicationToCanalCommunicationDTO(DonorPhysicalLanguageCommunication donorPhysicalLanguageCommunication);

	Iterable<LanguageCommunicationDTO> donorPhysicalLanguageCommunicationToCanalCommunicationDTO(Iterable<DonorPhysicalLanguageCommunication> donorPhysicalLanguageCommunications);

	@Mapping(target = "id", ignore = true)
	@Mapping(source = "id", target = "languageCommunicationId")
	DonorPhysicalLanguageCommunication languageCommunicationDTOToDonorPhysicalLanguageCommunication(LanguageCommunicationDTO languageCommunicationDTO);

	Iterable<DonorPhysicalLanguageCommunication> languageCommunicationDTOToDonorPhysicalLanguageCommunication(Iterable<LanguageCommunicationDTO> languageCommunicationDTOS);

	@Mapping(source = "typeIdentity.id", target = "typeIdentity")
	@Mapping(source = "status.id", target = "donorStatusId")
	@Mapping(source = "city.id", target = "cityId")
	@Mapping(source = "profession.id", target = "professionId")
	@Mapping(source = "canalCommunications", target = "donorPhysicalCanalCommunication")
	@Mapping(source = "languageCommunications", target = "donorPhysicalLanguageCommunications")
	DonorPhysical donorPhysicalDtoToModel(DonorPhysicalDTO donor);

	Iterable<DonorPhysical> donorPhysicalListDtoToModal(Iterable<DonorPhysicalDTO> donors);

	NoteDTO noteModelToDto(NoteDonor donor);

	@Mapping(target = "donor", ignore = true)
	@Mapping(source = "canalDonationId", target = "canalDonation.id")
	DonationDTO donationToDonationDTO(Donation donation);

	@Mapping(target = "donation", ignore = true)
	@Mapping(source = "productNatureId", target = "productNature.id")
	@Mapping(source = "productUnitId", target = "productUnit.id")
	DonationProductNatureDTO donationProductNatureToDonationProductNatureDTO(DonationProductNature donationProductNature);

	//****************************************************************************************

	@Mapping(target = "donor", ignore = true)
	TakenInChargeDonorDTO takenInChargeDonorDTOToTakenInChargeDonor(TakenInChargeDonor takenInChargeDonor);

	@Mapping(target = "donor", ignore = true)
	TakenInChargeDonor takenInChargeDonorToTakenInChargeDonorDTO(TakenInChargeDonorDTO takenInChargeDonorDTO);

	@Mapping(target = "takenInChargeDonor", ignore = true)
	TakenInChargeOperationDTO takenInChargeOperationToTakenInChargeOperationDTO(TakenInChargeOperation takenInChargeOperation);

	@Mapping(target = "takenInChargeDonor", ignore = true)
	TakenInChargeOperation takenInChargeOperationDTOToTakenInChargeOperation(TakenInChargeOperationDTO takenInChargeOperationDTO);

	@Mapping(target = "takenInChargeDonors", ignore = true)
	TakenInChargeDTO takenInChargeDTOToTakenInCharge(TakenInCharge takenInCharge);

	@Mapping(target = "takenInChargeDonors", ignore = true)
	TakenInCharge takenInChargeToTakenInChargeDTO(TakenInChargeDTO takenInChargeDTO);

	NoteTakenInChargeDTO takenInChargeNoteToTakenInChargeNoteDTO(NoteTakenInCharge takenInChargeNote);
	@Mapping(target = "takenInCharge", ignore = true)
    NoteTakenInCharge takenInChargeNoteDTOToTakenInChargeNote(NoteTakenInChargeDTO takenInChargeNoteDTO);

	DocumentTakenInChargeDTO takenInChargeDocumentToTakenInChargeDocumentDTO(DocumentTakenInCharge takenInChargeDocument);

	@Mapping(target = "takenInCharge", ignore = true)
	DocumentTakenInCharge takenInChargeDocumentDTOToTakenInChargeDocument(DocumentTakenInChargeDTO takenInChargeDocumentDTO);

	@Mapping(target = "takenInCharge", ignore = true)
	@Mapping(target = "bankCards", ignore = true)
	TakenInChargeBeneficiaryDTO takenInChargeBeneficiaryDTOToTakenInChargeBeneficiary(TakenInChargeBeneficiary takenInChargeBeneficiary);

	@Mapping(target = "takenInCharge", ignore = true)
	@Mapping(target = "bankCards", ignore = true)
	TakenInChargeBeneficiary takenInChargeBeneficiaryToTakenInChargeBeneficiaryDTO(TakenInChargeBeneficiaryDTO takenInChargeBeneficiaryDTO);

	@Mapping(target = "takenInChargeBeneficiaries", ignore = true)
	@Mapping(target = "notes", ignore = true)
	@Mapping(target = "beneficiaryServices", ignore = true)
	@Mapping(target = "scholarshipBeneficiaries", ignore = true)
	@Mapping(target = "epsResidents", ignore = true)
	@Mapping(target = "diseaseTreatments", ignore = true)
	@Mapping(target = "educations", ignore = true)
	@Mapping(target = "documents", ignore = true)
	BeneficiaryDTO beneficiaryToBeneficiaryDTO(Beneficiary beneficiary);

	@Mapping(target = "beneficiary", ignore = true)
	@Mapping(target = "bankCards", ignore = true)
	@Mapping(target = "familyMember", ignore = true)
	PersonDTO personToPersonDTO(Person person);



	@Mapping(source = "firstName",target = "prenom")
	@Mapping(source = "lastName",target = "nom")
	@Mapping(source = "firstNameAr",target = "prenomArabe")
	@Mapping(source = "lastNameAr",target = "nomArabe")
	@Mapping(source = "sex",target = "sexe")
	@Mapping(source = "phoneNumber",target = "telephone")
	@Mapping(source = "identityCode",target = "numIdentite")
	@Mapping(source = "address",target = "adresse")
	@Mapping(source = "addressAr",target = "adresseArabe")
	@Mapping(source = "firstDonationYear", target = "AnneePremiereDonation")
	@Mapping(target = "canalCommunications", ignore = true)
	@Mapping(target = "languageCommunications", ignore = true)
	@Mapping(target = "TypeIdentite", ignore = true)
	@Mapping(target = "profession", ignore = true)
	DonorPhysicalAuditDTO donorPhysicaldtoToDonorPhysicalAuditDto(DonorPhysicalDTO donorPhysicalDTO);


	@Mapping(source = "typeIdentity",target = "typeIdentity.id")
	@Mapping(target = "status.id", source = "donorStatusId")
	@Mapping(target = "city.id", source = "cityId")
	@Mapping(target = "profession.id", source = "professionId")
	@Mapping(target = "canalCommunications", ignore = true)
	@Mapping(target = "languageCommunications", ignore = true)
	DonorPhysicalDTO donorPhysicalModelToDtoForAudit(DonorPhysical donor);



}
