package ma.almobadara.backend.dto.mobile;

import lombok.*;
import ma.almobadara.backend.dto.beneficiary.SmallZoneDTO;

import java.time.LocalDate;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class AssistantMobileDTO {
    private Long id;
    private String code;
    private String firstName;
    private String lastName;
    private String email;
    private String phone;
    private String pictureUrl;
    private String pictureBase64;
    private SmallZoneDTO zone;
    private LocalDate dateAffectationToZone;
    private int beneficiaryCount;  // Number of beneficiaries in the assistant's zone
    private int familyCount;       // Number of families in the assistant's zone
    private String device_token;
}
