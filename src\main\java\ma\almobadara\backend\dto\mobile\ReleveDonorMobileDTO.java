package ma.almobadara.backend.dto.mobile;

import lombok.*;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;
import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@SuperBuilder
@ToString
@AllArgsConstructor
public class ReleveDonorMobileDTO {
    private LocalDateTime date;
    private Double amount;
    private Double executedAmount;
    private LocalDateTime dateExecution;
    private String direction;
    private String type;
    private String canal;
    private String service;
}
