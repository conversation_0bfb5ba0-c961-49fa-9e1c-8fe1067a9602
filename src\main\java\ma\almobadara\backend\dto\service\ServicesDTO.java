package ma.almobadara.backend.dto.service;


import lombok.*;
import ma.almobadara.backend.dto.administration.ConsultServiceCollectEpsDTO;
import ma.almobadara.backend.dto.administration.EpsLightDTO;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@Builder
public class ServicesDTO {
    private Long id;
    private String name;
    private String code;
    private BigDecimal costs;
    private Boolean priority;
    private Boolean propositionSystem;
    private Long amountPerBeneficiary;
    private Boolean statutIsActif;
    private Long serviceCategoryId;
    private Long serviceCategoryTypeId;
    private Long serviceCollectEpsId;
    private String category;
    private String typeCategory;
    private String commentaire;
    private Boolean isDedicatedToEps;
    private String month;
    private String year;
    private String collectionType;
    private String distributionType;
    private EpsLightDTO eps;
    private Long epsId;
    private List<ConsultServiceCollectEpsDTO> consultServiceCollectEpsDTOS;
}
