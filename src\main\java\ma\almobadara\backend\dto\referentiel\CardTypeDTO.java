package ma.almobadara.backend.dto.referentiel;

import lombok.*;
import org.springframework.hateoas.RepresentationModel;

import java.io.Serializable;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class CardTypeDTO extends RepresentationModel<CardTypeDTO> implements Serializable {

	private Long id;
	private String code;
	private String name;
	private String nameAr;
	private String nameEn;

}
