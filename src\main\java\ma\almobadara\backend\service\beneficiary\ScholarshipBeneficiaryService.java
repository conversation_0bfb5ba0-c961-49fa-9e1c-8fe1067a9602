package ma.almobadara.backend.service.beneficiary;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.Audit.AuditApplicationService;
import ma.almobadara.backend.clients.RefFeignClient;
import ma.almobadara.backend.dto.beneficiary.ScholarshipBeneficiaryDTO;
import ma.almobadara.backend.dto.referentiel.CurrencyDTO;
import ma.almobadara.backend.dto.referentiel.ScholarshipDTO;
import ma.almobadara.backend.mapper.ScholarshipBeneficiaryMapper;
import ma.almobadara.backend.model.beneficiary.Beneficiary;
import ma.almobadara.backend.model.beneficiary.ScholarshipBeneficiary;
import ma.almobadara.backend.repository.beneficiary.BeneficiaryRepository;
import ma.almobadara.backend.repository.beneficiary.ScholarshipBeneficiaryRepository;
import ma.almobadara.backend.util.times.TimeWatch;
import org.springframework.data.rest.webmvc.ResourceNotFoundException;
import org.springframework.stereotype.Service;

import java.util.*;

import static ma.almobadara.backend.service.administration.CacheAdUserService.getUsernameFromJwt;
import static ma.almobadara.backend.util.constants.GlobalConstants.*;

@RequiredArgsConstructor
@Slf4j
@Service
public class ScholarshipBeneficiaryService {

    private final ScholarshipBeneficiaryMapper scholarshipBeneficiaryMapper;
    private final ScholarshipBeneficiaryRepository scholarshipBeneficiaryRepository;
    private final BeneficiaryRepository beneficiaryRepository;
    private final RefFeignClient refFeignClient;
    private final AuditApplicationService auditApplicationService;


    public ScholarshipBeneficiary addScholarshipToBeneficiary(Beneficiary beneficiary, ScholarshipBeneficiaryDTO scholarshipBeneficiaryDTO) {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service create or update scholarship by ID : {}", scholarshipBeneficiaryDTO.getId());

        ScholarshipBeneficiary scholarshipBeneficiary = scholarshipBeneficiaryMapper.scholarshipBeneficiaryDTOToScholarshipBeneficiary(scholarshipBeneficiaryDTO);
        String oldAudit=null;

        if (scholarshipBeneficiaryDTO.getId() != null) {
            Optional<ScholarshipBeneficiary> existingScholarshipOpt = scholarshipBeneficiaryRepository.findById(scholarshipBeneficiaryDTO.getId());
            if (existingScholarshipOpt.isPresent()) {
                ScholarshipBeneficiary existingScholarship = existingScholarshipOpt.get();
                scholarshipBeneficiary.setId(existingScholarship.getId());
                Map<String,String> oldparams=prepareScholarShipBeneficiary(existingScholarshipOpt.get());
                oldAudit=existingScholarship.getAudit(oldparams,beneficiary.getPerson().getFirstName()+" - "+beneficiary.getPerson().getLastName());
            }
        }

        scholarshipBeneficiary.setBeneficiary(beneficiary);
        scholarshipBeneficiaryRepository.save(scholarshipBeneficiary);
        Map<String,String> newparams=prepareScholarShipBeneficiary(scholarshipBeneficiary);
        String newAudit=scholarshipBeneficiary.getAudit(newparams,beneficiary.getPerson().getFirstName()+" - "+beneficiary.getPerson().getLastName());
        if(scholarshipBeneficiaryDTO.getId()!=null){
            auditApplicationService.audit("Modification d'un Bource pour  Beneficiare : " + beneficiary.getCode(), getUsernameFromJwt(), "Add Family Member",
                    oldAudit,newAudit, BENEFICIAIRE, UPDATE);
        }else{
            auditApplicationService.audit("Ajout d'un Bource pour  Beneficiare : " + beneficiary.getCode(), getUsernameFromJwt(), "Add Family Member",
                    null,newAudit, BENEFICIAIRE, CREATE);
        }

        log.debug("End service create or update scholarship by ID : {}, took {}", scholarshipBeneficiaryDTO.getId(), watch.toMS());
        return scholarshipBeneficiary;
    }

    private Map<String,String> prepareScholarShipBeneficiary(ScholarshipBeneficiary scholarshipBeneficiary){
        Map<String,String> params = new HashMap<>();

        if(scholarshipBeneficiary.getScholarshipId() != null){
            ScholarshipDTO scholarshipDTO=refFeignClient.getMetScholarship(scholarshipBeneficiary.getScholarshipId());
            params.put("bource",scholarshipDTO.getName());
        }
        else{
            params.put("bource",null);
        }

        if(scholarshipBeneficiary.getCurrencyId() != null){
            CurrencyDTO currencyDTO=refFeignClient.getParCurrency(scholarshipBeneficiary.getCurrencyId());
            System.out.println(currencyDTO);
            params.put("currency",currencyDTO.getName());
        }
        else{
            params.put("currency",null);
        }
        return params;
    }


    public List<ScholarshipBeneficiaryDTO> getScholarshipByBeneficiaryId(Long beneficiaryId) {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service get scholarship by ID : {}",beneficiaryId );
        Beneficiary beneficiary = beneficiaryRepository.findById(beneficiaryId)
                .orElseThrow(() -> new ResourceNotFoundException("Beneficiary not found with id: " + beneficiaryId));

        List<ScholarshipBeneficiary> scholarshipBeneficiaries = scholarshipBeneficiaryRepository.findByBeneficiary(beneficiary);
        List<ScholarshipBeneficiaryDTO> scholarshipBeneficiaryDTOS = new ArrayList<>();

        for (ScholarshipBeneficiary scholarshipBeneficiary : scholarshipBeneficiaries) {

            ScholarshipDTO scholarshipDTO = refFeignClient.getMetScholarship(scholarshipBeneficiary.getScholarshipId());
            CurrencyDTO currencyDTO = new CurrencyDTO();
            if(scholarshipBeneficiary.getCurrencyId() != null){
                 currencyDTO = refFeignClient.getParCurrency(scholarshipBeneficiary.getCurrencyId());
            }

            ScholarshipBeneficiaryDTO scholarshipBeneficiaryDTO = scholarshipBeneficiaryMapper.scholarshipBeneficiaryToScholarshipBeneficiaryDTO(scholarshipBeneficiary);
            scholarshipBeneficiaryDTO.setScholarship(scholarshipDTO);
            if (currencyDTO != null) {
                scholarshipBeneficiaryDTO.setCurrency(currencyDTO);
            }
            scholarshipBeneficiaryDTOS.add(scholarshipBeneficiaryDTO);
        }
        log.debug("End service get scholarship, took {}", watch.toMS());
        return scholarshipBeneficiaryDTOS;
    }

    public void deleteScholarship(Long id) {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service delete scholarship by ID : {}", id);

        // Retrieve the existing scholarship record to be deleted
        ScholarshipBeneficiary scholarshipBeneficiary = scholarshipBeneficiaryRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Scholarship not found with ID: " + id));
        Optional<Beneficiary> beneficiary =beneficiaryRepository.findById(scholarshipBeneficiary.getBeneficiary().getId());
        Map<String,String> newparams=prepareScholarShipBeneficiary(scholarshipBeneficiary);
        String newAudit=scholarshipBeneficiary.getAudit(newparams,beneficiary.get().getPerson().getFirstName()+" - "+beneficiary.get().getPerson().getLastName());
        // Proceed with deletion
        auditApplicationService.audit("Suppression d'un Bource pour  Beneficiare : " + beneficiary.get().getCode(), getUsernameFromJwt(), "Add Family Member",
                newAudit,null, BENEFICIAIRE, DELETE);
        scholarshipBeneficiaryRepository.deleteById(id);
        log.debug("End service delete scholarship by ID : {}, took {}", id, watch.toMS());
    }

}
