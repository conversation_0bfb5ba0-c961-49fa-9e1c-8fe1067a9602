package ma.almobadara.backend.clients.config;

import feign.RequestInterceptor;
import feign.auth.BasicAuthRequestInterceptor;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@RequiredArgsConstructor
@Configuration
@AllArgsConstructor
public class RefFeignClientConfiguration {

    @Value("${ref.username}")
    private  String username;
    @Value("${ref.password}")
    private  String password;
    @Bean
    public RequestInterceptor basicAuthRequestInterceptor() {
        return new BasicAuthRequestInterceptor(
                username,  password
        );
    }

}
