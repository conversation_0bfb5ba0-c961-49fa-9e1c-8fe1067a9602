package ma.almobadara.backend.controller.donor;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.persistence.EntityNotFoundException;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.config.HasAccessToModule;
import ma.almobadara.backend.dto.aideComplemenatire.AideComplementaireBeneficiareDto;
import ma.almobadara.backend.dto.aideComplemenatire.AideComplementaireDonateurDTO;
import ma.almobadara.backend.dto.donor.*;
import ma.almobadara.backend.dto.exportentities.ExportFileDTO;
import ma.almobadara.backend.dto.referentiel.CanalDonationDTO;
import ma.almobadara.backend.dto.service.ServicesDTO;
import ma.almobadara.backend.enumeration.Functionality;
import ma.almobadara.backend.enumeration.Module;
import ma.almobadara.backend.service.donor.DonorService;
import ma.almobadara.backend.util.page.PageDto;
import ma.almobadara.backend.util.page.SortDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static ma.almobadara.backend.service.administration.CacheAdUserService.logUserInfo;

@RestController
@Slf4j
@RequestMapping("/donors")
public class DonorController {

    private final DonorService donorService;

    @Autowired
    public DonorController(DonorService donorService) {
        this.donorService = donorService;
    }
    @PostMapping(value = "/physical/create", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    @HasAccessToModule(modules = {Module.DONOR}, functionalities = {Functionality.CREATE})
    @Operation(summary = "Create a donor", description = "Add a new donor", tags = {"Donor"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = DonorDTO.class))))
    })
    public ResponseEntity<DonorDTO> createDonorPhysical(@Valid @ModelAttribute DonorPhysicalDTO physicalDonorDto) {

        logUserInfo("createDonorPhysical", physicalDonorDto.getFirstName(), physicalDonorDto.getLastName());

        DonorDTO donorPhysiqueCreated = new DonorDTO();
        HttpStatus status;
        try {
            if(physicalDonorDto.getAnonymeId()!=null){
                donorPhysiqueCreated=  donorService.convertAnonymeToPhysique(physicalDonorDto.getAnonymeId(), physicalDonorDto);
            }
            else{
                donorPhysiqueCreated = donorService.addDonorPhysique(physicalDonorDto);

            }
            status = HttpStatus.OK;
            log.info("End createDonorPhysical. Created donor with id: {}, OK", donorPhysiqueCreated.getId());
        } catch (Exception e) {
            status = HttpStatus.INTERNAL_SERVER_ERROR;
            log.error("End createDonorPhysical. KO: {}", e.getMessage());
        }

        return new ResponseEntity<>(donorPhysiqueCreated, new HttpHeaders(), status);
    }


    @GetMapping("/loadAideComplementaireForDonor/{id}")
    public ResponseEntity<List<AideComplementaireDonateurDTO>> getdonorsAideComplementaire(
            @PathVariable Long id
    ) {

        try {
            List<AideComplementaireDonateurDTO> beneficiaryDTOS = donorService.getAideComplementaireForDonor(id);

            return ResponseEntity.ok(beneficiaryDTOS);
        } catch (Exception e) {

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    @PostMapping(value = "/anonymous/create", consumes = {MediaType.APPLICATION_JSON_VALUE})
    @HasAccessToModule(modules = {Module.DONOR}, functionalities = {Functionality.CREATE})
    @Operation(summary = "Create an anonymous donor", description = "Add a new anonymous donor", tags = {"Donor"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successful operation", content = @Content(schema = @Schema(implementation = DonorAnonymeDTO.class))),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<DonorDTO> createDonorAnonyme(@Valid @RequestBody DonorAnonymeDTO donorAnonymeDTO) {
        DonorDTO createdDonorAnonyme;
        HttpStatus status;

        try {
            createdDonorAnonyme = donorService.addDonorAnonyme(donorAnonymeDTO);
            status = HttpStatus.OK;
            log.info("End createDonorAnonyme. Created donor with id: {}, OK", createdDonorAnonyme.getId());
        } catch (Exception e) {
            status = HttpStatus.INTERNAL_SERVER_ERROR;
            log.error("End createDonorAnonyme. KO: {}", e.getMessage());
            return new ResponseEntity<>(null, new HttpHeaders(), status);
        }

        return new ResponseEntity<>(createdDonorAnonyme, new HttpHeaders(), status);
    }


    // getByIddonor anonyme

    @GetMapping(value = "/anonyme/{idDonor}", produces = {"application/json"})
    @Operation(summary = "Find donor anonyme by ID", description = "Returns a single donor anonyme", tags = {"Donor"})
    @HasAccessToModule(modules = {Module.DONOR}, functionalities = {Functionality.VIEW})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(schema = @Schema(implementation = DonorAnonymeDTO.class))),
            @ApiResponse(responseCode = "404", description = "Donor anonyme not found")})
    public ResponseEntity<DonorAnonymeDTO> getDonorAnonymeByID(@PathVariable Long idDonor) {

            logUserInfo("getDonorAnonymeByID", String.valueOf(idDonor));

            DonorAnonymeDTO donorAnonymeDTO = null;
            HttpStatus status;
            try {
                donorAnonymeDTO = donorService.getDonorAnonymeById(idDonor);
                status = HttpStatus.OK;
                log.info("End resource getDonorAnonymeByID : {}. Retrieved donor anonyme: {}, OK", idDonor, donorAnonymeDTO);
            } catch (Exception e) {
                status = HttpStatus.NOT_FOUND;
                log.error("End resource getDonorAnonymeByID : {}. KO: {}", idDonor, e.getMessage());
            }

            return new ResponseEntity<>(donorAnonymeDTO, status);
    }

    @Operation(summary = "Find donor by ID", description = "Returns a single donor", tags = {"Donor"})
    @HasAccessToModule(modules = {Module.DONOR}, functionalities = {Functionality.VIEW})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(schema = @Schema(implementation = DonorDTO.class))),
            @ApiResponse(responseCode = "404", description = "Donor not found")})
    @GetMapping(value = "/{idDonor}", produces = {"application/json"})
    public ResponseEntity<DonorDTO> getDonorByID(@PathVariable Long idDonor) {

        logUserInfo("getDonorByID", String.valueOf(idDonor));

        DonorDTO donorDTO = null;
        HttpStatus status;
        try {
            donorDTO = donorService.getDonorById(idDonor);
            status = HttpStatus.OK;
            log.info("End resource getDonorByID : {}. Retrieved donor: {}, OK", idDonor, donorDTO);
        } catch (Exception e) {
            status = HttpStatus.NOT_FOUND;
            log.error("End resource getDonorByID : {}. KO: {}", idDonor, e.getMessage());
        }

        return new ResponseEntity<>(donorDTO, status);
    }

//    @DeleteMapping(value = "delete/{idDonor}", headers = "Accept=application/json")
//    @HasAccessToModule(modules = {Module.DONOR}, functionalities = {Functionality.DELETE})
//    @Operation(summary = "Delete donor by ID", description = "delete donor by ID", tags = {"Donor"})
//    @ApiResponses(value = {
//            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = DonorDTO.class))))})
//    public ResponseEntity<Void> deleteDonor(@PathVariable Long idDonor) {
//
//        logUserInfo("deleteDonor", String.valueOf(idDonor));
//
//        HttpStatus status;
//        try {
//            donorService.deleteDonor(idDonor);
//            status = HttpStatus.OK;
//            log.info("End resource: deleteDonorById({}), OK", idDonor);
//        } catch (Exception e) {
//            status = HttpStatus.INTERNAL_SERVER_ERROR;
//            log.error("End resource: deleteDonorById({}). Error: {}", idDonor, e.getMessage());
//        }
//
//        return new ResponseEntity<>(status);
//    }

    @DeleteMapping(value = "delete/{idDonor}", headers = "Accept=application/json")
    @HasAccessToModule(modules = {Module.DONOR}, functionalities = {Functionality.DELETE})
    @Operation(summary = "Delete donor by ID", description = "delete donor by ID", tags = {"Donor"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = DonorDTO.class))))})
    public ResponseEntity<String> deleteDonor(@PathVariable Long idDonor) {

        logUserInfo("deleteDonor", String.valueOf(idDonor));

        try {
            donorService.deleteDonor(idDonor);
            return ResponseEntity.ok("Donor has been successfully deleted.");
        } catch (IllegalStateException e) {
            return ResponseEntity.status(HttpStatus.OK).body(e.getMessage());
        } catch (IllegalArgumentException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(e.getMessage());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("An error occurred.");
        }
    }


    @PostMapping(value = "/moral/create", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    @HasAccessToModule(modules = {Module.DONOR}, functionalities = {Functionality.CREATE})
    @Operation(summary = "Create a donor", description = "add a new donor", tags = {"Donor"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = DonorDTO.class))))})
    public ResponseEntity<DonorDTO> createDonorMoral(@ModelAttribute DonorMoralDTO donorMoralDTO) {

        logUserInfo("createDonorMoral", donorMoralDTO.getCompany());

        DonorDTO donorMoralCreated = new DonorDTO();
        HttpStatus status;
        try {
            if(donorMoralDTO.getAnonymeId()!=null){
                donorMoralCreated =donorService.convertAnonymeToMoral(donorMoralDTO.getAnonymeId(), donorMoralDTO);
            }
            else{
                donorMoralCreated = donorService.addDonorMoral(donorMoralDTO);
            }
            status = HttpStatus.OK;
            log.info("End resource: createDonorMoral. Created donor with id: {}, OK", donorMoralCreated.getId());
        } catch (Exception e) {
            status = HttpStatus.INTERNAL_SERVER_ERROR;
            log.error("End resource: createDonorMoral. Error: {}", e.getMessage());
        }

        return new ResponseEntity<>(donorMoralCreated, new HttpHeaders(), status);
    }

    @GetMapping(value = "/findByCriteria", produces = "application/json")
    public ResponseEntity<Page<DonorDTO>> findDonorsByCriteria(
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String typeDonor,
            @RequestParam(required = false) String firstName,
            @RequestParam(required = false) String lastName,
            @RequestParam(required = false) String companyName,
            @RequestParam(required = false) final String anonymeDonor,
            @RequestParam(required = false) final String searchByDonorType,
            @RequestParam(required = false) final String searchByNom,
            @RequestParam(required = false) final String lastNameAr,
            @RequestParam(required = false) final String searchByPrenom,
            @RequestParam(required = false) final String searchByPhoneNum,
            @RequestParam(required = false) final String searchByEmail,
            @RequestParam(required = false) final Integer searchByStatus,
            @RequestParam(required = false) final Long searchByTagId

    ) {

        logUserInfo("findDonorsByCriteria", typeDonor, firstName, lastName, companyName);

        Page<DonorDTO> donorsByCriteria = null;
        HttpStatus status;
        try {
            donorsByCriteria = donorService.getDonorsByCriteria(page, size, typeDonor, firstName, lastName, companyName ,anonymeDonor, searchByDonorType, searchByNom, lastNameAr, searchByPrenom, searchByPhoneNum, searchByEmail, searchByStatus, searchByTagId);
            status = HttpStatus.OK;
            log.info("End resource : findDonorsByCriteria. Retrieved {} donors , OK ", donorsByCriteria.getContent().size());
        } catch (Exception e) {
            status = HttpStatus.INTERNAL_SERVER_ERROR;
            log.error("End resource : findDonorsByCriteria. Ko: {}", e.getMessage());
        }

        return new ResponseEntity<>(donorsByCriteria, status);
    }
    @GetMapping(value = "/csv", produces = {"application/json"})
    public ResponseEntity<ExportFileDTO> exportDonorsToCSV(
            @RequestParam(required = false) final String searchByDonorType,
            @RequestParam(required = false) final String searchByNom,
            @RequestParam(required = false) final String lastNameAr,
            @RequestParam(required = false) final String searchByPrenom,
            @RequestParam(required = false) final String searchByPhoneNum,
            @RequestParam(required = false) final String searchByEmail,
            @RequestParam(required = false) final Integer searchByStatus,
            @RequestParam(required = false) final Long searchByTagId
    ) {

        logUserInfo("exportDonorsToCSV for module Donor");

        ExportFileDTO exportFileDTO = null;
        HttpStatus status;
        try {
            exportFileDTO = donorService.exportFileWithName(searchByDonorType, searchByNom, lastNameAr , searchByPrenom, searchByPhoneNum, searchByEmail, searchByStatus, searchByTagId);
            status = HttpStatus.OK;
            log.info("End resource : exportDonorsToCSV. OK");
        } catch (Exception e) {
            status = HttpStatus.INTERNAL_SERVER_ERROR;
            log.error("End resource : exportDonorsToCSV. KO: {}", e.getMessage());
        }
        return new ResponseEntity<>(exportFileDTO, status);
    }




    @GetMapping("/{donorId}/releve-compte")
    public ResponseEntity<List<ReleveDonorDto>> getReleveCompte(
            @PathVariable Long donorId) {
        try {
            List<ReleveDonorDto> releveCompte = donorService.releveCompte(donorId);

            return new ResponseEntity<>(releveCompte, HttpStatus.OK);
        } catch (EntityNotFoundException e) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        } catch (Exception e) {
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/{donorId}/journal-operations")
    public ResponseEntity<List<JournalOperationDto>> getJournalOperation(
            @PathVariable Long donorId) {
        try {
            List<JournalOperationDto> releveCompte = donorService.getJournalOperation(donorId);
            return new ResponseEntity<>(releveCompte, HttpStatus.OK);
        } catch (EntityNotFoundException e) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        } catch (Exception e) {
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/{donorId}/journal-operations-reserve")
    public ResponseEntity<List<JournalOperationReserveDto>> getJournalOperationReserve(
            @PathVariable Long donorId) {
        try {
            List<JournalOperationReserveDto> releveCompte = donorService.getJournalOperationReserve(donorId);
            return new ResponseEntity<>(releveCompte, HttpStatus.OK);
        } catch (EntityNotFoundException e) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        } catch (Exception e) {
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/{donorId}/solde")
    public ResponseEntity<List<SoldeDto>> getSolde(
            @PathVariable Long donorId) {
        try {
            List<SoldeDto> releveCompte = donorService.getSolde(donorId);
            return new ResponseEntity<>(releveCompte, HttpStatus.OK);
        } catch (EntityNotFoundException e) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        } catch (Exception e) {
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

}
