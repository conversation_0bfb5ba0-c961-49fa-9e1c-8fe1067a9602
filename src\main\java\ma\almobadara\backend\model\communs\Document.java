package ma.almobadara.backend.model.communs;

import jakarta.persistence.*;
import lombok.*;
import ma.almobadara.backend.model.administration.Tag;
import ma.almobadara.backend.model.beneficiary.BeneficiaryDisease;
import ma.almobadara.backend.util.strings.HandleSpecialChars;
import org.hibernate.annotations.DynamicUpdate;
import org.springframework.format.annotation.DateTimeFormat;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import static ma.almobadara.backend.util.strings.HandleSpecialChars.escapeSpecialChars;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Inheritance(strategy = InheritanceType.JOINED)
@DynamicUpdate
@Builder
public class Document {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String label;
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date documentDate;
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date expiryDate;
    private String comment;
    private String fileUrl;
    private String fileName;
    private Long typeDocumentId;
    private String code;

    public String toAuditString(String fileTypeName) {
        SimpleDateFormat formatter = new SimpleDateFormat("dd-MM-yyyy");
        String date = escapeSpecialChars(formatter.format(documentDate));
        String date2 = (expiryDate != null?escapeSpecialChars(formatter.format(expiryDate)):"-");
        return "{" +
                "\"Titre\":\"" + (escapeSpecialChars(label) != null ? label : "null") + "\"," +
                "\"Type du fichier\":\"" + fileTypeName + "\"," +
                "\"Nom du fichier\":\"" + (fileName != null ? fileName : "null") + "\"," +
                "\"Date d'ajout\":\"" + escapeSpecialChars(date) + "\"," +
                "\"Date d'expiration\":\"" + escapeSpecialChars(date2) + "\"," +
                "\"Commentaire\":" + (escapeSpecialChars(comment).equals("null") ? "null" : "\"" + escapeSpecialChars(comment) + "\"") +
                "}";
    }

}
