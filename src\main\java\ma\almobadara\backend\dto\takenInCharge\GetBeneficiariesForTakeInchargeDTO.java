package ma.almobadara.backend.dto.takenInCharge;

import lombok.*;
import ma.almobadara.backend.dto.getbeneficiary.GetServiceDTO;

import java.util.Date;
import java.util.Set;
@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class GetBeneficiariesForTakeInchargeDTO {
    private static final long serialVersionUID = 7316619986300858856L;

    private Long id;
    private Boolean independent;
    private String firstName;
    private String lastName;
    private String zoneName;
    private Long categoryBeneficiaryId;
    private String categoryBeneficiaryName;
    private String pictureUrl;
    private String pictureBase64;
    private Long beneficiaryStatutId;
    private String beneficiaryStatut;
    private Long numberOfTakenInCharge;

}
