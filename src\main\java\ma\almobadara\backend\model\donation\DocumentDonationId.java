package ma.almobadara.backend.model.donation;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Objects;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class DocumentDonationId implements Serializable {

    private Long donation;
    private Long document;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        DocumentDonationId that = (DocumentDonationId) o;
        return Objects.equals(donation, that.donation) && Objects.equals(document, that.document);
    }

    @Override
    public int hashCode() {
        return Objects.hash(donation, document);
    }

}
