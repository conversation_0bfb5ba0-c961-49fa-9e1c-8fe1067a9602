package ma.almobadara.backend.repository.family;

import ma.almobadara.backend.model.family.TutorHistory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface TutorHistoryRepository extends JpaRepository<TutorHistory, Long> {
    @Query("SELECT th FROM TutorHistory th WHERE th.familyMember.family.id = :familyId")
    List<TutorHistory> findByFamilyId(Long familyId);
}
