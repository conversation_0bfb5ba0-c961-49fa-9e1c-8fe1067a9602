package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.family.TutorHistoryDTO;
import ma.almobadara.backend.model.family.TutorHistory;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface TutorHistoryMapper {

    @Mapping(target = "familyMemberName", expression = "java(tutorHistory.getFamilyMember().getPerson().getFirstName() + \" \" + tutorHistory.getFamilyMember().getPerson().getLastName())")
    @Mapping(target = "familyMemberRelationship",  ignore = true)
    TutorHistoryDTO toDTO(TutorHistory tutorHistory);
}
