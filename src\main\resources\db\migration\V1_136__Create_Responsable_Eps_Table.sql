CREATE TABLE responsable_eps (
     id BIGSERIAL PRIMARY KEY,
     picture_url VARCHAR(255),
     first_name <PERSON><PERSON><PERSON><PERSON>(255),
     last_name <PERSON><PERSON><PERSON><PERSON>(255),
     first_name_ar VARCHAR(255),
     last_name_ar VARCHAR(255),
     email VA<PERSON>HAR(255) ,
     phone_number <PERSON><PERSON><PERSON><PERSON>(50),
     identity_code <PERSON><PERSON><PERSON><PERSON>(100),
     is_deleted BO<PERSON>EAN NOT NULL DEFAULT FALSE,
     type_identity_id BIGINT,
     comment TEXT,
     function_contact_id BIGINT,
     eps_id BIGINT,
     CONSTRAINT fk_eps FOREIGN KEY (eps_id) REFERENCES eps(id) ON DELETE SET NULL
);
