package ma.almobadara.backend.service.beneficiary;


import jakarta.persistence.EntityNotFoundException;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.clients.RefFeignClient;
import ma.almobadara.backend.controller.referentiel.RefController;
import ma.almobadara.backend.dto.administration.CacheAdUserDTO;
import ma.almobadara.backend.dto.beneficiary.*;
import ma.almobadara.backend.dto.communs.DocumentAndEntityDto;
import ma.almobadara.backend.dto.communs.DocumentDTO;
import ma.almobadara.backend.dto.referentiel.*;
import ma.almobadara.backend.enumeration.Direction;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.mapper.CacheAdUserMapper;
import ma.almobadara.backend.mapper.DocumentMapper;
import ma.almobadara.backend.mapper.RapportMapper;
import ma.almobadara.backend.mapper.RapportPictureMapper;
import ma.almobadara.backend.model.AddedRapportResponse;
import ma.almobadara.backend.model.administration.CacheAdUser;
import ma.almobadara.backend.model.beneficiary.*;
import ma.almobadara.backend.model.donor.Correspondence;
import ma.almobadara.backend.model.donor.Donor;
import ma.almobadara.backend.model.donor.DonorMoral;
import ma.almobadara.backend.model.donor.DonorPhysical;
import ma.almobadara.backend.model.family.Family;
import ma.almobadara.backend.model.family.FamilyMember;
import ma.almobadara.backend.model.takenInCharge.TakenInCharge;
import ma.almobadara.backend.repository.administration.AssistantRepository;
import ma.almobadara.backend.repository.beneficiary.*;
import ma.almobadara.backend.repository.donor.CorrespondenceRepository;
import ma.almobadara.backend.repository.donor.DonorRepository;
import ma.almobadara.backend.repository.family.FamilyRepository;
import ma.almobadara.backend.repository.takenInCharge.TakenInChargeRepository;
import ma.almobadara.backend.service.administration.CacheAdUserService;
import ma.almobadara.backend.service.communs.DocumentService;
import ma.almobadara.backend.service.communs.MailSenderService;
import ma.almobadara.backend.service.donor.MinioService;
import ma.almobadara.backend.translation.TranslationService;
import ma.almobadara.backend.util.constants.CustomMultipartFile;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URISyntaxException;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static ma.almobadara.backend.service.administration.CacheAdUserService.getUsernameFromJwt;

@RequiredArgsConstructor
@Slf4j
@Service
public class RapportService {

    private final RapportRepository rapportRepository;
    private final RapportPictureRepository rapportPictureRepository;
    private final DonorRepository donorRepository;
    private final TakenInChargeRepository takenInChargeRepository;
    private final BeneficiaryRepository beneficiaryRepository;
    private final FamilyRepository familyRepository;
    private final RapportMapper rapportMapper;
    private final RapportPictureMapper rapportPictureMapper;
    private final MinioService minioService;
    private final RefFeignClient refFeignClient;
    private final RefController refController;
    private final DocumentService documentService;
    private final DocumentMapper documentMapper;
    private final DocumentBeneficiaryRepository documentBeneficiaryRepository;
    private final MailSenderService mailSenderService;
    private final AssistantRepository assistantRepository;
    private final AgendaRapportRepository agendaRapportRepository;
    private final HistoryRapportRepository historyRapportRepository;
    private final CorrespondenceRepository correspondenceRepository;
    private final CacheAdUserService cacheAdUserService;
    private final CacheAdUserMapper cacheAdUserMapper;
    private final TranslationService translationService;


    private final ExecutorService executorService = Executors.newCachedThreadPool();

    @Value("${minio.reportsFolder}")
    private String reportsFolder;

    @Value("${minio.picturesFolder}")
    private String picturesFolder;

    @Value("${minio.picture.abv}")
    private String abv;


    public AddedRapportResponse addRapport(RapportDto rapportDto, Long agendaRapportId) {
        rapportDto.setDateRapport(new Date());
        AgendaRapport agendaRapport = agendaRapportRepository.findById(agendaRapportId)
                .orElseThrow(() -> new IllegalArgumentException("AgendaRapport not found with id: " + agendaRapportId));
        int year = Integer.parseInt(agendaRapport.getYear());
        Long lastNumberRapport = rapportRepository.findLastNumberRapportByBeneficiaryAndYear(rapportDto.getBeneficiaryId(), year);
        Long newNumberRapport = (lastNumberRapport != null) ? lastNumberRapport + 1 : 1;
        rapportDto.setNumberRapport(newNumberRapport);

        rapportDto.setCodeRapport(generateCodeRapport(newNumberRapport, rapportDto.getBeneficiaryId(), year));
        Rapport rapport = rapportMapper.toEntity(rapportDto);
//        Donor donor = donorRepository.findById(rapportDto.getDonorId())
//                .orElseThrow(() -> new IllegalArgumentException("Donor not found with id: " + rapportDto.getDonorId()));
//        rapport.setDonor(donor);

        if (rapportDto.getDonorId() != null) {
            Donor donor = donorRepository.findById(rapportDto.getDonorId())
                    .orElseThrow(() -> new IllegalArgumentException("Donor not found with id: " + rapportDto.getDonorId()));
            rapport.setDonor(donor);
        } else {
            rapport.setDonor(null);
        }
        Beneficiary beneficiary = beneficiaryRepository.findById(rapportDto.getBeneficiaryId())
                .orElseThrow(() -> new IllegalArgumentException("Beneficiary not found with id: " + rapportDto.getBeneficiaryId()));
        rapport.setBeneficiary(beneficiary);

        if (rapportDto.getFamilyId() != null && !beneficiary.getIndependent()) {
            Family family = familyRepository.findById(rapportDto.getFamilyId())
                    .orElseThrow(() -> new IllegalArgumentException("Family not found with id: " + rapportDto.getFamilyId()));
            rapport.setFamily(family);
        } else {
            rapport.setFamily(null);
        }
        List<RapportPicture> pictures = saveRapportPictures(rapportDto.getPictures(), rapport);
        rapport.setPictures(pictures);

        rapport.setBeneficiaryBirthDate(beneficiary.getPerson().getBirthDate());
        rapport = rapportRepository.save(rapport);
        agendaRapport.setRapport(rapport);
        agendaRapport.setStatus(String.valueOf(RapportStatus.RAPPORT_INITIAL));
        agendaRapport.setDateRapport(new Date());
        agendaRapport.setYear(String.valueOf(Calendar.getInstance().get(Calendar.YEAR)));
        agendaRapport.setModifiedAt(Instant.now());
        agendaRapportRepository.save(agendaRapport);
        return AddedRapportResponse.builder()
                .id(rapport.getId())
                .code(rapport.getCodeRapport())
                .message("Rapport ajouté avec succès")
                .build();
    }


    private List<RapportPicture> saveRapportPictures(List<RapportPictureDTO> pictureDTOs, Rapport rapport) {
        List<RapportPicture> pictureEntities = new ArrayList<>();

        if (pictureDTOs == null || pictureDTOs.isEmpty()) {
            log.info("Aucune image à enregistrer.");
            return pictureEntities;
        }

        String reportPath = reportsFolder + "/RAPPORT_" + rapport.getCodeRapport();
        log.info("Chemin du rapport : {}", reportPath);

        for (RapportPictureDTO pictureDTO : pictureDTOs) {
            if (pictureDTO.getPicture() != null) {
                MultipartFile file = pictureDTO.getPicture();

                String fileName = generatePictureFileName(file, rapport.getCodeRapport());
                log.info("Nom du fichier généré : {}", fileName);

                String filePath = reportPath + "/" + picturesFolder + "/" + fileName;
                log.info("Chemin complet du fichier : {}", filePath);

                minioService.WriteToMinIO(file, reportPath + "/" + picturesFolder + "/", fileName);
                log.info("Fichier enregistré dans MinIO : {}", fileName);

//                // Traduction de la description
//                String description = pictureDTO.getDescription();
//                String descriptionAr = pictureDTO.getDescriptionAr();
//                String descriptionAng = pictureDTO.getDescriptionAng();
             //   Map<String, String> translations = new HashMap<>();
//                try {
//                    translations = translationService.translate(description, "fr");
//                } catch (Exception e) {
//                    log.error("Erreur lors de la traduction de la description", e);
//                }
                RapportPicture picture = RapportPicture.builder()
                        .url(filePath)
                        .description(pictureDTO.getDescription())
                        .descriptionAr(pictureDTO.getDescriptionAr())
                        .descriptionAng(pictureDTO.getDescriptionAng())
                        .rapport(rapport)
                        .build();

                pictureEntities.add(picture);
                log.info("Image ajoutée à la base de données : {}", filePath);
            }
        }
        return pictureEntities;
    }

//    private List<RapportPicture> saveRapportPictures(List<RapportPictureDTO> pictureDTOs, Rapport rapport) {
//    List<RapportPicture> pictureEntities = new ArrayList<>();
//
//    if (pictureDTOs == null || pictureDTOs.isEmpty()) {
//        log.info("Aucune image à enregistrer.");
//        return pictureEntities;
//    }
//
//    String reportPath = reportsFolder + "/RAPPORT_" + rapport.getCodeRapport();
//    log.info("Chemin du rapport : {}", reportPath);
//
//    for (RapportPictureDTO pictureDTO : pictureDTOs) {
//        if (pictureDTO.getPicture() != null) {
//            MultipartFile file = pictureDTO.getPicture();
//
//            String fileName = generatePictureFileName(file, rapport.getCodeRapport());
//            log.info("Nom du fichier généré : {}", fileName);
//
//            String filePath = reportPath + "/" + picturesFolder + "/" + fileName;
//            log.info("Chemin complet du fichier : {}", filePath);
//
//            minioService.WriteToMinIO(file, reportPath + "/" + picturesFolder + "/", fileName);
//            log.info("Fichier enregistré dans MinIO : {}", fileName);
//
//            RapportPicture picture = RapportPicture.builder()
//
//                    .url(filePath)
//                    .description(pictureDTO.getDescription())
//                    .rapport(rapport)
//                    .build();
//
//            pictureEntities.add(picture);
//            log.info("Image ajoutée à la base de données : {}", filePath);
//        }
//    }
//    return pictureEntities;
//}

    private String generatePictureFileName(MultipartFile file, String codeRapport) {

        String extension = FilenameUtils.getExtension(file.getOriginalFilename());

        String uniqueId = UUID.randomUUID().toString();

        String timestamp = String.valueOf(System.currentTimeMillis());

        return "RAPPORT_" + codeRapport + "_" + timestamp + "_" + uniqueId + "." + extension;
    }

    private Long generateNumberRapport() {
        Long lastNumber = rapportRepository.findMaxNumberRapport().orElse(0L);
        return lastNumber + 1;
    }

    private String generateCodeRapport(Long numberRapport,  Long beneficiaryId, int year) {
        return "RPT-" + numberRapport +  "-B" + beneficiaryId + "Y" +  year;
    }

    public BeneficiaryDonorsDto getBeneficiaryWithDonors(Long beneficiaryId) {

        Beneficiary beneficiary = beneficiaryRepository.findById(beneficiaryId)
                .orElseThrow(() -> new EntityNotFoundException("Beneficiary not found"));

        List<TakenInCharge> takenInCharges = takenInChargeRepository.findByBeneficiaryId(beneficiaryId);

        List<DonorInfoDto> donors = takenInCharges.stream()
                .flatMap(takenInCharge -> takenInCharge.getTakenInChargeDonors().stream())
                .map(takenInChargeDonor -> {
                    Donor donor = takenInChargeDonor.getDonor();
                    return DonorInfoDto.builder()
                            .donorId(donor.getId())
                            .donorCode(donor.getCode())
                            .donorType(donor instanceof DonorPhysical ? "Physical" : "Moral")
                            .firstName(donor instanceof DonorPhysical ? ((DonorPhysical) donor).getFirstName() : null)
                            .lastName(donor instanceof DonorPhysical ? ((DonorPhysical) donor).getLastName() : null)
                            .company(donor instanceof DonorMoral ? ((DonorMoral) donor).getCompany() : null)
                            .keepAnonymous(takenInChargeDonor.getKeepanonymous())
                            .build();
                })
                .collect(Collectors.toList());

        FamilyInfoDto familyInfo = null;
        CityDTO cityDTO = new CityDTO();
        String adressFamille = null;
        String adressFamilleAr = null;
        String phoneNumber = null;
        if (!beneficiary.getIndependent()) {
            Family family = familyRepository.findByPersonId(beneficiary.getPerson().getId());
            //int countMemberF =  family.getFamilyMembers().size();
            int countMemberF = (int) family.getFamilyMembers().stream()
                    .filter(member -> !member.getPerson().isDeceased())
                    .count();
            if (family != null) {
                familyInfo = FamilyInfoDto.builder()
                        .familyId(family.getId())
                        .familyCode(family.getCode())
                        .addressFamily(family.getAddressFamily())
                        .addressFamilyAr(family.getAddressFamilyAr())
                        .generalCommentFamily(family.getGeneralCommentFamily())
                        .phoneNumberFamily(family.getPhoneNumberFamily())
                        .numberOfFamilyMember(countMemberF)
                        .cityId(family.getCityId())
                        .familyMembers(family.getFamilyMembers().stream()
                                .filter(FamilyMember::isTutor)
                                .map(member -> FamilyMemberInfoDto.builder()
                                        .memberId(member.getId())
                                        .tutor(member.isTutor())
                                        .generalComment(member.getGeneralComment())
                                        .firstName(member.getPerson().getFirstName())
                                        .lastName(member.getPerson().getLastName())
                                        .firstNameAr(member.getPerson().getFirstNameAr())
                                        .lastNameAr(member.getPerson().getLastNameAr())
                                        .phoneNumber(family.getPhoneNumberFamily())
                                        .email(member.getPerson().getEmail())
                                        .profession(member.getPerson().getProfessionId() != null
                                                ? refFeignClient.getMetProfession(member.getPerson().getProfessionId())
                                                : null)
                                        .familyRelationship(member.getFamilyRelationshipId() != null
                                                ? refFeignClient.getMetFamilyRelationship(member.getFamilyRelationshipId())
                                                : null)
                                        .accommodationType(refFeignClient.getMetAccommodationType(family.getAccommodationTypeId()))
                                        .build())
                                .collect(Collectors.toList()))
                        .build();

                cityDTO = refFeignClient.getParCity(family.getCityId());
                adressFamille = family.getAddressFamily();
                adressFamilleAr = family.getAddressFamilyAr();
                phoneNumber = family.getPhoneNumberFamily();
            }

        } else {
            cityDTO = refFeignClient.getParCity(beneficiary.getPerson().getCityId());
        }

        SchoolLevelDTO schoolLevelDTO = new SchoolLevelDTO();
        if (beneficiary.getPerson().getSchoolLevelId() != null) {
            schoolLevelDTO = refFeignClient.getParSchoolLevel(beneficiary.getPerson().getSchoolLevelId());
        }

        return BeneficiaryDonorsDto.builder()
                .beneficiaryId(beneficiary.getId())
                .beneficiaryCode(beneficiary.getCode())
                .firstName(beneficiary.getPerson().getFirstName())
                .lastName(beneficiary.getPerson().getLastName())
                .firstNameAr(beneficiary.getPerson().getFirstNameAr())
                .lastNameAr(beneficiary.getPerson().getLastNameAr())
                .birthDate(beneficiary.getPerson().getBirthDate())
                .address(beneficiary.getIndependent() ? beneficiary.getPerson().getAddress() : adressFamille)
                .addressAr(beneficiary.getIndependent() ? beneficiary.getPerson().getAddressAr() : adressFamilleAr)
                .phoneNumber(beneficiary.getIndependent() ? beneficiary.getPerson().getPhoneNumber() : phoneNumber)
                .info(refController.getCityWithRegionAndCountry(cityDTO.getId()).getBody())
                .schoolLevelType(schoolLevelDTO.getType())
                .schoolLevel(schoolLevelDTO)
                .independent(beneficiary.getIndependent())
                .schoolName(beneficiary.getPerson().getSchoolName())
                .donors(donors)
                .family(familyInfo)
                .build();
    }

    @Transactional
    public RapportDto getRapportDetails(Long id) {
        Rapport rapport = rapportRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Rapport not found with id: " + id));

        List<DonorInfoDto> donors = getDonorsForBeneficiary(rapport.getBeneficiary().getId());

        return RapportDto.builder()
                .id(rapport.getId())
                .archived(rapport.getArchived())
                .codeRapport(rapport.getCodeRapport())
                .beneficiaryCode(rapport.getBeneficiaryCode())
                .numberRapport(rapport.getNumberRapport())
                .dateRapport(rapport.getDateRapport())
                .reference(rapport.getReference())
                .release(rapport.getRelease())
                .phoneNumber(rapport.getPhoneNumber())
                //.donorId(rapport.getDonor().getId())
                .donorId(rapport.getDonor() != null ? rapport.getDonor().getId() : null)
                .donors(donors)
                .donorFirstName(rapport.getDonorFirstName())
                .beneficiaryId(rapport.getBeneficiary().getId())
                .beneficiaryFirstName(rapport.getBeneficiary().getPerson().getFirstName())
                .beneficiaryFirstNameAr(rapport.getBeneficiary().getPerson().getFirstNameAr())
                .beneficiaryLastNameAr(rapport.getBeneficiary().getPerson().getLastNameAr())
                .beneficiaryLastName(rapport.getBeneficiary().getPerson().getLastName())
                .beneficiaryBirthDate((rapport.getBeneficiary().getPerson().getBirthDate()))
                .beneficiaryAddressAr(rapport.getBeneficiaryAddressAr())
                .beneficiaryAddress(rapport.getBeneficiaryAddress())
                .familyId(rapport.getFamily() != null
                        ? rapport.getFamily().getId()
                        : null)
               // .familyId(rapport.getFamily().getId())
                .tutorFirstName(rapport.getTutorFirstName())
                .tutorFirstNameAr(rapport.getTutorFirstNameAr())
                .tutorLastName(rapport.getTutorLastName())
                .tutorLastNameAr(rapport.getTutorLastNameAr())
                .numberOfFamilyMember(rapport.getNumberOfFamilyMember())
                .info(rapport.getCityId() != null
                        ? refController.getCityWithRegionAndCountry(rapport.getCityId()).getBody()
                        : null)
                .schoolLevel(rapport.getSchoolLevelId() != null
                        ? refController.getParSchoolLevel(rapport.getSchoolLevelId()).getBody()
                        : null)
                .schoolLevelType(rapport.getCityId() != null ? refController.getParSchoolLevel(rapport.getSchoolLevelId()).getBody().getType() : null)
                .schoolName(rapport.getSchoolName())
                .result(rapport.getResult())
                .profession(rapport.getProfessionId() != null
                        ? refController.getMetProfession(rapport.getProfessionId()).getBody()
                        : null)
                .accommodationType(rapport.getAccommodationTypeId() != null
                        ? refController.getMetAccommodationType(rapport.getAccommodationTypeId()).getBody()
                        : null)
                .familyRelationship(rapport.getFamilyRelationshipId() != null
                        ? refController.getMetFamilyRelationship(rapport.getFamilyRelationshipId()).getBody()
                        : null)
                .socialServiceAr(rapport.getSocialServiceAr())
                .socialServiceFr(rapport.getSocialServiceFr())
                .socialServiceEn(rapport.getSocialServiceEn())
                .recommendationAr(rapport.getRecommendationAr())
                .recommendationFr(rapport.getRecommendationFr())
                .recommendationEn(rapport.getRecommendationEn())
                .activityEducationalAr(rapport.getActivityEducationalAr())
                .activityEducationalFr(rapport.getActivityEducationalFr())
                .activityEducationalEn(rapport.getActivityEducationalEn())
                .pictures(rapport.getPictures().stream()
                        .map(picture -> {
                            RapportPictureDTO pictureDTO = RapportPictureDTO.builder()
                                    .url(picture.getUrl())
                                    .description(picture.getDescription())
                                    .descriptionAng(picture.getDescriptionAng())
                                    .descriptionAr(picture.getDescriptionAr())
                                    .build();
                            // Ajouter l'image en base64
                            if (picture.getUrl() != null) {
                                try {
                                    byte[] imageData = minioService.ReadFromMinIO(picture.getUrl(), null);
                                    String base64Image = Base64.getEncoder().encodeToString(imageData);
                                    pictureDTO.setPictureBase64(base64Image);
                                } catch (TechnicalException ex) {
                                    ex.printStackTrace();
                                }
                            }
                            return pictureDTO;
                        })
                        .collect(Collectors.toList()))
                .build();
    }

    private List<DonorInfoDto> getDonorsForBeneficiary(Long beneficiaryId) {
        // Récupérer tous les "TakenInCharge" associés au bénéficiaire
        List<TakenInCharge> takenInCharges = takenInChargeRepository.findByBeneficiaryId(beneficiaryId);

        return takenInCharges.stream()
                .flatMap(takenInCharge -> takenInCharge.getTakenInChargeDonors().stream())
                .map(takenInChargeDonor -> {
                    Donor donor = takenInChargeDonor.getDonor();
                    return DonorInfoDto.builder()
                            .donorId(donor.getId())
                            .donorCode(donor.getCode())
                            .donorType(donor instanceof DonorPhysical ? "Physical" : "Moral")
                            .firstName(donor instanceof DonorPhysical ? ((DonorPhysical) donor).getFirstName() : null)
                            .lastName(donor instanceof DonorPhysical ? ((DonorPhysical) donor).getLastName() : null)
                            .company(donor instanceof DonorMoral ? ((DonorMoral) donor).getCompany() : null)
                            .keepAnonymous(takenInChargeDonor.getKeepanonymous())
                            .build();
                })
                .collect(Collectors.toList());
    }

    @Transactional
    public Page<RapportDto> getRapportsByBeneficiary(Long beneficiaryId, int page, int size, HttpServletResponse response) {
        PageRequest pageRequest = PageRequest.of(page, size);

        Page<Rapport> rapports = rapportRepository.findByBeneficiaryId(beneficiaryId, pageRequest);

        if (rapports.isEmpty()) {
            response.setHeader("X-No-Rapports-Found", "No rapports found for beneficiary with id: " + beneficiaryId);
        }

        return rapports.map(rapport -> RapportDto.builder()
                .id(rapport.getId())
                .archived(rapport.getArchived())
                .codeRapport(rapport.getCodeRapport())
                .numberRapport(rapport.getNumberRapport())
                .dateRapport(rapport.getDateRapport())
                .reference(rapport.getReference())
                .release(rapport.getRelease())
                .donorId(rapport.getDonor().getId())
                .donorFirstName(rapport.getDonorFirstName())
                .donorFirstName(rapport.getDonorLastName())
                .beneficiaryId(rapport.getBeneficiary().getId())
                .beneficiaryFirstName(rapport.getBeneficiary().getPerson().getFirstName())
                .beneficiaryLastName(rapport.getBeneficiary().getPerson().getLastName())
                .beneficiaryBirthDate(rapport.getBeneficiary().getPerson().getBirthDate())
                .familyId(Boolean.TRUE.equals(rapport.getBeneficiary().getIndependent()) ?  null : rapport.getFamily().getId())
                .numberOfFamilyMember(rapport.getNumberOfFamilyMember())
                .pictures(rapport.getPictures().stream()
                        .map(picture -> RapportPictureDTO.builder()
                                .url(picture.getUrl())
                                .description(picture.getDescription())
                                .descriptionAng(picture.getDescriptionAng())
                                .descriptionAr(picture.getDescriptionAr())
                                .build())
                        .collect(Collectors.toList()))
                .build());
    }

    public void deleteRapport(Long id) {
        Rapport rapport = rapportRepository.findById(id)
                .orElseThrow(() -> new NoSuchElementException("Rapport not found with id: " + id));

        if (rapport.getPictures() != null && !rapport.getPictures().isEmpty()) {
            for (RapportPicture picture : rapport.getPictures()) {
                String filePath = picture.getUrl();
                try {
                    minioService.DeleteFromMinIo(filePath);
                    log.info("Fichier supprimé de MinIO : {}", filePath);
                } catch (TechnicalException e) {
                    log.error("Erreur lors de la suppression du fichier dans MinIO : {}", e.getMessage());
                }
            }
        }

        rapportRepository.delete(rapport);
        log.info("Rapport supprimé avec succès : {}", id);
    }

//    public File generateReport(Long beneficiaryId, Long rapportId, String reportType, String language) throws JRException, IOException, TechnicalException {
//        // Charger le modèle Jasper
//        InputStream jasperStream = getClass().getClassLoader().getResourceAsStream("Template/orphelin_fr.jrxml");
//        if (jasperStream == null) {
//            throw new FileNotFoundException("Template not found");
//        }
//
//        JasperReport jasperReport = JasperCompileManager.compileReport(jasperStream);
//
//        // Récupérer les informations du bénéficiaire
//        Rapport rapportByBeneficiaryAndId = rapportRepository.findByBeneficiaryIdAndId(beneficiaryId, rapportId)
//                .orElseThrow(() -> new RuntimeException("Rapport not found for the given beneficiary and rapport ID"));
//
//        RapportDto rapportDtoByBeneficiary = rapportMapper.toDto(rapportByBeneficiaryAndId);
//        // Préparer les paramètres pour le rapport
//        Map<String, Object> parameters = prepareReportParameters(rapportDtoByBeneficiary);
//        // Utiliser la source de données pour générer le rapport
//        JRBeanCollectionDataSource dataSource = (JRBeanCollectionDataSource) parameters.get("REPORT_DATA_SOURCE");
//        JasperPrint jasperPrint = JasperFillManager.fillReport(jasperReport, parameters, dataSource);
//
//        // Sauvegarder le rapport en tant que fichier PDF
//        File pdfFile = File.createTempFile("report_", ".pdf");
//        try (FileOutputStream fos = new FileOutputStream(pdfFile)) {
//            JasperExportManager.exportReportToPdfStream(jasperPrint, fos);
//        }
//
////        byte[] fileContent = Files.readAllBytes(pdfFile.toPath());
////
////        DocumentBeneficiaryAddDto documentDto = new DocumentBeneficiaryAddDto();
////        Beneficiary beneficiary = beneficiaryRepository.findById(beneficiaryId)
////                .orElseThrow(() -> new RuntimeException("Beneficiary not found"));
////        String entityType = "beneficiary";
////        addDocumentToBeneficiary(pdfFile, fileContent, beneficiary, documentDto, entityType);
//
//
//        AgendaRapport agendaRapport = agendaRapportRepository.findByRapportId(rapportId)
//                .orElse(null);
//
//        RapportStatus currentStatus = (agendaRapport != null && agendaRapport.getStatus() != null)
//                ? RapportStatus.valueOf(agendaRapport.getStatus())
//                : RapportStatus.RAPPORT_INITIAL;
//
//        if (currentStatus == RapportStatus.RAPPORT_FINAL) {
//
//            byte[] fileContent = Files.readAllBytes(pdfFile.toPath());
//            DocumentBeneficiaryAddDto documentDto = new DocumentBeneficiaryAddDto();
//            Beneficiary beneficiary = beneficiaryRepository.findById(beneficiaryId)
//                    .orElseThrow(() -> new RuntimeException("Beneficiary not found"));
//            String entityType = "beneficiary";
//
//            agendaRapport.setDateValidate(new Date());
//            agendaRapportRepository.save(agendaRapport);
//
//            addDocumentToBeneficiary(pdfFile, fileContent, beneficiary, documentDto, entityType);
//        }
//
//        return pdfFile;
//    }


    public List<File> generateReport(Long beneficiaryId, Long rapportId, String reportType) throws JRException, IOException, TechnicalException, URISyntaxException {
        List<File> generatedFiles = new ArrayList<>();
        List<String> templates = listJasperTemplates();

        for (String template : templates) {
            InputStream jasperStream = getClass().getClassLoader().getResourceAsStream("Template/" + template);
            if (jasperStream == null) {
                throw new FileNotFoundException("Template not found: " + template);
            }

            JasperReport jasperReport = JasperCompileManager.compileReport(jasperStream);

            Rapport rapportByBeneficiaryAndId = rapportRepository.findByBeneficiaryIdAndId(beneficiaryId, rapportId)
                    .orElseThrow(() -> new RuntimeException("Rapport not found for the given beneficiary and rapport ID"));

            RapportDto rapportDtoByBeneficiary = rapportMapper.toDto(rapportByBeneficiaryAndId);

            Map<String, Object> parameters = prepareReportParameters(rapportDtoByBeneficiary);

            JRBeanCollectionDataSource dataSource = (JRBeanCollectionDataSource) parameters.get("REPORT_DATA_SOURCE");
            JasperPrint jasperPrint = JasperFillManager.fillReport(jasperReport, parameters, dataSource);

            File pdfFile = File.createTempFile("report_" + template.replace(".jrxml", "") + "_", ".pdf");
            try (FileOutputStream fos = new FileOutputStream(pdfFile)) {
                JasperExportManager.exportReportToPdfStream(jasperPrint, fos);
            }

            AgendaRapport agendaRapport = agendaRapportRepository.findByRapportId(rapportId)
                    .orElse(null);

            RapportStatus currentStatus = (agendaRapport != null && agendaRapport.getStatus() != null)
                    ? RapportStatus.valueOf(agendaRapport.getStatus())
                    : RapportStatus.RAPPORT_INITIAL;

            if (currentStatus == RapportStatus.RAPPORT_FINAL) {
                byte[] fileContent = Files.readAllBytes(pdfFile.toPath());
                DocumentBeneficiaryAddDto documentDto = new DocumentBeneficiaryAddDto();
                Beneficiary beneficiary = beneficiaryRepository.findById(beneficiaryId)
                        .orElseThrow(() -> new RuntimeException("Beneficiary not found"));
                String entityType = "beneficiary";

                agendaRapport.setDateValidate(new Date());
                agendaRapportRepository.save(agendaRapport);

                addDocumentToBeneficiary(pdfFile, fileContent, beneficiary, documentDto, entityType, rapportId, template);
            }

            generatedFiles.add(pdfFile);
        }

        return generatedFiles;
    }

    private List<String> listJasperTemplates() throws IOException, URISyntaxException {
        List<String> templates = new ArrayList<>();
        String templateFolder = "Template";
        URL resource = getClass().getClassLoader().getResource(templateFolder);
        if (resource == null) {
            throw new FileNotFoundException("Template folder not found: " + templateFolder);
        }
        Path templatePath = Paths.get(resource.toURI());
        try (Stream<Path> paths = Files.list(templatePath)) {
            paths.filter(Files::isRegularFile)
                    .filter(path -> path.getFileName().toString().endsWith(".jrxml"))
                    .forEach(path -> templates.add(path.getFileName().toString()));
        }
        return templates;
    }
    private void addDocumentToBeneficiary(File pdfFile, byte[] fileContent, Beneficiary beneficiary, DocumentBeneficiaryAddDto documentDto, String entityType, Long rapportId,  String template) throws TechnicalException, IOException {
        DocumentAndEntityDto documentAndEntityDto = new DocumentAndEntityDto();
        DocumentDTO documentDTO = new DocumentDTO();
        MultipartFile multipartFile = new CustomMultipartFile(
                pdfFile.getName(),
                pdfFile.getName(),
                MediaType.APPLICATION_PDF_VALUE,
                fileContent
        );

        AgendaRapport agendaRapport = agendaRapportRepository.findByBeneficiaryIdAndRapportId(beneficiary.getId(), rapportId)
                .orElseThrow(() -> new RuntimeException("AgendaRapport not found"));

        String reportNumber = String.valueOf(agendaRapport.getNumberRapport());
        String reportYear = agendaRapport.getYear();
        String language = extractLanguageFromTemplate(template);
        String version = getVersionFromLanguage(language);

        String label = String.format("Rapport Kafalat %s/%s -  Version %s", reportNumber, reportYear,  version);
        documentDTO.setLabel(label);
        documentDTO.setDocumentDate(new Date());
        documentDTO.setExpiryDate(Date.from(LocalDate.now().plusMonths(1).atStartOfDay(ZoneId.systemDefault()).toInstant()));
        documentDTO.setComment(documentDto.getComment());
        documentDTO.setFileUrl(pdfFile.getAbsolutePath());
        documentDTO.setFileName(pdfFile.getName());
        documentDTO.setFile(multipartFile);
        documentDTO.setType(documentDto.getType());
        documentAndEntityDto.setDocumentDTO(documentDTO);
        documentAndEntityDto.setEntityId(beneficiary.getId());
        documentAndEntityDto.setEntityType(entityType);
        DocumentDTO newDocumentDTO = documentService.addDocument(documentAndEntityDto);

        DocumentBeneficiary documentBeneficiary = DocumentBeneficiary.builder()
                .beneficiary(beneficiary)
                .document(documentMapper.documentToModelToModel(newDocumentDTO))
                .build();

        documentBeneficiaryRepository.save(documentBeneficiary);
    }

    private String getVersionFromLanguage(String language) {
        return switch (language.toLowerCase()) {
            case "fr" -> "française";
            case "en" -> "anglaise";
            case "ar" -> "arabe";
            default -> "inconnue";
        };
    }

    private String extractLanguageFromTemplate(String templateName) {
        String[] parts = templateName.split("_");
        if (parts.length > 1) {
            String languagePart = parts[1];
            return languagePart.split("\\.")[0];
        }
        return "inconnue";
    }

    private Map<String, Object> prepareReportParameters(RapportDto rapportDtoByBeneficiary) throws IOException {
        Map<String, Object> parameters = new HashMap<>();

        Donor donor = rapportRepository.findById(rapportDtoByBeneficiary.getId())
                .map(Rapport::getDonor)
                .orElse(null);
        AgendaRapport agendaRapport=agendaRapportRepository.findByRapportId(rapportDtoByBeneficiary.getId()).orElseThrow(() -> new RuntimeException("AgendaRapport not found"));
        String donorFullName = "---";
        String donorFullNameAr = "---";
        if (donor instanceof DonorPhysical) {
            DonorPhysical donorPhysical = (DonorPhysical) donor;
            donorFullName = donorPhysical.getFirstName() + " " + donorPhysical.getLastName();
            donorFullNameAr = donorPhysical.getFirstNameAr() + " " + donorPhysical.getLastNameAr();
        } else {
            log.warn("Le donateur associé au rapport n'est pas un DonorPhysical !");
        }

        Date reportDate = rapportDtoByBeneficiary.getDateRapport();
        LocalDateTime localDateTime = reportDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();

        int year = localDateTime.getYear();
        Long reportNumber = agendaRapport.getNumberRapport();

        String numberRapport = "Rapport : " + reportNumber + " / " + year;
        String numberRapportAn = "Report : " + reportNumber + " / " + year;
        String numberRapportAr = " التقرير : " + year + " / " + reportNumber ;
        parameters.put("numberRapport", numberRapport);
        parameters.put("numberRapportAn", numberRapportAn);
        parameters.put("numberRapportAr", numberRapportAr);
        parameters.put("donorFullName", donorFullName);
        parameters.put("donorFullNameAr", donorFullNameAr);
        parameters.put("beneficiaryFullName", rapportDtoByBeneficiary.getBeneficiaryFirstName() + " " + rapportDtoByBeneficiary.getBeneficiaryLastName());
        parameters.put("beneficiaryFullNameAr", rapportDtoByBeneficiary.getBeneficiaryFirstNameAr() + " " + rapportDtoByBeneficiary.getBeneficiaryLastNameAr());
        parameters.put("beneficiaryBirthDate", rapportDtoByBeneficiary.getBeneficiaryBirthDate());
        parameters.put("beneficiaryCode", rapportDtoByBeneficiary.getBeneficiaryCode());
        parameters.put("beneficiaryAddress", rapportDtoByBeneficiary.getBeneficiaryAddress());
        CityDTO city = rapportDtoByBeneficiary.getCity().getId() != null
                ? refController.getParCity(rapportDtoByBeneficiary.getCity().getId()).getBody()
                : null;
        parameters.put("beneficiaryCity", city.getName() != null ? city.getName() : "");
        parameters.put("beneficiaryCityEn", city.getNameEn() != null ? city.getNameEn() : "");
        parameters.put("beneficiaryCityAr", city.getNameAr() != null ? city.getNameAr() : "");
        parameters.put("beneficiaryAddressAr", rapportDtoByBeneficiary.getBeneficiaryAddressAr());
        parameters.put("tutorFirstName", rapportDtoByBeneficiary.getTutorFirstName() + " " + rapportDtoByBeneficiary.getTutorLastName());
        parameters.put("tutorFirstNameAr", rapportDtoByBeneficiary.getTutorFirstNameAr() + " " + rapportDtoByBeneficiary.getTutorLastNameAr());
        parameters.put("tutorLastName", rapportDtoByBeneficiary.getTutorLastName());
        parameters.put("tutorLastNameAr", rapportDtoByBeneficiary.getTutorLastNameAr());
        ProfessionDTO pro = rapportDtoByBeneficiary.getProfession().getId() != null
                ? refController.getMetProfession(rapportDtoByBeneficiary.getProfession().getId()).getBody()
                : null;
        parameters.put("tutorProfession", pro != null && pro.getName() != null ? pro.getName() : "");
        parameters.put("tutorProfessionEn", pro != null && pro.getNameEn() != null ? pro.getNameEn() : "");
        parameters.put("tutorProfessionAr", pro != null && pro.getNameAr() != null ? pro.getNameAr(): "");
        AccommodationTypeDTO accommodation = refController.getMetAccommodationType(rapportDtoByBeneficiary.getAccommodationType().getId()).getBody();
        parameters.put("accomondationType", accommodation.getName() != null ? accommodation.getName() : "");
        parameters.put("accomondationTypeEn", accommodation.getNameEn() != null ? accommodation.getNameEn() : "");
        parameters.put("accomondationTypeAr", accommodation.getNameAr() != null ? accommodation.getNameAr() : "");
        parameters.put("activityEdEN", rapportDtoByBeneficiary.getActivityEducationalEn()  != null ? rapportDtoByBeneficiary.getActivityEducationalEn() : "");
        parameters.put("activityEdFr", rapportDtoByBeneficiary.getActivityEducationalFr() != null ? rapportDtoByBeneficiary.getActivityEducationalFr() : "");
        parameters.put("activityEdAr", rapportDtoByBeneficiary.getActivityEducationalAr() != null ? rapportDtoByBeneficiary.getActivityEducationalAr() : "");
        parameters.put("phoneNumber", rapportDtoByBeneficiary.getPhoneNumber() != null ? rapportDtoByBeneficiary.getPhoneNumber() : "");
        parameters.put("socialServiceEN", rapportDtoByBeneficiary.getSocialServiceEn() != null ? rapportDtoByBeneficiary.getSocialServiceEn() : "");
        parameters.put("socialServiceFr", rapportDtoByBeneficiary.getSocialServiceFr() != null ? rapportDtoByBeneficiary.getSocialServiceFr() : "");
        parameters.put("socialServiceAr", rapportDtoByBeneficiary.getSocialServiceAr() != null ? rapportDtoByBeneficiary.getSocialServiceAr() : "");
        parameters.put("recommendationEN", rapportDtoByBeneficiary.getRecommendationEn() != null ? rapportDtoByBeneficiary.getRecommendationEn() : "");
        parameters.put("recommendationFr", rapportDtoByBeneficiary.getRecommendationFr() != null ? rapportDtoByBeneficiary.getRecommendationFr() : "");
        parameters.put("recommendationAr", rapportDtoByBeneficiary.getRecommendationAr() != null ? rapportDtoByBeneficiary.getRecommendationAr() : "");
        FamilyRelationshipDTO familyRelationship = rapportDtoByBeneficiary.getFamilyRelationship().getId() != null
                ? refController.getMetFamilyRelationship(rapportDtoByBeneficiary.getFamilyRelationship().getId()).getBody()
                : null;
        parameters.put("familyRelation", familyRelationship != null && familyRelationship.getName() != null ? familyRelationship.getName() : "");
        parameters.put("familyRelationEn", familyRelationship != null && familyRelationship.getNameEn() != null ? familyRelationship.getNameEn() : "");
        parameters.put("familyRelationAr", familyRelationship != null && familyRelationship.getNameAr() != null ? familyRelationship.getNameAr() : "");
        parameters.put("numberOfFamily", rapportDtoByBeneficiary.getNumberOfFamilyMember());
        parameters.put("result", rapportDtoByBeneficiary.getResult());
        SchoolLevelDTO schoolLeve = refController.getParSchoolLevel(rapportDtoByBeneficiary.getSchoolLevel().getId()).getBody();
        parameters.put("schoolLevelType", schoolLeve.getType() != null ? translateSchoolLevelType(schoolLeve.getType(),"fr") : "");
        parameters.put("schoolLevelTypeAr", schoolLeve.getType() != null ? translateSchoolLevelType(schoolLeve.getType(),"ar") : "");
        parameters.put("schoolLevelTypeEn", schoolLeve.getType() != null ? translateSchoolLevelType(schoolLeve.getType(),"en") : "");
        parameters.put("schoolName", schoolLeve.getName() != null ? schoolLeve.getName() : "");
        parameters.put("schoolNameEn", schoolLeve.getNameEn() != null ? schoolLeve.getName() : "");
        parameters.put("schoolNameAr", schoolLeve.getNameAr() != null ? schoolLeve.getNameAr() : "");

        byte[] img1 = null, img2 = null, img3 = null;
        String des1En = null, des1Ar = null,des1 = null, des2 = null, des2En = null, des2Ar = null,des3 = null,des3Ar = null,des3En = null;

        List<Map<String, Object>> data = new ArrayList<>();
        if (rapportDtoByBeneficiary.getPictures() != null && !rapportDtoByBeneficiary.getPictures().isEmpty()) {
            int count = 0;
            for (RapportPictureDTO picture : rapportDtoByBeneficiary.getPictures()) {
                try {
                    byte[] imageData = minioService.ReadFromMinIO(picture.getUrl(), false);
                    if (imageData.length == 0) {
                        throw new IOException("L'image récupérée est vide.");
                    }

                    log.info("Taille de l'image récupérée : {}", imageData.length);

                    // Vérification de l'image avec BufferedImage
                    ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(imageData);
                    BufferedImage bufferedImage = ImageIO.read(byteArrayInputStream);

                    if (bufferedImage == null) {
                        throw new IOException("L'image récupérée depuis MinIO est invalide.");
                    }

                    // Si l'image est valide, ajout des données à la collection
                    Map<String, Object> row = new HashMap<>();
                    row.put("pictureBase64", imageData);
                    data.add(row);
                    if (count == 0) {
                        img1 = imageData;
                        des1 = picture.getDescription();
                        des1En = picture.getDescriptionAng();
                        des1Ar = picture.getDescriptionAr();
                    } else if (count == 1) {
                        img2 = imageData;
                        des2 = picture.getDescription();
                        des2En = picture.getDescriptionAng();
                        des2Ar = picture.getDescriptionAr();
                    } else if (count == 2) {
                        img3 = imageData;
                        des3 = picture.getDescription();
                        des3En = picture.getDescriptionAng();
                        des3Ar = picture.getDescriptionAr();
                    }
                } catch (IOException | TechnicalException e) {
                    log.error("Erreur lors de la récupération ou du traitement de l'image : {}", picture.getUrl(), e);
                    Map<String, Object> row = new HashMap<>();
                    row.put("pictureBase64", null);
                    data.add(row);
                }
                count++;
            }
        }else{
            log.error("image est null");
            Map<String, Object> row = new HashMap<>();
            row.put("pictureBase64", null);
            data.add(row);
        }
        parameters.put("img1", img1);
        parameters.put("img2", img2);
        parameters.put("img3", img3);
        parameters.put("des1", des1 != null ? des1 : "");
        parameters.put("des1En", des1En != null ? des1En : "");
        parameters.put("des1Ar", des1Ar != null ? des1Ar : "");
        parameters.put("des2", des2 != null ? des2 : "");
        parameters.put("des2Ar", des2Ar != null ? des2Ar : "");
        parameters.put("des2En", des2En != null ? des2En : "");
        parameters.put("des3", des3 != null ? des3 : "");
        parameters.put("des3En", des3En != null ? des3En : "");
        parameters.put("des3Ar", des3Ar != null ? des3Ar : "");

        JRBeanCollectionDataSource dataSource = new JRBeanCollectionDataSource(data);
        parameters.put("REPORT_DATA_SOURCE", dataSource);

        return parameters;
    }

    @Transactional
    public void validateReport(Long rapportId, String reportType, String language) throws JRException, IOException, TechnicalException, URISyntaxException {
        Rapport rapport = rapportRepository.findById(rapportId)
                .orElseThrow(() -> new RuntimeException("Rapport not found with ID: " + rapportId));

        Long beneficiaryId = rapport.getBeneficiary().getId();

        // Récupérer AgendaRapport à partir du rapportId
        AgendaRapport agendaRapport = agendaRapportRepository.findByRapportId(rapportId)
                .orElseThrow(() -> new RuntimeException("AgendaRapport not found for rapport ID: " + rapportId));

        // Déterminer le statut actuel
        RapportStatus currentStatus = (agendaRapport.getStatus() != null)
                ? RapportStatus.valueOf(agendaRapport.getStatus())
                : RapportStatus.RAPPORT_INITIAL;

        // Mettre à jour le statut selon le cas
        switch (currentStatus) {
            case RAPPORT_INITIAL:
                agendaRapport.setStatus(RapportStatus.RAPPORT_VALIDER_ASSISTANCE.name());
                break;
            case RAPPORT_VALIDER_ASSISTANCE:
                agendaRapport.setStatus(RapportStatus.RAPPORT_VALIDER_KAFALAT.name());
                break;
            case RAPPORT_A_COMPLETER_PAR_ASSISTANCE:
                agendaRapport.setStatus(RapportStatus.RAPPORT_VALIDER_ASSISTANCE.name());
                break;
            case RAPPORT_VALIDER_KAFALAT:
                agendaRapport.setStatus(RapportStatus.RAPPORT_FINAL.name());
                generateReport(beneficiaryId, rapportId, reportType);
                break;
            case RAPPORT_A_COMPLETER_PAR_KAFALAT:
                agendaRapport.setStatus(RapportStatus.RAPPORT_VALIDER_KAFALAT.name());
                break;
            case RAPPORT_FINAL:
                throw new IllegalStateException("Rapport is already in the final status.");
            default:
                throw new IllegalArgumentException("Invalid status for rapport: " + currentStatus);
        }
        agendaRapportRepository.save(agendaRapport);
    }

    @Transactional
    public AddedRapportResponse updateReport(Long id, RapportDto rapportDto) throws TechnicalException {
        log.debug("Start updateRapport with ID: {}, DTO: {}", id, rapportDto);

        Rapport existingRapport = rapportRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Rapport not found with ID: " + id));

        if (rapportDto.getReference() != null) {
            existingRapport.setReference(rapportDto.getReference());
        }
        if (rapportDto.getDateRapport() != null) {
            existingRapport.setDateRapport(rapportDto.getDateRapport());
        }
        if (rapportDto.getRelease() != null) {
            existingRapport.setRelease(rapportDto.getRelease());
        }
        if (rapportDto.getResult() != null) {
            existingRapport.setResult(rapportDto.getResult());
        }

        if (rapportDto.getDonorId() != null) {
            Donor donor = donorRepository.findById(rapportDto.getDonorId())
                    .orElseThrow(() -> new TechnicalException("Donor not found with ID: " + rapportDto.getDonorId()));
            existingRapport.setDonor(donor);
        }

        if (rapportDto.getFamilyId() != null) {
            Family family = familyRepository.findById(rapportDto.getFamilyId())
                    .orElseThrow(() -> new TechnicalException("Family not found with ID: " + rapportDto.getFamilyId()));
            existingRapport.setFamily(family);
        }

        if(rapportDto.getActivityEducationalFr() != null){
            existingRapport.setActivityEducationalEn(rapportDto.getActivityEducationalEn());
            existingRapport.setActivityEducationalFr(rapportDto.getActivityEducationalFr());
            existingRapport.setActivityEducationalAr(rapportDto.getActivityEducationalAr());
        }
        if(rapportDto.getSocialServiceAr()!= null){
            existingRapport.setSocialServiceAr(rapportDto.getSocialServiceAr());
            existingRapport.setSocialServiceFr(rapportDto.getSocialServiceFr());
            existingRapport.setSocialServiceEn(rapportDto.getSocialServiceEn());
        }
        if(rapportDto.getRecommendationAr()!= null){
            existingRapport.setRecommendationAr(rapportDto.getRecommendationAr());
            existingRapport.setRecommendationFr(rapportDto.getRecommendationFr());
            existingRapport.setRecommendationEn(rapportDto.getRecommendationEn());
        }

        if (rapportDto.getPictures() != null) {
            List<RapportPicture> updatedPictures = handlePicturesUpdate(rapportDto.getPictures(), existingRapport);
            existingRapport.getPictures().clear();
            existingRapport.getPictures().addAll(updatedPictures);
        }

        Rapport updatedRapport = rapportRepository.save(existingRapport);
        AgendaRapport agendaRapport = agendaRapportRepository.findByRapport(existingRapport)
                .orElseThrow(() -> new EntityNotFoundException("AgendaRapport not found for Rapport ID: " + existingRapport.getId()));

        agendaRapport.setModifiedAt(Instant.now());

        agendaRapportRepository.save(agendaRapport);

        log.debug("End updateRapport. Updated rapport ID: {}", updatedRapport.getId());

        return AddedRapportResponse.builder()
                .id(updatedRapport.getId())
                .code(updatedRapport.getCodeRapport())
                .message("Rapport mis à jour avec succès")
                .build();
    }

    private List<RapportPicture> handlePicturesUpdate(List<RapportPictureDTO> pictureDTOs, Rapport rapport) {
        List<RapportPicture> existingPictures = rapport.getPictures();
        Map<String, RapportPicture> existingPictureMap = existingPictures.stream()
                .collect(Collectors.toMap(RapportPicture::getUrl, picture -> picture));
        List<RapportPicture> updatedPictures = new ArrayList<>();

        for (RapportPictureDTO pictureDTO : pictureDTOs) {
            if (pictureDTO.getPicture() != null) {
                // Nouvelle image
                MultipartFile file = pictureDTO.getPicture();
                String fileName = generatePictureFileName(file, rapport.getCodeRapport());
                String filePath = reportsFolder + "/RAPPORT_" + rapport.getCodeRapport() + "/" + picturesFolder + "/" + fileName;

                minioService.WriteToMinIO(file, reportsFolder + "/RAPPORT_" + rapport.getCodeRapport() + "/" + picturesFolder + "/", fileName);

                String description = pictureDTO.getDescription();
                String descriptionAr = pictureDTO.getDescriptionAr();
                String descriptionAng = pictureDTO.getDescriptionAng();
//                Map<String, String> translations = new HashMap<>();
//                try {
//                    translations = translationService.translate(description, "fr");
//                } catch (Exception e) {
//                    log.error("Erreur lors de la traduction de la description", e);
//                }

                RapportPicture newPicture = RapportPicture.builder()
                        .url(filePath)
                        .description(pictureDTO.getDescription())
                        .descriptionAr(pictureDTO.getDescriptionAr())
                        .descriptionAng(pictureDTO.getDescriptionAng())
                        .rapport(rapport)
                        .build();
                updatedPictures.add(newPicture);
            } else if (existingPictureMap.containsKey(pictureDTO.getUrl())) {
                // Récupérer l'image existante
                RapportPicture existingPicture = existingPictureMap.get(pictureDTO.getUrl());

                // Mettre à jour les métadonnées si elles ont changé
                if (pictureDTO.getDescription() != null) {
                    existingPicture.setDescription(pictureDTO.getDescription());
                }
                if (pictureDTO.getDescriptionAr() != null) {
                    existingPicture.setDescriptionAr(pictureDTO.getDescriptionAr());
                }
                if (pictureDTO.getDescriptionAng() != null) {
                    existingPicture.setDescriptionAng(pictureDTO.getDescriptionAng());
                }

                updatedPictures.add(existingPicture);
                log.info("Métadonnées de l'image mises à jour : {}", existingPicture.getUrl());
            }
        }

        return updatedPictures;
    }

    @Transactional
    public Long rapportToComplete(Long rapportId, Long idStatutTarget, String rqComplete) {
        log.debug("Start service rapportToComplete");

        Rapport rapport = rapportRepository.findById(rapportId)
                .orElseThrow(() -> new RuntimeException("Rapport not found with ID: " + rapportId));

        Long beneficiaryId = rapport.getBeneficiary().getId();
        Beneficiary beneficiary = beneficiaryRepository.findById(beneficiaryId)
                .orElseThrow(() -> new RuntimeException("Beneficiary not found with ID: " + beneficiaryId));

        AgendaRapport agendaRapport = agendaRapportRepository.findByRapportId(rapportId)
                .orElseThrow(() -> new RuntimeException("AgendaRapport not found for rapport ID: " + rapportId));

        String assistantEmail = (beneficiary.getZone() != null && beneficiary.getZone().getAssistant() != null)
                ? beneficiary.getZone().getAssistant().getEmail()
                : "<EMAIL>";

        final String username = getUsernameFromJwt();
        final String rqCompleteFinal = rqComplete;

        if (idStatutTarget != null) {
            if (idStatutTarget.equals(RapportStatus.RAPPORT_A_COMPLETER_PAR_ASSISTANCE.getId())) {
                agendaRapport.setStatus(RapportStatus.RAPPORT_A_COMPLETER_PAR_ASSISTANCE.name());
                sendEmailAsync(assistantEmail, beneficiary, rqCompleteFinal, "Rapport à compléter par l'assistant", username);
            } else if (idStatutTarget.equals(RapportStatus.RAPPORT_A_COMPLETER_PAR_KAFALAT.getId())) {
                agendaRapport.setStatus(RapportStatus.RAPPORT_A_COMPLETER_PAR_KAFALAT.name());
                sendEmailAsync("<EMAIL>", beneficiary, rqCompleteFinal, "Rapport à compléter par service Kafalat", username);
            }
        } else {
            agendaRapport.setStatus(RapportStatus.RAPPORT_A_COMPLETER_PAR_ASSISTANCE.name());
            sendEmailAsync(assistantEmail, beneficiary, rqCompleteFinal, "Rapport à compléter par l'assistant", username);
        }

        if (rqComplete != null) {
            rapport.setDetailComplete(rqComplete);
        }
        agendaRapportRepository.save(agendaRapport);
        rapportRepository.save(rapport);

        log.debug("End service CompleteRapport");
        return idStatutTarget;
    }

    private void sendEmailAsync(String recipient, Beneficiary beneficiary, String rqComplete, String subject, String username) {
        executorService.submit(() -> {
            try {
                log.debug("Sending email to: {}", recipient);
                mailSenderService.sendCompletionEmailRapport(null, beneficiary, rqComplete, subject, recipient, "Marketing", username);
            } catch (Exception e) {
                log.error("Error sending email to: {}", recipient, e);
            }
        });
    }

//    public File viewReport(Long rapportId, String reportType, String language)
//            throws JRException, IOException, TechnicalException {
//
//        log.info("Génération du rapport PDF pour le rapport ID: {}", rapportId);
//
//        // Charger le modèle Jasper
//        InputStream jasperStream = getClass().getClassLoader().getResourceAsStream("Template/orphelin_fr.jrxml");
//        if (jasperStream == null) {
//            throw new FileNotFoundException("Template not found");
//        }
//
//        JasperReport jasperReport = JasperCompileManager.compileReport(jasperStream);
//
//        // Récupérer les informations du rapport
//        Rapport rapport = rapportRepository.findById(rapportId)
//                .orElseThrow(() -> new RuntimeException("Rapport non trouvé pour ID: " + rapportId));
//
//        RapportDto rapportDto = rapportMapper.toDto(rapport);
//
//        // Préparer les paramètres pour le rapport
//        Map<String, Object> parameters = prepareReportParameters(rapportDto);
//        JRBeanCollectionDataSource dataSource = (JRBeanCollectionDataSource) parameters.get("REPORT_DATA_SOURCE");
//
//        // Générer le rapport Jasper
//        JasperPrint jasperPrint = JasperFillManager.fillReport(jasperReport, parameters, dataSource);
//
//        String tempDir = System.getProperty("java.io.tmpdir");
//
//        File pdfFile = new File(tempDir, "report_" + UUID.randomUUID() + ".pdf");
//
//        try (FileOutputStream fos = new FileOutputStream(pdfFile)) {
//            JasperExportManager.exportReportToPdfStream(jasperPrint, fos);
//        }
//
//        log.info("Rapport PDF généré avec succès: {}", pdfFile.getAbsolutePath());
//        return pdfFile;
//    }
//

    @Transactional
    public void notifyAssistant(Long agendaRapportId) {
        log.debug("Start service notifyAssistant");

//        Rapport rapport = rapportRepository.findById(rapportId)
//                .orElseThrow(() -> new RuntimeException("Rapport not found with ID: " + rapportId));

        AgendaRapport rapport = agendaRapportRepository.findById(agendaRapportId)
                .orElseThrow(() -> new RuntimeException("AgendaRapport not found with ID: " + agendaRapportId));

        Beneficiary beneficiary = rapport.getBeneficiary();
        if (beneficiary == null) {
            throw new RuntimeException("Beneficiary not found for rapport ID: " + agendaRapportId);
        }

        String assistantEmail = (beneficiary.getZone() != null && beneficiary.getZone().getAssistant() != null)
                ? beneficiary.getZone().getAssistant().getEmail()
                : "<EMAIL>";

        String firstNameAssistant = (beneficiary.getZone() != null && beneficiary.getZone().getAssistant() != null)
                ? beneficiary.getZone().getAssistant().getFirstName()
                : "";

        String lastNameAssistant = (beneficiary.getZone() != null && beneficiary.getZone().getAssistant() != null)
                ? beneficiary.getZone().getAssistant().getLastName()
                : "";

        final String username = firstNameAssistant + " " + lastNameAssistant;

        final  Date dateRapport = rapport.getDatePlanned();

        String subject = mailSenderService.buildNotificationEmailSubject(beneficiary);

        String emailBody = mailSenderService.buildNotificationEmailBody(username, beneficiary, dateRapport);

        sendNotificationEmailAsync(assistantEmail, subject, emailBody);

        log.debug("End service notifyAssistant");
    }
    private String translateSchoolLevelType(String schoolLevelType, String language) {
        switch (language.toLowerCase()) {
            case "fr":
                if (schoolLevelType.equals("primaire")) {
                    return "Primaire";
                } else if (schoolLevelType.equals("secondaire")) {
                    return "Secondaire";
                } else if (schoolLevelType.equals("universitaire")) {
                    return "Universitaire";
                } else if (schoolLevelType.equals("préscolaire")) {
                    return "Préscolaire";
                }
                return schoolLevelType;
            case "en":
                if (schoolLevelType.equals("primaire")) {
                    return "Primary";
                } else if (schoolLevelType.equals("secondaire")) {
                    return "Secondary";
                } else if (schoolLevelType.equals("universitaire")) {
                    return "University";
                } else if (schoolLevelType.equals("préscolaire")) {
                    return "Preschool";
                }
                return schoolLevelType;
            case "ar":
                if (schoolLevelType.equals("primaire")) {
                    return "الابتدائية";
                } else if (schoolLevelType.equals("secondaire")) {
                    return "الثانوية";
                } else if (schoolLevelType.equals("universitaire")) {
                    return "الجامعة";
                } else if (schoolLevelType.equals("préscolaire")) {
                    return "التمهيدي";
                }
                return schoolLevelType;
            default:
                return schoolLevelType;
        }
    }

    private void sendNotificationEmailAsync(String recipient, String subject, String emailBody) {
        executorService.submit(() -> {
            try {
                log.debug("Sending notification email to: {}", recipient);
                mailSenderService.sendNewMail(recipient, subject, emailBody);
            } catch (Exception e) {
                log.error("Error sending notification email to: {}", recipient, e);
            }
        });
    }

    public List<DonorInfoDto> findDonorsForBeneficiary(Long beneficiaryId) {

        Beneficiary beneficiary = beneficiaryRepository.findById(beneficiaryId)
                .orElseThrow(() -> new EntityNotFoundException("Beneficiary not found with ID: " + beneficiaryId));

        List<TakenInCharge> takenInCharge = takenInChargeRepository.findByBeneficiaryId(beneficiaryId);

        return takenInCharge.stream()
                .flatMap(tk -> tk.getTakenInChargeDonors().stream())
                .map(takenInChargeDonor -> {
                    Donor donor = takenInChargeDonor.getDonor();
                    return DonorInfoDto.builder()
                            .donorId(donor.getId())
                            .donorCode(donor.getCode())
                            .donorType(donor instanceof DonorPhysical ? "Physical" : "Moral")
                            .firstName(donor instanceof DonorPhysical ? ((DonorPhysical) donor).getFirstName() : null)
                            .lastName(donor instanceof DonorPhysical ? ((DonorPhysical) donor).getLastName() : null)
                            .company(donor instanceof DonorMoral ? ((DonorMoral) donor).getCompany() : null)
                            .keepAnonymous(takenInChargeDonor.getKeepanonymous())
                            .build();
                })
                .collect(Collectors.toList());
    }

//    public File viewReportWithDonor(Long rapportId, Long donorId, String reportType, String language)
//            throws JRException, IOException, TechnicalException {
//
//        log.info("Génération du rapport PDF pour le rapport ID: {}", rapportId);
//
//        // Charger le modèle Jasper
//        InputStream jasperStream = getClass().getClassLoader().getResourceAsStream("Template/orphelin_fr.jrxml");
//        if (jasperStream == null) {
//            throw new FileNotFoundException("Template not found");
//        }
//
//        JasperReport jasperReport = JasperCompileManager.compileReport(jasperStream);
//
//        // Récupérer les informations du rapport
//        Rapport rapport = rapportRepository.findById(rapportId)
//                .orElseThrow(() -> new RuntimeException("Rapport non trouvé pour ID: " + rapportId));
//
//        RapportDto rapportDto = rapportMapper.toDto(rapport);
//
//        // Préparer les paramètres pour le rapport
//        Map<String, Object> parameters = prepareReportParametersWithDonor(rapportDto, donorId);
//        JRBeanCollectionDataSource dataSource = (JRBeanCollectionDataSource) parameters.get("REPORT_DATA_SOURCE");
//
//        // Générer le rapport Jasper
//        JasperPrint jasperPrint = JasperFillManager.fillReport(jasperReport, parameters, dataSource);
//
//        String tempDir = System.getProperty("java.io.tmpdir");
//
//        // Créer le fichier PDF dans ce dossier
//        File pdfFile = new File(tempDir, "report_" + UUID.randomUUID() + ".pdf");
//
//        try (FileOutputStream fos = new FileOutputStream(pdfFile)) {
//            JasperExportManager.exportReportToPdfStream(jasperPrint, fos);
//        }
//
//        log.info("Rapport PDF généré avec succès: {}", pdfFile.getAbsolutePath());
//
//        final String username = getUsernameFromJwt();
//        final CacheAdUserDTO cacheAdUserDTO = cacheAdUserService.getConnectedUser();
//        CacheAdUser cacheAdUser = cacheAdUserMapper.mapToEntity(cacheAdUserDTO);
//
//        AgendaRapport agendaRapport = agendaRapportRepository.findByRapportId(rapportId)
//                .orElseThrow(() -> new RuntimeException("AgendaRapport non trouvé pour le rapport ID: " + rapportId));
//
//        HistoryRapport historyRapport = HistoryRapport.builder()
//                .dateCommunicated(new Date())
//                .communicatedBy(username)
//                .rapport(rapport)
//                .beneficiary(rapport.getBeneficiary())
//                .agendaRapport(agendaRapport)
//                .donor(donorRepository.findById(donorId).orElseThrow(() -> new RuntimeException("Donor non trouvé")))
//                .build();
//
//        historyRapportRepository.save(historyRapport);
//
//        Correspondence correspondence = Correspondence.builder()
//                .date(new Date().toInstant()
//                        .atZone(ZoneId.systemDefault())
//                        .toLocalDate())
//                .affectedTo(cacheAdUser)
//                .subject("Rapport Kafalat")
//                .content("Le rapport Kafalat N° " + rapportDto.getNumberRapport() +"/" +agendaRapport.getYear() + " a été communiqué au donateur." )
//                .canalCommunicationId(7L)
//                .direction(Direction.OUTGOING)
//                .donor(donorRepository.findById(donorId).orElseThrow(() -> new RuntimeException("Donor non trouvé")))
//                .build();
//
//        correspondenceRepository.save(correspondence);
//
//        return pdfFile;
//    }


    private Map<String, Object> prepareReportParametersWithDonor(RapportDto rapportDtoByBeneficiary, Long donorId) throws IOException {
        Map<String, Object> parameters = new HashMap<>();

        Donor donor = donorRepository.findById(donorId)
                .orElseThrow(() -> new RuntimeException("Donor non trouvé pour ID: " + donorId));

        String donorFullName = "---";
        String donorFullNameAr = "---";
        if (donor instanceof DonorPhysical) {
            DonorPhysical donorPhysical = (DonorPhysical) donor;
            donorFullName = donorPhysical.getFirstName() + " " + donorPhysical.getLastName();
            donorFullNameAr = donorPhysical.getFirstNameAr() + " " + donorPhysical.getLastNameAr();
        } else {
            log.warn("Le donateur associé au rapport n'est pas un DonorPhysical !");
        }

        Date reportDate = rapportDtoByBeneficiary.getDateRapport();
        LocalDateTime localDateTime = reportDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        int month = localDateTime.getMonthValue();
        int year = localDateTime.getYear();
        AgendaRapport agendaRapport = agendaRapportRepository.findByRapportId(rapportDtoByBeneficiary.getId())
                .orElseThrow(() -> new RuntimeException("AgendaRapport non trouvé pour le rapport ID: " + rapportDtoByBeneficiary.getId()));

        Long reportNumber=agendaRapport.getNumberRapport();

        String numberRapport = "Rapport : " + reportNumber + " / " + year;
        String numberRapportAn = "Report : " + reportNumber + " / " + year;
        String numberRapportAr = " التقرير : " + year + " / " + reportNumber ;
        parameters.put("numberRapport", numberRapport);
        parameters.put("numberRapportAn", numberRapportAn);
        parameters.put("numberRapportAr", numberRapportAr);
        parameters.put("donorFullName", donorFullName);
        parameters.put("donorFullNameAr", donorFullNameAr);
        parameters.put("beneficiaryFullName", rapportDtoByBeneficiary.getBeneficiaryFirstName() + " " + rapportDtoByBeneficiary.getBeneficiaryLastName());
        parameters.put("beneficiaryFullNameAr", rapportDtoByBeneficiary.getBeneficiaryFirstNameAr() + " " + rapportDtoByBeneficiary.getBeneficiaryLastNameAr());
        parameters.put("beneficiaryBirthDate", rapportDtoByBeneficiary.getBeneficiaryBirthDate());
        parameters.put("beneficiaryCode", rapportDtoByBeneficiary.getBeneficiaryCode());
        parameters.put("beneficiaryAddress", rapportDtoByBeneficiary.getBeneficiaryAddress());
        CityDTO city = rapportDtoByBeneficiary.getCity().getId() != null
                ? refController.getParCity(rapportDtoByBeneficiary.getCity().getId()).getBody()
                : null;
        parameters.put("beneficiaryCity", city.getName() != null ? city.getName() : "");
        parameters.put("beneficiaryCityEn", city.getNameEn() != null ? city.getNameEn() : "");
        parameters.put("beneficiaryCityAr", city.getNameAr() != null ? city.getNameAr() : "");
        parameters.put("beneficiaryAddressAr", rapportDtoByBeneficiary.getBeneficiaryAddressAr());
        parameters.put("tutorFirstName", rapportDtoByBeneficiary.getTutorFirstName() + " " + rapportDtoByBeneficiary.getTutorLastName());
        parameters.put("tutorFirstNameAr", rapportDtoByBeneficiary.getTutorFirstNameAr() + " " + rapportDtoByBeneficiary.getTutorLastNameAr());
        parameters.put("tutorLastName", rapportDtoByBeneficiary.getTutorLastName());
        parameters.put("tutorLastNameAr", rapportDtoByBeneficiary.getTutorLastNameAr());
        ProfessionDTO pro = rapportDtoByBeneficiary.getProfession().getId() != null
                ? refController.getMetProfession(rapportDtoByBeneficiary.getProfession().getId()).getBody()
                : null;
        parameters.put("tutorProfession", pro != null && pro.getName() != null ? pro.getName() : "");
        parameters.put("tutorProfessionEn", pro != null && pro.getNameEn() != null ? pro.getNameEn() : "");
        parameters.put("tutorProfessionAr", pro != null && pro.getNameAr() != null ? pro.getNameAr(): "");
        AccommodationTypeDTO accommodation = refController.getMetAccommodationType(rapportDtoByBeneficiary.getAccommodationType().getId()).getBody();
        parameters.put("accomondationType", accommodation.getName() != null ? accommodation.getName() : "");
        parameters.put("accomondationTypeEn", accommodation.getNameEn() != null ? accommodation.getNameEn() : "");
        parameters.put("accomondationTypeAr", accommodation.getNameAr() != null ? accommodation.getNameAr() : "");
        parameters.put("activityEdEN", rapportDtoByBeneficiary.getActivityEducationalEn()  != null ? rapportDtoByBeneficiary.getActivityEducationalEn() : "");
        parameters.put("activityEdFr", rapportDtoByBeneficiary.getActivityEducationalFr() != null ? rapportDtoByBeneficiary.getActivityEducationalFr() : "");
        parameters.put("activityEdAr", rapportDtoByBeneficiary.getActivityEducationalAr() != null ? rapportDtoByBeneficiary.getActivityEducationalAr() : "");
        parameters.put("phoneNumber", rapportDtoByBeneficiary.getPhoneNumber() != null ? rapportDtoByBeneficiary.getPhoneNumber() : "");
        parameters.put("socialServiceEN", rapportDtoByBeneficiary.getSocialServiceEn() != null ? rapportDtoByBeneficiary.getSocialServiceEn() : "");
        parameters.put("socialServiceFr", rapportDtoByBeneficiary.getSocialServiceFr() != null ? rapportDtoByBeneficiary.getSocialServiceFr() : "");
        parameters.put("socialServiceAr", rapportDtoByBeneficiary.getSocialServiceAr() != null ? rapportDtoByBeneficiary.getSocialServiceAr() : "");
        parameters.put("recommendationEN", rapportDtoByBeneficiary.getRecommendationEn() != null ? rapportDtoByBeneficiary.getRecommendationEn() : "");
        parameters.put("recommendationFr", rapportDtoByBeneficiary.getRecommendationFr() != null ? rapportDtoByBeneficiary.getRecommendationFr() : "");
        parameters.put("recommendationAr", rapportDtoByBeneficiary.getRecommendationAr() != null ? rapportDtoByBeneficiary.getRecommendationAr() : "");
        FamilyRelationshipDTO familyRelationship = rapportDtoByBeneficiary.getFamilyRelationship().getId() != null
                ? refController.getMetFamilyRelationship(rapportDtoByBeneficiary.getFamilyRelationship().getId()).getBody()
                : null;
        parameters.put("familyRelation", familyRelationship != null && familyRelationship.getName() != null ? familyRelationship.getName() : "");
        parameters.put("familyRelationEn", familyRelationship != null && familyRelationship.getNameEn() != null ? familyRelationship.getNameEn() : "");
        parameters.put("familyRelationAr", familyRelationship != null && familyRelationship.getNameAr() != null ? familyRelationship.getNameAr() : "");
        parameters.put("numberOfFamily", rapportDtoByBeneficiary.getNumberOfFamilyMember());
        parameters.put("result", rapportDtoByBeneficiary.getResult());
        SchoolLevelDTO schoolLeve = refController.getParSchoolLevel(rapportDtoByBeneficiary.getSchoolLevel().getId()).getBody();
        parameters.put("schoolLevelType", schoolLeve.getType() != null ? schoolLeve.getType() : "");
        parameters.put("schoolName", schoolLeve.getName() != null ? schoolLeve.getName() : "");
        parameters.put("schoolNameEn", schoolLeve.getNameEn() != null ? schoolLeve.getName() : "");
        parameters.put("schoolNameAr", schoolLeve.getNameAr() != null ? schoolLeve.getNameAr() : "");

        byte[] img1 = null, img2 = null, img3 = null;
        String des1En = null, des1Ar = null,des1 = null, des2 = null, des2En = null, des2Ar = null,des3 = null,des3Ar = null,des3En = null;

        List<Map<String, Object>> data = new ArrayList<>();
        if (rapportDtoByBeneficiary.getPictures() != null && !rapportDtoByBeneficiary.getPictures().isEmpty()) {
            int count = 0;
            for (RapportPictureDTO picture : rapportDtoByBeneficiary.getPictures()) {
                try {
                    byte[] imageData = minioService.ReadFromMinIO(picture.getUrl(), false);
                    if (imageData.length == 0) {
                        throw new IOException("L'image récupérée est vide.");
                    }
                    log.info("Taille de l'image récupérée : {}", imageData.length);
                    ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(imageData);
                    BufferedImage bufferedImage = ImageIO.read(byteArrayInputStream);

                    if (bufferedImage == null) {
                        throw new IOException("L'image récupérée depuis MinIO est invalide.");
                    }
                    Map<String, Object> row = new HashMap<>();
                    row.put("pictureBase64", imageData);
                    data.add(row);
                    if (count == 0) {
                        img1 = imageData;
                        des1 = picture.getDescription();
                        des1En = picture.getDescriptionAng();
                        des1Ar = picture.getDescriptionAr();
                    } else if (count == 1) {
                        img2 = imageData;
                        des2 = picture.getDescription();
                        des2En = picture.getDescriptionAng();
                        des2Ar = picture.getDescriptionAr();
                    } else if (count == 2) {
                        img3 = imageData;
                        des3 = picture.getDescription();
                        des3En = picture.getDescriptionAng();
                        des3Ar = picture.getDescriptionAr();
                    }
                } catch (IOException | TechnicalException e) {
                    log.error("Erreur lors de la récupération ou du traitement de l'image : {}", picture.getUrl(), e);
                    Map<String, Object> row = new HashMap<>();
                    row.put("pictureBase64", null);
                    data.add(row);
                }
                count++;
            }
        }else{
            log.error("image est null");
            Map<String, Object> row = new HashMap<>();
            row.put("pictureBase64", null);
            data.add(row);
        }
        parameters.put("img1", img1);
        parameters.put("img2", img2);
        parameters.put("img3", img3);
        parameters.put("des1", des1 != null ? des1 : "");
        parameters.put("des1En", des1En != null ? des1En : "");
        parameters.put("des1Ar", des1Ar != null ? des1Ar : "");
        parameters.put("des2", des2 != null ? des2 : "");
        parameters.put("des2En", des2En != null ? des2En : "");
        parameters.put("des2Ar", des2Ar != null ? des2Ar : "");
        parameters.put("des3", des3 != null ? des3 : "");
        parameters.put("des3En", des3En != null ? des3En : "");
        parameters.put("des3Ar", des3Ar != null ? des3Ar : "");

        JRBeanCollectionDataSource dataSource = new JRBeanCollectionDataSource(data);
        parameters.put("REPORT_DATA_SOURCE", dataSource);

        return parameters;
    }


    public File viewReport(Long rapportId, String reportType, String language)
            throws JRException, IOException, TechnicalException {

        log.info("Génération du rapport PDF pour le rapport ID: {} avec langue: {}", rapportId, language);

        // Construire le chemin du fichier en fonction de la langue choisie
        String templatePath = String.format("Template/orphelin_%s.jrxml", language);

        // Charger le modèle Jasper
        InputStream jasperStream = getClass().getClassLoader().getResourceAsStream(templatePath);
        if (jasperStream == null) {
            throw new FileNotFoundException("Template introuvable pour la langue : " + language);
        }

        JasperReport jasperReport = JasperCompileManager.compileReport(jasperStream);

        // Récupérer les informations du rapport
        Rapport rapport = rapportRepository.findById(rapportId)
                .orElseThrow(() -> new RuntimeException("Rapport non trouvé pour ID: " + rapportId));

        RapportDto rapportDto = rapportMapper.toDto(rapport);

        // Préparer les paramètres pour le rapport
        Map<String, Object> parameters = prepareReportParameters(rapportDto);
        JRBeanCollectionDataSource dataSource = (JRBeanCollectionDataSource) parameters.get("REPORT_DATA_SOURCE");

        // Générer le rapport Jasper
        JasperPrint jasperPrint = JasperFillManager.fillReport(jasperReport, parameters, dataSource);

        String tempDir = System.getProperty("java.io.tmpdir");
        File pdfFile = new File(tempDir, "report_" + UUID.randomUUID() + ".pdf");

        try (FileOutputStream fos = new FileOutputStream(pdfFile)) {
            JasperExportManager.exportReportToPdfStream(jasperPrint, fos);
        }

        log.info("Rapport PDF généré avec succès: {}", pdfFile.getAbsolutePath());
        return pdfFile;
    }


    public File viewReportWithDonor(Long rapportId, Long donorId, String reportType, String language)
            throws JRException, IOException, TechnicalException {

        log.info("Génération du rapport PDF pour le rapport ID: {} avec langue: {}", rapportId, language);

        // Construire le chemin du fichier en fonction de la langue
        String templatePath = String.format("Template/orphelin_%s.jrxml", language);

        // Charger le modèle Jasper
        InputStream jasperStream = getClass().getClassLoader().getResourceAsStream(templatePath);
        if (jasperStream == null) {
            throw new FileNotFoundException("Template introuvable pour la langue : " + language);
        }

        JasperReport jasperReport = JasperCompileManager.compileReport(jasperStream);

        // Récupérer les informations du rapport
        Rapport rapport = rapportRepository.findById(rapportId)
                .orElseThrow(() -> new RuntimeException("Rapport non trouvé pour ID: " + rapportId));

        RapportDto rapportDto = rapportMapper.toDto(rapport);

        // Préparer les paramètres pour le rapport
        Map<String, Object> parameters = prepareReportParametersWithDonor(rapportDto, donorId);
        JRBeanCollectionDataSource dataSource = (JRBeanCollectionDataSource) parameters.get("REPORT_DATA_SOURCE");

        // Générer le rapport Jasper
        JasperPrint jasperPrint = JasperFillManager.fillReport(jasperReport, parameters, dataSource);

        String tempDir = System.getProperty("java.io.tmpdir");

        File pdfFile = new File(tempDir, "report_" + UUID.randomUUID() + ".pdf");

        try (FileOutputStream fos = new FileOutputStream(pdfFile)) {
            JasperExportManager.exportReportToPdfStream(jasperPrint, fos);
        }

        log.info("Rapport PDF généré avec succès: {}", pdfFile.getAbsolutePath());

        // Enregistrement de l'historique et de la correspondance
        final String username = getUsernameFromJwt();
        final CacheAdUserDTO cacheAdUserDTO = cacheAdUserService.getConnectedUser();
        CacheAdUser cacheAdUser = cacheAdUserMapper.mapToEntity(cacheAdUserDTO);

        AgendaRapport agendaRapport = agendaRapportRepository.findByRapportId(rapportId)
                .orElseThrow(() -> new RuntimeException("AgendaRapport non trouvé pour le rapport ID: " + rapportId));

        HistoryRapport historyRapport = HistoryRapport.builder()
                .dateCommunicated(new Date())
                .communicatedBy(username)
                .rapport(rapport)
                .beneficiary(rapport.getBeneficiary())
                .agendaRapport(agendaRapport)
                .donor(donorRepository.findById(donorId).orElseThrow(() -> new RuntimeException("Donor non trouvé")))
                .build();

        historyRapportRepository.save(historyRapport);

        Correspondence correspondence = Correspondence.builder()
                .date(new Date().toInstant().atZone(ZoneId.systemDefault()).toLocalDate())
                .affectedTo(cacheAdUser)
                .subject("Rapport Kafalat")
                .content("Le rapport Kafalat N° " + rapportDto.getNumberRapport() + "/" + agendaRapport.getYear() +
                        " a été communiqué au donateur.")
                .canalCommunicationId(7L)
                .direction(Direction.OUTGOING)
                .donor(donorRepository.findById(donorId).orElseThrow(() -> new RuntimeException("Donor non trouvé")))
                .build();

        correspondenceRepository.save(correspondence);

        return pdfFile;
    }



}
