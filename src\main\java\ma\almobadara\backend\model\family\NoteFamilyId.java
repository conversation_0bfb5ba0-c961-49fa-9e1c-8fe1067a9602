package ma.almobadara.backend.model.family;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Objects;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class NoteFamilyId implements Serializable {

    private Long family;
    private Long note;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        NoteFamilyId that = (NoteFamilyId) o;
        return Objects.equals(family, that.family) && Objects.equals(note, that.note);
    }

    @Override
    public int hashCode() {
        return Objects.hash(family, note);
    }

}
