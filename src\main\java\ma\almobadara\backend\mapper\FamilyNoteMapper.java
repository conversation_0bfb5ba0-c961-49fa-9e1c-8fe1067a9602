package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.family.NoteFamilyDTO;
import ma.almobadara.backend.model.family.NoteFamily;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface FamilyNoteMapper {

	NoteFamilyDTO familyNoteToFamilyNoteDTO(NoteFamily familyNote);

	Iterable<NoteFamilyDTO> familyNoteToFamilyNoteDTO(Iterable<NoteFamily> familyNotes);

	NoteFamily familyNoteDTOToFamilyNote(NoteFamilyDTO familyNoteDTO);

	Iterable<NoteFamily> familyNoteDTOToFamilyNote(Iterable<NoteFamilyDTO> familyNoteDTOS);
}
