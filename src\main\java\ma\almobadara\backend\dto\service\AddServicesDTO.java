package ma.almobadara.backend.dto.service;

import lombok.*;

import java.math.BigDecimal;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@Builder
public class AddServicesDTO {
    private Long id;
    private BigDecimal costs;
    private Boolean priority;
    private Boolean propositionSystem;
    private Long amountPerBeneficiary;
    private Boolean statutIsActif;
    private Long serviceCategoryId;
    private Long serviceCategoryTypeId;
    private String commentaire;
    private Boolean isDedicatedToEps;
    private String collectionType;
    private String distributionType;
    private Long epsId;

}
