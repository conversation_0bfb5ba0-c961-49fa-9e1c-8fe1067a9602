package ma.almobadara.backend.model.beneficiary;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
public class BeneficiaryAllergy {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    protected Long id;
    private Long allergyId;
    @ManyToOne
    @JoinColumn(name = "beneficiaryId")
    private Beneficiary beneficiary;

}
