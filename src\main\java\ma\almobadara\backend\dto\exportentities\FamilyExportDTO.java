package ma.almobadara.backend.dto.exportentities;

import lombok.*;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class FamilyExportDTO {
    private String code;
    private Instant createdAt;
    private String familyName;
    private String tutorName;
    private String numTel;
    private String tutorAdress;
    private String tutorCity;
    private String tutorRegion;
    private String tutorCountry;
    private int numberOfFamilyMembers;
    private long numberOfBeneficiaries;
    private List<String> beneficiariesServices;

    public String getFormattedCreatedAt() {
        if (createdAt != null) {
            LocalDateTime localDateTime = LocalDateTime.ofInstant(createdAt, ZoneId.systemDefault());
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss");
            return localDateTime.format(formatter);
        } else {
            return "";
        }
    }
}
