package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.caisse.BudgetLineRequest;
import ma.almobadara.backend.dto.donation.BudgetLineDTO;
import ma.almobadara.backend.model.donation.BudgetLine;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface BudgetLineRequestMapper {

    List<BudgetLineRequest> budgetLinesToBudgetLineRequests(List<BudgetLine> budgetLines);


}
