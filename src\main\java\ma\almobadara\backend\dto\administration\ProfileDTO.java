package ma.almobadara.backend.dto.administration;

import lombok.Data;
import ma.almobadara.backend.enumeration.Functionality;
import ma.almobadara.backend.enumeration.Module;

import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Map;

@Data
public class ProfileDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    private Long id;
    private String nameProfile;
    private Map<Module, ArrayList<Functionality>> moduleFunctionalities;
}
