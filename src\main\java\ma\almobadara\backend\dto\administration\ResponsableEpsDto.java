package ma.almobadara.backend.dto.administration;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import ma.almobadara.backend.dto.referentiel.TypeIdentityDTO;
import ma.almobadara.backend.model.administration.Eps;
import ma.almobadara.backend.dto.referentiel.DonorContactFunctionDTO;
import org.springframework.web.multipart.MultipartFile;


@Getter
@Setter
public class ResponsableEpsDto {
    private Long id;

    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private MultipartFile picture;
    private String firstName;
    private String lastName;
    private String firstNameAr;
    private String lastNameAr;
    private String email;
    private String phoneNumber;
    private String identityCode;
    private TypeIdentityDTO typeIdentity;
    private String comment;
    private DonorContactFunctionDTO functionContact;
    private String pictureUrl;
    private Eps eps;
    private String pictureBase64;

}
