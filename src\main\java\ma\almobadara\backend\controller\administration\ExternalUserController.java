package ma.almobadara.backend.controller.administration;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.config.HasAccessToModule;
import ma.almobadara.backend.dto.administration.ExternalUserDTO;
import ma.almobadara.backend.dto.administration.ExternalUserInviteDTO;
import ma.almobadara.backend.enumeration.Functionality;
import ma.almobadara.backend.enumeration.Module;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.exceptions.UserAlreadyExistsException;
import ma.almobadara.backend.exceptions.UserNotFoundException;
import ma.almobadara.backend.service.administration.CacheAdUserService;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

import static ma.almobadara.backend.service.administration.CacheAdUserService.logUserInfo;

@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/api/external-users")
public class ExternalUserController {

    private final CacheAdUserService cacheAdUserService;

    @PostMapping(produces = MediaType.APPLICATION_JSON_VALUE)
    @HasAccessToModule(modules = {Module.USER}, functionalities = {Functionality.CREATE})
    @Operation(summary = "Register External User", description = "Register a new external user by email", tags = {"User Management"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "User registered successfully"),
            @ApiResponse(responseCode = "404", description = "User not found in Azure AD"),
            @ApiResponse(responseCode = "409", description = "User already exists"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<Object> registerExternalUser(@RequestBody ExternalUserDTO userDTO) {
        logUserInfo("registerExternalUser", "email: " + userDTO.getEmail());

        try {
            cacheAdUserService.loadAndSaveAdUserByEmail(userDTO.getEmail(), userDTO.getRoleId());
            log.info("External user registered successfully with email: {}", userDTO.getEmail());
            return ResponseEntity.ok(Map.of("message", "User registered successfully"));
        } catch (UserNotFoundException ex) {
            log.error("User not found in Azure AD with email: {}", userDTO.getEmail());
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(Map.of("error", "User not found", "message", ex.getMessage()));
        } catch (UserAlreadyExistsException ex) {
            log.error("User already exists with email: {}", userDTO.getEmail());
            return ResponseEntity.status(HttpStatus.CONFLICT)
                    .body(Map.of("error", "User already exists", "message", ex.getMessage()));
        } catch (Exception ex) {
            log.error("Error registering external user with email {}: {}", userDTO.getEmail(), ex.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Internal server error", "message", ex.getMessage()));
        }
    }

    @PostMapping(value = "/invite", produces = MediaType.APPLICATION_JSON_VALUE)
    @HasAccessToModule(modules = {Module.USER}, functionalities = {Functionality.CREATE})
    @Operation(summary = "Invite External User", description = "Invite a new external user to Azure AD by email", tags = {"User Management"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "User invited successfully"),
            @ApiResponse(responseCode = "409", description = "User already exists"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<Object> inviteExternalUser(@RequestBody ExternalUserInviteDTO inviteDTO) {
        logUserInfo("inviteExternalUser", "email: " + inviteDTO.getEmail());
        
        try {
            // The redirect URL is where the user will be sent after accepting the invitation
            String redirectUrl = inviteDTO.getRedirectUrl() != null ? 
                                 inviteDTO.getRedirectUrl() : 
                                 "https://your-application-url.com";
                             
            cacheAdUserService.inviteExternalUserByEmail(inviteDTO.getEmail(), inviteDTO.getRoleId(), redirectUrl);
            
            log.info("External user invited successfully with email: {}", inviteDTO.getEmail());
            return ResponseEntity.ok(Map.of(
                "message", "User invitation sent successfully",
                "email", inviteDTO.getEmail()
            ));
        } catch (UserAlreadyExistsException ex) {
            log.error("User already exists with email: {}", inviteDTO.getEmail());
            return ResponseEntity.status(HttpStatus.CONFLICT)
                    .body(Map.of("error", "User already exists", "message", ex.getMessage()));
        } catch (Exception ex) {
            log.error("Error inviting external user with email {}: {}", inviteDTO.getEmail(), ex.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Internal server error", "message", ex.getMessage()));
        }
    }
}
