package ma.almobadara.backend.model.donation;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class DonationProductNature {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    protected Long id;
    @ManyToOne
    @JoinColumn(name = "donation_id")
    private Donation donation;
    private double unitPrice;
    private int quantity;
    @Column(nullable = false)
    private Long productNatureId;
    private Long productUnitId;

}
