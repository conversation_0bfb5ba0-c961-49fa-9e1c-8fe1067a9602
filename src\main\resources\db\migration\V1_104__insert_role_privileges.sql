ALTER SEQUENCE role_privilege_id_seq RESTART WITH 1;

INSERT INTO role_privilege (role_id, privilege_id, feature_id, creation_date, update_date)
SELECT 1, p.id, f.id, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
FROM privilege p, feature f
WHERE p.id BETWEEN 1 AND 9
  AND f.id BETWEEN 1 AND 4;

INSERT INTO role_privilege (role_id, privilege_id, feature_id, creation_date, update_date)
SELECT 2, p.id, f.id, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
FROM privilege p, feature f
WHERE p.id BETWEEN 1 AND 6
  AND f.id BETWEEN 1 AND 4;

INSERT INTO role_privilege (role_id, privilege_id, feature_id, creation_date, update_date)
SELECT 3, p.id, f.id, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
FROM privilege p, feature f
WHERE p.id BETWEEN 1 AND 9
  AND f.id BETWEEN 1 AND 4;

INSERT INTO role_privilege (role_id, privilege_id, feature_id, creation_date, update_date)
SELECT 4, p.id, f.id, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
FROM privilege p, feature f
WHERE p.id BETWEEN 1 AND 9
  AND f.id BETWEEN 1 AND 4;
