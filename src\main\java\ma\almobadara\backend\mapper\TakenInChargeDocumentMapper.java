package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.takenInCharge.DocumentTakenInChargeDTO;
import ma.almobadara.backend.dto.takenInCharge.TakenInChargeDTO;
import ma.almobadara.backend.model.takenInCharge.DocumentTakenInCharge;
import ma.almobadara.backend.model.takenInCharge.TakenInCharge;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface TakenInChargeDocumentMapper {

	DocumentTakenInChargeDTO takenInChargeDocumentToTakenInChargeDocumentDTO(DocumentTakenInCharge takenInChargeDocument);

	Iterable<DocumentTakenInChargeDTO> takenInChargeDocumentToTakenInChargeDocumentDTO(Iterable<DocumentTakenInCharge> takenInChargeDocument);

    DocumentTakenInCharge takenInChargeDocumentDTOToTakenInChargeDocument(DocumentTakenInChargeDTO takenInChargeDocumentDTO);

	Iterable<DocumentTakenInCharge> takenInChargeDocumentDTOToTakenInChargeDocument(Iterable<DocumentTakenInChargeDTO> takenInChargeDocumentDTO);

	@Mapping(target = "services", ignore = true)
	@Mapping(target = "status", ignore = true)
	@Mapping(target = "takenInChargeDonors", ignore = true)
	@Mapping(target = "takenInChargeBeneficiaries", ignore = true)
	TakenInChargeDTO takenInChargeToTakenInChargeDTO(TakenInCharge takenInCharge);

}
