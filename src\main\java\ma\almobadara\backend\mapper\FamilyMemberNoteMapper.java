package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.family.NoteFamilyMemberDTO;
import ma.almobadara.backend.model.family.NoteFamilyMember;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface FamilyMemberNoteMapper {

	NoteFamilyMemberDTO familyMemberNoteToFamilyMemberNoteDTO(NoteFamilyMember familyMemberNote);

	Iterable<NoteFamilyMemberDTO> familyMemberNoteToFamilyMemberNoteDTO(Iterable<NoteFamilyMember> familyMemberNotes);

	NoteFamilyMember familyMemberNoteDTOToFamilyMemberNote(NoteFamilyMemberDTO familyMemberNoteDTO);

	Iterable<NoteFamilyMember> familyMemberNoteDTOToFamilyMemberNote(Iterable<NoteFamilyMemberDTO> familyMemberNoteDTOS);
}
