package ma.almobadara.backend.dto.aideComplemenatire;

import lombok.*;
import ma.almobadara.backend.model.service.Services;

import java.time.LocalDateTime;
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@Builder
public class AideComplementaireDonateurDTO {
    private Long id;
    private String name;
    private Services services;
    private Double amount;
    private String statut;
    private boolean typeDonation;
    private LocalDateTime execuionDate;
}
