package ma.almobadara.backend.repository.donor;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import ma.almobadara.backend.dto.referentiel.CityDTO;
import ma.almobadara.backend.dto.donor.DonorDTO;
import ma.almobadara.backend.dto.referentiel.DonorStatusDTO;
import ma.almobadara.backend.model.donor.Donor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DonorCustomizedRepositoryImpl implements DonorCustomizedRepository{
    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    DonorRepository donorRepository;

    @Override
    public Page<Donor> filterDonor(Pageable pageable, DonorDTO donorDTO, String columnName, String sortType, String type, String donorName, String phone, String mail, List<CityDTO> cityDTOList, List<DonorStatusDTO> StatusDTOList) {
        Page<Donor> listDonors = null;

        StringBuilder queryBuilder = new StringBuilder("select d.* ");
        StringBuilder countBuilder = new StringBuilder("select count(d.id) from donor d ");
        StringBuilder likeBuilder = new StringBuilder("%");
        StringBuilder andBuilder = new StringBuilder("and ");
        StringBuilder orBuilder = new StringBuilder("or ");

        queryBuilder.append("from donor d ");

        queryBuilder.append("LEFT JOIN donor_moral m ON d.id = m.id ");
        queryBuilder.append("LEFT JOIN donor_physical p ON d.id = p.id ");
        queryBuilder.append("LEFT JOIN donor_contact c ON c.donor_id = m.id ");
        queryBuilder.append("where 1 = 1 ");


/*************************************************Recherche Multiple******************************************************************************/

        if(donorDTO.getCode() != null){
            queryBuilder.append(andBuilder);
            queryBuilder.append("code like '");
            queryBuilder.append(donorDTO.getCode());
            queryBuilder.append( "' ");

        }



        if(donorDTO.getStatus()!= null && donorDTO.getStatus().getId() != null) {
            queryBuilder.append(andBuilder);
            queryBuilder.append("donor_status_id=" + donorDTO.getStatus().getId());
            queryBuilder.append(" ");

        }
        if(donorDTO.getCity()!= null && donorDTO.getCity().getId() != null) {
            queryBuilder.append(andBuilder);
            queryBuilder.append("city_id=" + donorDTO.getCity().getId());
            queryBuilder.append(" ");
        }
        if( type != null && !type.isEmpty()) {
            if(type.equals("DonorMoral")){
                queryBuilder.append(andBuilder);
                queryBuilder.append("m.id=d.id " );
                queryBuilder.append(" ");
            }
            else{
                queryBuilder.append(andBuilder);
                queryBuilder.append("p.id=d.id " );
                queryBuilder.append(" ");
            }

        }
        if( phone != null && !phone.isEmpty()) {

            queryBuilder.append(andBuilder);
            queryBuilder.append(" (c.phone_number like '"+phone+"' ");
            queryBuilder.append(orBuilder);
            queryBuilder.append(" p.phone_number like '"+phone+"' )");
            queryBuilder.append(" ");

        }
        if( mail!= null && !mail.isEmpty()) {

            queryBuilder.append(andBuilder);
            queryBuilder.append(" (c.email like '"+mail+"' ");
            queryBuilder.append(orBuilder);
            queryBuilder.append(" p.email like '"+mail+"' )");
            queryBuilder.append(" ");

        }
        if( donorName!= null && !donorName.isEmpty()) {

            queryBuilder.append(andBuilder);
            queryBuilder.append(" ( CONCAT(p.first_name,' ', p.last_name) like '"+donorName+"' ");
            queryBuilder.append(orBuilder);
            queryBuilder.append("CONCAT(m.company,' ', m.short_company) like '"+donorName+"' ) ");
            queryBuilder.append(" ");

        }
        if(columnName != null && !columnName.isEmpty() && sortType != null && !sortType.isEmpty()) {
            if (columnName.equals("statut")) {

                queryBuilder.append("order by donor_status_id ");
                queryBuilder.append(sortType);

            }else if (columnName.equals("nameCity")) {

                queryBuilder.append("order by city_id ");
                queryBuilder.append(sortType);

            }
            else if (columnName.equals("telephone")) {

                queryBuilder.append("order by trim(CONCAT(p.phone_number,c.phone_number))");
                queryBuilder.append(sortType);


            }
            else if (columnName.equals("email")) {

                queryBuilder.append("order by trim(CONCAT(p.email,c.email)) ");
                queryBuilder.append(sortType);


            }
            else if (columnName.equals("donorName")) {

                queryBuilder.append("order by trim(CONCAT(p.last_name,' ', p.first_name,m.company,' ',m.short_company)) ");
                queryBuilder.append(sortType);


            }

            else {
                queryBuilder.append("order by ");
                queryBuilder.append(columnName).append(" ");
                queryBuilder.append(sortType);
            }
        }

        if(donorDTO.getId()!= null && donorDTO.getId() != null) {

            queryBuilder.append(" ");
        }
        else{
            listDonors = donorRepository.findAll(pageable);
        }
/************************************************ End Recherche Multiple *****************************************************************************/

        Query filterQuery = entityManager.createNativeQuery(queryBuilder.toString(), Donor.class);
        filterQuery.setFirstResult((pageable.getPageNumber()) * pageable.getPageSize());
        filterQuery.setMaxResults(pageable.getPageSize());
        List <Donor> donorList = filterQuery.getResultList();

        return new PageImpl<>(donorList, pageable,  listDonors.getTotalElements());
    }

}
