package ma.almobadara.backend.dto.dashboard;

import lombok.*;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class ActiveKafalatesByMonthAndTypeDTO {

    private Integer month;
    private Integer year;
    private String sex;
    private Long cityId;
    private Long age;
//    private String city;
//    private String region;
    private Long servicesId;
    private Long totalActiveKafalates;
    private Long totalActiveOrphans;

    public ActiveKafalatesByMonthAndTypeDTO(Long servicesId, Integer month, Integer year, Long totalActiveKafalates) {
        this.month = month;
        this.year = year;
        this.servicesId = servicesId;
        this.totalActiveKafalates = totalActiveKafalates;
    }

    public ActiveKafalatesByMonthAndTypeDTO(String sex, Integer month, Integer year, Long totalActiveOrphans) {
        this.sex = sex;
        this.month = month;
        this.year = year;
        this.totalActiveOrphans = totalActiveOrphans;
    }

}
