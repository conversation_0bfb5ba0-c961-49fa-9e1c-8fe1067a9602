package ma.almobadara.backend.model.aideComplemenatire;

import jakarta.persistence.*;
import lombok.*;
import ma.almobadara.backend.enumeration.AideComplementaireStatut;
import ma.almobadara.backend.model.beneficiary.Beneficiary;
import ma.almobadara.backend.model.communs.Document;
import ma.almobadara.backend.model.service.Services;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AideComplementaire {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String name;
    @Column(unique = true)
    private String code;
    private String statut;
    private Double montantPrevu;
    private String slogan;
    private Double montantExecuter;
    @ManyToOne
    @JoinColumn(name = "service_id")
    private Services service;
    private LocalDateTime datePlanification;
    @CreationTimestamp
    @Column(updatable = false, nullable = false)
    protected Instant createdAt;
    @UpdateTimestamp
    protected Instant modifiedAt;
    private LocalDateTime dateExecution;
    private LocalDateTime dateDebut;
    private LocalDateTime dateFin;
    private boolean archived;
    @Column(columnDefinition = "TEXT")
    private String commentaire;
    @OneToMany(mappedBy = "aideComplementaire", cascade = CascadeType.ALL)
    private List<AideComplementaireDonorBeneficiary> aideComplementaireDonorBeneficiaries;
    @Column(name = "costs")
    private BigDecimal costs;
    @Column(name = "priority")
    private Boolean priority;
    @Column(name = "proposition_system")
    private Boolean propositionSystem;
    @Column(name = "amount_per_beneficiary")
    private Double amountPerBeneficiary;

    @ManyToMany(mappedBy = "aideComplementaires")
    private List<Beneficiary> beneficiaries;

    @ManyToOne
    @JoinColumn(name = "document_id", referencedColumnName = "id")
    private Document document;

    private LocalDateTime dateCloture;

    public void updateStatut() {
        if (this.dateExecution == null) {
            LocalDateTime now = LocalDateTime.now();
            if (now.isBefore(this.dateDebut)) {
                this.statut = AideComplementaireStatut.PLANIFIER.getValue();
            } else if (now.isAfter(this.dateDebut) && now.isBefore(this.dateFin)) {
                this.statut = AideComplementaireStatut.ENCOURS.getValue();
            } else if (now.isAfter(this.dateFin)) {
                this.statut = AideComplementaireStatut.ENATTENTEDEXECUTION.getValue();
            }
        }
    }

    public void setDateDebut(LocalDateTime dateDebut) {
        this.dateDebut = dateDebut;
        updateStatut();
    }

    public void setDateFin(LocalDateTime dateFin) {
        this.dateFin = dateFin;
        updateStatut();
    }

}
