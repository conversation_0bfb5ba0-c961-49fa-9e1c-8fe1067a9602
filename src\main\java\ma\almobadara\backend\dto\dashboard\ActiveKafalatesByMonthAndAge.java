package ma.almobadara.backend.dto.dashboard;

import lombok.*;

import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class ActiveKafalatesByMonthAndAge {

    private Integer month;
    private Integer year;
    private String sex;
    private Long cityId;
    private Long age;
    private Date birthDate;
    private Long servicesId;
    private Long totalActiveKafalates;
    private Long totalActiveOrphans;

    public ActiveKafalatesByMonthAndAge(Date birthDate, Integer month, Integer year, Long totalActiveOrphans) {
        this.month = month;
        this.year = year;
        this.birthDate = birthDate;
        this.totalActiveOrphans = totalActiveOrphans;
    }

}
