package ma.almobadara.backend.mapper;

import com.microsoft.graph.models.User;
import ma.almobadara.backend.dto.administration.CacheAdUserDTO;
import ma.almobadara.backend.model.administration.CacheAdUser;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring")
public interface CacheAdUserMapper {


    @Mapping(source = "id", target = "azureDirectoryId")
    @Mapping(source = "givenName", target = "firstName")
    @Mapping(source = "surname", target = "lastName")
    @Mapping(source = "userPrincipalName", target = "mail")
    @Mapping(target = "id", ignore = true)
    CacheAdUser mapGraphUserToCacheAdUser(User user);

    List<CacheAdUser> mapGraphUserToCacheAdUser(Iterable<User> iterable);

    @Mapping(source = "id", target = "id")
    @Mapping(source = "firstName", target = "firstName")
    @Mapping(source = "lastName", target = "lastName")
    @Mapping(source = "mail", target = "mail")
    @Mapping(source = "azureDirectoryId", target = "azureDirectoryId")
    @Mapping(source = "role", target = "role")
    @Mapping(source = "profile", target = "profile")
    @Mapping(source = "creationDate", target = "creationDate")
    @Mapping(source = "updateDate", target = "updateDate")
    @Mapping(source = "lastLoginInDate", target = "lastLoginInDate")
    @Mapping(target = "isDeleted", ignore = true)
    @Mapping(target = "mobilePhone", ignore = true)
    @Mapping(target = "jobTitle", ignore = true)
    CacheAdUser mapToEntity(CacheAdUserDTO dto);

}
