-- Create type_tag table
CREATE TABLE type_tag (
    id BIGSERIAL PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    read_only BOOLEAN DEFAULT FALSE,
    description VARCHAR(500)
);


-- Drop tag_document table if it exists
DROP TABLE IF EXISTS tag_document;

-- Create temporary table to store existing tag data
-- Drop existing tag table
DROP TABLE If EXISTS tag;

-- Recreate tag table with new structure
CREATE TABLE tag (
    id BIGSERIAL PRIMARY KEY,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    color VARCHAR(50),
    type_tag_id BIGINT,
    CONSTRAINT fk_type_tag FOREIGN KEY (type_tag_id) REFERENCES type_tag(id)
);


-- Create taggable table for polymorphic associations
CREATE TABLE taggable (
    id BIGSERIAL PRIMARY KEY,
    tag_id BIGINT NOT NULL,
    taggable_id BIGINT NOT NULL,
    taggable_type VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_tag FOREIGN KEY (tag_id) REFERENCES tag(id)
);

