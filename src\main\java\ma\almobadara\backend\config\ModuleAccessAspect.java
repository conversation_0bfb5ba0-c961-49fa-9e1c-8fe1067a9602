package ma.almobadara.backend.config;

import ma.almobadara.backend.dto.administration.CacheAdUserDTO;
import ma.almobadara.backend.enumeration.Functionality;
import ma.almobadara.backend.enumeration.Module;
import ma.almobadara.backend.exceptions.ForbiddenAccessException;
import ma.almobadara.backend.exceptions.UserNotFoundException;
import ma.almobadara.backend.service.administration.CacheAdUserService;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Aspect
@Component
public class ModuleAccessAspect {

    private final CacheAdUserService cacheAdUserService;

    @Autowired
    public ModuleAccessAspect(CacheAdUserService cacheAdUserService) {
        this.cacheAdUserService = cacheAdUserService;
    }

    @Before("@annotation(hasAccessToModule)")
    public void checkAccess(HasAccessToModule hasAccessToModule) throws Exception {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null) {
            throw new ForbiddenAccessException("Unauthorized access", HttpStatus.UNAUTHORIZED.value());
        }

        // Retrieve the module functionalities from the connected user's profile
        Map<Module, ArrayList<Functionality>> moduleFunctionalities = getModuleFunctionalities();

        // Check access for each module specified in the annotation
        Module[] modules = hasAccessToModule.modules();
        for (Module module : modules) {
            if (!moduleFunctionalities.containsKey(module)) {
                throw new ForbiddenAccessException("Access to module " + module + " is forbidden", HttpStatus.FORBIDDEN.value());
            }

            // Check if specific functionalities are specified
            if (hasAccessToModule.functionalities().length > 0) {
                // Check if the user has access to the specified functionalities within the module
                List<Functionality> functionalities = moduleFunctionalities.get(module);
                for (Functionality functionality : hasAccessToModule.functionalities()) {
                    if (!functionalities.contains(functionality)) {
                        throw new ForbiddenAccessException("Access to functionality " + functionality + " within module " + module + " is forbidden", HttpStatus.FORBIDDEN.value());
                    }
                }
            }
        }
    }

    private Map<Module, ArrayList<Functionality>> getModuleFunctionalities() {
        CacheAdUserDTO connectedUser = cacheAdUserService.getConnectedUser();
        if (connectedUser == null) {
            // Case: User not found
            throw new UserNotFoundException("User not found", 404);
        } else if (connectedUser.getProfile() == null || connectedUser.getProfile().getModuleFunctionalities() == null) {
            // Case: Forbidden (User found but profile or module functionalities not found)
            throw new ForbiddenAccessException("Access to this module is forbidden", HttpStatus.FORBIDDEN.value());
        }else {
            // Case: User found and has access
            return connectedUser.getProfile().getModuleFunctionalities();
        }
    }



}
