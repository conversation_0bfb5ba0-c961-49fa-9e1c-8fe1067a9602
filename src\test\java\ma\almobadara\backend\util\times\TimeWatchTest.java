package ma.almobadara.backend.util.times;

import org.junit.jupiter.api.Test;

import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.assertThat;

public class TimeWatchTest {

    @Test
    void testStart() {
        TimeWatch timeWatch = TimeWatch.start();
        assertThat(timeWatch).isNotNull();
    }

    @Test
    void testReset() {
        TimeWatch timeWatch = TimeWatch.start();
        long time1 = timeWatch.time();
        timeWatch.reset();
        long time2 = timeWatch.time();
        // Allow for a small difference due to system time
        assertThat(time2).isLessThan(time1);
    }

    @Test
    void testTime() {
        TimeWatch timeWatch = TimeWatch.start();
        try {
            // Introduce a small delay
            Thread.sleep(10);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        long time = timeWatch.time();
        assertThat(time).isGreaterThan(0);
    }

    @Test
    void testTimeWithTimeUnit() {
        TimeWatch timeWatch = TimeWatch.start();
        try {
            // Introduce a delay of 100ms
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        long timeMillis = timeWatch.time(TimeUnit.MILLISECONDS);
        // Allow for some imprecision
        assertThat(timeMillis).isGreaterThanOrEqualTo(100);
        long timeSeconds = timeWatch.time(TimeUnit.SECONDS);
        // Should be at least 0
        assertThat(timeSeconds).isGreaterThanOrEqualTo(0);
    }



    @Test
    void testToMS() {
        TimeWatch timeWatch = TimeWatch.start();
        try {
            Thread.sleep(150);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        String time = timeWatch.toMS();
        assertThat(time).matches("\\d+ ms");
    }
}
