CREATE SEQUENCE IF NOT EXISTS cache_ad_user_id_seq START WITH 1 INCREMENT BY 1;

CREATE TABLE action
(
    id             BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    date_entry     TIMESTAMP WITHOUT TIME ZONE,
    deadline       TIMESTAMP WITHOUT TIME ZONE,
    date_realize   TIMESTAMP WITHOUT TIME ZONE,
    content        VARCHAR(255),
    subject        VA<PERSON>HA<PERSON>(255),
    created_by_id  BIGINT,
    affected_to_id BIGINT,
    status         BIGINT,
    CONSTRAINT pk_action PRIMARY KEY (id)
);

CREATE TABLE action_beneficiary
(
    beneficiary_id BIGINT NOT NULL,
    action_id      BIGINT NOT NULL,
    CONSTRAINT pk_actionbeneficiary PRIMARY KEY (beneficiary_id, action_id)
);

CREATE TABLE action_donation
(
    donation_id BIGINT NOT NULL,
    action_id   BIGINT NOT NULL,
    CONSTRAINT pk_actiondonation PRIMARY KEY (donation_id, action_id)
);

CREATE TABLE action_donor
(
    donor_id  BIGINT NOT NULL,
    action_id BIGINT NOT NULL,
    CONSTRAINT pk_actiondonor PRIMARY KEY (donor_id, action_id)
);

CREATE TABLE action_family
(
    family_id BIGINT NOT NULL,
    action_id BIGINT NOT NULL,
    CONSTRAINT pk_actionfamily PRIMARY KEY (family_id, action_id)
);

CREATE TABLE action_family_member
(
    family_member_id BIGINT NOT NULL,
    action_id        BIGINT NOT NULL,
    CONSTRAINT pk_actionfamilymember PRIMARY KEY (family_member_id, action_id)
);

CREATE TABLE action_taken_in_charge
(
    taken_in_charge_id BIGINT NOT NULL,
    action_id          BIGINT NOT NULL,
    CONSTRAINT pk_actiontakenincharge PRIMARY KEY (taken_in_charge_id, action_id)
);

CREATE TABLE app_user
(
    id   BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    name VARCHAR(255),
    CONSTRAINT pk_app_user PRIMARY KEY (id)
);

CREATE TABLE audit_table
(
    audit_id        BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    canal           VARCHAR(255),
    app_name        VARCHAR(255),
    session_id      VARCHAR(255),
    activity        VARCHAR(255),
    operation       VARCHAR(255),
    sub_operation   VARCHAR(255),
    user_name       VARCHAR(255),
    user_ip         VARCHAR(255),
    user_agent      VARCHAR(255),
    message         VARCHAR(999),
    process_id      BIGINT,
    process         VARCHAR(255),
    machine         VARCHAR(255),
    machine_address VARCHAR(255),
    audit_time      TIMESTAMP WITHOUT TIME ZONE,
    initial_value   TEXT,
    new_value       TEXT,
    CONSTRAINT pk_audit_table PRIMARY KEY (audit_id)
);

CREATE TABLE bank_card
(
    id             BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    created_at     TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    modified_at    TIMESTAMP WITHOUT TIME ZONE,
    card_number    VARCHAR(255),
    account_number VARCHAR(255),
    delivery_date  date,
    expiry_date    date,
    status         VARCHAR(255),
    person_id      BIGINT,
    card_type_id   BIGINT,
    CONSTRAINT pk_bankcard PRIMARY KEY (id)
);

CREATE TABLE beneficiary
(
    id               BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    created_at       TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    modified_at      TIMESTAMP WITHOUT TIME ZONE,
    independent      BOOLEAN,
    eps_resident     BOOLEAN,
    archived         BOOLEAN,
    code             VARCHAR(255),
    added_year       VARCHAR(255),
    accounting_code  VARCHAR(255),
    person_id        BIGINT,
    action_status_id BIGINT,
    CONSTRAINT pk_beneficiary PRIMARY KEY (id)
);

CREATE TABLE beneficiary_allergy
(
    id             BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    allergy_id     BIGINT,
    beneficiary_id BIGINT,
    CONSTRAINT pk_beneficiaryallergy PRIMARY KEY (id)
);

CREATE TABLE beneficiary_disease
(
    id             BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    disease_id     BIGINT,
    beneficiary_id BIGINT,
    CONSTRAINT pk_beneficiarydisease PRIMARY KEY (id)
);

CREATE TABLE beneficiary_handicap
(
    id               BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    created_at       TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    modified_at      TIMESTAMP WITHOUT TIME ZONE,
    handicap_cause   VARCHAR(255),
    handicap_cost    DOUBLE PRECISION,
    handicap_type_id BIGINT,
    beneficiary_id   BIGINT,
    CONSTRAINT pk_beneficiaryhandicap PRIMARY KEY (id)
);

CREATE TABLE beneficiary_service
(
    id             BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    created_at     TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    modified_at    TIMESTAMP WITHOUT TIME ZONE,
    beneficiary_id BIGINT,
    service_id     BIGINT,
    status_id      BIGINT,
    CONSTRAINT pk_beneficiaryservice PRIMARY KEY (id)
);

CREATE TABLE beneficiary_year_count
(
    id    BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    year  VARCHAR(255),
    count BIGINT,
    CONSTRAINT pk_beneficiaryyearcount PRIMARY KEY (id)
);

CREATE TABLE cache_ad_user
(
    id                 BIGINT  NOT NULL,
    azure_directory_id VARCHAR(255),
    first_name         VARCHAR(255),
    last_name          VARCHAR(255),
    mail               VARCHAR(255),
    mobile_phone       VARCHAR(255),
    job_title          VARCHAR(255),
    is_deleted         BOOLEAN NOT NULL,
    role               VARCHAR(255),
    profile_id         BIGINT,
    creation_date      TIMESTAMP WITHOUT TIME ZONE,
    update_date        TIMESTAMP WITHOUT TIME ZONE,
    CONSTRAINT pk_cacheaduser PRIMARY KEY (id)
);

CREATE TABLE canal_communication
(
    id                     BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    canal_communication_id BIGINT,
    CONSTRAINT pk_canalcommunication PRIMARY KEY (id)
);

CREATE TABLE correspondence
(
    id                     BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    date                   date,
    direction              SMALLINT,
    content                VARCHAR(255),
    subject                VARCHAR(255),
    affected_to_id         BIGINT,
    canal_communication_id BIGINT,
    document_id            BIGINT,
    donor_id               BIGINT,
    CONSTRAINT pk_correspondence PRIMARY KEY (id)
);

CREATE TABLE disease_treatment
(
    id             BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    created_at     TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    modified_at    TIMESTAMP WITHOUT TIME ZONE,
    cost           DOUBLE PRECISION,
    type_id        BIGINT,
    beneficiary_id BIGINT,
    CONSTRAINT pk_diseasetreatment PRIMARY KEY (id)
);

CREATE TABLE document
(
    id               BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    label            VARCHAR(255),
    document_date    TIMESTAMP WITHOUT TIME ZONE,
    expiry_date      TIMESTAMP WITHOUT TIME ZONE,
    comment          VARCHAR(255),
    file_url         VARCHAR(255),
    file_name        VARCHAR(255),
    type_document_id BIGINT,
    CONSTRAINT pk_document PRIMARY KEY (id)
);

CREATE TABLE document_beneficiary
(
    document_id    BIGINT NOT NULL,
    beneficiary_id BIGINT NOT NULL,
    CONSTRAINT pk_documentbeneficiary PRIMARY KEY (document_id, beneficiary_id)
);

CREATE TABLE document_donation
(
    donation_id BIGINT NOT NULL,
    document_id BIGINT NOT NULL,
    CONSTRAINT pk_documentdonation PRIMARY KEY (donation_id, document_id)
);

CREATE TABLE document_donor
(
    donor_id    BIGINT NOT NULL,
    document_id BIGINT NOT NULL,
    CONSTRAINT pk_documentdonor PRIMARY KEY (donor_id, document_id)
);

CREATE TABLE document_education
(
    document_id  BIGINT NOT NULL,
    education_id BIGINT NOT NULL,
    CONSTRAINT pk_documenteducation PRIMARY KEY (document_id, education_id)
);

CREATE TABLE document_family
(
    family_id   BIGINT NOT NULL,
    document_id BIGINT NOT NULL,
    CONSTRAINT pk_documentfamily PRIMARY KEY (family_id, document_id)
);

CREATE TABLE document_family_member
(
    family_member_id BIGINT NOT NULL,
    document_id      BIGINT NOT NULL,
    CONSTRAINT pk_documentfamilymember PRIMARY KEY (family_member_id, document_id)
);

CREATE TABLE document_taken_in_charge
(
    taken_in_charge_id BIGINT NOT NULL,
    document_id        BIGINT NOT NULL,
    CONSTRAINT pk_documenttakenincharge PRIMARY KEY (taken_in_charge_id, document_id)
);

CREATE TABLE donation
(
    id                      BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    value                   DOUBLE PRECISION,
    reception_date          TIMESTAMP WITHOUT TIME ZONE,
    identified_donor        BOOLEAN                                 NOT NULL,
    unidentified_donor_name VARCHAR(255),
    comment                 VARCHAR(255),
    transaction_number      VARCHAR(255),
    type                    VARCHAR(255),
    value_currency          DOUBLE PRECISION,
    archived                BOOLEAN,
    created_at              TIMESTAMP WITHOUT TIME ZONE,
    canal_donation_id       BIGINT,
    currency_id             BIGINT,
    code                    VARCHAR(255),
    donor_id                BIGINT,
    CONSTRAINT pk_donation PRIMARY KEY (id)
);

CREATE TABLE donation_product_nature
(
    id                BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    donation_id       BIGINT,
    unit_price        DOUBLE PRECISION                        NOT NULL,
    quantity          INTEGER                                 NOT NULL,
    product_nature_id BIGINT                                  NOT NULL,
    product_unit_id   BIGINT,
    CONSTRAINT pk_donationproductnature PRIMARY KEY (id)
);

CREATE TABLE donor
(
    id                  BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    balance             DOUBLE PRECISION,
    identity_code       VARCHAR(255),
    address             VARCHAR(255),
    address_ar          VARCHAR(255),
    donor_status_id     BIGINT,
    action_status_id    BIGINT,
    city_id             BIGINT,
    created_at          TIMESTAMP WITHOUT TIME ZONE,
    deleted_at          TIMESTAMP WITHOUT TIME ZONE,
    code                VARCHAR(255),
    first_donation_year VARCHAR(255),
    CONSTRAINT pk_donor PRIMARY KEY (id)
);

CREATE TABLE donor_contact
(
    id                        BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    first_name                VARCHAR(255),
    last_name                 VARCHAR(255),
    first_name_ar             VARCHAR(255),
    last_name_ar              VARCHAR(255),
    sex                       VARCHAR(255),
    email                     VARCHAR(255),
    phone_number              VARCHAR(255),
    main_contact              BOOLEAN,
    birth_date                TIMESTAMP WITHOUT TIME ZONE,
    donor_contact_function_id BIGINT,
    donor_id                  BIGINT,
    CONSTRAINT pk_donorcontact PRIMARY KEY (id)
);

CREATE TABLE donor_contact_canal_communication
(
    id                     BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    canal_communication_id BIGINT,
    donor_contact_id       BIGINT,
    CONSTRAINT pk_donorcontactcanalcommunication PRIMARY KEY (id)
);

CREATE TABLE donor_contact_language_communication
(
    id                        BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    language_communication_id BIGINT,
    donor_contact_id          BIGINT,
    CONSTRAINT pk_donorcontactlanguagecommunication PRIMARY KEY (id)
);

CREATE TABLE donor_moral
(
    id                  BIGINT NOT NULL,
    company             VARCHAR(255),
    short_company       VARCHAR(255),
    type                VARCHAR(255),
    logo_url            VARCHAR(255),
    activity_sector_id  BIGINT,
    type_donor_moral_id BIGINT,
    CONSTRAINT pk_donormoral PRIMARY KEY (id)
);

CREATE TABLE donor_physical
(
    id            BIGINT NOT NULL,
    first_name    VARCHAR(255),
    last_name     VARCHAR(255),
    first_name_ar VARCHAR(255),
    last_name_ar  VARCHAR(255),
    sex           VARCHAR(255),
    email         VARCHAR(255),
    phone_number  VARCHAR(255),
    type          VARCHAR(255),
    picture_url   VARCHAR(255),
    type_identity BIGINT,
    profession_id BIGINT,
    CONSTRAINT pk_donorphysical PRIMARY KEY (id)
);

CREATE TABLE donor_physical_canal_communication
(
    id                     BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    canal_communication_id BIGINT,
    donor_physical_id      BIGINT,
    CONSTRAINT pk_donorphysicalcanalcommunication PRIMARY KEY (id)
);

CREATE TABLE donor_physical_language_communication
(
    id                        BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    language_communication_id BIGINT,
    donor_physical_id         BIGINT,
    CONSTRAINT pk_donorphysicallanguagecommunication PRIMARY KEY (id)
);

CREATE TABLE donor_year_count
(
    id    BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    year  VARCHAR(255),
    count BIGINT,
    CONSTRAINT pk_donoryearcount PRIMARY KEY (id)
);

CREATE TABLE education
(
    id              BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    created_at      TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    modified_at     TIMESTAMP WITHOUT TIME ZONE,
    education_type  VARCHAR(255),
    school_name     VARCHAR(255),
    school_name_ar  VARCHAR(255),
    succeed         BOOLEAN,
    mark            DOUBLE PRECISION,
    city_id         BIGINT,
    school_year_id  BIGINT,
    school_level_id BIGINT,
    honor_id        BIGINT,
    major_id        BIGINT,
    beneficiary_id  BIGINT,
    CONSTRAINT pk_education PRIMARY KEY (id)
);

CREATE TABLE eps_resident
(
    id             BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    created_at     TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    modified_at    TIMESTAMP WITHOUT TIME ZONE,
    eps_id         BIGINT,
    entry_date     TIMESTAMP WITHOUT TIME ZONE,
    release_date   TIMESTAMP WITHOUT TIME ZONE,
    beneficiary_id BIGINT,
    CONSTRAINT pk_eps_resident PRIMARY KEY (id)
);

CREATE TABLE external_income
(
    id               BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    created_at       TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    modified_at      TIMESTAMP WITHOUT TIME ZONE,
    amount           DOUBLE PRECISION,
    periodicity      INTEGER                                 NOT NULL,
    comment          VARCHAR(255),
    income_source_id BIGINT,
    family_member_id BIGINT,
    CONSTRAINT pk_externalincome PRIMARY KEY (id)
);

CREATE TABLE family
(
    id          BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    created_at  TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    modified_at TIMESTAMP WITHOUT TIME ZONE,
    code        VARCHAR(255),
    CONSTRAINT pk_family PRIMARY KEY (id)
);

CREATE TABLE family_member
(
    id                     BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    created_at             TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    modified_at            TIMESTAMP WITHOUT TIME ZONE,
    tutor                  BOOLEAN                                 NOT NULL,
    tutor_start_date       TIMESTAMP WITHOUT TIME ZONE,
    tutor_end_date         TIMESTAMP WITHOUT TIME ZONE,
    family_relationship_id BIGINT,
    code                   VARCHAR(255),
    person_id              BIGINT,
    family_id              BIGINT,
    CONSTRAINT pk_familymember PRIMARY KEY (id)
);

CREATE TABLE family_year_count
(
    id    BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    year  VARCHAR(255),
    count BIGINT,
    CONSTRAINT pk_familyyearcount PRIMARY KEY (id)
);

CREATE TABLE glasses
(
    id             BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    use_glasses    BOOLEAN,
    beneficiary_id BIGINT,
    CONSTRAINT pk_glasses PRIMARY KEY (id)
);

CREATE TABLE note
(
    id            BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    created_at    TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    modified_at   TIMESTAMP WITHOUT TIME ZONE,
    objet         VARCHAR(255),
    content       VARCHAR(255),
    created_date  TIMESTAMP WITHOUT TIME ZONE,
    created_by_id BIGINT,
    CONSTRAINT pk_note PRIMARY KEY (id)
);

CREATE TABLE note_beneficiary
(
    beneficiary_id BIGINT NOT NULL,
    note_id        BIGINT NOT NULL,
    CONSTRAINT pk_notebeneficiary PRIMARY KEY (beneficiary_id, note_id)
);

CREATE TABLE note_donation
(
    donation_id BIGINT NOT NULL,
    note_id     BIGINT NOT NULL,
    CONSTRAINT pk_notedonation PRIMARY KEY (donation_id, note_id)
);

CREATE TABLE note_donor
(
    donor_id BIGINT NOT NULL,
    note_id  BIGINT NOT NULL,
    CONSTRAINT pk_notedonor PRIMARY KEY (donor_id, note_id)
);

CREATE TABLE note_donor_contact
(
    id               BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    content          VARCHAR(255),
    donor_contact_id BIGINT,
    CONSTRAINT pk_notedonorcontact PRIMARY KEY (id)
);

CREATE TABLE note_education
(
    education_id BIGINT NOT NULL,
    note_id      BIGINT NOT NULL,
    CONSTRAINT pk_noteeducation PRIMARY KEY (education_id, note_id)
);

CREATE TABLE note_family
(
    family_id BIGINT NOT NULL,
    note_id   BIGINT NOT NULL,
    CONSTRAINT pk_notefamily PRIMARY KEY (family_id, note_id)
);

CREATE TABLE note_family_member
(
    family_member_id BIGINT NOT NULL,
    note_id          BIGINT NOT NULL,
    CONSTRAINT pk_notefamilymember PRIMARY KEY (family_member_id, note_id)
);

CREATE TABLE note_taken_in_charge
(
    taken_in_charge_id BIGINT NOT NULL,
    note_id            BIGINT NOT NULL,
    CONSTRAINT pk_notetakenincharge PRIMARY KEY (taken_in_charge_id, note_id)
);

CREATE TABLE person
(
    id               BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    created_at       TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    modified_at      TIMESTAMP WITHOUT TIME ZONE,
    picture_url      VARCHAR(255),
    first_name       VARCHAR(255),
    last_name        VARCHAR(255),
    first_name_ar    VARCHAR(255),
    last_name_ar     VARCHAR(255),
    sex              VARCHAR(255),
    email            VARCHAR(255),
    phone_number     VARCHAR(255),
    address          VARCHAR(255),
    address_ar       VARCHAR(255),
    deceased         BOOLEAN                                 NOT NULL,
    death_date       TIMESTAMP WITHOUT TIME ZONE,
    death_reason     VARCHAR(255),
    identity_code    VARCHAR(255),
    type_identity_id BIGINT,
    school_level_id  BIGINT,
    city_id          BIGINT,
    profession_id    BIGINT,
    birth_date       TIMESTAMP WITHOUT TIME ZONE,
    CONSTRAINT pk_person PRIMARY KEY (id)
);

CREATE TABLE profile
(
    id           BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    name_profile VARCHAR(255),
    is_deleted   BOOLEAN                                 NOT NULL,
    CONSTRAINT pk_profile PRIMARY KEY (id)
);

CREATE TABLE scholarship_beneficiary
(
    id             BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    created_at     TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    modified_at    TIMESTAMP WITHOUT TIME ZONE,
    scholarship_id BIGINT,
    amount         DOUBLE PRECISION,
    periodicity    INTEGER                                 NOT NULL,
    start_date     TIMESTAMP WITHOUT TIME ZONE,
    end_date       TIMESTAMP WITHOUT TIME ZONE,
    comment        VARCHAR(255),
    currency_id    BIGINT,
    value_currency DOUBLE PRECISION,
    beneficiary_id BIGINT,
    CONSTRAINT pk_scholarshipbeneficiary PRIMARY KEY (id)
);

CREATE TABLE taken_in_charge
(
    id         BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    code       VARCHAR(255),
    start_date TIMESTAMP WITHOUT TIME ZONE,
    end_date   TIMESTAMP WITHOUT TIME ZONE,
    service_id BIGINT,
    status_id  BIGINT,
    CONSTRAINT pk_takenincharge PRIMARY KEY (id)
);

CREATE TABLE taken_in_charge_beneficiary
(
    id                 BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    beneficiary_id     BIGINT,
    taken_in_charge_id BIGINT,
    CONSTRAINT pk_takeninchargebeneficiary PRIMARY KEY (id)
);

CREATE TABLE taken_in_charge_beneficiary_bank_card
(
    bank_card_id                   BIGINT NOT NULL,
    taken_in_charge_beneficiary_id BIGINT NOT NULL
);

CREATE TABLE taken_in_charge_donor
(
    id                 BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    keepanonymous      BOOLEAN,
    donor_id           BIGINT,
    taken_in_charge_id BIGINT,
    CONSTRAINT pk_takeninchargedonor PRIMARY KEY (id)
);

CREATE TABLE taken_in_charge_operation
(
    id                       BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    amount                   DOUBLE PRECISION                        NOT NULL,
    management_fees          DOUBLE PRECISION                        NOT NULL,
    planning_date            TIMESTAMP WITHOUT TIME ZONE,
    execution_date           TIMESTAMP WITHOUT TIME ZONE,
    closure_date             TIMESTAMP WITHOUT TIME ZONE,
    comment                  VARCHAR(255),
    code                     VARCHAR(255),
    taken_in_charge_donor_id BIGINT                                  NOT NULL,
    created_at               TIMESTAMP WITHOUT TIME ZONE,
    CONSTRAINT pk_takeninchargeoperation PRIMARY KEY (id)
);

CREATE TABLE taken_in_charge_year_count
(
    id    BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    year  VARCHAR(255),
    count BIGINT,
    CONSTRAINT pk_takeninchargeyearcount PRIMARY KEY (id)
);

CREATE TABLE user_profile_module_functionality
(
    profile_id                 BIGINT       NOT NULL,
    functionality              SMALLINT[] DEFAULT '{}',
    module_functionalities_key VARCHAR(255) NOT NULL,
    CONSTRAINT pk_user_profile_module_functionality PRIMARY KEY (profile_id, module_functionalities_key)
);

ALTER TABLE beneficiary
    ADD CONSTRAINT uc_beneficiary_code UNIQUE (code);

ALTER TABLE beneficiary
    ADD CONSTRAINT uc_beneficiary_person UNIQUE (person_id);

ALTER TABLE donation
    ADD CONSTRAINT uc_donation_code UNIQUE (code);

ALTER TABLE donor
    ADD CONSTRAINT uc_donor_code UNIQUE (code);

ALTER TABLE family
    ADD CONSTRAINT uc_family_code UNIQUE (code);

ALTER TABLE family_member
    ADD CONSTRAINT uc_familymember_code UNIQUE (code);

ALTER TABLE family_member
    ADD CONSTRAINT uc_familymember_person UNIQUE (person_id);

ALTER TABLE glasses
    ADD CONSTRAINT uc_glasses_beneficiaryid UNIQUE (beneficiary_id);

ALTER TABLE taken_in_charge
    ADD CONSTRAINT uc_takenincharge_code UNIQUE (code);

ALTER TABLE taken_in_charge_operation
    ADD CONSTRAINT uc_takeninchargeoperation_code UNIQUE (code);

ALTER TABLE action_beneficiary
    ADD CONSTRAINT FK_ACTIONBENEFICIARY_ON_ACTION FOREIGN KEY (action_id) REFERENCES action (id);

ALTER TABLE action_beneficiary
    ADD CONSTRAINT FK_ACTIONBENEFICIARY_ON_BENEFICIARY FOREIGN KEY (beneficiary_id) REFERENCES beneficiary (id);

ALTER TABLE action_donation
    ADD CONSTRAINT FK_ACTIONDONATION_ON_ACTION FOREIGN KEY (action_id) REFERENCES action (id);

ALTER TABLE action_donation
    ADD CONSTRAINT FK_ACTIONDONATION_ON_DONATION FOREIGN KEY (donation_id) REFERENCES donation (id);

ALTER TABLE action_donor
    ADD CONSTRAINT FK_ACTIONDONOR_ON_ACTION FOREIGN KEY (action_id) REFERENCES action (id);

ALTER TABLE action_donor
    ADD CONSTRAINT FK_ACTIONDONOR_ON_DONOR FOREIGN KEY (donor_id) REFERENCES donor (id);

ALTER TABLE action_family_member
    ADD CONSTRAINT FK_ACTIONFAMILYMEMBER_ON_ACTION FOREIGN KEY (action_id) REFERENCES action (id);

ALTER TABLE action_family_member
    ADD CONSTRAINT FK_ACTIONFAMILYMEMBER_ON_FAMILY_MEMBER FOREIGN KEY (family_member_id) REFERENCES family_member (id);

ALTER TABLE action_family
    ADD CONSTRAINT FK_ACTIONFAMILY_ON_ACTION FOREIGN KEY (action_id) REFERENCES action (id);

ALTER TABLE action_family
    ADD CONSTRAINT FK_ACTIONFAMILY_ON_FAMILY FOREIGN KEY (family_id) REFERENCES family (id);

ALTER TABLE action_taken_in_charge
    ADD CONSTRAINT FK_ACTIONTAKENINCHARGE_ON_ACTION FOREIGN KEY (action_id) REFERENCES action (id);

ALTER TABLE action_taken_in_charge
    ADD CONSTRAINT FK_ACTIONTAKENINCHARGE_ON_TAKEN_IN_CHARGE FOREIGN KEY (taken_in_charge_id) REFERENCES taken_in_charge (id);

ALTER TABLE action
    ADD CONSTRAINT FK_ACTION_ON_AFFECTED_TO FOREIGN KEY (affected_to_id) REFERENCES cache_ad_user (id);

ALTER TABLE action
    ADD CONSTRAINT FK_ACTION_ON_CREATED_BY FOREIGN KEY (created_by_id) REFERENCES cache_ad_user (id);

ALTER TABLE bank_card
    ADD CONSTRAINT FK_BANKCARD_ON_PERSON FOREIGN KEY (person_id) REFERENCES person (id);

ALTER TABLE beneficiary_allergy
    ADD CONSTRAINT FK_BENEFICIARYALLERGY_ON_BENEFICIARYID FOREIGN KEY (beneficiary_id) REFERENCES beneficiary (id);

ALTER TABLE beneficiary_disease
    ADD CONSTRAINT FK_BENEFICIARYDISEASE_ON_BENEFICIARY FOREIGN KEY (beneficiary_id) REFERENCES beneficiary (id);

ALTER TABLE beneficiary_handicap
    ADD CONSTRAINT FK_BENEFICIARYHANDICAP_ON_BENEFICIARYID FOREIGN KEY (beneficiary_id) REFERENCES beneficiary (id);

ALTER TABLE beneficiary_service
    ADD CONSTRAINT FK_BENEFICIARYSERVICE_ON_BENEFICIARY FOREIGN KEY (beneficiary_id) REFERENCES beneficiary (id);

ALTER TABLE beneficiary
    ADD CONSTRAINT FK_BENEFICIARY_ON_PERSON FOREIGN KEY (person_id) REFERENCES person (id);

ALTER TABLE cache_ad_user
    ADD CONSTRAINT FK_CACHEADUSER_ON_PROFILE FOREIGN KEY (profile_id) REFERENCES profile (id);

ALTER TABLE correspondence
    ADD CONSTRAINT FK_CORRESPONDENCE_ON_AFFECTED_TO FOREIGN KEY (affected_to_id) REFERENCES cache_ad_user (id);

ALTER TABLE correspondence
    ADD CONSTRAINT FK_CORRESPONDENCE_ON_DOCUMENT FOREIGN KEY (document_id) REFERENCES document (id);

ALTER TABLE correspondence
    ADD CONSTRAINT FK_CORRESPONDENCE_ON_DONOR FOREIGN KEY (donor_id) REFERENCES donor (id);

ALTER TABLE disease_treatment
    ADD CONSTRAINT FK_DISEASETREATMENT_ON_BENEFICIARY FOREIGN KEY (beneficiary_id) REFERENCES beneficiary (id);

ALTER TABLE document_beneficiary
    ADD CONSTRAINT FK_DOCUMENTBENEFICIARY_ON_BENEFICIARY FOREIGN KEY (beneficiary_id) REFERENCES beneficiary (id);

ALTER TABLE document_beneficiary
    ADD CONSTRAINT FK_DOCUMENTBENEFICIARY_ON_DOCUMENT FOREIGN KEY (document_id) REFERENCES document (id);

ALTER TABLE document_donation
    ADD CONSTRAINT FK_DOCUMENTDONATION_ON_DOCUMENT FOREIGN KEY (document_id) REFERENCES document (id);

ALTER TABLE document_donation
    ADD CONSTRAINT FK_DOCUMENTDONATION_ON_DONATION FOREIGN KEY (donation_id) REFERENCES donation (id);

ALTER TABLE document_donor
    ADD CONSTRAINT FK_DOCUMENTDONOR_ON_DOCUMENT FOREIGN KEY (document_id) REFERENCES document (id);

ALTER TABLE document_donor
    ADD CONSTRAINT FK_DOCUMENTDONOR_ON_DONOR FOREIGN KEY (donor_id) REFERENCES donor (id);

ALTER TABLE document_education
    ADD CONSTRAINT FK_DOCUMENTEDUCATION_ON_DOCUMENT FOREIGN KEY (document_id) REFERENCES document (id);

ALTER TABLE document_education
    ADD CONSTRAINT FK_DOCUMENTEDUCATION_ON_EDUCATION FOREIGN KEY (education_id) REFERENCES education (id);

ALTER TABLE document_family_member
    ADD CONSTRAINT FK_DOCUMENTFAMILYMEMBER_ON_DOCUMENT FOREIGN KEY (document_id) REFERENCES document (id);

ALTER TABLE document_family_member
    ADD CONSTRAINT FK_DOCUMENTFAMILYMEMBER_ON_FAMILY_MEMBER FOREIGN KEY (family_member_id) REFERENCES family_member (id);

ALTER TABLE document_family
    ADD CONSTRAINT FK_DOCUMENTFAMILY_ON_DOCUMENT FOREIGN KEY (document_id) REFERENCES document (id);

ALTER TABLE document_family
    ADD CONSTRAINT FK_DOCUMENTFAMILY_ON_FAMILY FOREIGN KEY (family_id) REFERENCES family (id);

ALTER TABLE document_taken_in_charge
    ADD CONSTRAINT FK_DOCUMENTTAKENINCHARGE_ON_DOCUMENT FOREIGN KEY (document_id) REFERENCES document (id);

ALTER TABLE document_taken_in_charge
    ADD CONSTRAINT FK_DOCUMENTTAKENINCHARGE_ON_TAKEN_IN_CHARGE FOREIGN KEY (taken_in_charge_id) REFERENCES taken_in_charge (id);

ALTER TABLE donation_product_nature
    ADD CONSTRAINT FK_DONATIONPRODUCTNATURE_ON_DONATION FOREIGN KEY (donation_id) REFERENCES donation (id);

ALTER TABLE donation
    ADD CONSTRAINT FK_DONATION_ON_DONOR FOREIGN KEY (donor_id) REFERENCES donor (id);

ALTER TABLE donor_contact_canal_communication
    ADD CONSTRAINT FK_DONORCONTACTCANALCOMMUNICATION_ON_DONOR_CONTACT FOREIGN KEY (donor_contact_id) REFERENCES donor_contact (id);

ALTER TABLE donor_contact_language_communication
    ADD CONSTRAINT FK_DONORCONTACTLANGUAGECOMMUNICATION_ON_DONOR_CONTACT FOREIGN KEY (donor_contact_id) REFERENCES donor_contact (id);

ALTER TABLE donor_contact
    ADD CONSTRAINT FK_DONORCONTACT_ON_DONOR FOREIGN KEY (donor_id) REFERENCES donor_moral (id);

ALTER TABLE donor_moral
    ADD CONSTRAINT FK_DONORMORAL_ON_ID FOREIGN KEY (id) REFERENCES donor (id);

ALTER TABLE donor_physical_canal_communication
    ADD CONSTRAINT FK_DONORPHYSICALCANALCOMMUNICATION_ON_DONOR_PHYSICAL FOREIGN KEY (donor_physical_id) REFERENCES donor_physical (id);

ALTER TABLE donor_physical_language_communication
    ADD CONSTRAINT FK_DONORPHYSICALLANGUAGECOMMUNICATION_ON_DONOR_PHYSICAL FOREIGN KEY (donor_physical_id) REFERENCES donor_physical (id);

ALTER TABLE donor_physical
    ADD CONSTRAINT FK_DONORPHYSICAL_ON_ID FOREIGN KEY (id) REFERENCES donor (id);

ALTER TABLE education
    ADD CONSTRAINT FK_EDUCATION_ON_BENEFICIARY FOREIGN KEY (beneficiary_id) REFERENCES beneficiary (id);

ALTER TABLE eps_resident
    ADD CONSTRAINT FK_EPS_RESIDENT_ON_BENEFICIARY FOREIGN KEY (beneficiary_id) REFERENCES beneficiary (id);

ALTER TABLE external_income
    ADD CONSTRAINT FK_EXTERNALINCOME_ON_FAMILY_MEMBER FOREIGN KEY (family_member_id) REFERENCES family_member (id);

ALTER TABLE family_member
    ADD CONSTRAINT FK_FAMILYMEMBER_ON_FAMILY FOREIGN KEY (family_id) REFERENCES family (id);

ALTER TABLE family_member
    ADD CONSTRAINT FK_FAMILYMEMBER_ON_PERSON FOREIGN KEY (person_id) REFERENCES person (id);

ALTER TABLE glasses
    ADD CONSTRAINT FK_GLASSES_ON_BENEFICIARYID FOREIGN KEY (beneficiary_id) REFERENCES beneficiary (id);

ALTER TABLE note_beneficiary
    ADD CONSTRAINT FK_NOTEBENEFICIARY_ON_BENEFICIARY FOREIGN KEY (beneficiary_id) REFERENCES beneficiary (id);

ALTER TABLE note_beneficiary
    ADD CONSTRAINT FK_NOTEBENEFICIARY_ON_NOTE FOREIGN KEY (note_id) REFERENCES note (id);

ALTER TABLE note_donation
    ADD CONSTRAINT FK_NOTEDONATION_ON_DONATION FOREIGN KEY (donation_id) REFERENCES donation (id);

ALTER TABLE note_donation
    ADD CONSTRAINT FK_NOTEDONATION_ON_NOTE FOREIGN KEY (note_id) REFERENCES note (id);

ALTER TABLE note_donor_contact
    ADD CONSTRAINT FK_NOTEDONORCONTACT_ON_DONOR_CONTACT FOREIGN KEY (donor_contact_id) REFERENCES donor_contact (id);

ALTER TABLE note_donor
    ADD CONSTRAINT FK_NOTEDONOR_ON_DONOR FOREIGN KEY (donor_id) REFERENCES donor (id);

ALTER TABLE note_donor
    ADD CONSTRAINT FK_NOTEDONOR_ON_NOTE FOREIGN KEY (note_id) REFERENCES note (id);

ALTER TABLE note_education
    ADD CONSTRAINT FK_NOTEEDUCATION_ON_EDUCATION FOREIGN KEY (education_id) REFERENCES education (id);

ALTER TABLE note_education
    ADD CONSTRAINT FK_NOTEEDUCATION_ON_NOTE FOREIGN KEY (note_id) REFERENCES note (id);

ALTER TABLE note_family_member
    ADD CONSTRAINT FK_NOTEFAMILYMEMBER_ON_FAMILY_MEMBER FOREIGN KEY (family_member_id) REFERENCES family_member (id);

ALTER TABLE note_family_member
    ADD CONSTRAINT FK_NOTEFAMILYMEMBER_ON_NOTE FOREIGN KEY (note_id) REFERENCES note (id);

ALTER TABLE note_family
    ADD CONSTRAINT FK_NOTEFAMILY_ON_FAMILY FOREIGN KEY (family_id) REFERENCES family (id);

ALTER TABLE note_family
    ADD CONSTRAINT FK_NOTEFAMILY_ON_NOTE FOREIGN KEY (note_id) REFERENCES note (id);

ALTER TABLE note_taken_in_charge
    ADD CONSTRAINT FK_NOTETAKENINCHARGE_ON_NOTE FOREIGN KEY (note_id) REFERENCES note (id);

ALTER TABLE note_taken_in_charge
    ADD CONSTRAINT FK_NOTETAKENINCHARGE_ON_TAKEN_IN_CHARGE FOREIGN KEY (taken_in_charge_id) REFERENCES taken_in_charge (id);

ALTER TABLE note
    ADD CONSTRAINT FK_NOTE_ON_CREATED_BY FOREIGN KEY (created_by_id) REFERENCES cache_ad_user (id);

ALTER TABLE scholarship_beneficiary
    ADD CONSTRAINT FK_SCHOLARSHIPBENEFICIARY_ON_BENEFICIARY FOREIGN KEY (beneficiary_id) REFERENCES beneficiary (id);

ALTER TABLE taken_in_charge_beneficiary
    ADD CONSTRAINT FK_TAKENINCHARGEBENEFICIARY_ON_BENEFICIARY FOREIGN KEY (beneficiary_id) REFERENCES beneficiary (id);

ALTER TABLE taken_in_charge_beneficiary
    ADD CONSTRAINT FK_TAKENINCHARGEBENEFICIARY_ON_TAKEN_IN_CHARGE FOREIGN KEY (taken_in_charge_id) REFERENCES taken_in_charge (id);

ALTER TABLE taken_in_charge_donor
    ADD CONSTRAINT FK_TAKENINCHARGEDONOR_ON_DONOR FOREIGN KEY (donor_id) REFERENCES donor (id);

ALTER TABLE taken_in_charge_donor
    ADD CONSTRAINT FK_TAKENINCHARGEDONOR_ON_TAKEN_IN_CHARGE FOREIGN KEY (taken_in_charge_id) REFERENCES taken_in_charge (id);

ALTER TABLE taken_in_charge_operation
    ADD CONSTRAINT FK_TAKENINCHARGEOPERATION_ON_TAKEN_IN_CHARGE_DONOR FOREIGN KEY (taken_in_charge_donor_id) REFERENCES taken_in_charge_donor (id);

ALTER TABLE taken_in_charge_beneficiary_bank_card
    ADD CONSTRAINT fk_takinchabenbancar_on_bank_card FOREIGN KEY (bank_card_id) REFERENCES bank_card (id);

ALTER TABLE taken_in_charge_beneficiary_bank_card
    ADD CONSTRAINT fk_takinchabenbancar_on_taken_in_charge_beneficiary FOREIGN KEY (taken_in_charge_beneficiary_id) REFERENCES taken_in_charge_beneficiary (id);

ALTER TABLE user_profile_module_functionality
    ADD CONSTRAINT fk_user_profile_module_functionality_on_profile FOREIGN KEY (profile_id) REFERENCES profile (id);