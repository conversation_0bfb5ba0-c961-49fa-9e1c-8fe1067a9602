package ma.almobadara.backend.dto.beneficiary;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import org.springframework.web.multipart.MultipartFile;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class RapportPictureDTO {

    private Long id;
    private String url;
    private String description;
    private String descriptionAr;
    private String descriptionAng;
    private String pictureBase64;

    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private MultipartFile picture;

}
