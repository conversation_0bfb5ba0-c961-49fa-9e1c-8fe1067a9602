package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.administration.*;
import ma.almobadara.backend.dto.beneficiary.EpsBeneficiaryDTO;
import ma.almobadara.backend.model.administration.Eps;
import ma.almobadara.backend.model.beneficiary.Beneficiary;
import ma.almobadara.backend.dto.administration.EpsDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Mapper(componentModel = "spring")
public interface EpsMapper {
    @Mapping(target = "cityDetails", ignore = true) // Will be set separately in service
    @Mapping(target = "zone", ignore = true) // Will be set separately in service
    FicheEpsDTO epsToFicheEpsDTO(Eps eps);

    @Mapping(target = "cityIds", ignore = true) // Will be set separately in service
    @Mapping(target = "zone", ignore = true) // Will be set separately in service
    Eps ficheEpsDTOToEps(FicheEpsDTO ficheEpsDTO);

    @Mapping(target = "fullName", source = "beneficiary", qualifiedByName = "mapFullName")
    @Mapping(target = "phoneNumber", source = "beneficiary", qualifiedByName = "mapPhoneNumber")
    @Mapping(target = "type", expression = "java(beneficiary.getIndependent() ? \"Indépendant\" : \"Membre de famille\")")
    @Mapping(target = "category", ignore = true) // Will be set in the service
    @Mapping(target = "status", source = "beneficiary.beneficiaryStatut.nameStatut")
    @Mapping(target = "createdAt", source = "beneficiary.person.createdAt", defaultExpression = "java(java.time.Instant.now())")
    EpsBeneficiaryDTO beneficiaryToEpsBeneficiaryDTO(Beneficiary beneficiary);

    @Named("mapFullName")
    default String mapFullName(Beneficiary beneficiary) {
        if (beneficiary.getPerson() == null) return "N/A";
        return beneficiary.getPerson().getFirstName() + " " + beneficiary.getPerson().getLastName();
    }

    @Named("mapPhoneNumber")
    default String mapPhoneNumber(Beneficiary beneficiary) {
        return beneficiary.getPerson() != null ? beneficiary.getPerson().getPhoneNumber() : "N/A";
    }

    @Mapping(target = "cityIds", source = "cityIds", qualifiedByName = "stringToLongList")
    @Mapping(target = "cityDetails", ignore = true) // Will be set separately in service
    @Mapping(target = "zone", ignore = true) // Will be set separately in service
    @Mapping(target = "beneficiariesCount", ignore = true) // Will be set separately in service
    @Mapping(target = "ageGroupIds", ignore = true) // Will be set separately in service
    EpsListDTO epsToEpsListDTO(Eps eps);

    @Mapping(target = "cityIds", ignore = true) // Will be set separately in service
    @Mapping(target = "zone", ignore = true) // Will be set separately in service
    Eps epsListDTOToEps(EpsListDTO epsListDTO);

    @Named("stringToLongList")
    default List<Long> stringToLongList(String cityIds) {
        if (cityIds == null || cityIds.isEmpty()) {
            return Collections.emptyList();
        }
        return Arrays.stream(cityIds.split(",\\s*"))
                .map(Long::parseLong)
                .collect(Collectors.toList());
    }

    EpsMapper INSTANCE = Mappers.getMapper(EpsMapper.class);

    @Mapping(target = "ageGroupIds", ignore = true)
    @Mapping(target = "cityIds", ignore = true)
    EpsDto epsToEpsDTO(Eps eps);

    @Mapping(target = "ageGroupIds", source = "ageGroupIds", ignore = true)
    @Mapping(target = "cityIds", source = "cityIds", ignore = true)
    Eps epsDTOToEps(EpsDto epsDto);


    EpsLightDTO epsToEpsLightListDTO(Eps epsLightListDTO);



}
