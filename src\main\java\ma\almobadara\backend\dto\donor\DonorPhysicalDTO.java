package ma.almobadara.backend.dto.donor;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.Email;
import lombok.*;
import lombok.experimental.SuperBuilder;
import ma.almobadara.backend.dto.administration.CacheAdUserDTO;
import ma.almobadara.backend.dto.administration.TagDTO;
import ma.almobadara.backend.dto.referentiel.CanalCommunicationDTO;
import ma.almobadara.backend.dto.referentiel.LanguageCommunicationDTO;
import ma.almobadara.backend.dto.referentiel.ProfessionDTO;
import ma.almobadara.backend.dto.referentiel.TypeIdentityDTO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@SuperBuilder
@ToString
@AllArgsConstructor
public class DonorPhysicalDTO extends DonorDTO {

	private String firstName;

	private String lastName;

	private Long anonymeId;

	private CacheAdUserDTO createdBy;

	private String firstNameAr;

	private String lastNameAr;

	private String sex;

	private String email;

	private String phoneNumber;

	private String pictureUrl;

	private String picture64;

	@JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
	private MultipartFile picture;

	private String type = "Physique";

	private List<CanalCommunicationDTO> canalCommunications;

	private List<LanguageCommunicationDTO> languageCommunications;

	private TypeIdentityDTO typeIdentity;

	private ProfessionDTO profession;

	private List<TagDTO> tags;

}
