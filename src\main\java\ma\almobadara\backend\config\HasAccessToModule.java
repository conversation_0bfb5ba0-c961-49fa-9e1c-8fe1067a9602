package ma.almobadara.backend.config;

import ma.almobadara.backend.enumeration.Functionality;
import ma.almobadara.backend.enumeration.Module;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface HasAccessToModule {
    Module[] modules(); // Array of modules
    Functionality[] functionalities() ; // Array of functionality names
}
