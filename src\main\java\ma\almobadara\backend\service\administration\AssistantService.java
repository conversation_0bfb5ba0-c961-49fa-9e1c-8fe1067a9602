package ma.almobadara.backend.service.administration;

import jakarta.persistence.EntityNotFoundException;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.controller.referentiel.RefController;
import ma.almobadara.backend.dto.administration.AssistantDTO;
import ma.almobadara.backend.dto.administration.ZoneDTO;
import ma.almobadara.backend.dto.referentiel.LanguageCommunicationDTO;
import ma.almobadara.backend.dto.referentiel.SchoolLevelDTO;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.exceptions.UserAlreadyExistsException;
import ma.almobadara.backend.exceptions.UserNotFoundException;
import ma.almobadara.backend.mapper.AssistantMapper;
import ma.almobadara.backend.model.administration.Assistant;
import ma.almobadara.backend.model.administration.CacheAdUser;
import ma.almobadara.backend.model.administration.HistoryZone;
import ma.almobadara.backend.model.administration.Zone;
import ma.almobadara.backend.repository.administration.AssistantRepository;
import ma.almobadara.backend.repository.administration.CacheAdUserRepository;
import ma.almobadara.backend.repository.administration.HistoryZoneRepository;
import ma.almobadara.backend.repository.administration.ZoneRepository;
import ma.almobadara.backend.service.donor.MinioService;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class AssistantService {
    private final CacheAdUserRepository cacheAdUserRepository;
    private final ZoneRepository zoneRepository;

    private final AssistantRepository assistantRepository;
    private final HistoryZoneRepository historyZoneRepository;
    private final AssistantMapper assistantMapper;
    private final CacheAdUserService cacheAdUserService;
    private final ZoneService zoneService;
    private final RefController refController;
    private final MinioService minioService;

    @Value("${minio.assistantFolder}")
    private String assistantFolder;

    @Value("${minio.profilePicture.folder}")
    private String folderPathPicture;

    @Transactional
    public void createAssistant(AssistantDTO assistantDTO) throws TechnicalException {
        log.debug("Start service create assistant");
        Long userId = null;
        try {
            userId = checkAndSaveUser(assistantDTO.getEmail());
            if (userId != null) {
                Long finalUserId = userId;
                CacheAdUser cacheAdUser = cacheAdUserRepository.findById(userId)
                        .orElseThrow(() -> new EntityNotFoundException("User not found with ID " + finalUserId));
                assistantDTO.setFirstName(cacheAdUser.getFirstName());
                assistantDTO.setLastName(cacheAdUser.getLastName());
            }
            assistantDTO.setCacheAdUserId(userId);

            if (assistantDTO.getZoneId() != null) {
                if (assistantRepository.existsByZoneId(assistantDTO.getZoneId())) {
                    throw new TechnicalException("The zone is already assigned to another assistant");
                }
            }

            Assistant assistant = assistantMapper.toEntity(assistantDTO);
            assistant.setCode(generateAssistantCode());

            // Set password if provided
            if (assistantDTO.getPassword() != null && !assistantDTO.getPassword().isEmpty()) {
                assistant.setPassword(assistantDTO.getPassword());
            }

            if (assistantDTO.getPicture() != null) {
                try {
                    String pictureUrl = handlePicture(assistantDTO.getPicture(), assistant);
                    assistant.setPictureUrl(pictureUrl);
                } catch (IOException e) {
                    log.error("Error occurred while saving the assistant: {}", e.getMessage());
                    throw new RuntimeException(e.getMessage(), e);
                }
            }


            if (assistantDTO.getZoneId() != null) {
                Zone zone = zoneService.findZoneById(assistantDTO.getZoneId());
                assistant.setZone(zone);
            } else {
                assistant.setZone(null);
            }

            Assistant savedAssistant = assistantRepository.save(assistant);
            log.info("Assistant saved with ID {}", savedAssistant.getId());
        } catch (Exception e) {
            if (userId != null) {
                cacheAdUserService.deleteUserById(Math.toIntExact(userId));
            }
            log.error("Error occurred while saving the assistant: {}", e.getMessage());
            throw new RuntimeException(e.getMessage(), e);
        }

        log.debug("End service create assistant");
    }

    @Transactional
    public void updateAssistant(Long id, AssistantDTO assistantDTO) throws TechnicalException {
        log.debug("Start service update assistant with id {}", id);

        // Step 1: Retrieve the existing Assistant entity by ID
        Assistant existingAssistant = assistantRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Assistant not found with ID " + id));

        // Step 2: Check if the new email is already assigned to another assistant
        if (assistantDTO.getEmail() != null &&
                assistantRepository.existsByEmailAndIdNot(assistantDTO.getEmail(), id)) {
            throw new TechnicalException("Assistant with email " + assistantDTO.getEmail() + " already exists");
        }

        // Step 3: Check if the new zone ID is already assigned to another assistant
        if (assistantDTO.getZoneId() != null) {
            if (assistantRepository.existsByZoneIdAndIdNot(assistantDTO.getZoneId(), id)) {
                throw new TechnicalException("The zone is already assigned to another assistant");
            }
            Zone newZone = zoneService.findZoneById(assistantDTO.getZoneId()); // Managed entity
            existingAssistant.setZone(newZone);
        } else {
            existingAssistant.setZone(null);
        }

        // Step 4: Update other fields and set the update date
        assistantMapper.updateAssistantFromDTO(assistantDTO, existingAssistant);
        existingAssistant.setUpdateDate(LocalDateTime.now());

        // Update password if provided
        if (assistantDTO.getPassword() != null && !assistantDTO.getPassword().isEmpty()) {
            existingAssistant.setPassword(assistantDTO.getPassword());
        }

        if (assistantDTO.getPicture() != null) {
            try {
                String pictureUrl = handlePicture(assistantDTO.getPicture(), existingAssistant);
                existingAssistant.setPictureUrl(pictureUrl);
            } catch (IOException e) {
                log.error("Error occurred while updating the assistant: {}", e.getMessage());
                throw new RuntimeException("Error updating assistant: " + e.getMessage(), e);
            }
        }


        // Step 5: Save the updated assistant
        try {
            assistantRepository.save(existingAssistant);
        } catch (Exception e) {
            log.error("Error occurred while updating the assistant: {}", e.getMessage());
            throw new RuntimeException("Error updating assistant: " + e.getMessage(), e);
        }

        log.debug("End service update assistant with id {}", id);
    }

    private String handlePicture(MultipartFile picture, Assistant assistant) throws IOException {
        Instant instant = Instant.now();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.BASIC_ISO_DATE;

        String assistantPath = assistantFolder
                + assistant.getLastName().toUpperCase() + "-"
                + assistant.getFirstName().substring(0, 1).toUpperCase()
                + assistant.getFirstName().substring(1) + "_"
                + assistant.getCode();

        String fileName = assistant.getLastName().toUpperCase() + "-"
                + assistant.getFirstName().substring(0, 1).toUpperCase()
                + assistant.getFirstName().substring(1) + "_"
                + instant.atZone(ZoneId.systemDefault()).toLocalDate().format(dateTimeFormatter) + "."
                + FilenameUtils.getExtension(picture.getOriginalFilename());

        String pictureUrl = assistantPath + "/" + folderPathPicture + "/" + fileName;

        minioService.WriteToMinIO(
                picture,
                assistantPath + "/" + folderPathPicture + "/",
                fileName
        );

        return pictureUrl;
    }


    public Page<AssistantDTO> getAllAssistants(int page, int size) {
        log.debug("Start service  get all assistants with page {} and size {}", page, size);
        // we should sort the assistants by their update date
        Pageable pageable = PageRequest.of(page, size);
        Page<Assistant> assistants = assistantRepository.findAllOrderByUpdateDate(pageable);
        log.debug("End service  get all assistants with page {} and size {}", page, size);
        return assistants.map(assistantMapper::toDTO);
    }

    public AssistantDTO getAssistantById(Long id) throws TechnicalException {

        Assistant assistant = assistantRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Assistant not found with ID: " + id));

        log.info("Found Assistant: {}", assistant);

        AssistantDTO assistantDTO = assistantMapper.toDTO(assistant);

        // Handling SchoolLevelDTO
        if (assistant.getSchoolLevelId() != null) {
            SchoolLevelDTO fullSchoolLevelDTO = refController.getParSchoolLevel(assistant.getSchoolLevelId()).getBody();
            assistantDTO.setSchoolLevel(fullSchoolLevelDTO);
        }
        if(assistant.getPictureUrl() !=null)
        {
            byte[] imageData = minioService.ReadFromMinIO(assistant.getPictureUrl(),null);
            String base64Image = Base64.getEncoder().encodeToString(imageData);
            assistantDTO.setPicture64(base64Image);

        }

        processLanguageCommunicationDetails(assistantDTO);

        return assistantDTO;
    }

    private void processLanguageCommunicationDetails(AssistantDTO assistantDTO) {
        List<Long> languageCommunicationIds = assistantDTO.getLanguageCommunicationIds();
        if (languageCommunicationIds != null && !languageCommunicationIds.isEmpty()) {
            List<LanguageCommunicationDTO> languageCommunicationDetails = languageCommunicationIds.stream()
                    .map(this::fetchLanguageCommunicationDetails)
                    .collect(Collectors.toList());
            assistantDTO.setLanguageCommunicationDetails(languageCommunicationDetails);
        }
    }

    private LanguageCommunicationDTO fetchLanguageCommunicationDetails(Long languageCommunicationIds) {
        return refController.getParLanguageCommunication(languageCommunicationIds).getBody();
    }

//    public AssistantDTO getAssistantById(Long id) {
//
//        Assistant assistant = assistantRepository.findById(id)
//                .orElseThrow(() -> new EntityNotFoundException("Assistant not found with ID: " + id));
//
//        log.info("Found Assistant: {}", assistant);
//
//        AssistantDTO assistantDTO = assistantMapper.toDTO(assistant);
//
//        SchoolLevelDTO levelDTO = assistantDTO.getSchoolLevel();
//        SchoolLevelDTO fullSchoolLevelDTO = refController.getParSchoolLevel(levelDTO.getId()).getBody();
//        assistantDTO.setSchoolLevel(fullSchoolLevelDTO);
//
//        List<LanguageCommunicationDTO> languageCommunicationDTO = assistantDTO.getLanguageCommunication();
//        List<LanguageCommunicationDTO> fullLanguageCommunicationDTO = refController.getParLanguageCommunication(languageCommunicationDTO.getId()).getBody();
//        assistantDTO.setLanguageCommunication(fullLanguageCommunicationDTO);
//
//        return assistantDTO;
//    }

//    @Transactional
//    public AssistantDTO createAssistant(AssistantDTO assistantDTO) throws TechnicalException {
//        log.debug("Start service create assistant");
//        try {
//            if (Boolean.TRUE.equals(assistantRepository.existsByCode(assistantDTO.getCode()))) {
//                throw new TechnicalException("The code is already used");
//            }
//            if (Boolean.TRUE.equals(assistantRepository.existsAllByEmail(assistantDTO.getEmail()))) {
//                throw new TechnicalException("Assistant with email");
//            }
//
//            if (assistantDTO.getZoneId() != null) {
//                if (assistantRepository.existsByZoneId(assistantDTO.getZoneId())) {
//                    throw new TechnicalException("The zone is already assigned to another assistant");
//                }
//            }
//
//            Assistant assistant = assistantMapper.toEntity(assistantDTO);
//
//            if (assistantDTO.getZoneId() != null) {
//                // Retrieve the Zone entity from the repository
//                Zone zone = zoneService.findZoneById(assistantDTO.getZoneId());
//                assistant.setZone(zone);
//            } else {
//                assistant.setZone(null);
//            }
//            Assistant savedAssistant = assistantRepository.save(assistant);
//
//            log.info("Assistant saved with ID {}", savedAssistant.getId());
//        } catch (Exception e) {
//
//            log.error("Error occurred while saving the assistant: {}", e.getMessage());
//            throw new RuntimeException(e.getMessage(), e);
//        }
//
//        log.debug("End service create assistant");
//        return assistantDTO;
//    }

    // we should add a funtion of code generation for the assistant
    // we should add a function of code generation for the assistant A + Year + 0001
    public String generateAssistantCode() {
        // Get the last assistant code
        Optional<Assistant> lastAssistant = assistantRepository.findLastAssistantCode();
        String lastCode = lastAssistant.map(Assistant::getCode).orElse(null);
        String currentYear = String.valueOf(LocalDateTime.now().getYear());
        String newCode;
        if (lastCode != null && lastCode.substring(1, 5).equals(currentYear)) {
            int lastCodeNumber = Integer.parseInt(lastCode.substring(5));
            newCode = "A" + currentYear + String.format("%04d", lastCodeNumber + 1);
        } else {
            newCode = "A" + currentYear + "0001";
        }
        return newCode;
    }

   /* @Transactional
    public AssistantDTO createAssistant(AssistantDTO assistantDTO) throws TechnicalException {
        log.debug("Start service create assistant");
        try {
            if (Boolean.TRUE.equals(assistantRepository.existsAllByEmail(assistantDTO.getEmail()))) {
                throw new TechnicalException("Assistant with email");
            }

            Assistant assistant = assistantMapper.toEntity(assistantDTO);

            if (assistantDTO.getZoneId() != null) {
                // Récupérer la Zone associée
                Zone zone = zoneService.findZoneById(assistantDTO.getZoneId());

                // Définir la zone dans l'Assistant
                assistant.setZone(zone);

                // Initialiser le status de l'Assistant en fonction du status de la Zone
                assistant.setStatus(zone.isStatus());
            } else {
                assistant.setZone(null);
                // Si aucune zone n'est associée, tu peux définir un status par défaut
                assistant.setStatus(false);
            }
            assistant.setCode(generateAssistantCode());

            Assistant savedAssistant = assistantRepository.save(assistant);

            log.info("Assistant saved with ID {}", savedAssistant.getId());
        } catch (Exception e) {
            log.error("Error occurred while saving the assistant: {}", e.getMessage());
            throw new RuntimeException(e.getMessage(), e);
        }

        log.debug("End service create assistant");
        return assistantDTO;
    }*/


    private Long checkAndSaveUser(String userEmail) throws TechnicalException {
        try {
            return cacheAdUserService.checkAndSaveAdUserByEmail(userEmail);
        } catch (UserAlreadyExistsException | UserNotFoundException e) {
            log.error("Failed to create assistant: {}", e.getMessage());
            throw new TechnicalException(e.getMessage());
        } catch (Exception e) {
            log.error("Unexpected error occurred while checking and saving user: {}", e.getMessage());
            throw new RuntimeException("An unexpected error occurred while handling user.", e);
        }
    }


    public void deleteAssistant(Long id) throws TechnicalException {
        Assistant assistant = assistantRepository.findById(id).orElse(null);

        if (assistant != null) {
            // we should  check if the assistant has no beneficiaries(mean actif zone thzt related to beneficiaries)
            if (zoneRepository.existsByAssistantId(id)) {
                throw new TechnicalException("The assistant is already assigned to a zone");

            }
            assistantRepository.deleteById(id);

            if (assistant.getCacheAdUser() != null) {
                try {
                    cacheAdUserService.deleteUserById(Math.toIntExact(assistant.getCacheAdUser().getId()));
                } catch (Exception e) {
                    log.error("Error deleting user with ID {}: {}", assistant.getCacheAdUser().getId(), e.getMessage());
                }
            }

        }

    }


    /*public boolean deleteAssistant(Long id) throws TechnicalException {
        if (assistantRepository.existsById(id)) {
            Assistant assistant = assistantRepository.getReferenceById(id);
            if (assistant.getZone() != null) {
                throw new TechnicalException("The assistant is already assigned to a zone");
            }
            assistant.setDeleted(true);
            assistantRepository.save(assistant);
            return true;
        }
        return false;
    }

     */


    // get the disponible zones for the assistant = the zones that are not assigned to any assistant
    public List<ZoneDTO> getDisponibleZones() {
        return zoneService.getDisponibleZones();
    }

    //  @Transactional
//    public void changeZone(Long assistantId, LocalDate endDate, Long newZoneId, LocalDate newZoneDate) throws TechnicalException {
//        log.debug("Start service change zone for assistant with id {}", assistantId);
//
//        // Step 1: Retrieve the existing Assistant entity by ID
//        Assistant assistant = assistantRepository.findById(assistantId)
//                .orElseThrow(() -> new TechnicalException("Assistant not found with ID " + assistantId));
//
//        // Step 2: Validate and set the end date
//        if (endDate != null) {
//            assistant.setDateEndAffectationToZone(endDate);
//        }
//
//        // Step 3: Change the zone if a new zone ID is provided
//        if (newZoneId != null) {
//            Zone newZone = zoneService.findZoneById(newZoneId);
//            if (newZone == null) {
//                throw new TechnicalException("New zone not found with ID " + newZoneId);
//            }
//            // we should check if the zone is already assigned to another assistant
//            if (assistantRepository.existsByZoneIdAndIdNot(newZoneId, assistantId)) {
//                throw new TechnicalException("The zone is already assigned to another assistant");
//            }
//            assistant.setZone(newZone);
//            assistant.setDateAffectationToZone(newZoneDate);
//            assistant.setDateEndAffectationToZone(endDate);
//        }
//        // Save the updated Assistant entity
//        assistantRepository.save(assistant);
//
//        log.debug("End service change zone for assistant with id {}", assistantId);
//    }


    @Transactional
    public void changeZone(Long assistantId, LocalDate endDate, Long newZoneId, LocalDate newZoneDate) throws TechnicalException {
        log.debug("Start service change zone for assistant with id {}", assistantId);
        zoneService.clearExpiredAssistants();
        Assistant assistant = assistantRepository.findById(assistantId)
                .orElseThrow(() -> new TechnicalException("Assistant not found with ID " + assistantId));

        if (endDate != null) {
            assistant.setDateEndAffectationToZone(endDate);
        }

        if (assistant.getZone() != null) {
            HistoryZone historique = new HistoryZone();
            // we should get the assistant name from the coonected user of the assistant and not from the assistant
            if (assistant.getCacheAdUser() != null) {
                historique.setAssistant(assistant.getCacheAdUser().getFirstName() + " " + assistant.getCacheAdUser().getLastName());
            }
            historique.setZone(assistant.getZone());
            historique.setDateAffectation(assistant.getDateAffectationToZone());
            historique.setDateFinAffectation(newZoneDate);
            historyZoneRepository.save(historique);
        }


        if (newZoneId != null) {
            Zone newZone = zoneService.findZoneById(newZoneId);
            if (newZone == null) {
                throw new TechnicalException("New zone not found with ID " + newZoneId);
            }

            if (assistantRepository.existsByZoneIdAndIdNot(newZoneId, assistantId)) {
                Zone zone = zoneRepository.findById(newZoneId).orElseThrow(() -> new TechnicalException("Zone not found with ID " + newZoneId));
                if (zone.getAssistant() != null) {
                    throw new TechnicalException("The zone is already assigned to another assistant");
                } else {
                    // we should find if thre is a assiant have the same zone id in this case we shuold make her zone to null
                    Assistant assistant1 = assistantRepository.findByZoneId(newZoneId);
                    assistant1.setZone(null);
                    assistant1.setDateEndAffectationToZone(null);
                    assistant1.setDateAffectationToZone(null);
                }

            }

            assistant.setZone(newZone);
            assistant.setDateAffectationToZone(newZoneDate);
            assistant.setDateEndAffectationToZone(endDate);
            assistant.setStatus(newZone.isStatus());
        } else {
            assistant.setZone(null);
            assistant.setStatus(false);
        }

        assistantRepository.save(assistant);

        log.debug("End service change zone for assistant with id {}", assistantId);
    }

    public AssistantDTO getAssistantByEmail(String email) throws TechnicalException {
        log.debug("Start service get assistant by email {}", email);

        Assistant assistant = assistantRepository.findByEmail(email)
                .orElseThrow(() -> new EntityNotFoundException("Assistant not found with email: " + email));

        AssistantDTO assistantDTO = assistantMapper.toDTO(assistant);

        // Handling SchoolLevelDTO
        if (assistant.getSchoolLevelId() != null) {
            SchoolLevelDTO fullSchoolLevelDTO = refController.getParSchoolLevel(assistant.getSchoolLevelId()).getBody();
            assistantDTO.setSchoolLevel(fullSchoolLevelDTO);
        }

        if(assistant.getPictureUrl() != null) {
            try {
                byte[] imageData = minioService.ReadFromMinIO(assistant.getPictureUrl(), null);
                String base64Image = Base64.getEncoder().encodeToString(imageData);
                assistantDTO.setPicture64(base64Image);
            } catch (Exception e) {
                log.error("Error reading picture for assistant with email {}: {}", email, e.getMessage());
            }
        }

        processLanguageCommunicationDetails(assistantDTO);

        log.debug("End service get assistant by email {}", email);
        return assistantDTO;
    }

    /**
     * Updates the device token for an assistant
     * @param assistantId The ID of the assistant
     * @param deviceToken The new device token
     * @return The updated AssistantDTO
     * @throws TechnicalException If the assistant is not found or other technical issues occur
     */
    @Transactional
    public AssistantDTO updateDeviceToken(Long assistantId, String deviceToken) throws TechnicalException {
        log.debug("Start service update device token for assistant with id {}", assistantId);

        Assistant assistant = assistantRepository.findById(assistantId)
                .orElseThrow(() -> new EntityNotFoundException("Assistant not found with ID: " + assistantId));

        assistant.setDevice_token(deviceToken);
        assistantRepository.save(assistant);

        AssistantDTO assistantDTO = assistantMapper.toDTO(assistant);

        log.debug("End service update device token for assistant with id {}", assistantId);
        return assistantDTO;
    }


//    @Transactional
//    public void changeZone(Long assistantId, LocalDate endDate, Long newZoneId, LocalDate newZoneDate) throws TechnicalException {
//        log.debug("Start service change zone for assistant with id {}", assistantId);
//
//        // Step 1: Retrieve the existing Assistant entity by ID
//        Assistant assistant = assistantRepository.findById(assistantId)
//                .orElseThrow(() -> new TechnicalException("Assistant not found with ID " + assistantId));
//
//        // Step 2: Validate and set the end date
//        if (endDate != null) {
//            assistant.setDateEndAffectationToZone(endDate);
//        }
//
//        // Step 3: Record the current zone in HistoriqueZone before changing
//        if (assistant.getZone() != null) {
//            HistoryZone historique = new HistoryZone();
//            historique.setAssistant(assistant);
//            historique.setZone(assistant.getZone());
//            historique.setDateAffectation(assistant.getDateAffectationToZone());
//            historique.setDateFinAffectation(endDate);
//            historyZoneRepository.save(historique);  // Save the history
//        }
//
//        // Step 4: Change the zone if a new zone ID is provided
//        if (newZoneId != null) {
//            Zone newZone = zoneService.findZoneById(newZoneId);
//            if (newZone == null) {
//                throw new TechnicalException("New zone not found with ID " + newZoneId);
//            }
//            // Check if the zone is already assigned to another assistant
//            if (assistantRepository.existsByZoneIdAndIdNot(newZoneId, assistantId)) {
//                throw new TechnicalException("The zone is already assigned to another assistant");
//            }
//            assistant.setZone(newZone);
//            assistant.setDateAffectationToZone(newZoneDate);
//            assistant.setDateEndAffectationToZone(endDate);
//        }
//
//        // Save the updated Assistant entity
//        assistantRepository.save(assistant);
//
//        log.debug("End service change zone for assistant with id {}", assistantId);
//    }

}
