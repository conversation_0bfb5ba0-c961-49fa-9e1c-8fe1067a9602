package ma.almobadara.backend.service.administration;

import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.*;
import lombok.RequiredArgsConstructor;
import ma.almobadara.backend.dto.administration.*;
import ma.almobadara.backend.dto.donation.DonationDTO;
import ma.almobadara.backend.dto.donation.DonationServiceCollectEpsDto;
import ma.almobadara.backend.dto.service.ServiceLightDTO;
import ma.almobadara.backend.enumeration.StatutCollecte;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.mapper.EpsMapper;
import ma.almobadara.backend.mapper.ServiceCollectEpsMapper;
import ma.almobadara.backend.model.administration.Eps;
import ma.almobadara.backend.model.administration.ServiceCollectEps;
import ma.almobadara.backend.model.donation.BudgetLine;
import ma.almobadara.backend.model.donation.Donation;
import ma.almobadara.backend.model.donor.Donor;
import ma.almobadara.backend.model.donor.DonorAnonyme;
import ma.almobadara.backend.model.donor.DonorMoral;
import ma.almobadara.backend.model.donor.DonorPhysical;
import ma.almobadara.backend.model.service.Services;
import ma.almobadara.backend.repository.administration.EpsRepository;
import ma.almobadara.backend.repository.administration.ServiceCollectEpsRepository;
import ma.almobadara.backend.repository.donation.BudgetLineRepository;
import ma.almobadara.backend.repository.donation.DonationRepository;
import ma.almobadara.backend.repository.services.ServicesRepository;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.YearMonth;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@RequiredArgsConstructor
public class ServiceCollectEpsService {

    private final ServiceCollectEpsRepository serviceCollectEpsRepository;
    private final ServicesRepository servicesRepository;
    private final ServiceCollectEpsMapper serviceCollectEpsMapper;
    private final EpsRepository epsRepository;
    private final EntityManager entityManager;
    private final BudgetLineRepository budgetLineRepository;
    private final EpsMapper epsMapper;


    // Paginated search method
    public Page<ConsultServiceCollectEpsDTO> getAllServiceCollectEps(
            int page, int size, Integer searchByMois, Integer searchByAnnee,
            String searchByName, String searchByStatut, Integer searchByEps, Integer searchByService) {

        Pageable pageable = PageRequest.of(page, size,Sort.by(Sort.Order.desc("dateModification")));
        Page<ServiceCollectEps> serviceCollectEpsPage;

        // Filtering using Criteria API
        if (searchByMois != null || searchByAnnee != null || searchByName != null || searchByEps != null || searchByService != null) {
            serviceCollectEpsPage = filterServiceCollectEps(searchByMois, searchByAnnee, searchByName, searchByEps, searchByService, pageable);
        } else {
            serviceCollectEpsPage = serviceCollectEpsRepository.findByIsDeletedNull(pageable);
        }

        // Get current year and month
        LocalDate now = LocalDate.now();
        int currentMonth = now.getMonthValue();
        int currentYear = now.getYear();

        // Apply filtering based on 'statut'
        if (searchByStatut != null) {
            serviceCollectEpsPage = new PageImpl<>(
                    serviceCollectEpsPage.getContent().stream()
                            .filter(serviceCollectEps -> {
                                int serviceMonth = serviceCollectEps.getMois();
                                int serviceYear = serviceCollectEps.getAnnee();
                                switch (searchByStatut.toLowerCase()) {
                                    case "planifier":
                                        return (serviceYear > currentYear) ||
                                                (serviceYear == currentYear && serviceMonth > currentMonth);
                                    case "fermer":
                                        return serviceCollectEps.getIsCloture()==null &&((serviceYear < currentYear) ||
                                                (serviceYear == currentYear && serviceMonth < currentMonth));
                                    case "ouvert":
                                        return serviceYear == currentYear && serviceMonth == currentMonth;
                                    case "cloture":
                                        return Boolean.TRUE.equals(serviceCollectEps.getIsCloture());
                                    default:
                                        return true;
                                }
                            })
                            .collect(Collectors.toList()),
                    pageable,
                    serviceCollectEpsPage.getTotalElements()
            );
        }

        // Convert to DTO
        List<ConsultServiceCollectEpsDTO> serviceCollectEpsDTOs = serviceCollectEpsPage.getContent().stream()
                .map(serviceCollectEps -> {
                    ConsultServiceCollectEpsDTO consultServiceCollectEpsDTO = new ConsultServiceCollectEpsDTO();
                    consultServiceCollectEpsDTO.setNom(serviceCollectEps.getNom());
                    consultServiceCollectEpsDTO.setCode(serviceCollectEps.getCode());
                    Eps eps = serviceCollectEps.getService().getEps();
                    EpsLightDTO epsLightDTO = epsMapper.epsToEpsLightListDTO(eps);
                    consultServiceCollectEpsDTO.setEps(epsLightDTO);
                    consultServiceCollectEpsDTO.setId(serviceCollectEps.getId());
                    consultServiceCollectEpsDTO.setIsCloture(serviceCollectEps.getIsCloture());
                    consultServiceCollectEpsDTO.setAnnee(serviceCollectEps.getAnnee());
                    List<BudgetLine> budgetLines=budgetLineRepository.findByServiceCollectEps(serviceCollectEps);

                    if(!budgetLines.isEmpty()){
                        consultServiceCollectEpsDTO.setIsContainDons(Boolean.TRUE);
                    }
                    else{
                        consultServiceCollectEpsDTO.setIsContainDons(Boolean.FALSE);
                    }
                    consultServiceCollectEpsDTO.setMois(serviceCollectEps.getMois());
                    return consultServiceCollectEpsDTO;
                })
                .collect(Collectors.toList());

        return new PageImpl<>(serviceCollectEpsDTOs, pageable, serviceCollectEpsPage.getTotalElements());
    }


    // Filtering with Criteria API
    private Page<ServiceCollectEps> filterServiceCollectEps(
            Integer searchByMois, Integer searchByAnnee, String searchByName,
            Integer searchByEps, Integer searchByService, Pageable pageable) {

        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<ServiceCollectEps> criteriaQuery = criteriaBuilder.createQuery(ServiceCollectEps.class);
        Root<ServiceCollectEps> root = criteriaQuery.from(ServiceCollectEps.class);

        Predicate predicate = buildPredicate(criteriaBuilder, root, searchByMois, searchByAnnee, searchByName, searchByEps, searchByService);
        criteriaQuery.where(predicate);

        TypedQuery<ServiceCollectEps> typedQuery = entityManager.createQuery(criteriaQuery);
        long totalCount = typedQuery.getResultList().size();
        typedQuery.setFirstResult((int) pageable.getOffset());
        typedQuery.setMaxResults(pageable.getPageSize());
        List<ServiceCollectEps> resultList = typedQuery.getResultList();

        return new PageImpl<>(resultList, pageable, totalCount);
    }

    // Building the predicate for search filtering
    private Predicate buildPredicate(CriteriaBuilder criteriaBuilder, Root<ServiceCollectEps> root,
                                     Integer searchByMois, Integer searchByAnnee, String searchByName,
                                     Integer searchByEps, Integer searchByService) {
        Predicate predicate = criteriaBuilder.conjunction();
        predicate = criteriaBuilder.and(predicate, criteriaBuilder.isNull(root.get("isDeleted")));
        if (searchByMois != null) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("mois"), searchByMois));
        }

        if (searchByAnnee != null) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("annee"), searchByAnnee));
        }

        if (searchByName != null) {
            predicate = criteriaBuilder.and(predicate,
                    criteriaBuilder.like(criteriaBuilder.lower(root.get("nom")), "%" + searchByName.toLowerCase() + "%"));
        }

        if (searchByEps != null) {
            Join<ServiceCollectEps, Services> serviceJoin = root.join("service");
            Join<Services, Eps> epsJoin = serviceJoin.join("eps");
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(epsJoin.get("id"), searchByEps));
        }

        if (searchByService != null) {
            Join<ServiceCollectEps, Services> serviceJoin = root.join("service");
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(serviceJoin.get("id"), searchByService));
        }

        return predicate;
    }


    // Optional method to fetch all without pagination
    public List<ServiceCollectEpsDTO> getAllServiceCollectEpsList() {
        List<ServiceCollectEps> serviceCollectEpsList = serviceCollectEpsRepository.findAll();
        return serviceCollectEpsList.stream()
                .map(serviceCollectEpsMapper::serviceCollectEpstoServiceCollectEpsDTO)
                .collect(Collectors.toList());
    }

    public ConsultServiceCollectEpsDTO getServiceCollectEpsDTO(Long id){
        Optional<ServiceCollectEps> serviceCollectEpsOptional=serviceCollectEpsRepository.findById(id);
        ServiceCollectEps serviceCollectEps=serviceCollectEpsOptional.get();
        ConsultServiceCollectEpsDTO consultServiceCollectEpsDTO = new ConsultServiceCollectEpsDTO();
        consultServiceCollectEpsDTO.setNom(serviceCollectEps.getNom());
        consultServiceCollectEpsDTO.setCode(serviceCollectEps.getCode());
        Eps eps = serviceCollectEps.getService().getEps();
        EpsLightDTO epsLightDTO = epsMapper.epsToEpsLightListDTO(eps);

        ServiceLightDTO serviceLightDTO=new ServiceLightDTO(serviceCollectEps.getService().getId(),
                serviceCollectEps.getService().getName(),
                serviceCollectEps.getService().getCode());
        consultServiceCollectEpsDTO.setEps(epsLightDTO);
        consultServiceCollectEpsDTO.setServiceLightDTO(serviceLightDTO);
        consultServiceCollectEpsDTO.setId(serviceCollectEps.getId());
        consultServiceCollectEpsDTO.setIsCloture(serviceCollectEps.getIsCloture());
        consultServiceCollectEpsDTO.setAnnee(serviceCollectEps.getAnnee());
        consultServiceCollectEpsDTO.setCommentaire(serviceCollectEps.getCommentaire());
        consultServiceCollectEpsDTO.setMois(serviceCollectEps.getMois());
        List<BudgetLine> budgetLines=budgetLineRepository.findByServiceCollectEps(serviceCollectEps);
        if(!budgetLines.isEmpty()){
            consultServiceCollectEpsDTO.setIsContainDons(Boolean.TRUE);
        }
        else{
            consultServiceCollectEpsDTO.setIsContainDons(Boolean.FALSE);
        }
        return consultServiceCollectEpsDTO;
    }

    @Transactional
    public ServiceCollectEpsDTO createServiceCollectEps(ServiceCollectEpsDTO dto) {
        Services service = servicesRepository.findById(dto.getServiceId())
                .orElseThrow(() -> new IllegalArgumentException("Service EPS introuvable"));

            Optional<ServiceCollectEps> existingEps = serviceCollectEpsRepository.findByServiceAndMoisAndAnneeAndIsDeletedNull(service, dto.getMois(), dto.getAnnee());

        if (existingEps.isPresent()) {
            ServiceCollectEps existing = existingEps.get();
            if (dto.getId() == null || !existing.getId().equals(dto.getId())) {
                throw new IllegalArgumentException("Un service de collecte pour cet EPS existe déjà pour le mois et l'année sélectionnés. Veuillez choisir une autre période ou modifier l'existant.");
            }
        }
        String nom = String.format("%s %02d/%d", service.getName(), dto.getMois(), dto.getAnnee());

        ServiceCollectEps serviceCollectEps = serviceCollectEpsMapper.serviceCollectEpsDTOtoServiceCollectEps(dto);
        if(dto.getId()==null){
            String code = generateCode();
            serviceCollectEps.setCode(code);
        }


        serviceCollectEps.setNom(nom);
        serviceCollectEps.setService(service);
        serviceCollectEps.setAnnee(dto.getAnnee());
        serviceCollectEps.setMois(dto.getMois());
        serviceCollectEps.setCommentaire(dto.getCommentaire());

        ServiceCollectEps savedEntity = serviceCollectEpsRepository.save(serviceCollectEps);
        return serviceCollectEpsMapper.serviceCollectEpstoServiceCollectEpsDTO(savedEntity);
    }

    private String generateCode() {
        String lastCode = serviceCollectEpsRepository.findLastCode();

        int newCounter = 1;

        if (lastCode != null && lastCode.matches("SEPS\\d{4}")) {
            String lastCounterStr = lastCode.substring(lastCode.length() - 4);
            try {
                newCounter = Integer.parseInt(lastCounterStr) + 1;
            } catch (NumberFormatException e) {
                newCounter = 1;
            }
        }

        return String.format("SEPS%04d", newCounter);
    }


    public ServiceCollectEpsDTO FindServiceCollectEpsByIdForAdd(Long id) throws TechnicalException {
        Optional<ServiceCollectEps> optionalServiceCollectEps = serviceCollectEpsRepository.findById(id);
        if (optionalServiceCollectEps.isPresent()) {
            ServiceCollectEpsDTO epstoServiceCollectEpsDTO = serviceCollectEpsMapper.serviceCollectEpstoServiceCollectEpsDTO(optionalServiceCollectEps.get());
            epstoServiceCollectEpsDTO.setCode(optionalServiceCollectEps.get().getCode());
            epstoServiceCollectEpsDTO.setServiceId(optionalServiceCollectEps.get().getService().getId());
            epstoServiceCollectEpsDTO.setEpsId(optionalServiceCollectEps.get().getService().getEps().getId());
            epstoServiceCollectEpsDTO.setIsCloture(optionalServiceCollectEps.get().getIsCloture());
            return epstoServiceCollectEpsDTO;
        } else {
            throw new TechnicalException("Service Collect eps not found");
        }
    }

    public List<EpsAndServiceDto> findEpsForServiceCollectEps() {
        return epsRepository.findByStatusTrueAndServicesSizeGreaterThan0().stream()
                .map(eps -> {
                    List<Services> servicesWithNullEps = eps.getServices().stream()
                            .peek(service -> {
                                service.setEps(null);
                                service.setServiceCollectEpsList(null);
                            })
                            .filter(Services::getStatutIsActif)
                            .collect(Collectors.toList());

                    return new EpsAndServiceDto(
                            eps.getId(),
                            eps.getName(),
                            eps.getCode(),
                            servicesWithNullEps
                    );
                })
                .filter(epsAndServiceDto -> !epsAndServiceDto.getServices().isEmpty())
                .collect(Collectors.toList());
    }


    public void deleteServiceCollectEps(Long id){
        Optional<ServiceCollectEps> serviceCollectEps=serviceCollectEpsRepository.findById(id);

        if(serviceCollectEps.isPresent()){
            List<BudgetLine> budgetLines=budgetLineRepository.findByServiceCollectEps(serviceCollectEps.get());
            if(!budgetLines.isEmpty()){
                throw new IllegalArgumentException("Service  Collect contain  donations");
            }
            ServiceCollectEps serviceCollectEps1=serviceCollectEps.get();
            serviceCollectEps1.setIsDeleted(true);
            serviceCollectEpsRepository.save(serviceCollectEps1);
        }
    }

    public void clotureServiceCollectEps(Long id){
        Optional<ServiceCollectEps> serviceCollectEps=serviceCollectEpsRepository.findById(id);
        if(serviceCollectEps.isPresent()){
            ServiceCollectEps serviceCollectEps1=serviceCollectEps.get();
            serviceCollectEps1.setIsCloture(true);
            serviceCollectEpsRepository.save(serviceCollectEps1);
        }
    }
    public Page<DonationServiceCollectEpsDto> getDonationsByServiceCollectEps(
            Long id,
            Date donationMinDate,
            Date donationMaxDate,
            String donationType,
            String donorName,
            Double minAmount,
            Double maxAmount,
            Pageable pageable) {

        Optional<ServiceCollectEps> serviceCollectEpsOptional = serviceCollectEpsRepository.findById(id);

        if (serviceCollectEpsOptional.isPresent()) {
            List<BudgetLine> budgetLines = budgetLineRepository.findByServiceCollectEps(serviceCollectEpsOptional.get());

            // Apply filters
            Stream<BudgetLine> budgetLineStream = budgetLines.stream()
                    .filter(budgetLine -> {
                        boolean matches = true;

                        // Filter by donation date if provided
                        if (donationMinDate != null || donationMaxDate != null) {
                            Date receptionDate = budgetLine.getDonation().getReceptionDate();

                            if (receptionDate != null) {
                                if (donationMinDate != null) {
                                    matches = matches && !receptionDate.before(donationMinDate);
                                }
                                if (donationMaxDate != null) {
                                    matches = matches && !receptionDate.after(donationMaxDate);
                                }
                            }
                        }


                        // Filter by donation type if provided
                        if (donationType != null && !donationType.isEmpty()) {
                            matches = matches && donationType.equals(budgetLine.getDonation().getType());
                        }

                        // Filter by donor name if provided
                        if (donorName != null && !donorName.isEmpty()) {
                            Donor donor = budgetLine.getDonation().getDonor();
                            String actualDonorName = "";

                            if (donor instanceof DonorPhysical) {
                                DonorPhysical donorPhysical = (DonorPhysical) donor;
                                actualDonorName = donorPhysical.getFirstName() + " " + donorPhysical.getLastName();
                            } else if (donor instanceof DonorMoral) {
                                DonorMoral donorMoral = (DonorMoral) donor;
                                actualDonorName = donorMoral.getCompany();
                            } else {
                                DonorAnonyme donorAnonyme = (DonorAnonyme) donor;
                                actualDonorName = donorAnonyme.getName();
                            }

                            matches = matches && actualDonorName.toLowerCase().contains(donorName.toLowerCase());
                        }

                        // Filter by min amount if provided
                        if (minAmount != null) {
                            matches = matches && budgetLine.getAmount() >= minAmount;
                        }

                        // Filter by max amount if provided
                        if (maxAmount != null) {
                            matches = matches && budgetLine.getAmount() <= maxAmount;
                        }

                        return matches;
                    });

            // Convert to DTOs
            List<DonationServiceCollectEpsDto> allDonations = budgetLineStream
                    .map(budgetLine -> {
                        DonationServiceCollectEpsDto donationServiceCollectEpsDto = new DonationServiceCollectEpsDto();
                        donationServiceCollectEpsDto.setDonationCode(budgetLine.getDonation().getCode());
                        donationServiceCollectEpsDto.setDonationId(budgetLine.getDonation().getId());
                        donationServiceCollectEpsDto.setDonationDate(budgetLine.getDonation().getReceptionDate());
                        donationServiceCollectEpsDto.setDonationType(budgetLine.getDonation().getType());
                        donationServiceCollectEpsDto.setMontant(budgetLine.getAmount());
                        donationServiceCollectEpsDto.setDonorId(budgetLine.getDonation().getDonor().getId());

                        Donor donor = budgetLine.getDonation().getDonor();
                        if (donor instanceof DonorPhysical) {
                            DonorPhysical donorPhysical = (DonorPhysical) donor;
                            donationServiceCollectEpsDto.setDonorName(donorPhysical.getFirstName() + " " + donorPhysical.getLastName());
                        } else if (donor instanceof DonorMoral) {
                            DonorMoral donorMoral = (DonorMoral) donor;
                            donationServiceCollectEpsDto.setDonorName(donorMoral.getCompany());
                        } else {
                            DonorAnonyme donorAnonyme = (DonorAnonyme) donor;
                            donationServiceCollectEpsDto.setDonorName(donorAnonyme.getName());
                        }
                        return donationServiceCollectEpsDto;
                    })
                    .collect(Collectors.toList());

            // Apply pagination
            int start = (int) pageable.getOffset();
            int end = Math.min((start + pageable.getPageSize()), allDonations.size());

            List<DonationServiceCollectEpsDto> paginatedList = allDonations.subList(start, end);

            return new PageImpl<>(paginatedList, pageable, allDonations.size());
        }

        // Return empty page if service collect EPS not found
        return Page.empty(pageable);
    }

    public List<ServiceCollectEpsLightDto> getServiceCollectEpsForDonation(){
        List<ServiceCollectEps> serviceCollectEps=serviceCollectEpsRepository.getByIsDeletedNullAndIsClotureNull();
        serviceCollectEps= serviceCollectEps.stream().filter(serviceCollectEps1 -> {
            LocalDate now = LocalDate.now();
            int currentMonth = now.getMonthValue();
            int currentYear = now.getYear();
            return serviceCollectEps1.getAnnee() > currentYear ||
                    (serviceCollectEps1.getAnnee() == currentYear && serviceCollectEps1.getMois() >= currentMonth);

        }).toList();

        return serviceCollectEps.stream().map(serviceCollectEps1 -> new ServiceCollectEpsLightDto(serviceCollectEps1.getId(),serviceCollectEps1.getNom(),serviceCollectEps1.getService().getId())).toList();
    }
}
