package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.administration.ConsultServiceCollectEpsDTO;
import ma.almobadara.backend.dto.administration.ServiceCollectEpsDTO;
import ma.almobadara.backend.model.administration.ServiceCollectEps;
import ma.almobadara.backend.model.service.Services;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface ServiceCollectEpsMapper {

    @Mapping(source = "serviceId", target = "service.id") // Map serviceId to service object
    ServiceCollectEps serviceCollectEpsDTOtoServiceCollectEps(ServiceCollectEpsDTO serviceCollectEpsDTO);

    @Mapping(source = "service.id", target = "serviceId") // Map service object to serviceId
    ServiceCollectEpsDTO serviceCollectEpstoServiceCollectEpsDTO(ServiceCollectEps serviceCollectEps);

}
