package ma.almobadara.backend.model.beneficiary;


import jakarta.persistence.*;
import lombok.*;

@Getter
@Setter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
public class DiseaseTreatment extends BaseEntity{

    private Double cost;
    private Long typeId;
    private String comment;
    @ManyToOne
    @JoinColumn(name = "beneficiary_id")
    private Beneficiary beneficiary;

}
