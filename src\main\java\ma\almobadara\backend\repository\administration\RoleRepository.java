package ma.almobadara.backend.repository.administration;

import ma.almobadara.backend.model.administration.Role;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface RoleRepository extends JpaRepository<Role, Long> {
    Role findByName(String name);

    //find all
    Page<Role> findAll(Pageable pageable);

    Role findByCode(String code);
}