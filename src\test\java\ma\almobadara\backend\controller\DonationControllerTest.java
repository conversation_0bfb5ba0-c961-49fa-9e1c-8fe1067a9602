package ma.almobadara.backend.controller;


import ma.almobadara.backend.config.Messages;
import ma.almobadara.backend.config.TestSecurityConfig;
import ma.almobadara.backend.controller.donation.DonationController;
import ma.almobadara.backend.dto.donation.DonationDTO;
import ma.almobadara.backend.dto.donation.DonationHistoryDTO;
import ma.almobadara.backend.service.Donation.DonationService;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.cloud.autoconfigure.RefreshAutoConfiguration;
import org.springframework.context.annotation.Import;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.util.Arrays;
import java.util.List;

import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@WebMvcTest(DonationController.class)
@ExtendWith(MockitoExtension.class)
@Import({TestSecurityConfig.class, Messages.class})
@ImportAutoConfiguration(RefreshAutoConfiguration.class)
class DonationControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private DonationService donationService;

    private DonationDTO donationDTO;
    @BeforeEach
    void setUp() {
        donationDTO = new DonationDTO();
        donationDTO.setId(1L);
        donationDTO.setType("Money");
    }

    @Test
    void createDonation_ShouldReturn200_WhenSuccessful() throws Exception {
        // Arrange
        Mockito.when(donationService.addDonation(Mockito.any(DonationDTO.class)))
                .thenReturn(donationDTO);

        // Act & Assert
        mockMvc.perform(MockMvcRequestBuilders.post("/donations/create")
                        .contentType(MediaType.MULTIPART_FORM_DATA)
                        .flashAttr("donationDTO", donationDTO))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(1L))
                .andExpect(jsonPath("$.type").value("Money"));

        // Verify interaction
        Mockito.verify(donationService, times(1)).addDonation(Mockito.any(DonationDTO.class));
    }

    @Test
    void createDonation_ShouldReturn500_WhenExceptionThrown() throws Exception {
        // Arrange
        when(donationService.addDonation(Mockito.any(DonationDTO.class)))
                .thenThrow(new RuntimeException("Unexpected error"));

        // Act & Assert
        mockMvc.perform(MockMvcRequestBuilders.post("/donations/create")
                        .contentType(MediaType.MULTIPART_FORM_DATA)
                        .flashAttr("donationDTO", donationDTO))
                .andExpect(status().isInternalServerError());

        // Verify interaction
        verify(donationService, times(1)).addDonation(Mockito.any(DonationDTO.class));
    }

    @Test
    void findAll_ShouldReturn200_WhenSuccessful() throws Exception {

        List<DonationDTO> donationDTOList = Arrays.asList(
                DonationDTO.builder().id(1L).build(),
                DonationDTO.builder().id(2L).build()
        );

        Pageable pageable = PageRequest.of(0, 10);
        Page<DonationDTO> donationDTOPage = new PageImpl<>(donationDTOList, pageable, donationDTOList.size());


        when(donationService.getAllDonations(0,10,null,null,null,null,null,null,null,null,null,null,null)).thenReturn(
                donationDTOPage
        );

        mockMvc.perform(get("/donations/findAll")
                        .header("Accept", "application/json"))
                .andExpect(status().isOk());


    }

    @Test
    void findAll_ShouldReturn500_WhenUnsuccessful() throws Exception {

        when(donationService.getAllDonations(0,10,null,null,null,null,null,null,null,null,null,null,null))
                .thenThrow(new RuntimeException("Unexpected error"));

        mockMvc.perform(get("/donations/findAll")
                        .header("Accept", "application/json"))
                .andExpect(status().isInternalServerError());


    }

    @Test
    void findById_ShouldReturn200_WhenSuccessful() throws Exception {
        Long donationId = 3L;
        DonationDTO donationDTO = DonationDTO.builder().id(donationId).type("Charity").build();

        when(donationService.getDonationById(donationId)).thenReturn(donationDTO);

        // Act & Assert
        mockMvc.perform(get("/donations/{id}", donationId)  // Correct URI format with path variable
                        .header("Accept", "application/json"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(donationDTO.getId()))  // Assert ID in response
                .andExpect(jsonPath("$.type").value(donationDTO.getType()));  // Assert type in response
        // Verify that the service method is called once with the correct argument
        verify(donationService, times(1)).getDonationById(donationId);

    }

    @Test
    void findById_ShouldReturn404_WhenUnsuccessful() throws Exception {
        Long donationId = 3L;

        when(donationService.getDonationById(donationId))
                .thenThrow(new RuntimeException("User not found"));

        // Act & Assert
        mockMvc.perform(get("/donations/{id}", donationId)  // Correct URI format with path variable
                        .header("Accept", "application/json"))
                .andExpect(status().isNotFound());
        // Verify that the service method is called once with the correct argument
        verify(donationService, times(1)).getDonationById(donationId);

    }

    @Test
    void deleteDonation_ShouldReturn2xx_WhenSuccessful() throws Exception {
        Long donationId = 1L;
        DonationDTO donationDTO = DonationDTO.builder().id(donationId).type("Charity").build();

        when(donationService.getDonationById(donationId)).thenReturn(donationDTO);

        // Act & Assert
        mockMvc.perform(delete("/donations/delete/{id}", donationId)  // Correct URI format with path variable
                        .header("Accept", "application/json"))
                .andExpect(status().is2xxSuccessful());
        // Verify that the service method is called once with the correct argument

    }

    @Test
    void getDonationHistoryByDonationId_ShouldReturn200_WhenSuccessful() throws Exception {
        // Arrange
        Long donationId = 3L;
        List<DonationHistoryDTO> donationHistoryDTOList = Arrays.asList(
                new DonationHistoryDTO(1L, null,"Status Updated",null,null, null,null,null,null,null,null,null),
                new DonationHistoryDTO(2L,null, "Donation Processed",null, null,null,null,null,null,null,null,null)
        );

        when(donationService.getDonationHistory(donationId)).thenReturn(donationHistoryDTOList);

        // Act & Assert
        mockMvc.perform(get("/donations/history/{donationId}", donationId)
                        .header("Accept", "application/json"))
                .andExpect(status().isOk())  // Expecting 200 OK
                .andExpect(jsonPath("$.size()").value(donationHistoryDTOList.size()))  // Assert the size of the list
                .andExpect(jsonPath("$[0].id").value(donationHistoryDTOList.get(0).getId())) ; // Assert the first element

        // Verify that the service method is called once with the correct argument
        verify(donationService, times(1)).getDonationHistory(donationId);
    }

    @Test
    void getDonationHistoryByDonationId_ShouldReturn500_WhenUnsuccessful() throws Exception {
        // Arrange
        Long donationId = 3L;


        when(donationService.getDonationHistory(donationId)).thenThrow(new RuntimeException("Unexpected error"));

        // Act & Assert
        mockMvc.perform(get("/donations/history/{donationId}", donationId)
                        .header("Accept", "application/json"))
                .andExpect(status().isInternalServerError());  // Expecting 200 OK
        // Verify that the service method is called once with the correct argument
        verify(donationService, times(1)).getDonationHistory(donationId);
    }
}
