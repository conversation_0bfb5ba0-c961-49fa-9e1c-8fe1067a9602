package ma.almobadara.backend.model.family;


import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Objects;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ActionFamilyId implements Serializable {

    private Long family;
    private Long action;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ActionFamilyId that = (ActionFamilyId) o;
        return Objects.equals(family, that.family) && Objects.equals(action, that.action);
    }

    @Override
    public int hashCode() {
        return Objects.hash(family, action);
    }

}
