package ma.almobadara.backend.enumeration;

import lombok.Getter;

@Getter
public enum AgeGroup {
    UNDER_6("moins de 6 ans"),
    BETWEEN_6_12("6-12 ans"),
    BETWEEN_12_18("12-18 ans"),
    ADULTS("adultes");

    private final String label;

    AgeGroup(String label) {
        this.label = label;
    }

    public static AgeGroup fromId(Long id) {
        return switch (id.intValue()) {
            case 1 -> UNDER_6;
            case 2 -> BETWEEN_6_12;
            case 3 -> BETWEEN_12_18;
            case 4 -> ADULTS;
            default -> throw new IllegalArgumentException("Invalid AgeGroup ID");
        };
    }
}

