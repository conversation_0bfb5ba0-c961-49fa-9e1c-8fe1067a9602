package ma.almobadara.backend.dto.donor;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import ma.almobadara.backend.dto.aideComplemenatire.BeneficiaryAideComplemenatireDTO;
import ma.almobadara.backend.dto.takenInCharge.TakenInChargeDonorDTO;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class DonorReleveDto {

    //table donation
    private List<ReleveDonorDto> donations;
    private Double totalDonation;

    //TakenInCharge
    private List<TakenInChargeDonorDTO> takenInChargeDonors;

    //AideComplementaireDonorBeneficiary
    private List<BeneficiaryAideComplemenatireDTO> beneficiaryAideComplemenatireDTOList;

}
