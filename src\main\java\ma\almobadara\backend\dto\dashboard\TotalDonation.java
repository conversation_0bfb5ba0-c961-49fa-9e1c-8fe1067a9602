package ma.almobadara.backend.dto.dashboard;

import lombok.*;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class TotalDonation {

    private Integer month;
    private Integer year;
    private Double financialDonations;
    private Double natureDonations;
    private Double totalDonations;

//    public TotalDonation(Integer month, Integer year, Double financialDonations, Double natureDonations, Double totalDonations) {
//        this.month = month;
//        this.year = year;
//        this.financialDonations = financialDonations;
//        this.natureDonations = natureDonations;
//        this.totalDonations = totalDonations;
//    }
}
