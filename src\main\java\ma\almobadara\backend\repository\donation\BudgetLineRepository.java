package ma.almobadara.backend.repository.donation;

import ma.almobadara.backend.enumeration.BudgetLineStatus;
import ma.almobadara.backend.model.administration.ServiceCollectEps;
import ma.almobadara.backend.model.donation.BudgetLine;
import ma.almobadara.backend.model.donation.Donation;
import ma.almobadara.backend.model.service.Services;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface BudgetLineRepository  extends JpaRepository<BudgetLine, Long> {

    List<BudgetLine> findByServiceAndStatus(Services service, BudgetLineStatus status);

    @Query("SELECT b FROM BudgetLine b WHERE LOWER(b.service.name) LIKE LOWER(CONCAT('%', :query, '%')) OR LOWER(b.comment) LIKE LOWER(CONCAT('%', :query, '%'))  ")
    Page<BudgetLine> searchDonation(@Param("query") String query, Pageable pageable);

    @Query("SELECT b FROM BudgetLine b WHERE b.donation.donor.id = :id")
    List<BudgetLine> findBudgetLineByDonor(@Param("id") Long id);

    List<BudgetLine> findByServiceCollectEps(ServiceCollectEps serviceCollectEps);

    // getbudgetlines by donation id
    List<BudgetLine> findByDonationId(Long donorId);
    List<BudgetLine> findByDonationIdAndServiceId(Long donorId, Long serviceId);
    // find bydonationsIdandTypeodBudgetLine
    List<BudgetLine> findByDonationIdAndType(Long donationId, String type);
    //findByCode
    BudgetLine findByCode(String code);

    List<BudgetLine> findByTypePriseEnChargeId(Long typePriseEnChargeId);
    List<BudgetLine> findByServiceId(Long serviceId);


    List<BudgetLine> findByAideComplementaireId(Long aideComplementaireId);
    List<BudgetLine> findByAideComplementaireIdAndDonationIdAndServiceIdAndStatus(Long aideComplementaireId,Long donationId, Long serviceId,  BudgetLineStatus status);

    //findByDonationIdAndServiceIdAndStatus
    List<BudgetLine> findByDonationIdAndServiceIdAndStatusAndNatureBudgetLine(Long donation_id, Long service_id, BudgetLineStatus status,boolean isNature);
    List<BudgetLine> findByDonationIdAndServiceIdAndStatus(Long donation_id, Long service_id, BudgetLineStatus status);

    //findByTakenInChargeOperationId
    List<BudgetLine> findByTakenInChargeOperationId(Long takenInChargeOperationId);

    BudgetLine findFirstByDonationIdAndServiceIdAndStatus(Long donationId, Long serviceId, BudgetLineStatus status);

    List<BudgetLine> findByServiceIdAndStatus( Long service_id, BudgetLineStatus status);

    List<BudgetLine> findByAideComplementaireIdAndStatus(Long aideComplementaireId, BudgetLineStatus status);

    @Query("SELECT b FROM BudgetLine b " +
            "JOIN b.takenInChargeOperation o " +
            "JOIN o.takenInChargeDonor d " +
            "JOIN d.takenInCharge t " +
            "WHERE b.donation.id = :donationId " +
            "AND b.service.id = :serviceId " +
            "AND t.id = :takenInChargeId " +
            "AND b.status = :status")
    BudgetLine findFirstByDonationIdAndServiceIdAndTakenInChargeIdAndStatus(
            @Param("donationId") Long donationId,
            @Param("serviceId") Long serviceId,
            @Param("takenInChargeId") Long takenInChargeId,
            @Param("status") BudgetLineStatus status
    );

    @Query("SELECT b.code FROM BudgetLine b WHERE b.code LIKE :datePart ORDER BY b.code DESC LIMIT 1")
    String findLastCodeByDate(@Param("datePart") String datePart);

}
