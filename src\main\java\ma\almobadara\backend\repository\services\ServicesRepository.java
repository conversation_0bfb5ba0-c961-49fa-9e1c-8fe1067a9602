package ma.almobadara.backend.repository.services;

import ma.almobadara.backend.model.administration.Eps;
import ma.almobadara.backend.model.service.Services;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ServicesRepository extends JpaRepository<Services, Long>, JpaSpecificationExecutor<Services> {
    boolean existsByServiceCategoryIdAndServiceCategoryTypeId(Long serviceCategoryId, Long serviceCategoryTypeId);

    Optional<Services> findByServiceCategoryIdAndServiceCategoryTypeId(Long serviceCategoryId, Long serviceCategoryTypeId);
    List<Services> findByStatutIsActifTrue();


    List<Services> findByStatutIsActifTrueAndServiceCategoryId(Long serviceCategoryId);

    Optional<Services> findByServiceCategoryIdAndServiceCategoryTypeIdAndIsDedicatedToEpsAndEps(Long serviceCategoryId, Long serviceCategoryTypeId, Boolean isDedicatedToEps, Eps eps);







}
