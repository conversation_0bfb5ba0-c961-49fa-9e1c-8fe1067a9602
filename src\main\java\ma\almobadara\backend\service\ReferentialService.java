package ma.almobadara.backend.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.config.Messages;
import ma.almobadara.backend.controller.referentiel.RefController;
import ma.almobadara.backend.dto.referentiel.*;
import ma.almobadara.backend.exceptions.TechnicalException;
import org.springframework.stereotype.Service;

import static ma.almobadara.backend.util.constants.GlobalConstants.*;

@Service
@RequiredArgsConstructor
@Slf4j
public class ReferentialService {

    private final RefController refController;
    private final Messages messages;


    public CityDTO findCityById(Long id) throws TechnicalException {
        log.debug("start service findCityById id: {}", id);
        try {
            var city = refController.getParCity(id);
            return city.getBody();
        } catch (Exception e) {
            throw new TechnicalException(messages.get(CITY_NOT_FOUND));
        }
    }

    public ProfessionDTO findProfessionById(Long id) throws TechnicalException {
        log.debug("start service findProfessionById id: {}", id);
        try {
            var profession = refController.getMetProfession(id);
            return profession.getBody();
        } catch (Exception e) {
            throw new TechnicalException(messages.get(PROFESSION_NOT_FOUND));
        }
    }

    public AccommodationTypeDTO findAccommodationTypeById(Long id) throws TechnicalException {
        log.debug("start service findAccommodationById id: {}", id);
        try {
            var accommodationType = refController.getMetAccommodationType(id);
            return accommodationType.getBody();
        } catch (Exception e) {
            throw new TechnicalException(messages.get(ACCOMMODATION_NOT_FOUND));
        }
    }
    public AccommodationNatureDTO findAccommodationNatureById(Long id) throws TechnicalException {
        log.debug("start service findAccommodationById id: {}", id);
        try {
            var accommodationNature = refController.getParAccommodationNature(id);
            return accommodationNature.getBody();
        } catch (Exception e) {
            throw new TechnicalException(messages.get(ACCOMMODATION_NOT_FOUND));
        }
    }

    public DeathReasonDTO findMetDeathReasonById(Long id) throws TechnicalException {
        log.debug("start service findMetDeathReasonById id: {}", id);
        try {
            var metDeathReason = refController.getMetDeathReason(id);
            return metDeathReason.getBody();
        } catch (Exception e) {
            throw new TechnicalException(messages.get(MET_DEATH_REASON_NOT_FOUND));
        }
    }

    public EducationSystemTypeDTO findEducationSystemTypeById(Long id) throws TechnicalException {
        log.debug("start service findEducationSystemTypeById id: {}", id);
        try {
            var educationSystemType = refController.getConsEducationSystemType(id);
            return educationSystemType.getBody();
        } catch (Exception e) {
            throw new TechnicalException(messages.get(EDUCATION_SYSTEM_TYPE_NOT_FOUND));
        }
    }


    public TypeIdentityDTO findTypeIdentityById(Long id) throws TechnicalException {
        log.debug("start service findTypeIdentityById id: {}", id);
        try {
            var typeIdentity = refController.getParTypeIdentity(id);
            return typeIdentity.getBody();
        } catch (Exception e) {
            throw new TechnicalException(messages.get(TYPE_IDENTITY_NOT_FOUND));
        }
    }

    public SchoolLevelDTO findSchoolLevelById(Long id) throws TechnicalException {
        log.debug("start service findSchoolLevelById id: {}", id);
        try {
            var schoolLevel = refController.getParSchoolLevel(id);
            return schoolLevel.getBody();
        } catch (Exception e) {
            throw new TechnicalException(messages.get(SCOOL_LEVEL_NOT_FOUND));
        }
    }

    public CardTypeDTO findCardTypeById(Long id) throws TechnicalException {
        log.debug("start service findCardTypeById id: {}", id);
        try {
            var cardType = refController.getConsCardType(id);
            return cardType.getBody();
        } catch (Exception e) {
            throw new TechnicalException(messages.get(CARD_TYPE_NOT_FOUND));
        }
    }

    public ClotureMotifTypeDTO findMotifTypeById(Long id) throws TechnicalException {
        log.debug("start service findCardTypeById id: {}", id);
        try {
            var cardType = refController.getClotureMotifTypeById(id);
            return cardType.getBody();
        } catch (Exception e) {
            throw new TechnicalException(messages.get(CARD_TYPE_NOT_FOUND));
        }
    }

}
