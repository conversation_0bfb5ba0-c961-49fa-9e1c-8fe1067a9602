package ma.almobadara.backend.model.donation;

import jakarta.persistence.*;
import lombok.*;
import ma.almobadara.backend.model.communs.Note;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@IdClass(NoteDonationId.class)
public class NoteDonation {

    @Id
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "donation_id")
    private Donation donation;

    @Id
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "note_id")
    private Note note;

}
