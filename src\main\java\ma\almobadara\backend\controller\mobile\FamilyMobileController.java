package ma.almobadara.backend.controller.mobile;

import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.family.FamilyDTO;
import ma.almobadara.backend.dto.mobile.FamilyMobileDTO;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.service.mobile.FamilyMobileService;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;

import static ma.almobadara.backend.service.administration.CacheAdUserService.logUserInfo;

@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping("/mobile/family")
@CrossOrigin(origins = "*")
public class FamilyMobileController {

    private final FamilyMobileService familyMobileService;

    /**
     * Get all families for mobile with pagination
     * @param page Optional page number (defaults to 0)
     * @return Page of FamilyMobileDTO
     */
    @GetMapping("/all")
    public ResponseEntity<Page<FamilyMobileDTO>> getAllFamilies(
            @RequestParam(required = false) Optional<Integer> page) {

        logUserInfo("getAllFamiliesForMobile", "page: " + page.orElse(0));

        try {
            Page<FamilyMobileDTO> families = familyMobileService.getAllFamiliesForMobile(page);
            log.info("End resource getAllFamiliesForMobile. Retrieved families: {}, OK", families.getTotalElements());
            return new ResponseEntity<>(families, HttpStatus.OK);
        } catch (Exception e) {
            log.error("End resource getAllFamiliesForMobile. KO: {}", e.getMessage());
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get families for mobile filtered by assistant ID with pagination
     * @param assistantId The ID of the assistant
     * @param page Optional page number (defaults to 0)
     * @return Page of FamilyMobileDTO
     */
    @GetMapping("/by-assistant/{assistantId}")
    public ResponseEntity<Page<FamilyMobileDTO>> getFamiliesByAssistantId(
            @PathVariable Long assistantId,
            @RequestParam(required = false) Optional<Integer> page) {

        logUserInfo("getFamiliesByAssistantId", "assistantId: " + assistantId + ", page: " + page.orElse(0));

        try {
            Page<FamilyMobileDTO> families = familyMobileService.getFamiliesByAssistantId(assistantId, page);
            log.info("End resource getFamiliesByAssistantId. Retrieved families: {}, OK", families.getTotalElements());
            return new ResponseEntity<>(families, HttpStatus.OK);
        } catch (EntityNotFoundException e) {
            log.error("End resource getFamiliesByAssistantId. KO: {}", e.getMessage());
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        } catch (Exception e) {
            log.error("End resource getFamiliesByAssistantId. KO: {}", e.getMessage());
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get a family by ID
     * @param id The ID of the family
     * @return FamilyDTO with full details
     */
    @GetMapping("/{id}")
    public ResponseEntity<FamilyDTO> getFamilyById(@PathVariable Long id) {

        logUserInfo("getFamilyByIdMobile", "id: " + id);

        try {
            FamilyDTO family = familyMobileService.getFamilyById(id);
            log.info("End resource getFamilyByIdMobile. Retrieved family ID: {}, OK", id);
            return new ResponseEntity<>(family, new HttpHeaders(), HttpStatus.OK);
        } catch (EntityNotFoundException e) {
            log.error("End resource getFamilyByIdMobile. KO: {}", e.getMessage());
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        } catch (TechnicalException e) {
            log.error("End resource getFamilyByIdMobile. KO: {}", e.getMessage());
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
