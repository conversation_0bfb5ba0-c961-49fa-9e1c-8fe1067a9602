package ma.almobadara.backend.dto.takenInCharge.operationTakenInCharge;

import jakarta.persistence.Entity;
import lombok.*;
import ma.almobadara.backend.dto.communs.ActionDTO;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class DonorOperationDTO {

    private Long id;
    private String firstName;
    private String lastName;
    private String firstNameAr;
    private String lastNameAr;
    private String type;

}
