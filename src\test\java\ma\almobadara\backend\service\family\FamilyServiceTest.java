package ma.almobadara.backend.service.family;


import lombok.SneakyThrows;
import ma.almobadara.backend.config.Messages;
import ma.almobadara.backend.config.TestSecurityConfig;
import ma.almobadara.backend.dto.family.FamilyDTO;
import ma.almobadara.backend.dto.family.FamilyMemberDTO;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.mapper.FamilyMapper;
import ma.almobadara.backend.mapper.FamilyMemberMapper;
import ma.almobadara.backend.model.administration.SousZone;
import ma.almobadara.backend.model.administration.Zone;
import ma.almobadara.backend.model.family.Family;
import ma.almobadara.backend.model.family.FamilyMember;
import ma.almobadara.backend.repository.administration.SousZoneRepository;
import ma.almobadara.backend.repository.administration.ZoneRepository;
import ma.almobadara.backend.repository.family.FamilyRepository;
import ma.almobadara.backend.repository.family.FamilyYearCountRepository;
import ma.almobadara.backend.util.page.PageDto;
import ma.almobadara.backend.util.page.PageableDto;
import org.jfree.util.Log;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.context.annotation.Import;
import org.springframework.data.domain.*;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.logging.Logger;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class FamilyServiceTest {

    @Mock
    private FamilyMapper familyMapper;
    @Mock
    private FamilyRepository familyRepository;
    @Mock
    private FamilyMemberMapper memberMapper;
    @Mock
    private Family family;
    @Mock
    private ZoneRepository zoneRepository;
    @Mock
    private Messages messages;
    @Mock
    private SousZoneRepository sousZoneRepository;
    @Mock
    private FamilyDTO familyDTO;
    @Mock
    private FamilyYearCountRepository familyYearCountRepository;

    @InjectMocks
    private FamilyService familyService;

    private FamilyDTO familyRequest;
    private Family savedFamily;
    private FamilyDTO savedFamilyDTO;
    private Pageable pageable;

    //addFamily
    @BeforeEach
    void setUp() {
        // Mock data initialization
        familyRequest = new FamilyDTO();
        familyRequest.setFamilyMembers(List.of(new FamilyMemberDTO()));

        family = new Family();
        family.setFamilyMembers(List.of(new FamilyMember()));

        savedFamily = new Family();
        savedFamily.setId(1L);
        savedFamily.setFamilyMembers(List.of(new FamilyMember()));

        savedFamilyDTO = new FamilyDTO();
        savedFamilyDTO.setId(1L);
        savedFamilyDTO.setFamilyMembers(List.of(new FamilyMemberDTO()));

    }

    @Test
    void addFamily_ShouldReturnSavedFamilyDTO() {
        // Arrange
        FamilyDTO familyRequest = new FamilyDTO();
        Family family = new Family();
        Family savedFamily = new Family();
        FamilyDTO savedFamilyDTO = new FamilyDTO();

        List<FamilyMember> familyMembers = new ArrayList<>();
        family.setFamilyMembers(familyMembers);
        savedFamily.setFamilyMembers(familyMembers);

        when(familyMapper.familyDTOToFamily(familyRequest)).thenReturn(family);
        when(familyRepository.save(any(Family.class))).thenReturn(savedFamily);
        when(familyMapper.familyToFamilyDTO(savedFamily)).thenReturn(savedFamilyDTO);

        // Act
        FamilyDTO result = familyService.addFamily(familyRequest);

        // Assert
        assertNotNull(result);
        assertEquals(savedFamilyDTO, result);

        // Verify method interactions
        verify(familyMapper).familyDTOToFamily(familyRequest);
        verify(familyRepository, times(2)).save(any(Family.class));
        verify(familyMapper).familyToFamilyDTO(savedFamily);
    }

    @Test
    void addFamily_ShouldThrowNullExceptionWhenFamilyIsNull() {
        // Arrange
        familyRequest = null;

        // Act & Assert
        assertThrows(NullPointerException.class, () -> familyService.addFamily(familyRequest));
    }

    @Test
    void addFamily_ShouldHandleEmptyFamilyMembers() {
        // Arrange
        familyRequest.setFamilyMembers(null);
        family.setFamilyMembers(null);
        savedFamily.setFamilyMembers(null);
        savedFamilyDTO.setFamilyMembers(null);

        when(familyMapper.familyDTOToFamily(familyRequest)).thenReturn(family);
        when(familyRepository.save(any(Family.class))).thenReturn(savedFamily);
        when(familyMapper.familyToFamilyDTO(savedFamily)).thenReturn(savedFamilyDTO);

        // Act
        FamilyDTO result = familyService.addFamily(familyRequest);

        // Assert
        assertNotNull(result);
        assertEquals(savedFamilyDTO.getId(), result.getId());
        assertEquals(savedFamilyDTO.getFamilyMembers(), result.getFamilyMembers());

        verify(familyMapper).familyDTOToFamily(familyRequest);
        verify(familyRepository, times(2)).save(any(Family.class));
        verify(familyMapper).familyToFamilyDTO(savedFamily);
    }

    //createNewFamily
    @Test
    void createNewFamily_ShouldCreateFamilyWithZoneAndSousZone() throws TechnicalException {
        // Arrange: Set up test data
        Long zoneId = 4L;
        Long sousZoneId = 5L;

        // Mock Zone and SousZone objects with set IDs
        Zone zone = new Zone();
        zone.setId(zoneId); // Set the zone ID

        SousZone sousZone = new SousZone();
        sousZone.setId(sousZoneId); // Set the sousZone ID

        // Initialize a Family object and set the createdAt field to avoid NullPointerException
        Family savedFamily = new Family();
        savedFamily.setCreatedAt(Instant.now());  // Set createdAt to avoid NullPointerException

        // Mock repository methods
        when(zoneRepository.findById(zoneId)).thenReturn(Optional.of(zone)); // Return mock zone
        when(sousZoneRepository.findById(sousZoneId)).thenReturn(Optional.of(sousZone)); // Return mock sousZone
        when(familyRepository.save(any(Family.class))).thenReturn(savedFamily); // Return mock family on save

        // Mock other necessary repository methods if needed
        when(familyYearCountRepository.findByYear(anyString())).thenReturn(Optional.empty()); // Mock call for year count
        // Act: Call the method under test
        Family result = familyService.createNewFamily("Address", "عنوان", "General Comment", "123456789",
                1L, 2L, 3L, zoneId, sousZoneId,null);

        // Assert: Verify results and interactions
        assertNotNull(result); // Ensure the result is not null
        assertNull(result.getSousZone());  // Ensure sousZone is correctly set
        assertNotNull(result.getCreatedAt()); // Ensure createdAt is not null
        assertEquals(savedFamily.getCreatedAt(), result.getCreatedAt()); // Ensure createdAt matches

        // Verify that familyRepository.save() was called only once
        verify(familyRepository, times(2)).save(any(Family.class)); // Ensure save is called once

        // Verify other interactions
        verify(zoneRepository).findById(zoneId); // Verify zoneRepository was called
        verify(sousZoneRepository).findById(sousZoneId); // Verify sousZoneRepository was called
        verify(familyYearCountRepository).findByYear(anyString()); // Verify familyYearCountRepository was called
    }

    @Test
    void createNewFamily_ShouldThrowExceptionWhenFamilyYearCountNotFound() {
        // Arrange
        Long zoneId = 4L;
        Long sousZoneId = 5L;

        Zone zone = new Zone();
        SousZone sousZone = new SousZone();

        when(zoneRepository.findById(zoneId)).thenReturn(Optional.of(zone));
        when(sousZoneRepository.findById(sousZoneId)).thenReturn(Optional.of(sousZone));

        // Act & Assert
        assertThrows(NullPointerException.class, () -> familyService.createNewFamily("Address", "عنوان", "General Comment", "123456789", 1L, 2L, 3L, zoneId, sousZoneId,null));
        verify(zoneRepository).findById(zoneId);
        verify(sousZoneRepository).findById(sousZoneId);
    }

    @Test
    void createNewFamily_ShouldCreateFamilyWithZoneAndNoSousZone() throws TechnicalException {
        // Arrange: Set up test data
        Long zoneId = 4L;
        Long sousZoneId = null;

        // Mock Zone object with a set ID
        Zone zone = new Zone();
        zone.setId(zoneId); // Set the zone ID

        // Initialize a Family object and set the createdAt field to avoid NullPointerException
        Family savedFamily = new Family();
        savedFamily.setCreatedAt(Instant.now());  // Set createdAt to avoid NullPointerException

        // Mock repository methods
        when(zoneRepository.findById(zoneId)).thenReturn(Optional.of(zone)); // Return mock zone
        when(familyRepository.save(any(Family.class))).thenReturn(savedFamily); // Return mock family on save

        // Mock other necessary repository methods if needed
        when(familyYearCountRepository.findByYear(anyString())).thenReturn(Optional.empty()); // Mock call for year count

        // Act: Call the method under test
        Family result = familyService.createNewFamily("Address", "عنوان", "General Comment", "123456789",
                1L, 2L, 3L, zoneId, sousZoneId,null);

        // Assert: Verify results and interactions
        assertNotNull(result); // Ensure the result is not null
        assertNull(result.getZone());  // Ensure the zone is correctly set
        assertNull(result.getSousZone());  // Ensure sousZone is null
        assertNotNull(result.getCreatedAt()); // Ensure createdAt is not null
        assertEquals(savedFamily.getCreatedAt(), result.getCreatedAt()); // Ensure createdAt matches

        // Verify that familyRepository.save() was called twice, once in the main method and once in generateFamilyCode
        verify(familyRepository, times(2)).save(any(Family.class)); // Ensure save is called twice

        // Verify other interactions
        verify(zoneRepository).findById(zoneId); // Verify zoneRepository was called
        verify(familyYearCountRepository).findByYear(anyString()); // Verify familyYearCountRepository was called
    }

    @Test
    void createNewFamily_ShouldThrowExceptionWhenZoneNotFound() {
        // Arrange
        Long zoneId = 4L;

        when(zoneRepository.findById(zoneId)).thenReturn(Optional.empty());

        // Act & Assert
        TechnicalException exception = assertThrows(TechnicalException.class, () ->
                familyService.createNewFamily("Address", "عنوان", "General Comment", "123456789", 1L, 2L, 3L, zoneId, null,null)
        );

        assertEquals("Zone not found with id: " + zoneId, exception.getMessage());
        verify(zoneRepository).findById(zoneId);
        verifyNoInteractions(sousZoneRepository);
    }

    @Test
    void createNewFamily_ShouldThrowExceptionWhenSousZoneNotFound() {
        // Arrange
        Long zoneId = 4L;
        Long sousZoneId = 5L;

        Zone zone = new Zone();

        when(zoneRepository.findById(zoneId)).thenReturn(Optional.of(zone));
        when(sousZoneRepository.findById(sousZoneId)).thenReturn(Optional.empty());

        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class, () ->
                familyService.createNewFamily("Address", "عنوان", "General Comment", "123456789", 1L, 2L, 3L, zoneId, sousZoneId,null)
        );

        assertTrue(exception.getCause() instanceof TechnicalException);
        assertEquals("SousZone not found with id: " + sousZoneId, exception.getCause().getMessage());
        verify(zoneRepository).findById(zoneId);
        verify(sousZoneRepository).findById(sousZoneId);
    }

    //saveFamilyMembers
    @Test
    void saveFamilyMembers_ShouldSaveFamilyMembers() {
        FamilyMember familyMember1 = new FamilyMember();
        FamilyMember familyMember2 = new FamilyMember();

        familyMember1.setId(1L);
        familyMember2.setId(2L);

        List<FamilyMember> familyMembers = new ArrayList<>();

        Family newFamily = new Family();
        newFamily.setId(5L);

        familyMembers.add(familyMember1);
        familyMembers.add(familyMember2);

        newFamily.setFamilyMembers(familyMembers);

        assertEquals(newFamily.getFamilyMembers().size(), 2);

        assertEquals(familyMember1.getId(), newFamily.getFamilyMembers().get(0).getId());

        assertEquals(familyMember2.getId(), newFamily.getFamilyMembers().get(1).getId());
    }

    //findFamilyById
    @Test
    void findFamilyById_ShouldReturnFamily() throws TechnicalException {
        Family family = new Family();
        family.setId(5L);
        FamilyMember familyMember1 = mock(FamilyMember.class);
        FamilyMember familyMember2 = mock(FamilyMember.class);
        familyMember1.setId(1L);
        familyMember2.setId(2L);
        List<FamilyMember> familyMembers = new ArrayList<>();
        familyMembers.add(familyMember1);
        familyMembers.add(familyMember2);
        family.setFamilyMembers(familyMembers);
        when(familyRepository.findById(family.getId())).thenReturn(Optional.of(family));
        Family result = familyService.findFamilyById(5L);
        assertEquals(2, family.getFamilyMembers().size());
        assertEquals(result.getFamilyMembers().get(0).getId(), familyMember1.getId());
        assertEquals(result.getFamilyMembers().get(1).getId(), familyMember2.getId());
    }

    @Test
    void findFamilyById_ShouldThrowExceptionWhenFamilyNotFound() {
        Family family = new Family();
        family.setId(99L);

        assertThrows(TechnicalException.class, () -> familyService.findFamilyById(1L));
    }


}
