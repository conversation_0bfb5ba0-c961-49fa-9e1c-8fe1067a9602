package ma.almobadara.backend.service.donor;

import com.google.common.io.ByteStreams;
import io.minio.*;
import io.minio.errors.MinioException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.config.Messages;
import ma.almobadara.backend.exceptions.TechnicalException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;

import static ma.almobadara.backend.util.constants.GlobalConstants.*;

@Service
@Slf4j
@RequiredArgsConstructor
public class MinioService {
    @Value("${minio.access.name}")
    String accessKey;
    @Value("${minio.access.secret}")
    String accessSecret;
    @Value("${minio.url}")
    String minioUrl;
    @Value("${minio.bucket}")
    String bucketName;

    Long partSize = -1L;
    private final Messages messages;

    public void WriteToMinIO(MultipartFile file, String path, String fileName) {
        // Define maximum file size (e.g., 10 MB)
        long maxFileSize = 10 * 1024 * 1024; // 10 MB

        // Validate file size
        if (file.getSize() > maxFileSize) {
            throw new IllegalArgumentException("File size exceeds the maximum limit of " + (maxFileSize / (1024 * 1024)) + " MB.");
        }

        try {
            MinioClient minioClient = MinioClient.builder().endpoint(minioUrl)
                    .credentials(accessKey, accessSecret).build();

            boolean bucketExists = minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build());
            if (!bucketExists) {
                minioClient.makeBucket(MakeBucketArgs.builder().bucket(bucketName).build());
            }

            PutObjectArgs p = PutObjectArgs.builder().bucket(bucketName).object(path + fileName).stream(file.getInputStream(), file.getSize(), partSize).build();

            minioClient.putObject(p);

        } catch (MinioException | InvalidKeyException | IOException | NoSuchAlgorithmException e) {
            log.error("Error occurred: " + e);
        }
    }


    public void DeleteFromMinIo(String path) throws TechnicalException {
        try {
            MinioClient minioClient = MinioClient.builder().endpoint(minioUrl)
                    .credentials(accessKey, accessSecret).build();
            RemoveObjectArgs a = RemoveObjectArgs.builder().bucket(bucketName).object(path).build();
            minioClient.removeObject(a);
        } catch (MinioException | InvalidKeyException | NoSuchAlgorithmException e) {
            log.error("Error occurred while deleting from MinIO: {}", e.getMessage(), e);
            throw new TechnicalException(messages.get(FAILED_TO_DELETE_FILE_FROM_MINIO));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public byte[] downloadFromMinIO(String path) throws TechnicalException {
        try {
            MinioClient minioClient = MinioClient.builder().endpoint(minioUrl)
                    .credentials(accessKey, accessSecret).build();
            GetObjectArgs getObjectArgs = GetObjectArgs.builder().bucket(bucketName).object(path).build();
            InputStream stream = minioClient.getObject(getObjectArgs);
            return ByteStreams.toByteArray(stream);
        } catch (MinioException | InvalidKeyException | NoSuchAlgorithmException e) {
            log.error("Error occurred while downloading from MinIO: {}", e.getMessage(), e);
            throw new TechnicalException(messages.get(FAILED_TO_DOWNLOAD_FILE_FROM_MINIO));
        } catch (IOException e) {
            log.error("IO error occurred while downloading from MinIO: {}", e.getMessage(), e);
            throw new TechnicalException(messages.get(FAILED_TO_READ_OR_WRITE_FILE_TO_MINIO));
        }
    }

    public byte[] ReadFromMinIO(String path,Boolean withBase64) throws TechnicalException {
        try {
            MinioClient minioClient = MinioClient.builder().endpoint(minioUrl)
                    .credentials(accessKey, accessSecret).build();

            GetObjectArgs getObjectArgs = GetObjectArgs.builder().bucket(bucketName).object(path).build();

            InputStream stream = minioClient.getObject(getObjectArgs);
            byte[] bytes = ByteStreams.toByteArray(stream);
            if(withBase64 != null && !withBase64){
                return bytes;
            }
            else{
                return Base64.getEncoder().encode(bytes);
            }
        } catch (MinioException | InvalidKeyException | NoSuchAlgorithmException e) {
            log.error("Error occurred: {}", e.getMessage(), e);
            throw new TechnicalException(messages.get(FAILED_TO_DOWNLOAD_FILE_FROM_MINIO));
        } catch (IOException e) {
            log.error("IO error occurred: {}", e.getMessage(), e);
            throw new TechnicalException(messages.get(FAILED_TO_READ_OR_WRITE_FILE_TO_MINIO));
        }
    }

}
