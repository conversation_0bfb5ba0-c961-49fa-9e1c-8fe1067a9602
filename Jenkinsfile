def customConfig(env) {
	if (env == 'develop') {
		customconf = ['dev','mob-dev','mobadaracreds','*************']
		return customconf
	}
	else if (env == 'recette') {
		customconf = ['recette','mob-rct','mobadaracreds-rct','*************']
		return customconf
	}
}

pipeline {
	agent { label 'master' }
	environment {
		pom = readMavenPom file: 'pom.xml'
		PROJECT_NAME = "${pom.artifactId}"
		BRANCH_NAME = "${env.BRANCH_NAME}"
	}

	stages {
		stage("Build") {
			steps {
				script {
					sh "chmod +x ./mvnw && ./mvnw clean install -DskipTests"
				}
			}
		}

		stage("Code Analysis") {
			when {
				expression {true}
			}
			environment {
				scannerHome = tool 'SonarScanner'
			}
			steps {
				withSonarQubeEnv('SonarQubeServer') {
					sh "${scannerHome}/bin/sonar-scanner -Dsonar.filesize.limit=200 \
					            -Dsonar.java.binaries=target/classes \
								-Dsonar.coverage.jacoco.xmlReportPaths=target/site/jacoco/jacoco.xml \
								-Dsonar.projectKey=$PROJECT_NAME \
								-Dsonar.coverage.exclusions=**/config/**/*"
				}
			}
		}
		
		stage("push artifact") {
			steps {
				script {
		        def credentialsId = customConfig("${BRANCH_NAME}").get(2)
		        def server_ip = customConfig("${BRANCH_NAME}").get(3)
				
				withCredentials([usernamePassword(credentialsId: "${credentialsId}", passwordVariable: 'MOBPASSWD', usernameVariable: 'MOBUSER')]) {
				echo ' copying jar to remote vm'
				sh """sshpass -p ${MOBPASSWD.replace('^','\\^').replace('&', '\\&').replace('$','\\$')} scp -o PubkeyAuthentication=no -o PreferredAuthentications=password -o StrictHostKeyChecking=no $WORKSPACE/target/*.jar ${MOBUSER}@"${server_ip}":/home/<USER>/appback/app.jar""" 
				}
				}
			}
		}

		stage("run app") {
			steps {
				script {
				def profile = customConfig("${BRANCH_NAME}").get(0)
		        def env = customConfig("${BRANCH_NAME}").get(1)
		        def credentialsId = customConfig("${BRANCH_NAME}").get(2)
		        def server_ip = customConfig("${BRANCH_NAME}").get(3)
				
				withCredentials([usernamePassword(credentialsId: "${credentialsId}", passwordVariable: 'MOBPASSWD', usernameVariable: 'MOBUSER')]) {
				echo ' running jar'
				sh """sshpass -p ${MOBPASSWD.replace('^','\\^').replace('&', '\\&').replace('$','\\$')} ssh -o PubkeyAuthentication=no -o PreferredAuthentications=password -o StrictHostKeyChecking=no ${MOBUSER}@"${server_ip}" << EOSSH
				sudo systemctl restart appback.service
				exit
			EOSSH """
				}
				}
			}
		}
	}

	post { 
		always { 
			cleanWs()
		}
	}
}
