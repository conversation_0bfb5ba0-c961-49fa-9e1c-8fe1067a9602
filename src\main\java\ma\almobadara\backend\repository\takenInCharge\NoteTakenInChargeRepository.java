package ma.almobadara.backend.repository.takenInCharge;

import ma.almobadara.backend.model.takenInCharge.NoteTakenInCharge;
import ma.almobadara.backend.model.takenInCharge.TakenInCharge;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface NoteTakenInChargeRepository extends JpaRepository<NoteTakenInCharge, Long> {

    Iterable<NoteTakenInCharge> findByTakenInCharge(TakenInCharge takenInCharge);

    List<NoteTakenInCharge> findByNoteId(Long id);

    List<NoteTakenInCharge> findByTakenInChargeId(Long takenInChargeId);

}
