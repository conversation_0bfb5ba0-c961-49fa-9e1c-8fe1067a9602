package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.reclamation.ReclamationDTO;
import ma.almobadara.backend.model.reclamation.Reclamation;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(componentModel = "spring")
public interface ReclamationMapper {

    @Mapping(source = "donor.id", target = "donorId")
    @Mapping(source = "respondedAt", target = "respondedAt")
    @Mapping(target = "donor", ignore = true)
    ReclamationDTO toDTO(Reclamation reclamation);

    @Mapping(source = "donorId", target = "donor.id")
    @Mapping(source = "respondedAt", target = "respondedAt")
    Reclamation toEntity(ReclamationDTO reclamationDTO);

    @Mapping(source = "respondedAt", target = "respondedAt")
    void updateReclamationFromDTO(ReclamationDTO reclamationDTO, @MappingTarget Reclamation reclamation);
}
