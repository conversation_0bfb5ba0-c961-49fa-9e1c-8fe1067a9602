package ma.almobadara.backend.controller.donor;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.donor.DonorNoteDetailsDTO;
import ma.almobadara.backend.service.donor.NoteDonorService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping("/donors")
public class NoteDonorController {

    private final NoteDonorService noteDonorService;

    @GetMapping("/getNotesByDonorId/{donorId}")
    public List<DonorNoteDetailsDTO> getNotesByDonorId(@PathVariable Long donorId) {
        List<DonorNoteDetailsDTO> noteDetailsDTOS = noteDonorService.getNotesByDonorId(donorId);
        return ResponseEntity.ok(noteDetailsDTOS).getBody();
    }

}
