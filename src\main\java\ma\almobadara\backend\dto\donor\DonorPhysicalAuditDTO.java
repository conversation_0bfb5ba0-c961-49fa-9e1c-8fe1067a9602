package ma.almobadara.backend.dto.donor;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import ma.almobadara.backend.dto.referentiel.DonorStatusDTO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;


@Getter
@Setter
@NoArgsConstructor
@SuperBuilder
@AllArgsConstructor
public class DonorPhysicalAuditDTO {
    private String nom;

    private String prenom;

    private String prenomArabe;

    private String nomArabe;

    private String sexe;

    private String email;

    private String telephone;

//    private String pictureUrl;
//
//    private String picture64;
//
//    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
//    private MultipartFile picture;

    private String type = "Physique";
    private String statut;

    private String numIdentite;
    private String adresse;
    private String adresseArabe;


    private List<String> canalCommunications;

    private List<String> languageCommunications;

    private String TypeIdentite;

    private String profession;

    private String AnneePremiereDonation;

    @Override
    public String toString() {
        return "DonorPhysicalAuditDTO{" +
                "nom='" + nom + '\'' +
                ", prenom='" + prenom + '\'' +
                ", prenomArabe='" + prenomArabe + '\'' +
                ", nomArabe='" + nomArabe + '\'' +
                ", sexe='" + sexe + '\'' +
                ", email='" + email + '\'' +
                ", telephone='" + telephone + '\'' +
                ", type='" + type + '\'' +
                ", statut='" + statut + '\'' +
                ", numIdentite='" + numIdentite + '\'' +
                ", adresse='" + adresse + '\'' +
                ", adresseArabe='" + adresseArabe + '\'' +
                ", canalCommunications=" + canalCommunications +
                ", languageCommunications=" + languageCommunications +
                ", TypeIdentite='" + TypeIdentite + '\'' +
                ", profession='" + profession + '\'' +
                ", AnneePremiereDonation='" + AnneePremiereDonation + '\'' +
                '}';
    }
}
