package ma.almobadara.backend.service.reclamation;

import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.reclamation.ReclamationDTO;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.mapper.ReclamationMapper;
import ma.almobadara.backend.model.donor.Donor;
import ma.almobadara.backend.model.donor.DonorPhysical;
import ma.almobadara.backend.model.donor.DonorMoral;
import ma.almobadara.backend.model.reclamation.Reclamation;
import ma.almobadara.backend.repository.donor.DonorRepository;
import ma.almobadara.backend.repository.reclamation.ReclamationRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class ReclamationService {

    private final ReclamationRepository reclamationRepository;
    private final DonorRepository donorRepository;
    private final ReclamationMapper reclamationMapper;

    /**
     * Create a new reclamation
     * @param reclamationDTO The reclamation data
     * @return The created reclamation
     * @throws TechnicalException if there's an error creating the reclamation
     */
    @Transactional
    public ReclamationDTO createReclamation(ReclamationDTO reclamationDTO) throws TechnicalException {
        log.debug("Start service createReclamation");

        try {
            // Validate donor if provided and set donor name


            // Convert DTO to entity
            Reclamation reclamation = reclamationMapper.toEntity(reclamationDTO);

            // Save the reclamation
            Reclamation savedReclamation = reclamationRepository.save(reclamation);

            // Get the saved reclamation as DTO
            ReclamationDTO savedReclamationDTO = reclamationMapper.toDTO(savedReclamation);

            // Set the donor name in the returned DTO
            savedReclamationDTO.setDonorName(reclamationDTO.getDonorName());

            log.debug("End service createReclamation");
            return savedReclamationDTO;
        } catch (Exception e) {
            log.error("Error creating reclamation: {}", e.getMessage());
            throw new TechnicalException("Error creating reclamation: " + e.getMessage());
        }
    }

    /**
     * Get all reclamations with pagination
     * @param pageable Pagination information
     * @return Page of reclamations
     */
    public Page<ReclamationDTO> getAllReclamations(Pageable pageable) {
        log.debug("Start service getAllReclamations");
        Page<Reclamation> reclamations = reclamationRepository.findAll(pageable);

        log.debug("End service getAllReclamations");
        return reclamations.map(reclamation -> {
            ReclamationDTO dto = reclamationMapper.toDTO(reclamation);
            setDonorName(dto, reclamation);
            dto.setDonor(null);
            return dto;
        });
    }

    /**
     * Get a reclamation by ID
     * @param id The reclamation ID
     * @return The reclamation
     * @throws TechnicalException if the reclamation is not found
     */
    public ReclamationDTO getReclamationById(Long id) throws TechnicalException {
        log.debug("Start service getReclamationById with ID: {}", id);

        Reclamation reclamation = reclamationRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Reclamation not found with ID: " + id));

        ReclamationDTO dto = reclamationMapper.toDTO(reclamation);
        setDonorName(dto, reclamation);
        dto.setDonor(null);

        log.debug("End service getReclamationById");
        return dto;
    }

    /**
     * Update a reclamation
     * @param id The reclamation ID
     * @param reclamationDTO The updated reclamation data
     * @return The updated reclamation
     * @throws TechnicalException if there's an error updating the reclamation
     */
    @Transactional
    public ReclamationDTO updateReclamation(Long id, ReclamationDTO reclamationDTO) throws TechnicalException {
        log.debug("Start service updateReclamation with ID: {}", id);

        try {
            // Find the existing reclamation
            Reclamation existingReclamation = reclamationRepository.findById(id)
                    .orElseThrow(() -> new EntityNotFoundException("Reclamation not found with ID: " + id));

            // Validate donor if provided
            if (reclamationDTO.getDonorId() != null) {
                Donor donor = donorRepository.findById(reclamationDTO.getDonorId())
                        .orElseThrow(() -> new EntityNotFoundException("Donor not found with ID: " + reclamationDTO.getDonorId()));
                existingReclamation.setDonor(donor);
            }

            // Update fields
            existingReclamation.setTitle(reclamationDTO.getTitle());
            existingReclamation.setDescription(reclamationDTO.getDescription());

            // Update new fields
            if (reclamationDTO.getStatus() != null) {
                existingReclamation.setStatus(reclamationDTO.getStatus());
            }

            // Check if admin is responding (response or respondedBy is being updated)
            boolean isResponding = (reclamationDTO.getResponse() != null && !reclamationDTO.getResponse().equals(existingReclamation.getResponse()))
                    || (reclamationDTO.getRespondedBy() != null && !reclamationDTO.getRespondedBy().equals(existingReclamation.getRespondedBy()));

            existingReclamation.setResponse(reclamationDTO.getResponse());
            existingReclamation.setRespondedBy(reclamationDTO.getRespondedBy());

            // If admin is responding, set respondedAt to current time
            if (isResponding) {
                existingReclamation.setRespondedAt(LocalDateTime.now());
            } else {
                // Otherwise, use the value from DTO (if any)
                existingReclamation.setRespondedAt(reclamationDTO.getRespondedAt());
            }

            // Save the updated reclamation
            Reclamation updatedReclamation = reclamationRepository.save(existingReclamation);

            // Convert to DTO and set donor name
            ReclamationDTO updatedDTO = reclamationMapper.toDTO(updatedReclamation);
            setDonorName(updatedDTO, updatedReclamation);

            log.debug("End service updateReclamation");
            return updatedDTO;
        } catch (Exception e) {
            log.error("Error updating reclamation: {}", e.getMessage());
            throw new TechnicalException("Error updating reclamation: " + e.getMessage());
        }
    }

    /**
     * Delete a reclamation
     * @param id The reclamation ID
     * @throws TechnicalException if there's an error deleting the reclamation
     */
    @Transactional
    public void deleteReclamation(Long id) throws TechnicalException {
        log.debug("Start service deleteReclamation with ID: {}", id);

        try {
            // Check if the reclamation exists
            if (!reclamationRepository.existsById(id)) {
                throw new EntityNotFoundException("Reclamation not found with ID: " + id);
            }

            // Delete the reclamation
            reclamationRepository.deleteById(id);

            log.debug("End service deleteReclamation");
        } catch (Exception e) {
            log.error("Error deleting reclamation: {}", e.getMessage());
            throw new TechnicalException("Error deleting reclamation: " + e.getMessage());
        }
    }

    /**
     * Get all reclamations by donor ID
     * @param donorId The donor ID
     * @return List of reclamations
     */
    public List<ReclamationDTO> getReclamationsByDonorId(Long donorId) {
        log.debug("Start service getReclamationsByDonorId with donor ID: {}", donorId);

        List<Reclamation> reclamations = reclamationRepository.findByDonorId(donorId);

        // Get the donor information before removing it
        Donor donor = null;
        if (!reclamations.isEmpty() && reclamations.get(0).getDonor() != null) {
            donor = reclamations.get(0).getDonor();
        }

        //remove donor from reclamations
        reclamations.forEach(reclamation -> reclamation.setDonor(null));

        log.debug("End service getReclamationsByDonorId");
        List<ReclamationDTO> dtos = reclamations.stream()
                .map(reclamationMapper::toDTO)
                .collect(Collectors.toList());

        // Set donor name for all DTOs
        if (donor != null) {
            final Donor finalDonor = donor;
            dtos.forEach(dto -> {
                dto.setDonorId(donorId);
                if (finalDonor instanceof DonorPhysical) {
                    DonorPhysical physicalDonor = (DonorPhysical) finalDonor;
                    dto.setDonorName(physicalDonor.getFirstName() + " " + physicalDonor.getLastName());
                } else if (finalDonor instanceof DonorMoral) {
                    DonorMoral moralDonor = (DonorMoral) finalDonor;
                    dto.setDonorName(moralDonor.getCompany());
                } else if (finalDonor.getAnonymeId() != null) {
                    dto.setDonorName("Anonymous");
                } else {
                    dto.setDonorName("Unknown");
                }
            });
        } else {
            dtos.forEach(dto -> dto.setDonorName("No Donor"));
        }

        return dtos;
    }

    /**
     * Get all reclamations by donor ID with pagination
     * @param donorId The donor ID
     * @param pageable Pagination information
     * @return Page of reclamations
     */
    public Page<ReclamationDTO> getReclamationsByDonorId(Long donorId, Pageable pageable) {
        log.debug("Start service getReclamationsByDonorId with donor ID: {} and pagination", donorId);

        Page<Reclamation> reclamations = reclamationRepository.findByDonorId(donorId, pageable);

        // Get donor information from repository
        Donor donor = donorRepository.findById(donorId).orElse(null);

        log.debug("End service getReclamationsByDonorId with pagination");
        return reclamations.map(reclamation -> {
            ReclamationDTO dto = reclamationMapper.toDTO(reclamation);

            // Set donor name based on the donor we retrieved
            if (donor != null) {
                if (donor instanceof DonorPhysical) {
                    DonorPhysical physicalDonor = (DonorPhysical) donor;
                    dto.setDonorName(physicalDonor.getFirstName() + " " + physicalDonor.getLastName());
                } else if (donor instanceof DonorMoral) {
                    DonorMoral moralDonor = (DonorMoral) donor;
                    dto.setDonorName(moralDonor.getCompany());
                } else if (donor.getAnonymeId() != null) {
                    dto.setDonorName("Anonymous");
                } else {
                    dto.setDonorName("Unknown");
                }
            } else {
                dto.setDonorName("No Donor");
            }

            return dto;
        });
    }

    /**
     * Search reclamations by title
     * @param keyword The search keyword
     * @param pageable Pagination information
     * @return Page of reclamations
     */
    public Page<ReclamationDTO> searchReclamationsByTitle(String keyword, Pageable pageable) {
        log.debug("Start service searchReclamationsByTitle with keyword: {}", keyword);

        Page<Reclamation> reclamations = reclamationRepository.findByTitleContainingIgnoreCase(keyword, pageable);

        log.debug("End service searchReclamationsByTitle");
        return reclamations.map(reclamation -> {
            ReclamationDTO dto = reclamationMapper.toDTO(reclamation);
            setDonorName(dto, reclamation);
            return dto;
        });
    }

    /**
     * Helper method to set the donor name in a ReclamationDTO based on the donor type
     * @param dto The ReclamationDTO to update
     * @param reclamation The Reclamation entity
     */
    private void setDonorName(ReclamationDTO dto, Reclamation reclamation) {
        if (reclamation.getDonor() != null) {
            Donor donor = reclamation.getDonor();

            // Set donor name based on donor type
            if (donor instanceof DonorPhysical) {
                DonorPhysical physicalDonor = (DonorPhysical) donor;
                dto.setDonorName(physicalDonor.getFirstName() + " " + physicalDonor.getLastName());
            } else if (donor instanceof DonorMoral) {
                DonorMoral moralDonor = (DonorMoral) donor;
                dto.setDonorName(moralDonor.getCompany());
            } else if (donor.getAnonymeId() != null) {
                // Anonymous donor
                dto.setDonorName("Anonymous");
            } else {
                // Default case
                dto.setDonorName("Unknown");
            }
        } else {
            // No donor provided
            dto.setDonorName("No Donor");
        }
    }
}
