package ma.almobadara.backend.enumeration.EntitiesToExport;

import lombok.Getter;

@Getter
public enum BeneficiaryExportHeaders {
    CODE("Code"),
    DATE_CREATION("Date de création"),
    FIRST_NAME("Prénom"),
    LAST_NAME("Nom"),
    BIRTH_DATE("Date de naissance"),
    ID_TYPE("Type d'identité"),
    IDENTITY("Identité"),
    PHONE_NUMBER("Téléphone"),
    EMAIL("Email"),
    SEX("Sexe"),
    ADDRESS("Adresse"),
    CITY("Ville"),
    REGION("Région"),
    COUNTRY("Pays"),
    BENEFICIARY_TYPE("Type de Bénéficiaire");


    private final String headerName;

    BeneficiaryExportHeaders(String headerName) {
        this.headerName = headerName;
    }

}

