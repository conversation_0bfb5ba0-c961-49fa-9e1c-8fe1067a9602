package ma.almobadara.backend.dto.beneficiary;

import lombok.*;

import java.time.Instant;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class AgendaRapportDto {

    private Long id;
    private String status;
    private Long donorId;
    private List<DonorInfoDto> donors;
    private Long beneficiaryId;
    private Date dateRapport;
    private Date dateValidate;
    private Date datePlanned;
    private Long rapportId;
    private String year;
    private String detailComplete;
    protected Instant modifiedAt;
    private Long numberRapport;

}
