package ma.almobadara.backend.dto.donor;

import lombok.*;
import org.springframework.hateoas.RepresentationModel;

import java.io.Serializable;
import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class NoteDonorContactDTO extends RepresentationModel<NoteDonorContactDTO> implements Serializable {

	private Long id;

	private String content;

	private LocalDateTime createdAt;

	private DonorContactDTO donorContact;

}
