package ma.almobadara.backend.model.donor;

import jakarta.persistence.*;
import lombok.*;
import ma.almobadara.backend.model.communs.Action;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@IdClass(ActionDonorId.class)
public class ActionDonor {

    @Id
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "donor_id")
    private Donor donor;

    @Id
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "action_id")
    private Action action;

}
