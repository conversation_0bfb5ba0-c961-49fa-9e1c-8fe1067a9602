package ma.almobadara.backend.dto.administration;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class RolePrivilegeDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private String codeRole;
    private String codePrivilege;
    private String codeFeature;
}
