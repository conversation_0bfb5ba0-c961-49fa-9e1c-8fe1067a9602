package ma.almobadara.backend.exceptions;

import lombok.Getter;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@Getter
@ResponseStatus(HttpStatus.NOT_FOUND)
public class UserNotFoundException extends RuntimeException {
    private final int statusCode;
    public UserNotFoundException(String message, int statusCode) {
        super(message);
        this.statusCode = statusCode;
    }


}
