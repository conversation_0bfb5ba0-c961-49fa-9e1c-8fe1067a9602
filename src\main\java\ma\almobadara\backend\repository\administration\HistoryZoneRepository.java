package ma.almobadara.backend.repository.administration;

import ma.almobadara.backend.model.administration.HistoryZone;
import ma.almobadara.backend.model.administration.Zone;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface HistoryZoneRepository extends JpaRepository<HistoryZone, Long> {

    List<HistoryZone> findByZoneId(Long zoneId);
}
