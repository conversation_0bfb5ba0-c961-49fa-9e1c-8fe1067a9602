package ma.almobadara.backend.dto.dashboard;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class KafalatStatsDTO {
    private Map<String, Long> kafalatByStatus; // actif/inactif
    private Map<String, Long> kafalatByService;
    private Map<String, Long> kafalatByMonth;
}