package ma.almobadara.backend.repository.beneficiary;

import ma.almobadara.backend.model.beneficiary.HistoryRapport;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
public interface HistoryRapportRepository extends JpaRepository<HistoryRapport, Long> {
    @Modifying
    @Transactional

    @Query("UPDATE HistoryRapport h SET h.donor.id = :newDonorId WHERE h.donor.id = :oldDonorId")
    void updateDonorId(@Param("oldDonorId") Long oldDonorId, @Param("newDonorId") Long newDonorId);

    @Query("SELECT h FROM HistoryRapport h WHERE h.donor.id = :donorId")
    List<HistoryRapport> findByDonorId(@Param("donorId") Long donorId);

}
