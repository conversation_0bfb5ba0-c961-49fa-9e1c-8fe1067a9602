-- Create the reclamation table with all columns
CREATE TABLE reclamation (
    id BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    donor_id BIGINT,
    status VARCHAR(255) DEFAULT 'ENVOYE',
    response TEXT,
    responded_by VARCHAR(255),
    responded_at TIMESTAMP WITHOUT TIME ZONE,
    PRIMARY KEY (id),
    CONSTRAINT fk_reclamation_donor FOREIGN KEY (donor_id) REFERENCES donor(id)
);

-- Update existing records to have the default status
UPDATE reclamation SET status = 'ENVOYE' WHERE status IS NULL;