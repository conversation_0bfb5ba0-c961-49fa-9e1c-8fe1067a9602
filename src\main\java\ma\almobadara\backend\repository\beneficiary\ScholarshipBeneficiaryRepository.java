package ma.almobadara.backend.repository.beneficiary;


import ma.almobadara.backend.model.beneficiary.Beneficiary;
import ma.almobadara.backend.model.beneficiary.ScholarshipBeneficiary;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ScholarshipBeneficiaryRepository extends JpaRepository<ScholarshipBeneficiary, Long> {

    List<ScholarshipBeneficiary> findByBeneficiary(Beneficiary beneficiary);
    List<ScholarshipBeneficiary> findByBeneficiaryId(Long id);

}
