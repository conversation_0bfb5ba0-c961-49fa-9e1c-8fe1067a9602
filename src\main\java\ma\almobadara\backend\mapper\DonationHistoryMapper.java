package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.donation.DonationHistoryDTO;
import ma.almobadara.backend.model.donation.DonationHistory;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface DonationHistoryMapper {
    @Mapping(target = "aideComplementaireName", source = "aideComplementaire.name")
    @Mapping(target = "newServiceName", source = "newService.name")
    @Mapping(target = "oldServiceName", source = "oldService.name")
    @Mapping(target = "newServiceId", source = "newService.id")
    @Mapping(target = "oldServiceId", source = "oldService.id")
    @Mapping(target = "aideComplementaireId", source = "aideComplementaire.id")
    DonationHistoryDTO  donationHistoryToDonationHistoryDTO(DonationHistory donationHistory);
}
