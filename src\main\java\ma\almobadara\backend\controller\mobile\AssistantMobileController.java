package ma.almobadara.backend.controller.mobile;

import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.administration.AssistantDTO;
import ma.almobadara.backend.dto.administration.LoginDTO;
import ma.almobadara.backend.dto.mobile.AssistantMobileDTO;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.service.mobile.AssistantMobileService;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import static ma.almobadara.backend.service.administration.CacheAdUserService.logUserInfo;

@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping("/mobile/assistant")
@CrossOrigin(origins = "*")
public class AssistantMobileController {

    private final AssistantMobileService assistantMobileService;

    /**
     * Get an assistant by ID with counts of beneficiaries and families in their zone
     * @param id The ID of the assistant
     * @return AssistantMobileDTO with assistant details and counts
     */
    @GetMapping("/{id}")
    public ResponseEntity<AssistantMobileDTO> getAssistantById(@PathVariable Long id) {

        logUserInfo("getAssistantByIdMobile", "id: " + id);

        try {
            AssistantMobileDTO assistant = assistantMobileService.getAssistantById(id);
            log.info("End resource getAssistantByIdMobile. Retrieved assistant ID: {}, OK", id);
            return new ResponseEntity<>(assistant, new HttpHeaders(), HttpStatus.OK);
        } catch (EntityNotFoundException e) {
            log.error("End resource getAssistantByIdMobile. KO: {}", e.getMessage());
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        } catch (TechnicalException e) {
            log.error("End resource getAssistantByIdMobile. KO: {}", e.getMessage());
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Login an assistant with email and password
     * @param loginDTO The login credentials
     * @return AssistantDTO with full assistant details
     */
    @PostMapping("/login")
    public ResponseEntity<AssistantDTO> loginAssistant(@RequestBody LoginDTO loginDTO) {

        logUserInfo("loginAssistant", "email: " + loginDTO.getEmail());

        try {
            AssistantDTO assistant = assistantMobileService.loginAssistant(loginDTO);
            log.info("End resource loginAssistant. Login successful for email: {}, OK", loginDTO.getEmail());
            return new ResponseEntity<>(assistant, HttpStatus.OK);
        } catch (Exception e) {
            log.error("End resource loginAssistant. KO: {}", e.getMessage());
            return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
        }
    }
}
