package ma.almobadara.backend.model.donor;

import jakarta.persistence.Entity;
import jakarta.persistence.Column;
import lombok.*;
import lombok.experimental.SuperBuilder;

import static ma.almobadara.backend.util.strings.HandleSpecialChars.escapeSpecialChars;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class DonorAnonyme extends Donor {

    @Column(nullable = false)
    private String name; // e.g., "Anonyme 1"
    private String description; // Description of the anonymous donor
    private String type = "Anonyme";

    public String toDtoString() {
        return "{"
                + "\"Nom\": \"" + escapeSpecialChars(name) + "\","
                + "\"Statut\": \"" + (super.getStatus() != null ? escapeSpecialChars(super.getStatus().getName()) : "-") + "\","
                + "\"Description\": \"" + (description != null ? escapeSpecialChars(description) : "-") + "\","
                + "\"Code\": \"" + (super.getCode() != null ? escapeSpecialChars(super.getCode()) : "-") + "\","
                + "\"Type\": \"" + escapeSpecialChars(type) + "\""
                + "}";
    }


}
