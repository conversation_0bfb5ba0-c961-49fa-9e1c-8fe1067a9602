package ma.almobadara.backend.service.migration;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.beneficiary.BeneficiaryAddDTO;
import ma.almobadara.backend.dto.family.AddedFamilyMemberResponse;
import ma.almobadara.backend.dto.family.FamilyMemberAddDTO;
import ma.almobadara.backend.exceptions.FunctionalException;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.model.beneficiary.Person;
import ma.almobadara.backend.model.family.FamilyMember;
import ma.almobadara.backend.repository.beneficiary.PersonRepository;
import ma.almobadara.backend.repository.family.FamilyMemberRepository;
import ma.almobadara.backend.service.beneficiary.BeneficiaryService;
import ma.almobadara.backend.service.family.FamilyMemberService;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Service
@RequiredArgsConstructor
@Slf4j
public class BeneficiaryFamilyMigrationService {
    private final FamilyMemberService familyMemberService;
    private final BeneficiaryService beneficiaryService;
    private final PersonRepository personRepository;
    private final FamilyMemberRepository familyMemberRepository;
    private static final Long RELATIONSHIP_FATHER = 1L;
    private static final Long RELATIONSHIP_MOTHER = 2L;
    private static final Long RELATIONSHIP_SON = 3L;
    private static final Long RELATIONSHIP_DAUGHTER = 4L;

    public void migrateBeneficiaryFamily(MultipartFile file ,
                                            Long zoneId,
                                            Long cityId,
                                            Long beneficiaryCategoryId,
                                            Long beneficiaryStatusId) throws IOException, FunctionalException {
        log.info("Request to migrateBeneficiaryFamily : {}", file);
        try (Workbook workbook = new XSSFWorkbook(file.getInputStream())) {
            Sheet sheet = workbook.getSheetAt(0);
            for (Row row : sheet) {
                if (row.getRowNum() == 0) continue;
                if (row.getCell(0) == null || row.getCell(0).getCellType() == CellType.BLANK) {
                    break;
                }
                // befire the creation of teh family part we shold check if itjust a bother so inthis case we has to begin from teh addnchildren part
                FamilyMember tutorFamilyMember = familyMemberRepository.findByFirstIdentityCode(getCellValue(row, 18));
                if (tutorFamilyMember != null) {
                    List<FamilyMemberAddDTO> childrenFamilyMemberAddDTO = createChildrenInformation(row, tutorFamilyMember.getFamily().getId());
                    familyMemberService.addListOfFamilyMembers(childrenFamilyMemberAddDTO);
                    BeneficiaryAddDTO beneficiaryAddDTO = createBeneficiaryPersonalInformation(row , beneficiaryCategoryId, beneficiaryStatusId , zoneId);
                    beneficiaryService.addBeneficiary(beneficiaryAddDTO);
                    continue;
                }
                FamilyMemberAddDTO familyMemberAddDTO = createGeneralFamilyInformation(row , zoneId, cityId);
                AddedFamilyMemberResponse familyMemberResponse = familyMemberService.addFamilyMember(familyMemberAddDTO);

                FamilyMemberAddDTO fatherFamilyMemberAddDTO = createFatherInformation(row, familyMemberResponse.getFamilyId());
                familyMemberService.addFamilyMember(fatherFamilyMemberAddDTO);

                FamilyMemberAddDTO motherFamilyMemberAddDTO = createMotherInformation(row, familyMemberResponse.getFamilyId());
                familyMemberService.addFamilyMember(motherFamilyMemberAddDTO);

                List<FamilyMemberAddDTO> childrenFamilyMemberAddDTO = createChildrenInformation(row, familyMemberResponse.getFamilyId());
                familyMemberService.addListOfFamilyMembers(childrenFamilyMemberAddDTO);

                BeneficiaryAddDTO beneficiaryAddDTO = createBeneficiaryPersonalInformation(row , beneficiaryCategoryId, beneficiaryStatusId , zoneId);
                beneficiaryService.addBeneficiary(beneficiaryAddDTO);
            }
        } catch (TechnicalException e) {
            log.error("Error during migration: {}", e.getMessage());
            throw new IOException("Error during migration: " + e.getMessage());
        }

    }

    // first step is teh creation of the family
     //step 1.1  create family without mmembers

    private FamilyMemberAddDTO createGeneralFamilyInformation(Row row , Long zoneId, Long cityId) {
        FamilyMemberAddDTO familyMemberAddDTO = new FamilyMemberAddDTO();
        familyMemberAddDTO.setCityId(cityId);
        familyMemberAddDTO.setAddress(getCellValue(row, 33));
        familyMemberAddDTO.setAddressAr(getCellValue(row, 32));
        familyMemberAddDTO.setPhoneNumber(getCellValue(row, 22));
        familyMemberAddDTO.setAccommodationNatureId(4L);
        familyMemberAddDTO.setAccommodationTypeId(10L);
        familyMemberAddDTO.setZoneId(zoneId);
        familyMemberAddDTO.setGeneralComment("Les information general sur l'hébergement sont les suivantes" + getCellValue(row, 29) + " " + getCellValue(row, 30) + " " + getCellValue(row, 31));
        return familyMemberAddDTO;
    }

    //step 1.2 create the father info

    private FamilyMemberAddDTO createFatherInformation(Row row, Long familyId) throws TechnicalException {
        FamilyMemberAddDTO familyMemberAddDTO = new FamilyMemberAddDTO();
        familyMemberAddDTO.setFamilyId(familyId);
        familyMemberAddDTO.setFamilyRelationshipId(RELATIONSHIP_FATHER);
        String[] nameParts =splitName(getCellValue(row, 11));
        familyMemberAddDTO.setFirstName(nameParts[0]);
        familyMemberAddDTO.setLastName(nameParts[1]);
        String[] namePartsAr = splitName(getCellValue(row, 10));
        familyMemberAddDTO.setFirstNameAr(namePartsAr[0]);
        familyMemberAddDTO.setLastNameAr(namePartsAr[1]);
        familyMemberAddDTO.setBirthDate(convertStringToDate(Objects.requireNonNull(getCellValue(row, 12))));
        familyMemberAddDTO.setSex("Homme");
        familyMemberAddDTO.setDeceased(true);
        familyMemberAddDTO.setDeathDate(convertStringToDate(Objects.requireNonNull(getCellValue(row, 13))));
        return familyMemberAddDTO;
    }


    //step 1.3 create the mother info

    private FamilyMemberAddDTO createMotherInformation(Row row, Long familyId) throws TechnicalException {
        FamilyMemberAddDTO familyMemberAddDTO = new FamilyMemberAddDTO();
        familyMemberAddDTO.setFamilyId(familyId);
        familyMemberAddDTO.setFamilyRelationshipId(RELATIONSHIP_MOTHER);
        familyMemberAddDTO.setTutor(true);
        familyMemberAddDTO.setTutorStartDate(convertStringToDate(Objects.requireNonNull(getCellValue(row, 34))));
        String[] nameParts = splitName(getCellValue(row, 16));
        familyMemberAddDTO.setFirstName(nameParts[0]);
        familyMemberAddDTO.setLastName(nameParts[1]);
        String[] namePartsAr = splitName(getCellValue(row, 15));
        familyMemberAddDTO.setFirstNameAr(namePartsAr[0]);
        familyMemberAddDTO.setLastNameAr(namePartsAr[1]);
        familyMemberAddDTO.setBirthDate(convertStringToDate(Objects.requireNonNull(getCellValue(row, 17))));
        familyMemberAddDTO.setIdentityCode(getCellValue(row, 18));
        familyMemberAddDTO.setSex("Femme");
        return familyMemberAddDTO;
    }

    // step 1.4 create the children info it gonne just one by line and it the one gonne be the beneficary

    private List<FamilyMemberAddDTO> createChildrenInformation(Row row, Long familyId) throws TechnicalException {
        List<FamilyMemberAddDTO> familyMemberAddDTOList = new ArrayList<>();
        FamilyMemberAddDTO familyMemberAddDTO = new FamilyMemberAddDTO();
        familyMemberAddDTO.setFamilyId(familyId);
        String genderCell = getCellValue(row, 5);
        if ("Femme".equals(genderCell)) {
            familyMemberAddDTO.setFamilyRelationshipId(RELATIONSHIP_DAUGHTER);
        } else if ("Homme".equals(genderCell)) {
            familyMemberAddDTO.setFamilyRelationshipId(RELATIONSHIP_SON);
        }
        String[] nameParts = splitName(getCellValue(row, 3));
        familyMemberAddDTO.setFirstName(nameParts[0]);
        familyMemberAddDTO.setLastName(nameParts[1]);
        familyMemberAddDTO.setBirthDate(convertStringToDate(Objects.requireNonNull(getCellValue(row, 4 ))));

        String[] namePartsAr = splitName(getCellValue(row, 2));
        familyMemberAddDTO.setFirstNameAr(namePartsAr[0]);
        familyMemberAddDTO.setLastNameAr(namePartsAr[1]);
        familyMemberAddDTOList.add(familyMemberAddDTO);
        return familyMemberAddDTOList;
    }

    // second Step creation of the Beneficiary

    // step 1.1 enter personal incofrmation
    private BeneficiaryAddDTO createBeneficiaryPersonalInformation(Row row, Long beneficiaryCategoryId, Long beneficiaryStatusId, Long zoneId) throws TechnicalException {
        BeneficiaryAddDTO beneficiaryAddDTO = new BeneficiaryAddDTO();
        String[] nameParts = splitName(getCellValue(row, 3));
        Person person = personRepository.findByFirstNameAndLastNameAndBirthDate(nameParts[0], nameParts[1], convertStringToDate(Objects.requireNonNull(getCellValue(row, 4))));
        if (person == null) {
            throw new TechnicalException("No person found for name: " + nameParts[0] + " " + nameParts[1]);
        }
        // Set person information
        beneficiaryAddDTO.setPersonId(person.getId());
        beneficiaryAddDTO.setIndependent(false);
        beneficiaryAddDTO.setArchived(false);
        beneficiaryAddDTO.setOldBeneficiary(true);
        beneficiaryAddDTO.setCodeBeneficiary(getCellValue(row, 1));
        beneficiaryAddDTO.setFirstName(nameParts[0]);
        beneficiaryAddDTO.setLastName(nameParts[1]);
        String[] namePartsAr = splitName(getCellValue(row, 2));
        beneficiaryAddDTO.setFirstNameAr(namePartsAr[0]);
        beneficiaryAddDTO.setLastNameAr(namePartsAr[1]);
        beneficiaryAddDTO.setSex(getCellValue(row, 5));
        beneficiaryAddDTO.setBirthDate(convertStringToDate(Objects.requireNonNull(getCellValue(row, 4))));
        // Set additional details
        beneficiaryAddDTO.setZoneId(zoneId);
        beneficiaryAddDTO.setRemarqueAr(getCellValue(row, 35));
        beneficiaryAddDTO.setCategoryBeneficiaryId(beneficiaryCategoryId);
        beneficiaryAddDTO.setBeneficiaryStatusId(beneficiaryStatusId);
        String typeKafalat = getCellValue(row, 36);
        assert typeKafalat != null;
        beneficiaryAddDTO.setTypeKafalatId(typeKafalat.startsWith("eps") ? 1L : 2L);

        // Parse addedYear safely from format "DD/MM/YYYY"*
        String addedDateStr = getCellValue(row, 34);

        try {

            if (addedDateStr != null && !addedDateStr.trim().isEmpty()) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
                LocalDate addedDate = LocalDate.parse(addedDateStr, formatter);
                beneficiaryAddDTO.setAddedYear(addedDate.toString());
            } else {
                beneficiaryAddDTO.setAddedYear(null);
            }
        } catch (Exception e) {
            throw new TechnicalException("Invalid added date format in row " + row.getRowNum() + ": " + addedDateStr);
        }
        return beneficiaryAddDTO;
    }

    private String getCellValue(Row row, int cellIndex) {
        try {
            if (row.getCell(cellIndex) != null) {
                switch (row.getCell(cellIndex).getCellType()) {
                    case STRING:
                        return row.getCell(cellIndex).getStringCellValue();
                    case NUMERIC:
                        // Check if the cell contains a date (numeric type)
                        if (org.apache.poi.ss.usermodel.DateUtil.isCellDateFormatted(row.getCell(cellIndex))) {
                            // Convert the numeric date to a string formatted as dd/MM/yyyy
                            return new SimpleDateFormat("dd/MM/yyyy").format(row.getCell(cellIndex).getDateCellValue());
                        } else {
                            // Return the numeric value as a string (if it's not a date)
                            return String.valueOf(row.getCell(cellIndex).getNumericCellValue());
                        }
                    case BOOLEAN:
                        return String.valueOf(row.getCell(cellIndex).getBooleanCellValue());
                    case FORMULA:
                        return row.getCell(cellIndex).getCellFormula();
                    default:
                        return "";
                }
            }
        } catch (Exception e) {
            log.error("Error getting cell value from row {} at index {}", row.getRowNum(), cellIndex, e);
        }
        return "";
    }

    private Date convertStringToDate(String dateString) throws TechnicalException {
        if (dateString == null || dateString.trim().isEmpty()) {
            return null; // Return null for empty values
        }

        SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
        dateFormat.setLenient(false); // Ensures strict parsing

        try {
            return dateFormat.parse(dateString);
        } catch (java.text.ParseException e) {
            throw new TechnicalException("Invalid date format: " + dateString);
        }
    }

    private String[] splitName(String fullName) {
        if (fullName == null || fullName.isEmpty()) return new String[]{"", ""};
        String[] parts = fullName.split(" ", 2);
        return new String[]{parts.length > 0 ? parts[0] : "", parts.length > 1 ? parts[1] : ""};
    }
}
