package ma.almobadara.backend.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.model.administration.CacheAdUser;
import ma.almobadara.backend.oauth2.OAuth2ResourceServerConfiguration;
import ma.almobadara.backend.properties.Auth0Properties;
import ma.almobadara.backend.repository.administration.CacheAdUserRepository;
import ma.almobadara.backend.service.administration.CacheAdUserService;
import ma.almobadara.backend.service.authRefrentiel.AuthenticationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.core.DelegatingOAuth2TokenValidator;
import org.springframework.security.oauth2.core.OAuth2Error;
import org.springframework.security.oauth2.core.OAuth2TokenValidator;
import org.springframework.security.oauth2.core.OAuth2TokenValidatorResult;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.jwt.JwtValidators;
import org.springframework.security.oauth2.jwt.NimbusJwtDecoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.OncePerRequestFilter;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.FilterChain;
import java.io.IOException;

import java.util.ArrayList;
import java.util.List;

@Configuration
@EnableWebSecurity
@EnableMethodSecurity(securedEnabled = true, jsr250Enabled = true)
@Slf4j
@RequiredArgsConstructor
public class SecurityConfig {

    private final OAuth2ResourceServerConfiguration oAuth2ResourceServerConfiguration;
    private final Auth0Properties auth0Properties;
    @Value("${almobadra.config.allowed-origin:*}")
    private List<String> allowedOriginPatterns;
    private final CacheAdUserRepository cacheAdUserRepository;
    private final JwtAuthFilter jwtAuthFilter;
    private final CustomJwtAuthenticationConverter customJwtAuthenticationConverter;
    private final JwtDecoder azureJwtDecoder;
    private final JwtDecoder auth0JwtDecoder;

    private static final ThreadLocal<String> REQUEST_PATH_HOLDER = new ThreadLocal<>();

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http.csrf(AbstractHttpConfigurer::disable)
                .authorizeHttpRequests(auth -> auth
                        .requestMatchers("/api-docs", "/swagger-ui/**", "/actuator/health", "/batch/**").permitAll()
                        .requestMatchers("/mobile/*/login/**", "/mobile/*/reset-password/**").permitAll()
                        .anyRequest().authenticated()
                )
                .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
                .oauth2ResourceServer(oauth2 -> oauth2
                        .jwt(jwtConfigurer -> jwtConfigurer
                                .jwtAuthenticationConverter(customJwtAuthenticationConverter.getJwtAuthenticationConverter())
                                .decoder(jwt -> {
                                    String requestPath = REQUEST_PATH_HOLDER.get();
                                    log.debug("Processing JWT for request path: {}", requestPath);
                                    
                                    // Use Auth0 decoder for mobile APIs
                                    if (requestPath != null && requestPath.startsWith("/mobile/")) {
                                        log.debug("Using Auth0 decoder for mobile API request");
                                        try {
                                            if (jwt == null || jwt.trim().isEmpty()) {
                                                log.error("Auth0 token is null or empty");
                                                throw new IllegalArgumentException("Token cannot be null or empty");
                                            }
                                            log.debug("Attempting to decode Auth0 token: {}", jwt.substring(0, Math.min(20, jwt.length())) + "...");
                                            
                                            Jwt decodedJwt = auth0JwtDecoder.decode(jwt);
                                            log.debug("Successfully decoded token with Auth0 decoder");
                                            return decodedJwt;
                                        } catch (Exception auth0Error) {
                                            log.error("Auth0 JWT validation failed: {}", auth0Error.getMessage());
                                            throw auth0Error;
                                        }
                                    }
                                    
                                    // Use Azure decoder for web APIs
                                    log.debug("Using Azure decoder for web API request");
                                    try {
                                        if (jwt == null || jwt.trim().isEmpty()) {
                                            log.error("Azure token is null or empty");
                                            throw new IllegalArgumentException("Token cannot be null or empty");
                                        }
                                        log.debug("Attempting to decode Azure token: {}", jwt.substring(0, Math.min(20, jwt.length())) + "...");
                                        
                                        Jwt decodedJwt = azureJwtDecoder.decode(jwt);
                                        log.debug("Successfully decoded token with Azure decoder. Claims: {}", decodedJwt.getClaims());
                                        return decodedJwt;
                                    } catch (Exception e) {
                                        log.error("Azure JWT validation failed: {}", e.getMessage(), e);
                                        throw e;
                                    }
                                })
                        )
                )
                .addFilterBefore(new OncePerRequestFilter() {
                    @Override
                    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
                            throws ServletException, IOException {
                        try {
                            String requestPath = request.getRequestURI();
                            String authHeader = request.getHeader("Authorization");
                            log.debug("Request path: {}, Authorization header present: {}", 
                                requestPath, 
                                authHeader != null ? "Yes" : "No");
                            
                            REQUEST_PATH_HOLDER.set(requestPath);
                            filterChain.doFilter(request, response);
                        } finally {
                            REQUEST_PATH_HOLDER.remove();
                        }
                    }
                }, UsernamePasswordAuthenticationFilter.class)
                .addFilterBefore(jwtAuthFilter, UsernamePasswordAuthenticationFilter.class);

        return http.build();
    }

    @Bean
    CorsConfigurationSource corsConfigurationSource() {
        var corsConfiguration = new CorsConfiguration();
        corsConfiguration.setAllowedHeaders(List.of("*"));
        corsConfiguration.setAllowedMethods(List.of("*"));
        corsConfiguration.setAllowedOriginPatterns(allowedOriginPatterns);

        var source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", corsConfiguration);
        return source;
    }

    public static class AudienceValidator implements OAuth2TokenValidator<Jwt> {
        private final String audience;
        private final CacheAdUserRepository cacheAdUserRepository;

        public AudienceValidator(String audience, CacheAdUserRepository cacheAdUserRepository) {
            this.audience = audience;
            this.cacheAdUserRepository = cacheAdUserRepository;
        }

        @Override
        public OAuth2TokenValidatorResult validate(Jwt jwt) {
            log.debug("Validating Azure token audience. Expected: {}, Actual: {}", audience, jwt.getAudience());
            
            if (jwt.getAudience().contains(audience)) {
                String azureId = jwt.getClaimAsString("oid");
                log.debug("Azure ID (oid) from token: {}", azureId);
                
                if (azureId == null || azureId.isEmpty()) {
                    log.error("Azure ID (oid) is missing from token");
                    OAuth2Error error = new OAuth2Error("invalid_token", "Azure ID (oid) is missing", null);
                    return OAuth2TokenValidatorResult.failure(error);
                }
                
                CacheAdUser connectedUser = cacheAdUserRepository.findByAzureDirectoryIdAndIsDeletedIsFalse(azureId);
                if (connectedUser == null) {
                    log.error("User not found for Azure ID: {}", azureId);
                    OAuth2Error error = new OAuth2Error("invalid_token", "User not found for Azure ID: " + azureId, null);
                    return OAuth2TokenValidatorResult.failure(error);
                }

                return OAuth2TokenValidatorResult.success();
            }
            
            log.error("Invalid audience in Azure token. Expected: {}, Actual: {}", audience, jwt.getAudience());
            OAuth2Error error = new OAuth2Error("invalid_token", "Invalid audience", null);
            return OAuth2TokenValidatorResult.failure(error);
        }
    }

    public static class Auth0AudienceValidator implements OAuth2TokenValidator<Jwt> {
        private final String audience;

        public Auth0AudienceValidator(String audience) {
            this.audience = audience;
        }

        @Override
        public OAuth2TokenValidatorResult validate(Jwt jwt) {
            log.debug("Validating Auth0 token audience. Expected: {}, Actual: {}", audience, jwt.getAudience());
            if (jwt.getAudience().contains(audience)) {
                log.debug("Auth0 audience validation successful");
                return OAuth2TokenValidatorResult.success();
            }
            log.error("Auth0 audience validation failed");
            OAuth2Error error = new OAuth2Error("invalid_token", "Invalid audience", null);
            return OAuth2TokenValidatorResult.failure(error);
        }
    }
}
