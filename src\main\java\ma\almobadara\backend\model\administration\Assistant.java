package ma.almobadara.backend.model.administration;

import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Assistant {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @Column(unique = true)
    private String code;
    private Long cityId ;
    private String address ;
    private boolean isDeleted;
    @CreationTimestamp
    @Column(updatable = false)
    private LocalDateTime creationDate;
    @UpdateTimestamp
    private LocalDateTime updateDate;

    @Column(name = "status", nullable = false)
    private boolean status;

    @OneToOne
    @JoinColumn(name = "zone_id")
    private Zone zone;

    @OneToOne
    @JoinColumn(name = "cache_ad_user_id")
    private CacheAdUser cacheAdUser;

// date of affectation to the zone
    @Column(name = "date_affectation_to_zone")
    private LocalDate  dateAffectationToZone;
    // date of being out of the zone
    @Column(name = "date_end_affectation_to_zone")
    private LocalDate dateEndAffectationToZone;
    private String firstName;
    private String lastName;
    private String email;
    private Date birthDate;
    //private Long languageCommunicationId;
    private Long schoolLevelId;

    @Column(name = "language_communication_ids", length = 1000)
    private String languageCommunicationIds;
    private String pictureUrl;

    private String cinNumber;
    private String phone;
    private Boolean oldAssistant;
    private String password;
    private String device_token;


    public List<Long> getLanguageCommunicationIdsList() {
        if (languageCommunicationIds == null || languageCommunicationIds.isEmpty()) {
            return Collections.emptyList();
        }
        return Arrays.asList(languageCommunicationIds.split(",\\s*"))
                .stream()
                .map(Long::parseLong)
                .toList();
    }

    public void setLanguageCommunicationIdList(List<Long> languageCommunicationIds) {
        if (languageCommunicationIds == null || languageCommunicationIds.isEmpty()) {
            this.languageCommunicationIds = "";
        } else {
            this.languageCommunicationIds = String.join(", ", languageCommunicationIds.stream()
                    .map(String::valueOf)
                    .toList());
        }
    }

}
