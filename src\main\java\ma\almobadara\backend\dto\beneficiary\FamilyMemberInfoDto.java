package ma.almobadara.backend.dto.beneficiary;

import lombok.*;
import ma.almobadara.backend.dto.referentiel.AccommodationTypeDTO;
import ma.almobadara.backend.dto.referentiel.FamilyRelationshipDTO;
import ma.almobadara.backend.dto.referentiel.ProfessionDTO;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class FamilyMemberInfoDto {

    private Long memberId;
    private boolean tutor;
    private String generalComment;
    private String firstName;
    private String lastName;
    private String firstNameAr;
    private String lastNameAr;
    private String phoneNumber;
    private ProfessionDTO profession;
    private FamilyRelationshipDTO familyRelationship;
    private AccommodationTypeDTO accommodationType;
    private String email;

}
