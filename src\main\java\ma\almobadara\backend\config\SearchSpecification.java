package ma.almobadara.backend.config;

import org.springframework.data.jpa.domain.Specification;
import jakarta.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;

public class SearchSpecification<T> implements Specification<T> {
    private final String query;
    private final List<String> fields;

    public SearchSpecification(String query, List<String> fields) {
        this.query = query;
        this.fields = fields;
    }

    @Override
    public Predicate toPredicate(
            jakarta.persistence.criteria.Root<T> root,
            jakarta.persistence.criteria.CriteriaQuery<?> query,
            jakarta.persistence.criteria.CriteriaBuilder criteriaBuilder
    ) {
        List<Predicate> predicates = new ArrayList<>();

        for (String field : fields) {
            predicates.add(criteriaBuilder.like(
                    criteriaBuilder.lower(root.get(field)),
                    "%" + this.query.toLowerCase() + "%"
            ));
        }

        return criteriaBuilder.or(predicates.toArray(new Predicate[0]));
    }
}
