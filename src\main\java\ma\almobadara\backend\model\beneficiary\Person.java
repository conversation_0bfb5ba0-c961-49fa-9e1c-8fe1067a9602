package ma.almobadara.backend.model.beneficiary;

import jakarta.persistence.Entity;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import lombok.*;
import ma.almobadara.backend.model.family.FamilyMember;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Entity
@Getter
@Setter
@NoArgsConstructor
@Builder
@ToString
@AllArgsConstructor
public class Person extends BaseEntity {

    private String pictureUrl;
    private String firstName;
    private String lastName;
    private String firstNameAr;
    private String lastNameAr;
    private String sex;
    private String email;
    private String phoneNumber;
    private String address;
    private String addressAr;
    private boolean deceased;
    private boolean educated;
    private Date deathDate;
    private String deathReason;
    private Long deathReasonId;
    private String identityCode;
    private Long typeIdentityId;
    private Long schoolLevelId;
    private String schoolName;
    private String schoolNameAr;
    private Long cityId;
    private Long professionId;
    private Long accommodationTypeId;
    private Long accommodationNatureId;
    private Long typeKafalatId;
    private Long sourceBeneficiaryId;
    private String sourceBeneficiaryComment;
    private String typePriseEnChargeIdsList; // Store as comma-separated values
    private Long categoryBeneficiaryId;
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date birthDate;
    @OneToMany(mappedBy = "person")
    private List<BankCard> bankCards;
    @OneToOne(mappedBy = "person")
    private Beneficiary beneficiary;
    @OneToOne(mappedBy = "person")
    private FamilyMember familyMember;

}
