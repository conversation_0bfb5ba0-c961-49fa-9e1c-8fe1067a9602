package ma.almobadara.backend.controller.family;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.config.HasAccessToModule;
import ma.almobadara.backend.dto.exportentities.ExportFileDTO;
import ma.almobadara.backend.dto.family.FamilyDTO;
import ma.almobadara.backend.dto.family.FamilyMemberAddDTO;
import ma.almobadara.backend.dto.family.TutorHistoryDTO;
import ma.almobadara.backend.enumeration.Functionality;
import ma.almobadara.backend.enumeration.Module;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.service.family.FamilyService;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.domain.Page;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.*;

import java.util.*;

import static ma.almobadara.backend.service.administration.CacheAdUserService.logUserInfo;

//@RefreshScope
@RestController
@AllArgsConstructor
@Slf4j
@RequestMapping("/families")
public class FamilyController {

    private final FamilyService familyService;
    @PostMapping(value = "", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    @HasAccessToModule(modules = {Module.FAMILLE}, functionalities = {Functionality.CREATE})
    @Operation(summary = "Create a Family", description = "add a new family", tags = {"SIG"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = FamilyDTO.class))))})
    public ResponseEntity<FamilyDTO> createFamily(@ModelAttribute FamilyDTO familyRequest) {
        logUserInfo("createFamily");

        FamilyDTO createdFamily;
        HttpStatus status;
        try {
            createdFamily = familyService.addFamily(familyRequest);
            status = HttpStatus.OK;
            log.info("End resource Create Family, OK");
        } catch (Exception e) {
            createdFamily = null;
            status = HttpStatus.INTERNAL_SERVER_ERROR;
            log.error("End resource Create Family, KO: {}", e.getMessage());
        }

        return new ResponseEntity<>(createdFamily, new HttpHeaders(), status);
    }

    @Operation(summary = "Find All Families", description = "Find all families", tags = {"SIG"})
    @HasAccessToModule(modules = {Module.FAMILLE}, functionalities = {Functionality.VIEW})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = FamilyDTO.class))))})
    @GetMapping(value = "", produces = {"application/json"})
    public ResponseEntity<Page<FamilyDTO>> getAllFamilies(@RequestParam Optional<Integer> page,
    @RequestParam(required = false) final String searchByTutorName,
    @RequestParam(required = false) final String lastNameAr,
    @RequestParam(required = false) final Long minNumber,
    @RequestParam(required = false) final Long maxNumber,
    @RequestParam(required = false) final String numTel,
    @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") final Date minDate,
    @RequestParam(required = false) final Long searchByTagId,
    @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") final Date maxDate) {

        logUserInfo("getAllFamilies");

        Page<FamilyDTO> familyDTOS;
        HttpStatus status;
        try {
            familyDTOS = familyService.getAllFamilies(page,searchByTutorName,lastNameAr, minNumber,maxNumber,numTel,minDate,maxDate,searchByTagId);
            status = HttpStatus.OK;
            log.info("End resource Get All Families, OK");
        } catch (Exception e) {
            familyDTOS = null;
            status = HttpStatus.INTERNAL_SERVER_ERROR;
            log.error("End resource Get All Families, KO: {}", e.getMessage());
        }

        return new ResponseEntity<>(familyDTOS, new HttpHeaders(), status);
    }

    @Operation(summary = "Find family by ID", description = "Returns a single family", tags = {"SIG"})
    @HasAccessToModule(modules = {Module.FAMILLE}, functionalities = {Functionality.UPDATE})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(schema = @Schema(implementation = FamilyDTO.class))),
            @ApiResponse(responseCode = "404", description = "Family not found")})
    @GetMapping(value = "/{idFamily}", produces = {"application/json"})
    public FamilyDTO getFamilyById(@PathVariable Long idFamily) {

        logUserInfo("getFamilyById", String.valueOf(idFamily));

        FamilyDTO familyDTO;
        try {
            familyDTO = familyService.getFamilyById(idFamily);

            log.info("End resource Get One Family with ID: {}, OK", idFamily);
        } catch (TechnicalException e) {
            familyDTO = null;
            log.error("End resource Get One Family with ID: {}, KO: Family not found", idFamily);
        }

        return familyDTO;
    }

    @Operation(summary = "Find Family By Beneficiary", description = "Find Family By Beneficiary", tags = {"SIG"})
    @HasAccessToModule(modules = {Module.FAMILLE}, functionalities = {Functionality.VIEW})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = FamilyDTO.class))))})
    @GetMapping(value = "/beneficiary/{beneficiaryId}", produces = {"application/json"})
    public ResponseEntity<FamilyDTO> getFamilyForBeneficiary(@PathVariable Long beneficiaryId) {

        logUserInfo("getFamilyForBeneficiary", String.valueOf(beneficiaryId));

        FamilyDTO familyDTO;
        HttpStatus status;
        familyDTO = familyService.getFamilyForOneBeneficiary(beneficiaryId);
        status = HttpStatus.OK;
        log.info("End resource Get Family By Beneficiary with Beneficiary ID: {}, OK", beneficiaryId);

        return new ResponseEntity<>(familyDTO, new HttpHeaders(), status);
    }

    @Operation(summary = "Find All Families", description = "Find all families", tags = {"SIG"})
    @HasAccessToModule(modules = {Module.FAMILLE}, functionalities = {Functionality.VIEW})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = FamilyDTO.class))))})
    @GetMapping(value = "/light", produces = {"application/json"})
    public ResponseEntity<List<FamilyDTO>> getAllFamiliesForBeneficiary() {
        log.info("Start resource Get All Families");

        logUserInfo("getAllFamiliesForBeneficiary");

        List<FamilyDTO> familyDTOS;
        HttpStatus status;
        familyDTOS = familyService.getAllFamiliesForBeneficiary();
        status = HttpStatus.OK;
        log.info("End resource Get All Families, OK");

        return new ResponseEntity<>(familyDTOS, new HttpHeaders(), status);
    }

    @GetMapping(value = "/csv", produces = {"application/json"})
    public ResponseEntity<ExportFileDTO> exportFamiliesToCSV(@RequestParam(required = false) String searchByTutorName,
                                                                @RequestParam(required = false) String lastNameAr,
                                                                @RequestParam(required = false) Long minNumber,
                                                                @RequestParam(required = false) Long maxNumber,
                                                                @RequestParam(required = false) String numTel,
                                                                @RequestParam(required = false) Date minDate,
                                                                @RequestParam(required = false) Date maxDate) {
        logUserInfo("Export All Families To CSV");
        HttpStatus status;
        try{
            ExportFileDTO exportFileDTO = familyService.exportFileWithName(searchByTutorName, lastNameAr, minNumber, maxNumber, numTel, minDate, maxDate);
            status = HttpStatus.OK;
            return new ResponseEntity<>(exportFileDTO, status);
        } catch (Exception ex) {
            log.error("Error while exporting families to CSV: {}", ex.getMessage());
            status = HttpStatus.INTERNAL_SERVER_ERROR;
            return new ResponseEntity<>(null, status);
        }
    }

    @GetMapping(value = "/tutor-history/{familyId}", produces = {"application/json"})
    @Operation(summary = "Get tutor history by family id", description = "Get tutor history by family id", tags = {"tutor-history"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(schema = @Schema(implementation = FamilyMemberAddDTO.class)))})
    public ResponseEntity<List<TutorHistoryDTO>> getTutorHistoryByFamilyId(@PathVariable Long familyId) {
        logUserInfo("getTutorHistoryByFamilyId");
        try {
            List<TutorHistoryDTO> ListTutorHistory = familyService.getTutorHistoryByFamilyId(familyId);
            logUserInfo("getTutorHistoryByFamilyId", String.valueOf(familyId));
            return ResponseEntity.ok(ListTutorHistory);
        } catch (TechnicalException e) {
            log.error("End resource getTutorHistoryByFamilyId, KO: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }

    }


}
