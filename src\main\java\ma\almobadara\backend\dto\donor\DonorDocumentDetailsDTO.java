package ma.almobadara.backend.dto.donor;

import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class DonorDocumentDetailsDTO {

    private Long id;
    private String label;
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date documentDate;
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date expiryDate;
    private String comment;
    private String fileUrl;
    private String fileName;
    private Long typeDocumentId;
    private Double balance;
    private String identityCode;
    private String address;
    private String addressAr;

}
