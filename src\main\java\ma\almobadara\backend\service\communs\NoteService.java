package ma.almobadara.backend.service.communs;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.Audit.AuditApplicationService;
import ma.almobadara.backend.config.Messages;
import ma.almobadara.backend.dto.communs.NoteAndEntityDto;
import ma.almobadara.backend.dto.communs.NoteDTO;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.mapper.NoteMapper;
import ma.almobadara.backend.model.administration.CacheAdUser;
import ma.almobadara.backend.model.beneficiary.Beneficiary;
import ma.almobadara.backend.model.beneficiary.NoteBeneficiary;
import ma.almobadara.backend.model.communs.Note;
import ma.almobadara.backend.model.donation.Donation;
import ma.almobadara.backend.model.donation.NoteDonation;
import ma.almobadara.backend.model.donor.*;
import ma.almobadara.backend.model.family.Family;
import ma.almobadara.backend.model.family.NoteFamily;
import ma.almobadara.backend.model.takenInCharge.NoteTakenInCharge;
import ma.almobadara.backend.model.takenInCharge.TakenInCharge;
import ma.almobadara.backend.repository.administration.CacheAdUserRepository;
import ma.almobadara.backend.repository.beneficiary.BeneficiaryRepository;
import ma.almobadara.backend.repository.communs.*;
import ma.almobadara.backend.repository.donation.DonationRepository;
import ma.almobadara.backend.repository.donation.NoteDonationRepository;
import ma.almobadara.backend.repository.donor.DonorRepository;
import ma.almobadara.backend.repository.family.FamilyRepository;
import ma.almobadara.backend.repository.takenInCharge.NoteTakenInChargeRepository;
import ma.almobadara.backend.repository.takenInCharge.TakenInChargeRepository;
import ma.almobadara.backend.util.times.TimeWatch;
import org.hibernate.Hibernate;
import org.hibernate.proxy.HibernateProxy;
import org.springframework.beans.BeanUtils;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;


import static ma.almobadara.backend.service.administration.CacheAdUserService.getUsernameFromJwt;
import static ma.almobadara.backend.util.constants.GlobalConstants.*;
import static ma.almobadara.backend.util.strings.HandleSpecialChars.escapeSpecialChars;

@Service
@RequiredArgsConstructor
@Slf4j
public class NoteService {

    private final NoteRepository noteRepository;
    private final NoteMapper noteMapper;
    private final DonorRepository donorRepository;
    private final BeneficiaryRepository beneficiaryRepository;
    private final FamilyRepository familyRepository;
    private final NoteDonorRepository noteDonorRepository;
    private final NoteBeneficiaryRepository noteBeneficiaryRepository;
    private final NoteFamilyRepository noteFamilyRepository;
    private final CacheAdUserRepository cacheAdUserRepository;
    private final Messages messages;


    private final NoteTakenInChargeRepository noteTakenInChargeRepository;
    private final NoteDonationRepository noteDonationRepository;
    private final TakenInChargeRepository takenInChargeRepository;
    private final DonationRepository donationRepository;

    private final AuditApplicationService auditApplicationService;

    public NoteDTO addNoteAndAssignToEntity(NoteAndEntityDto noteAndEntityDto) throws TechnicalException {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service Create Note and Assign to Entity -> id: {}, type: {}, note: {}", noteAndEntityDto.getEntityId(), noteAndEntityDto.getEntityType(), noteAndEntityDto.getNoteDto().getObjet());

        Note note = noteMapper.noteDtoModelToModel(noteAndEntityDto.getNoteDto());
        Optional<CacheAdUser> createdBy = cacheAdUserRepository.findById(note.getCreatedBy().getId());
        if (createdBy.isEmpty()) {
            throw new TechnicalException(messages.get(USER_ID_CREATED_BY_NOT_FOUND));
        }

        Note oldExistingNote = null;
        Note existingNote = new Note();

        if (note.getId() != null){
            oldExistingNote = noteRepository.findById(note.getId()).orElseThrow();
            BeanUtils.copyProperties(oldExistingNote, existingNote);
        }

        Note newNote = noteRepository.save(note);
        newNote.setCreatedBy(createdBy.get());

        Object entity = getEntityById(noteAndEntityDto.getEntityId(), noteAndEntityDto.getEntityType());

        if (entity != null) {
            saveNoteEntityAssociation(newNote, entity, existingNote);
        } else {
            throw new TechnicalException(messages.get(ENTITY_ID_NOT_FOUND));
        }

        NoteDTO newNoteDTO = noteMapper.noteModelToDto(newNote);

        log.debug("End service Create Note and Assign to Entity -> id: {}, type: {}, note: {}, took {}", noteAndEntityDto.getEntityId(), noteAndEntityDto.getEntityType(), noteAndEntityDto.getNoteDto().getObjet(), watch.toMS());
        return newNoteDTO;

    }

    private void saveNoteEntityAssociation(Note newNote, Object entity, Note existingNote) {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service Save Note Entity Association -> note: {}", newNote);
        if (DonorMoral.class.equals(entity.getClass()) || DonorPhysical.class.equals(entity.getClass()) || DonorAnonyme.class.equals(entity.getClass())) {
            NoteDonor noteDonorEntity = NoteDonor.builder()
                    .donor((Donor) entity)
                    .note(newNote)
                    .build();
            noteDonorRepository.save(noteDonorEntity);
            Donor donor=(Donor) entity;

            if (existingNote != null && existingNote.getId() != null){
                if(donor instanceof DonorPhysical){
                    DonorPhysical exsitingDonor = (DonorPhysical) donor;

                    StringBuilder columnToAppend = new StringBuilder();
                    columnToAppend.append("\"Type  Donateur\": \"" + "Physique" + "\",");
                    columnToAppend.append("\"Code  Donateur\": \"" + escapeSpecialChars(exsitingDonor.getCode()) + "\",");
                    columnToAppend.append("\"Nom  Donateur\": \"" + escapeSpecialChars(exsitingDonor.getLastName() +" "+exsitingDonor.getFirstName()) + "\"");
                    auditApplicationService.audit("Modification  note du donateur : " + exsitingDonor.getCode(), getUsernameFromJwt(), "Modification  note du donateur",
                            existingNote.toDtoString(columnToAppend), newNote.toDtoString(columnToAppend), DONATEUR, UPDATE);

                }
                else if(donor instanceof DonorAnonyme){
                    DonorAnonyme exsitingDonor = (DonorAnonyme) donor;
                    StringBuilder columnToAppend = new StringBuilder();
                    columnToAppend.append("\"Type  Donateur\": \"" + "Anonyme" + "\",");
                    columnToAppend.append("\"Code  Donateur\": \"" + escapeSpecialChars(exsitingDonor.getCode()) + "\",");
                    columnToAppend.append("\"Nom  Donateur\": \"" + escapeSpecialChars(exsitingDonor.getName()) + "\"");
                    auditApplicationService.audit("Modification  note du donateur : " + exsitingDonor.getCode(), getUsernameFromJwt(), "Modification  note du donateur",
                            existingNote.toDtoString( columnToAppend), newNote.toDtoString(columnToAppend), DONATEUR, UPDATE);
                } else {
                    DonorMoral exsitingDonor = (DonorMoral) donor;
                    StringBuilder columnToAppend = new StringBuilder();
                    columnToAppend.append("\"Type  Donateur\": \"" + "Moral" + "\",");
                    columnToAppend.append("\"Code  Donateur\": \"" + escapeSpecialChars(exsitingDonor.getCode()) + "\",");
                    columnToAppend.append("\"Nom  Donateur\": \"" + escapeSpecialChars(exsitingDonor.getCompany()) + "\"");

                    auditApplicationService.audit("Modification  note du donateur : " + exsitingDonor.getCode(), getUsernameFromJwt(), "Modification  note du donateur",
                            existingNote.toDtoString(columnToAppend), newNote.toDtoString(columnToAppend), DONATEUR, UPDATE);
                }

            }else {
                if(donor instanceof DonorPhysical){
                    DonorPhysical exsitingDonor = (DonorPhysical) donor;
                    StringBuilder columnToAppend = new StringBuilder();
                    columnToAppend.append("\"Type  Donateur\": \"" + "Physique" + "\",");
                    columnToAppend.append("\"Code  Donateur\": \"" +escapeSpecialChars( exsitingDonor.getCode()) + "\",");
                    columnToAppend.append("\"Nom  Donateur\": \"" + exsitingDonor.getFirstName()+" "+exsitingDonor.getLastName() + "\"");
                    auditApplicationService.audit("Ajout  note du donateur : " + exsitingDonor.getCode(), getUsernameFromJwt(), "Ajout  note du donateur",
                           null, newNote.toDtoString(columnToAppend), DONATEUR,CREATE);
                }
                else if(donor instanceof DonorAnonyme){
                    DonorAnonyme exsitingDonor = (DonorAnonyme) donor;
                    StringBuilder columnToAppend = new StringBuilder();
                    columnToAppend.append("\"Type  Donateur\": \"" + "Anonyme" + "\",");
                    columnToAppend.append("\"Code  Donateur\": \"" + escapeSpecialChars(exsitingDonor.getCode()) + "\",");
                    columnToAppend.append("\"Nom  Donateur\": \"" + escapeSpecialChars(exsitingDonor.getName()) + "\"");
                    auditApplicationService.audit("Ajout  note du donateur : " + exsitingDonor.getCode(), getUsernameFromJwt(), "Ajout  note du donateur",
                            null, newNote.toDtoString(columnToAppend), DONATEUR, CREATE);
                } else {
                    DonorMoral exsitingDonor = (DonorMoral) donor;
                    StringBuilder columnToAppend = new StringBuilder();
                    columnToAppend.append("\"Type  Donateur\": \"" + "Moral" + "\",");
                    columnToAppend.append("\"Code  Donateur\": \"" + escapeSpecialChars(exsitingDonor.getCode()) + "\",");
                    columnToAppend.append("\"Nom  Donateur\": \"" + escapeSpecialChars(exsitingDonor.getCompany()) + "\"");
                    auditApplicationService.audit("Ajout  note du donateur : " + exsitingDonor.getCode(), getUsernameFromJwt(), "Ajout  note du donateur",
                            null, newNote.toDtoString(columnToAppend), DONATEUR,CREATE);
                }
            }
        } else if (Beneficiary.class.equals(entity.getClass())) {
            NoteBeneficiary noteBeneficiaryEntity = NoteBeneficiary.builder()
                    .beneficiary((Beneficiary) entity)
                    .note(newNote)
                    .build();
            noteBeneficiaryRepository.save(noteBeneficiaryEntity);
            Beneficiary beneficiary= (Beneficiary) entity;
            StringBuilder columnToAppend = new StringBuilder();
            columnToAppend.append("\"Code  bénéficiaire\": \"" +escapeSpecialChars(beneficiary.getCode()) + "\",");
            columnToAppend.append("\"Nom  bénéficiaire\": \"" +escapeSpecialChars(beneficiary.getPerson().getFirstName())+" "+escapeSpecialChars(beneficiary.getPerson().getLastName()) + "\"");
            if (existingNote != null && existingNote.getId() != null){
                auditApplicationService.audit("Modification  note du bénéficiaire : " + noteBeneficiaryEntity.getBeneficiary().getCode(), getUsernameFromJwt(), "Modification  note du beneficiaire",
                        existingNote.toDtoString(columnToAppend), newNote.toDtoString(columnToAppend), BENEFICIAIRE, UPDATE);
            }else {
                auditApplicationService.audit("Ajout d'une  note pour bénéficiaire : " + noteBeneficiaryEntity.getBeneficiary().getCode(), getUsernameFromJwt(), "Ajout note pour beneficiaire",
                        null, newNote.toDtoString(columnToAppend), BENEFICIAIRE, CREATE);
            }
        } else if (Family.class.equals(entity.getClass())) {
            NoteFamily noteFamilyEntity = NoteFamily.builder()
                    .family((Family) entity)
                    .note(newNote)
                    .build();
            noteFamilyRepository.save(noteFamilyEntity);
            Family familyEntity = (Family) entity;
            StringBuilder columnToAppend = new StringBuilder();
            columnToAppend.append("\"Code  famille\": \"" +escapeSpecialChars(familyEntity.getCode())  + "\"");
            if (existingNote != null && existingNote.getId() != null){
                auditApplicationService.audit("Modification  note  famille : " + noteFamilyEntity.getFamily().getCode(), getUsernameFromJwt(), "Modification note famille",
                        existingNote.toDtoString(columnToAppend), newNote.toDtoString(columnToAppend), FAMILLE, UPDATE);
            }else {
                auditApplicationService.audit("Ajout d'une note pour la famille :" + noteFamilyEntity.getFamily().getCode(), getUsernameFromJwt(), "Ajout note famille",
                        null,newNote.toDtoString(columnToAppend), FAMILLE, CREATE);
            }
        }
        else if (TakenInCharge.class.equals(entity.getClass())) {
            NoteTakenInCharge noteTakenInChargeEntity = NoteTakenInCharge.builder()
                    .takenInCharge((TakenInCharge) entity)
                    .note(newNote)
                    .build();
            noteTakenInChargeRepository.save(noteTakenInChargeEntity);
            TakenInCharge takenInChargeEntity = (TakenInCharge) entity;
            StringBuilder columnToAppend = new StringBuilder();
            columnToAppend.append("\"Code\": \"" +escapeSpecialChars(takenInChargeEntity.getCode()) + "\",");
            columnToAppend.append("\"Service \": \"" +escapeSpecialChars(takenInChargeEntity.getService().getName())+ "\"");
            if (existingNote != null && existingNote.getId() != null){
                auditApplicationService.audit("Modification de note  Prise en charge : " + noteTakenInChargeEntity.getTakenInCharge().getCode(), getUsernameFromJwt(), "Modification note Prise en charge",
                        existingNote.toDtoString(columnToAppend), newNote.toDtoString(columnToAppend), PRISEENCHARGE, UPDATE);
            }else {
                auditApplicationService.audit("Ajout d'une note pour la Prise en charge :" + noteTakenInChargeEntity.getTakenInCharge().getCode(), getUsernameFromJwt(), "Ajout note Prise en charge",
                        null, newNote.toDtoString(columnToAppend), PRISEENCHARGE, CREATE);
            }
        }
        else if (Donation.class.equals(entity.getClass())) {
            NoteDonation noteDonationEntity = NoteDonation.builder()
                    .donation((Donation) entity)
                    .note(newNote)
                    .build();
            noteDonationRepository.save(noteDonationEntity);
            Donation donationEntity = (Donation) entity;
            StringBuilder columnToAppend = new StringBuilder();
            columnToAppend.append("\"Code  Donation\": \"" +escapeSpecialChars(donationEntity.getCode()) + "\",");
            columnToAppend.append("\"Code  Donateur\": \"" +escapeSpecialChars(donationEntity.getDonor().getCode()) + "\",");
            columnToAppend.append("\"Type  Donation\": \"" +escapeSpecialChars(donationEntity.getType()) + "\"");

            if (existingNote != null && existingNote.getId() != null){
                auditApplicationService.audit("Modification note pour la Donation : " + noteDonationEntity.getDonation().getCode(), getUsernameFromJwt(), "Modification note Donation",
                        existingNote.toDtoString(columnToAppend), newNote.toDtoString(columnToAppend), DONATION2, UPDATE);
            }else {
                auditApplicationService.audit("Ajout d'une note pour la Donation :" + noteDonationEntity.getDonation().getCode(), getUsernameFromJwt(), "Ajout note Donation",
                        null, newNote.toDtoString(columnToAppend), DONATION2, CREATE);
            }
        }
        log.debug("End service Save Note Entity Association -> note: {}, took {}", newNote, watch.toMS());

    }

    private Object getEntityById(Long entityId, String entityType) throws TechnicalException {
        log.debug("Start service Get Entity By Id -> id: {}, type:{}", entityId, entityType);
        return switch (entityType.toLowerCase()) {
            case DONOR -> donorRepository.findById(entityId).orElse(null);
            case BENEFICIARY -> beneficiaryRepository.findById(entityId).orElse(null);
            case FAMILY-> familyRepository.findById(entityId).orElse(null);
            case TAKENINCHARGE-> takenInChargeRepository.findById(entityId).orElse(null);
            case DONATION-> donationRepository.findById(entityId).orElse(null);
            default -> throw new TechnicalException(messages.get(ENTITY_ID_NOT_FOUND));
        };
    }

    private List<NoteDTO> getAllNotesByTakenInCharge(Long takenInChargeId) {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service Get All Notes By TakenInChargeId {}", takenInChargeId);
        List<NoteTakenInCharge> noteTakenInChargeList = noteTakenInChargeRepository.findByTakenInChargeId(takenInChargeId);
        List<NoteDTO> noteDTOList = new ArrayList<>();
        TakenInCharge takenInCharge=takenInChargeRepository.findById(takenInChargeId).get();
        for (NoteTakenInCharge noteTakenInCharge : noteTakenInChargeList) {
            Note noteEntity = noteTakenInCharge.getNote();

            NoteDTO noteDTO = noteMapper.noteModelToDto(noteEntity);
            noteDTOList.add(noteDTO);
        }
        auditApplicationService.audit("Consultation des note du take in charge : "+takenInCharge.getCode(), getUsernameFromJwt(), "Consultation des notes du take in charge ",
                null, null, PRISEENCHARGE, CONSULTATION);
        log.debug("End service Get All Notes By TakenInCharge size: {}, took {}", noteDTOList.size(), watch.toMS());
        return noteDTOList;
    }

    public List<NoteDTO> getAllNotesByEntity(String entityType, Long entityId) throws TechnicalException {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service Get All Notes By EntityId: {}",entityId);
        List<NoteDTO> noteDTOList = switch (entityType.toLowerCase()) {
            case DONOR -> getAllNotesByDonor(entityId);
            case BENEFICIARY -> getAllNotesByBeneficiary(entityId);
            case FAMILY -> getAllNotesByFamily(entityId);
            case TAKENINCHARGE -> getAllNotesByTakenInCharge(entityId);
            case DONATION -> getAllNotesByDonation(entityId);
            default -> throw new TechnicalException(messages.get(ENTITY_TYPE_NOT_FOUND));
        };

        log.debug("End service Get All Notes By Entity, took {}", watch.toMS());
        return noteDTOList;
    }

    private List<NoteDTO> getAllNotesByDonor(Long donorId) {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service Get All Notes By DonorId {}", donorId);
        List<NoteDonor> noteDonorList = noteDonorRepository.findByDonorId(donorId);
        List<NoteDTO> noteDTOList = new ArrayList<>();
        Donor donorEntity = donorRepository.findById(donorId).get();
        for (NoteDonor noteDonor : noteDonorList) {
            Note noteEntity = noteDonor.getNote();

            NoteDTO noteDTO = noteMapper.noteModelToDto(noteEntity);
            noteDTOList.add(noteDTO);
        }

        auditApplicationService.audit("Consultation des note du donateur : "+donorEntity.getCode() , getUsernameFromJwt(), "Consultation des notes du donor",
                null, null, DONATION2, CONSULTATION);

        log.debug("End service Get All Notes By Donor, took {}", watch.toMS());
        return noteDTOList;
    }

    private List<NoteDTO> getAllNotesByFamily(Long familyId) {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service Get All Notes By FamilyId {}", familyId);
        List<NoteFamily> noteFamilyList = noteFamilyRepository.findByFamilyId(familyId);
        List<NoteDTO> noteDTOList = new ArrayList<>();
        Family familyEntity = familyRepository.findById(familyId).get();
        for (NoteFamily noteFamily : noteFamilyList) {
            Note noteEntity = noteFamily.getNote();

            NoteDTO noteDTO = noteMapper.noteModelToDto(noteEntity);
            noteDTOList.add(noteDTO);
        }

        auditApplicationService.audit("Consultation des note du Famille : "+familyEntity.getCode(), getUsernameFromJwt(), "Consultation des notes du Famille",
                null, null, FAMILLE, CONSULTATION);

        log.debug("End service Get All Notes By Family, took {}", watch.toMS());
        return noteDTOList;
    }

    private List<NoteDTO> getAllNotesByBeneficiary(Long beneficiaryId) {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service Get All Notes By BeneficiaryId {}", beneficiaryId);
        List<NoteBeneficiary> noteBeneficiaryList = noteBeneficiaryRepository.findByBeneficiaryId(beneficiaryId);
        List<NoteDTO> noteDTOList = new ArrayList<>();
        Beneficiary beneficiary=beneficiaryRepository.findById(beneficiaryId).get();
        for (NoteBeneficiary noteBeneficiary : noteBeneficiaryList) {
            Note noteEntity = noteBeneficiary.getNote();

            NoteDTO noteDTO = noteMapper.noteModelToDto(noteEntity);
            noteDTOList.add(noteDTO);
        }
            auditApplicationService.audit("Consultation des notes du bénéficiaire : "+beneficiary.getCode() , getUsernameFromJwt(), "Consultation des notes du Beneficiary",
                null, null, BENEFICIAIRE, CONSULTATION);
        log.debug("End service Get All Notes By Beneficiary, took {}", watch.toMS());
        return noteDTOList;
    }

    private List<NoteDTO> getAllNotesByDonation(Long donationId) {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service Get All Notes By donationId {}", donationId);
        List<NoteDonation> noteDonationList = noteDonationRepository.findByDonationId(donationId);
        List<NoteDTO> noteDTOList = new ArrayList<>();
        Donation donationEntity = donationRepository.findById(donationId).get();
        for (NoteDonation noteDonation : noteDonationList) {
            Note noteEntity = noteDonation.getNote();

            NoteDTO noteDTO = noteMapper.noteModelToDto(noteEntity);
            noteDTOList.add(noteDTO);
        }

        auditApplicationService.audit("Consultation des note du Donation :"+donationEntity.getCode() , getUsernameFromJwt(), "Consultation des notes du Donation",
                null, null, DONATION, CONSULTATION);

        log.debug("End service Get All Notes By Donation size: {}, took {}", noteDTOList.size(), watch.toMS());
        return noteDTOList;
    }

    public void deleteNote(Long id, String entityType) throws TechnicalException {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service Delete Note id: {}, entityType: {}", id, entityType);
        switch (entityType.toLowerCase()) {
            case DONOR -> {

                List<NoteDonor> noteDonorList = noteDonorRepository.findByNoteId(id);
                noteDonorRepository.deleteAll(noteDonorList);

                Note deleteNote = noteRepository.findById(id).orElseThrow(() -> new TechnicalException("Note not found"));
                Optional<Donor> donorOptional =donorRepository.findById(noteDonorList.get(0).getDonor().getId());
                Donor donor = donorOptional.get();
                if (donor instanceof HibernateProxy) {
                    donor = (Donor) Hibernate.unproxy(donor);
                }
                if(donor instanceof DonorPhysical){
                    DonorPhysical exsitingDonor = (DonorPhysical) donor;
                    StringBuilder columnToAppend = new StringBuilder();
                    columnToAppend.append("\"Type  donateur\": \"" + "Physique" + "\",");
                    columnToAppend.append("\"Code  donateur\": \"" + escapeSpecialChars(exsitingDonor.getCode())  + "\",");
                    columnToAppend.append("\"Nom  donateur\": \"" + escapeSpecialChars(exsitingDonor.getFirstName())+" "+escapeSpecialChars(exsitingDonor.getLastName()) + "\"");
                    auditApplicationService.audit("Suppression  note du donateur : " + exsitingDonor.getCode(), getUsernameFromJwt(), "Suppression  note du donateur",
                            deleteNote.toDtoString(columnToAppend), null, DONATEUR,DELETE);
                }
                else if(donor instanceof DonorAnonyme){
                    DonorAnonyme exsitingDonor = (DonorAnonyme) donor;
                    StringBuilder columnToAppend = new StringBuilder();
                    columnToAppend.append("\"Type  donateur\": \"" + "Anonyme" + "\",");
                    columnToAppend.append("\"Code  donateur\": \"" + escapeSpecialChars(exsitingDonor.getCode()) + "\",");
                    columnToAppend.append("\"Nom  donateur\": \"" + escapeSpecialChars(exsitingDonor.getName()) + "\"");
                    auditApplicationService.audit("Suppression  note du donateur : " + exsitingDonor.getCode(), getUsernameFromJwt(), "Suppression la note du donateur",
                            deleteNote.toDtoString(columnToAppend), null, DONATEUR, DELETE);
                } else {
                    DonorMoral exsitingDonor = (DonorMoral) donor;
                    StringBuilder columnToAppend = new StringBuilder();
                    columnToAppend.append("\"Type  donateur\": \"" + "Moral" + "\",");
                    columnToAppend.append("\"Code  donateur\": \"" + escapeSpecialChars(exsitingDonor.getCode()) + "\",");
                    columnToAppend.append("\"Nom  donateur\": \"" + escapeSpecialChars(exsitingDonor.getCompany()) + "\"");
                    auditApplicationService.audit("Suppression  note du donateur : " + exsitingDonor.getCode(), getUsernameFromJwt(), "Suppression  note du donateur",
                            deleteNote.toDtoString(columnToAppend),null, DONATEUR,DELETE);
                }
            }
            case BENEFICIARY -> {
                List<NoteBeneficiary> noteBeneficiaryList = noteBeneficiaryRepository.findByNoteId(id);
                noteBeneficiaryRepository.deleteAll(noteBeneficiaryList);

                Note deleteNote = noteRepository.findById(id).orElseThrow(() -> new TechnicalException("Note not found"));
                Optional<Beneficiary> beneficiaryOptional =beneficiaryRepository.findById(noteBeneficiaryList.get(0).getBeneficiary().getId());
                Beneficiary beneficiary = beneficiaryOptional.get();
                if (beneficiary instanceof HibernateProxy) {
                    beneficiary = (Beneficiary) Hibernate.unproxy(beneficiary);
                }

                StringBuilder columnToAppend = new StringBuilder();
                columnToAppend.append("\"Code\": \"" + escapeSpecialChars(beneficiary.getCode()) + "\",");
                columnToAppend.append("\"Nom  bénéficiaire\": \"" + escapeSpecialChars(beneficiary.getPerson().getLastName())+" "+escapeSpecialChars(beneficiary.getPerson().getFirstName())+ "\"");

                auditApplicationService.audit("Suppression   note du bénéficiaire : "+beneficiary.getCode() , getUsernameFromJwt(), "Delete a Beneficiary Note",
                        deleteNote.toDtoString(columnToAppend), null, BENEFICIAIRE, DELETE);
            }
            case FAMILY -> {
                List<NoteFamily> noteFamilyList = noteFamilyRepository.findByNoteId(id);
                noteFamilyRepository.deleteAll(noteFamilyList);

                Note deleteNote = noteRepository.findById(id).orElseThrow(() -> new TechnicalException("Note not found"));
                Optional<Family> familyOptional =familyRepository.findById(noteFamilyList.get(0).getFamily().getId());
                Family family = familyOptional.get();
                if (family instanceof HibernateProxy) {
                    family = (Family) Hibernate.unproxy(family);
                }

                StringBuilder columnToAppend = new StringBuilder();
                columnToAppend.append("\"Code  famille\": \"" + escapeSpecialChars(family.getCode()) + "\"");

                auditApplicationService.audit("Suppression   note du famille : "+family.getCode() , getUsernameFromJwt(), "Delete a Family Note",
                        deleteNote.toDtoString(columnToAppend), null, FAMILLE, DELETE);
            }
            case TAKENINCHARGE -> {
                List<NoteTakenInCharge> noteTakenInChargeList = noteTakenInChargeRepository.findByNoteId(id);
                noteTakenInChargeRepository.deleteAll(noteTakenInChargeList);

                Note deleteNote = noteRepository.findById(id).orElseThrow(() -> new TechnicalException("Note not found"));
                Optional<TakenInCharge> takenInCharge =takenInChargeRepository.findById(noteTakenInChargeList.get(0).getTakenInCharge().getId());
                TakenInCharge takenInCharge1 = takenInCharge.get();
                if (takenInCharge1 instanceof HibernateProxy) {
                    takenInCharge1 = (TakenInCharge) Hibernate.unproxy(takenInCharge1);
                }

                StringBuilder columnToAppend = new StringBuilder();
                columnToAppend.append("\"Code\": \"" + escapeSpecialChars(takenInCharge1.getCode()) + "\",");
                columnToAppend.append("\"Service\": \"" + escapeSpecialChars(takenInCharge1.getService().getName()) + "\"");
                auditApplicationService.audit("Suppression   note du prise en charge : "+takenInCharge1.getCode() , getUsernameFromJwt(), "Delete a TakenInCharge Note",
                        deleteNote.toDtoString(columnToAppend), null, PRISEENCHARGE, DELETE);
            }
            case DONATION -> {
                List<NoteDonation> noteDonationList = noteDonationRepository.findByNoteId(id);
                noteDonationRepository.deleteAll(noteDonationList);

                Note deleteNote = noteRepository.findById(id).orElseThrow(() -> new TechnicalException("Note not found"));
                Optional<Donation> donation =donationRepository.findById(noteDonationList.get(0).getDonation().getId());
                Donation donation1 = donation.get();
                if (donation1 instanceof HibernateProxy) {
                    donation1 = (Donation) Hibernate.unproxy(donation1);
                }

                StringBuilder columnToAppend = new StringBuilder();
                columnToAppend.append("\"Code  Donation\": \"" +escapeSpecialChars(donation1.getCode()) + "\",");
                columnToAppend.append("\"Code  Donateur\": \"" +escapeSpecialChars(donation1.getDonor().getCode()) + "\",");
                columnToAppend.append("\"Type  Donation\": \"" +escapeSpecialChars(donation1.getType()) + "\"");
                auditApplicationService.audit("Suppression   note du donation : "+donation1.getCode() , getUsernameFromJwt(), "Delete a Donation Note",
                        deleteNote.toDtoString(columnToAppend), null, DONATION2, DELETE);
            }
            default -> throw new TechnicalException(messages.get(ENTITY_TYPE_NOT_FOUND));
        }
        log.debug("End service Delete Notes id: {}, took {}", id, watch.toMS());

        noteRepository.deleteById(id);
    }

}
