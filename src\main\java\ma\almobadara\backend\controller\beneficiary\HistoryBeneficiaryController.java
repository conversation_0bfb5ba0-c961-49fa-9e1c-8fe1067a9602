package ma.almobadara.backend.controller.beneficiary;


import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.beneficiary.HistoryBeneficiaryDTO;
import ma.almobadara.backend.service.beneficiary.HistoryBeneficiaryService;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RefreshScope
@RestController
@AllArgsConstructor
@Slf4j
@RequestMapping("history-beneficiary")
public class HistoryBeneficiaryController {

    private final HistoryBeneficiaryService historyBeneficiaryService;
    @GetMapping("/beneficiary/{beneficiaryId}")
    public List<HistoryBeneficiaryDTO> getHistoryByBeneficiaryId(@PathVariable Long beneficiaryId) {
        log.info("Start resource getHistoryByBeneficiaryId: beneficiaryId={}", beneficiaryId);
        List<HistoryBeneficiaryDTO> historyBeneficiaryDTOList = historyBeneficiaryService.getHistoryByBeneficiaryId(beneficiaryId);
        log.info("End resource getHistoryByBeneficiaryId: beneficiaryId={}", beneficiaryId);
        return historyBeneficiaryDTOList;
    }

    @DeleteMapping("/beneficiary/{beneficiaryId}")
    public ResponseEntity<Void> clearHistoryByBeneficiaryId(@PathVariable Long beneficiaryId) {
        log.info("Start resource clearHistoryByBeneficiaryId: beneficiaryId={}", beneficiaryId);
        historyBeneficiaryService.clearHistoryByBeneficiaryId(beneficiaryId);
        log.info("End resource clearHistoryByBeneficiaryId: beneficiaryId={}", beneficiaryId);
        return ResponseEntity.noContent().build();
    }
}