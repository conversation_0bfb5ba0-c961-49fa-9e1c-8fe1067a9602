package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.donor.DonorDocumentDetailsDTO;
import ma.almobadara.backend.model.donor.DocumentDonor;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring")
public interface DocumentDonorDetailsMapper {

    @Mapping(source = "document.id", target = "id")
    @Mapping(source = "document.label", target = "label")
    @Mapping(source = "document.documentDate", target = "documentDate")
    @Mapping(source = "document.expiryDate", target = "expiryDate")
    @Mapping(source = "document.comment", target = "comment")
    @Mapping(source = "document.fileUrl", target = "fileUrl")
    @Mapping(source = "document.fileName", target = "fileName")
    @Mapping(source = "document.typeDocumentId", target = "typeDocumentId")
    @Mapping(source = "donor.balance", target = "balance")
    @Mapping(source = "donor.identityCode", target = "identityCode")
    @Mapping(source = "donor.address", target = "address")
    @Mapping(source = "donor.addressAr", target = "addressAr")
    DonorDocumentDetailsDTO documentDonorToDto(DocumentDonor documentDonor);
    List<DonorDocumentDetailsDTO> documentDonorListToDtoList(List<DocumentDonor> documentDonors);

}
