-- Create the Zone table
CREATE TABLE Zone (
                      id BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
                      code VA<PERSON>HA<PERSON>(255) UNIQUE NOT NULL,
                      name VA<PERSON>HA<PERSON>(255) NOT NULL,
                      name_ar VARCHAR(255),
                      details TEXT,
                      is_deleted B<PERSON><PERSON>EAN NOT NULL DEFAULT FALSE,
                      city_ids VARCHAR(1000),  -- Fixed: Removed the erroneous BOOLEAN definition
                      created_at TIMESTAMP WITHOUT TIME ZONE,
                      updated_at TIMESTAMP WITHOUT TIME ZONE,
                      assistant_id BIGINT,
                      CONSTRAINT pk_zone PRIMARY KEY (id)
);

-- Create the assistant table
CREATE TABLE assistant (
                           id BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
                           code VARCHAR(255) UNIQUE NOT NULL,
                           address VARCHAR(255),
                           city_id BIGINT,
                           creation_date TIMESTAMP WITHOUT TIME ZONE,
                           is_deleted BOOLEAN DEFAULT FALSE,
                           date_end_affectation_to_zone DATE,
                           date_affectation_to_zone DATE,
                           update_date TIMES<PERSON>MP WITHOUT TIME ZONE,
                           cache_ad_user_id BIGINT NOT NULL,
                           zone_id BIGINT,
                           PRIMARY KEY (id)
);

-- Add foreign key constraints
ALTER TABLE assistant ADD CONSTRAINT fk_assistant_cache_ad_user_id FOREIGN KEY (cache_ad_user_id) REFERENCES cache_ad_user(id);
ALTER TABLE assistant ADD CONSTRAINT fk_assistant_zone_id FOREIGN KEY (zone_id) REFERENCES zone(id);
ALTER TABLE Zone ADD CONSTRAINT fk_zone_assistant_id FOREIGN KEY (assistant_id) REFERENCES assistant(id);
