package ma.almobadara.backend.model.family;

import jakarta.persistence.*;
import lombok.*;
import ma.almobadara.backend.model.communs.Note;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@IdClass(NoteFamilyMemberId.class)
public class NoteFamilyMember{

    @Id
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "family_member_id")
    private FamilyMember familyMember;

    @Id
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "note_id")
    private Note note;

}
