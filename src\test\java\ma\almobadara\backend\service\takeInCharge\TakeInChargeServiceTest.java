package ma.almobadara.backend.service.takeInCharge;


import ma.almobadara.backend.Audit.AuditApplicationService;
import ma.almobadara.backend.controller.referentiel.RefController;
import ma.almobadara.backend.dto.donor.DonorDTO;
import ma.almobadara.backend.dto.service.ServicesDTO;
import ma.almobadara.backend.dto.takenInCharge.*;
import ma.almobadara.backend.enumeration.BudgetLineStatus;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.mapper.TakenInChargeMapper;
import ma.almobadara.backend.mapper.TakenInChargeOperationMapper;
import ma.almobadara.backend.mapper.TakenInChargeOperationMapperImpl;
import ma.almobadara.backend.model.beneficiary.Beneficiary;
import ma.almobadara.backend.model.donation.BudgetLine;
import ma.almobadara.backend.model.donation.Donation;
import ma.almobadara.backend.model.donor.Donor;
import ma.almobadara.backend.model.service.Services;
import ma.almobadara.backend.model.takenInCharge.*;
import ma.almobadara.backend.repository.beneficiary.BeneficiaryRepository;
import ma.almobadara.backend.repository.donation.BudgetLineRepository;
import ma.almobadara.backend.repository.donation.DonationRepository;
import ma.almobadara.backend.repository.donor.DonorRepository;
import ma.almobadara.backend.repository.donor.TakenInChargeDonorRepository;
import ma.almobadara.backend.repository.services.ServicesRepository;
import ma.almobadara.backend.repository.takenInCharge.TakenInChargeOperationHistoriqueRepository;
import ma.almobadara.backend.repository.takenInCharge.TakenInChargeOperationRepository;
import ma.almobadara.backend.repository.takenInCharge.TakenInChargeRepository;
import ma.almobadara.backend.service.takenInCharge.TakenInChargeService;
import ma.almobadara.backend.util.page.PageDto;
import java.time.*;

import org.apache.poi.ss.usermodel.DateUtil;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import ma.almobadara.backend.service.takeInCharge.TakeInChargeServiceTest;
import org.springframework.context.annotation.Import;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;
import ma.almobadara.backend.config.Messages;
@ExtendWith(MockitoExtension.class)

@Import({Messages.class})
public class TakeInChargeServiceTest {

    @Mock
    private TakenInChargeMapper takenInChargeMapper;
    @Mock
    private TakenInChargeRepository takenInChargeRepository;
    @Mock
    private ServicesRepository servicesRepository;
    @Mock
    private TakenInChargeDonorRepository takenInChargeDonorRepository;
    @Mock
    private DateUtil dateUtil;
    @Mock
    private Messages message;
    @Mock
    private DonorRepository donorRepository;
    @Mock
    private BudgetLineRepository budgetLineRepository;
    @Mock
    private DonationRepository donationRepository;
    @Mock
    private TakenInChargeOperationRepository takenInChargeOperationRepository;
    @Mock
    private TakenInChargeOperationHistoriqueRepository takenInChargeOperationHistoriqueRepository;
    @Mock
    private TakenInChargeOperationMapper takenInChargeOperationMapper;
    @Mock
    private RefController refController;
    @Mock
    private BeneficiaryRepository beneficiaryRepository;
    @Mock
    private AuditApplicationService auditApplicationService;

    @Spy
    @InjectMocks
    private TakenInChargeService takeInChargeService;

    @Test
    void addTakenInCharge_shouldAddTakenInCharge() throws Exception {
        TakenInChargeAuditDto takenInChargeAuditDto = TakenInChargeAuditDto.builder()
                .build();
        TakenInChargeBeneficiaryDTO takenInChargeBeneficiaryDTO = TakenInChargeBeneficiaryDTO.builder()
                .id(null)
                .build();
        TakenInChargeBeneficiary takenInChargeBeneficiary = TakenInChargeBeneficiary.builder()
                .id(null)
                .build();
        DonorDTO donorDTO = DonorDTO.builder()
                .id(1L)
                .build();
        Donor donor = Donor.builder()
                .id(1L)
                .build();
        TakenInChargeDonor takenInChargeDonor = TakenInChargeDonor.builder()
                .id(1L)
                .donor(donor)
                .build();
        TakenInChargeDonorDTO takenInChargeDonorDTO = TakenInChargeDonorDTO.builder()
                .id(1L)
                .donor(donorDTO)
                .build();
        TakenInCharge takenInCharge = TakenInCharge.builder()
                .id(1L)
                .takenInChargeBeneficiaries(List.of(takenInChargeBeneficiary))
                .takenInChargeDonors(List.of(takenInChargeDonor))
                .build();
        TakenInChargeDTO takenInChargeDTO = TakenInChargeDTO.builder()
                .id(2L)
                .takenInChargeBeneficiaries((List.of(takenInChargeBeneficiaryDTO)))
                .services(null)
                .takenInChargeDonors(List.of(takenInChargeDonorDTO))
                .build();

        // Given

        // When
        when(takenInChargeMapper.takenInChargeDTOToTakenInCharge(any(TakenInChargeDTO.class))).thenReturn(takenInCharge);
        when(takenInChargeMapper.takenInChargeToTakenInChargeDTO(any(TakenInCharge.class))).thenReturn(takenInChargeDTO);
        when(takenInChargeRepository.findById(anyLong())).thenReturn(Optional.of(takenInCharge));
        when(servicesRepository.findById(takenInChargeDTO.getServiceId())).thenReturn(Optional.empty());
        when(takenInChargeMapper.takenInChargeDtoToTakenInChargeAudit(any(TakenInChargeDTO.class))).thenReturn(takenInChargeAuditDto);
        when(donorRepository.findById(anyLong())).thenReturn(Optional.of(donor));

        // Then
        TakenInChargeDTO result = takeInChargeService.addTakenInCharge(takenInChargeDTO);

        assertNotNull(result);
    }
    @Test
    void getAllTakenInCharges_shouldReturnAllTakenInCharges() {
        // Given
        Optional<Integer> page = Optional.of(1);
        Page<TakenInCharge> takenInCharges = new PageImpl<>(List.of(TakenInCharge.builder().id(1L).build()));
        TakenInChargeDTO takenInChargeDTO = TakenInChargeDTO.builder().id(1L).build();

        doReturn(takenInCharges).when(takeInChargeService).getFilteredTakenInCharges(any(), any(), any(), any());

        when(takenInChargeMapper.takenInChargeToTakenInChargeDTOForList(any())).thenReturn(takenInChargeDTO);

        Page<TakenInChargeDTO> result = takeInChargeService.getAllTakenInCharges(null,null,null,page);
    }

    @Test
    void getTakenInChargeById() throws TechnicalException {
        Optional<TakenInCharge> takenInCharge = Optional.of(TakenInCharge.builder().id(1L).build());
        DonorDTO donorDTO = DonorDTO.builder().id(1L).build();
        TakenInChargeDonorDTO takenInChargeDonorDTO = TakenInChargeDonorDTO.builder().id(1L).donor(donorDTO).build();
        ServicesDTO service = ServicesDTO.builder().id(1L).build();
        TakenInChargeBeneficiary takenInChargeBeneficiary = TakenInChargeBeneficiary.builder().id(1L).build();
        TakenInChargeDTO takenInChargeDTO = TakenInChargeDTO.builder().takenInChargeDonors((new ArrayList(List.of(takenInChargeDonorDTO)))).takenInChargeBeneficiaries(null).services(service).id(1L).build();


        when(takenInChargeRepository.findById(anyLong())).thenReturn(takenInCharge);
        when(takenInChargeMapper.takenInChargeToTakenInChargeDTO(any(TakenInCharge.class))).thenReturn(takenInChargeDTO);
        when(takenInChargeDonorRepository.findAvailableBalanceByDonorAndService(1L, null)).thenReturn(1D);
        doNothing().when(takeInChargeService).setBeneficiaryCodes(any(), any());
        doNothing().when(takeInChargeService).setDonorCodes(any(), any());

        TakenInChargeDTO result = takeInChargeService.getTakenInChargeById(1L);
        assertNotNull(result);
    }
    @Test
    void testPlanTakenInCharge_Success() throws TechnicalException {
        List<TakenInChargeOperationDTO> newOperationDTOs = List.of(new TakenInChargeOperationDTO());
        List<TakenInChargeOperation> newOperation= new ArrayList<>(List.of(new TakenInChargeOperation()));
        TakenInChargeOperation takenInChargeOperation = TakenInChargeOperation.builder().id(1L).build();
        takenInChargeOperation.setTakenInChargeDonor(TakenInChargeDonor.builder().id(1L).donor(Donor.builder().id(1L).build()).build());
        newOperation.add(takenInChargeOperation);


        when((List<TakenInChargeOperation>)takenInChargeOperationMapper.takenInChargeOperationDTOToTakenInChargeOperation(newOperationDTOs)).thenReturn(newOperation);


        List<TakenInChargeOperationDTO> result = takeInChargeService.planTakenInCharge(newOperationDTOs);

        assertNotNull(result);
    }

    @Test
    void updateOperation_shouldUpdateOperation() throws TechnicalException {
        TakenInChargeOperationDTO takenInChargeOperationDTO = TakenInChargeOperationDTO.builder().planningDate(new Date()).id(1L).build();
        TakenInChargeOperation takenInChargeOperation = TakenInChargeOperation.builder().id(1L).planningDate(new Date()).build();
        when(takenInChargeOperationRepository.findById(anyLong())).thenReturn(Optional.of(takenInChargeOperation));
        when(takenInChargeOperationRepository.save(any())).thenReturn(takenInChargeOperation);
        when(takenInChargeOperationMapper.takenInChargeOperationToTakenInChargeOperationDTO(any(TakenInChargeOperation.class))).thenReturn(takenInChargeOperationDTO);
        when(takenInChargeOperationMapper.takenInChargeOperationDTOToTakenInChargeOperation(any(TakenInChargeOperationDTO.class))).thenReturn(takenInChargeOperation);

        TakenInChargeOperationDTO result = takeInChargeService.updateOperation(takenInChargeOperationDTO);

        assertNotNull(result);
    }
    @Test
    void reserveOperation_shouldReserveAnOperation() throws TechnicalException {
        DonorDTO donorDTO = DonorDTO.builder().id(1L).build();
        TakenInChargeOperationDTO takenInChargeOperationDTO = TakenInChargeOperationDTO.builder().id(1L).amount(2D).takenInChargeDonor(TakenInChargeDonorDTO.builder().donor(donorDTO).takenInCharge(TakenInChargeDTO.builder().id(1L).services(ServicesDTO.builder().id(1L).build()).build()).build()).build();
        TakenInChargeOperation takenInChargeOperation = TakenInChargeOperation.builder().id(1L).amount(2D).takenInChargeDonor(TakenInChargeDonor.builder().donor(Donor.builder().id(1L).build()).takenInCharge(TakenInCharge.builder().id(1L).service(Services.builder().id(1L).build()).build()).build()).build();
        Donation donation = Donation.builder().id(1L).value(5D).build();

        when(takenInChargeOperationRepository.findById(anyLong())).thenReturn(Optional.of(takenInChargeOperation));
        when(takenInChargeOperationRepository.save(any())).thenReturn(new TakenInChargeOperation());
        when(takenInChargeOperationMapper.takenInChargeOperationToTakenInChargeOperationDTO(any(TakenInChargeOperation.class))).thenReturn(takenInChargeOperationDTO);
        when(takenInChargeDonorRepository.findAvailableBalanceByDonorAndService(1L, 1L)).thenReturn(3D);
        when(donationRepository.findByDonorId(anyLong())).thenReturn(List.of(donation));
        when(budgetLineRepository.findByDonationIdAndServiceIdAndStatus(anyLong(), anyLong(), any())).thenReturn(List.of());
        when(takenInChargeOperationRepository.save(any())).thenReturn(new TakenInChargeOperation());
        doNothing().when(takeInChargeService).createOperationHistory(any(), any());


        TakenInChargeOperationDTO result = takeInChargeService.reserveOperation(takenInChargeOperationDTO);

        assertNotNull(result);
    }
    @Test
    void deleteOperation_shouldDeleteOperation() throws TechnicalException {
        TakenInChargeOperation takenInChargeOperation = TakenInChargeOperation.builder().id(1L).takenInChargeDonor(TakenInChargeDonor.builder().id(1L).takenInCharge(TakenInCharge.builder().id(1L).build()).build()).build();
        when(takenInChargeOperationRepository.findById(anyLong())).thenReturn(Optional.of(takenInChargeOperation));
        doNothing().when(takenInChargeOperationRepository).deleteById(anyLong());

        takeInChargeService.deleteOperation(1L);

        verify(takenInChargeOperationRepository, times(1)).deleteById(anyLong());
    }
    @Test
    void cancelReservationOperation_Success() throws TechnicalException {
        TakenInChargeOperation takenInChargeOperation = TakenInChargeOperation.builder().id(1L).code("code 3").amount(300).build();
        List<BudgetLine> budgetLines =List.of(
                BudgetLine.builder().id(1L).donation(Donation.builder().id(1L).build()).service(Services.builder().id(1L).build()).code("code 1").build(),
                BudgetLine.builder().id(2L).donation(Donation.builder().id(1L).build()).service(Services.builder().id(1L).build()).code("code 2").build()
        );
        TakenInChargeOperationDTO takenInChargeOperationDTO=TakenInChargeOperationDTO.builder().id(1L).code("code 3").build();
        when(takenInChargeOperationRepository.findById(anyLong())).thenReturn(Optional.of(takenInChargeOperation));
        when(budgetLineRepository.findByTakenInChargeOperationId(anyLong())).thenReturn(budgetLines);
        when(budgetLineRepository.findFirstByDonationIdAndServiceIdAndStatus(1L,1L, BudgetLineStatus.DISPONIBLE)).thenReturn(null);
        when(takenInChargeOperationRepository.save(any(TakenInChargeOperation.class))).thenReturn(takenInChargeOperation);
        when(takenInChargeOperationHistoriqueRepository.save(any(TakenInChargeOperationHistorique.class))).thenReturn(null);
        when(budgetLineRepository.save(any(BudgetLine.class))).thenReturn(null);
        when(takenInChargeOperationMapper.takenInChargeOperationToTakenInChargeOperationDTO(any(TakenInChargeOperation.class))).thenReturn(takenInChargeOperationDTO);

        TakenInChargeOperationDTO takenInChargeOperationDTO1= takeInChargeService.cancelReservationOperation(takenInChargeOperationDTO);

        assertNotNull(takenInChargeOperationDTO1);
        assertEquals(takenInChargeOperationDTO.getId(),takenInChargeOperationDTO1.getId());
        assertEquals(takenInChargeOperationDTO.getCode(),takenInChargeOperationDTO1.getCode());
    }
    @Test
    void cancelReservationOperation_NotFound() throws TechnicalException {
        TakenInChargeOperationDTO takenInChargeOperationDTO=TakenInChargeOperationDTO.builder().id(1L).code("code 3").build();
        when(takenInChargeOperationRepository.findById(anyLong())).thenReturn(Optional.empty());

        assertThrows(TechnicalException.class,()->{takeInChargeService.cancelReservationOperation(takenInChargeOperationDTO);});
    }

    @Test
    void executeOperation_Success() throws TechnicalException {
        TakenInChargeOperation takenInChargeOperation = TakenInChargeOperation.builder().status("Exécuté").id(1L).code("code 3").amount(300).build();
        List<BudgetLine> budgetLines =List.of(
                BudgetLine.builder().id(1L).donation(Donation.builder().id(1L).build()).service(Services.builder().id(1L).build()).code("code 1").build(),
                BudgetLine.builder().id(2L).donation(Donation.builder().id(1L).build()).service(Services.builder().id(1L).build()).code("code 2").build()
        );
        TakenInChargeOperationDTO takenInChargeOperationDTO=TakenInChargeOperationDTO.builder().status("Exécuté").executionDate(new Date()).id(1L).code("code 3").build();
        when(takenInChargeOperationRepository.findById(anyLong())).thenReturn(Optional.of(takenInChargeOperation));
        when(budgetLineRepository.findByTakenInChargeOperationId(anyLong())).thenReturn(budgetLines);
        when(takenInChargeOperationRepository.save(any(TakenInChargeOperation.class))).thenReturn(takenInChargeOperation);
        when(takenInChargeOperationHistoriqueRepository.save(any(TakenInChargeOperationHistorique.class))).thenReturn(null);
        when(budgetLineRepository.save(any(BudgetLine.class))).thenReturn(null);
        when(takenInChargeOperationMapper.takenInChargeOperationToTakenInChargeOperationDTO(any(TakenInChargeOperation.class))).thenReturn(takenInChargeOperationDTO);

        TakenInChargeOperationDTO takenInChargeOperationDTO1= takeInChargeService.executeOperation(takenInChargeOperationDTO);

        assertNotNull(takenInChargeOperationDTO1);
        assertEquals(takenInChargeOperationDTO.getStatus(),takenInChargeOperationDTO1.getStatus());
        assertEquals(takenInChargeOperationDTO.getId(),takenInChargeOperationDTO1.getId());
        assertEquals(takenInChargeOperationDTO.getCode(),takenInChargeOperationDTO1.getCode());
    }

    @Test
    void executeOperation_NotFound() throws TechnicalException {
        TakenInChargeOperationDTO takenInChargeOperationDTO=TakenInChargeOperationDTO.builder().id(1L).code("code 3").build();
        when(takenInChargeOperationRepository.findById(anyLong())).thenReturn(Optional.empty());

        assertThrows(TechnicalException.class,()->{takeInChargeService.executeOperation(takenInChargeOperationDTO);});
    }
}
