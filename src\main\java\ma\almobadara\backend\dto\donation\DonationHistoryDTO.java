package ma.almobadara.backend.dto.donation;

import lombok.*;

import java.util.Date;
@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class DonationHistoryDTO {
    private Long id;
    private Double amount;
    private String newServiceName;
    private String oldServiceName;
    private String aideComplementaireName;
    private Date executionDate;
    private Long newServiceId;
    private Long oldServiceId;
    private Long aideComplementaireId;
    private Double amountInitial;
    private Double amountReserved;
    private String type;

}
