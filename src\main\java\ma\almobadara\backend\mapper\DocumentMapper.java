package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.communs.DocumentAuditDTO;
import ma.almobadara.backend.dto.communs.DocumentDTO;
import ma.almobadara.backend.dto.donor.DocumentCorrespondenceDTO;
import ma.almobadara.backend.dto.referentiel.TypeDocumentDonorDTO;
import ma.almobadara.backend.model.communs.Document;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface DocumentMapper {


	@Mapping(source = "typeDocumentId", target = "type.id")
	@Mapping( target = "tags" , ignore = true)
	DocumentDTO documentModelToDto(Document document);

	Iterable<DocumentDTO> documentListModelToDto(Iterable<Document> documents);

	@Mapping(source = "type.id", target = "typeDocumentId")
	Document documentToModelToModel(DocumentDTO documentDonor);



	Iterable<Document> documentListDtoToModal(Iterable<DocumentDTO> documents);


	TypeDocumentDonorDTO typeDocumentDonorToDto(TypeDocumentDonorDTO typeDocumentDonor);

	Iterable<TypeDocumentDonorDTO> typeDocumentDonorListToDtoList(Iterable<TypeDocumentDonorDTO> typeDocumentDonors);

	@Mapping(source = "label", target = "Objet")
	@Mapping(source = "documentDate", target = "DateDocument")
	@Mapping(source = "expiryDate", target = "DateExpiration")
	@Mapping(source = "comment", target = "Commentaire")
	@Mapping(source = "fileName", target = "NomFichier")
	@Mapping( target = "TypeDocument" , ignore = true)
	DocumentAuditDTO documentToDocumentAuditDto(Document document);


	Document documentToModelToModel(DocumentCorrespondenceDTO documentCorrespondenceDTO);

}
