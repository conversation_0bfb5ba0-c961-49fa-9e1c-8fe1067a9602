package ma.almobadara.backend.model.takenInCharge;

import jakarta.persistence.*;
import lombok.*;
import ma.almobadara.backend.model.communs.Document;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@IdClass(DocumentTakenInChargeId.class)
public class DocumentTakenInCharge {

    @Id
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "taken_in_charge_id")
    private TakenInCharge takenInCharge;

    @Id
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "document_id")
    private Document document;

}
