package ma.almobadara.backend.controller.administration;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.CityWithRegionAndCountryDTO;
import ma.almobadara.backend.dto.administration.SousZoneDTO;
import ma.almobadara.backend.dto.administration.ZoneDTO;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.service.administration.SousZoneService;
import ma.almobadara.backend.service.administration.ZoneService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;


@RestController
@RequestMapping("/zones")
@AllArgsConstructor
@Slf4j
public class ZoneController {

    private final ZoneService zoneService;
    private final SousZoneService sousZoneService;


    @GetMapping
    public ResponseEntity<Page<ZoneDTO>> getAllZones(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "5") int size,
            @RequestParam(required = false) final String searchBycode,
            @RequestParam(required = false) final String searchByAssistantName,
            @RequestParam(required = false) final String searchByName,
            @RequestParam(required = false) final String searchByNameAr,
            @RequestParam(required = false) final String searchByStatus,
            @RequestParam(required = false) final String searchByCity
    ) {
        log.info("Start resource getAllZones");
        Page<ZoneDTO> zones = zoneService.getAllZones(page, size, searchBycode, searchByAssistantName, searchByName, searchByNameAr, searchByStatus, searchByCity);
        return ResponseEntity.ok(zones);
    }

    @GetMapping("/list")
    public ResponseEntity<List<ZoneDTO>> getAllZonesList(
    ) {
        log.info("Start resource getAllZones");
        List<ZoneDTO> zones = zoneService.getAllZonesList();
        return ResponseEntity.ok(zones);
    }

    @GetMapping("/{id}")
    public ResponseEntity<ZoneDTO> getZoneById(@PathVariable Long id) throws TechnicalException {
        log.info("Start resource getZoneById with id: {}", id);
        ZoneDTO zone = zoneService.getZoneById(id);
        log.info("End resource getZoneById with id: {}", id);
        return zone != null ? ResponseEntity.ok(zone) : ResponseEntity.notFound().build();
    }

    @PostMapping
    public ResponseEntity<ZoneDTO> createZone(@RequestBody ZoneDTO zoneDTO) throws TechnicalException {
        log.info("Start resource createZone with code: {}, name: {}", zoneDTO.getCode(), zoneDTO.getName());
        ZoneDTO createdZone = zoneService.createZone(zoneDTO);
        log.info("end resource createZone with code: {}, name: {}", zoneDTO.getCode(), zoneDTO.getName());
        return ResponseEntity.status(HttpStatus.CREATED).body(createdZone);
    }

    @PutMapping("/{id}")
    public ResponseEntity<ZoneDTO> updateZone(@PathVariable Long id, @RequestBody ZoneDTO zoneDTO) throws TechnicalException {
        log.info("Start resource updateZone with id: {}, code: {}, name: {}", id, zoneDTO.getCode(), zoneDTO.getName());
        ZoneDTO updatedZone = zoneService.updateZone(id, zoneDTO);
        log.info("End resource updateZone with id: {}, code: {}, name: {}", id, zoneDTO.getCode(), zoneDTO.getName());
        return ResponseEntity.status(HttpStatus.CREATED).body(updatedZone);
    }

    @DeleteMapping("/{id}")
    // we shoudl send the error catched by the service
    public ResponseEntity<Object> deleteZone(@PathVariable Long id) {
        log.info("Start resource deleteZone with id: {}", id);
        try {
            boolean isDeleted = zoneService.deleteZone(id);
        } catch (TechnicalException e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of("error", "Technical error", "message", e.getMessage()));
        }
        log.info("End resource deleteZone with id: {}", id);
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }

    @GetMapping("/cities")
    public ResponseEntity<List<CityWithRegionAndCountryDTO>> getAllCities() {
        log.info("Start resource getAllCities");
        List<CityWithRegionAndCountryDTO> cities = zoneService.getCityOfMorroco();
        return ResponseEntity.ok(cities);
    }

//    @PutMapping("/{zoneId}/status")
//    public ResponseEntity<ZoneDTO> changeZoneStatus(
//            @PathVariable Long zoneId,
//            @RequestParam boolean status) throws TechnicalException {
//        ZoneDTO updatedZone = zoneService.changeZoneStatus(zoneId, status);
//        return ResponseEntity.ok(updatedZone);
//    }

    @PutMapping("/{zoneId}/status")
    public ResponseEntity<Object> changeZoneStatus(
            @PathVariable Long zoneId,
            @RequestParam boolean status) {
        log.info("Start resource changeZoneStatus with zoneId: {}", zoneId);
        try {
            ZoneDTO updatedZone = zoneService.changeZoneStatus(zoneId, status);
            log.info("End resource changeZoneStatus with zoneId: {}", zoneId);
            return ResponseEntity.ok(updatedZone);
        } catch (TechnicalException e) {
            log.error("Error changing status for zone with id {}: {}", zoneId, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Technical error", "message", e.getMessage()));
        }
    }

    @GetMapping("/{zoneId}/details")
    public ResponseEntity<ZoneDTO> getZoneDetails(
            @PathVariable Long zoneId,
            @RequestParam(defaultValue = "0") int assistantsPage,
            @RequestParam(defaultValue = "10") int assistantsPageSize,
            @RequestParam(defaultValue = "0") int beneficiariesPage,
            @RequestParam(defaultValue = "10") int beneficiariesPageSize) throws TechnicalException {

        ZoneDTO zoneDTO = zoneService.getZoneWithBeneficiariesAndAssistants(zoneId, assistantsPage, assistantsPageSize, beneficiariesPage, beneficiariesPageSize);
        return ResponseEntity.ok(zoneDTO);
    }

/***************sous zone part **********/

@GetMapping("/{zoneId}/sous-zones")
public ResponseEntity<List<SousZoneDTO>> getAllSousZones(@PathVariable Long zoneId) {
    log.info("Start resource getAllSousZones for zoneId: {}", zoneId);
    List<SousZoneDTO> sousZones = sousZoneService.getSousZonesByZoneId(zoneId);
    log.info("End resource getAllSousZones for zoneId: {}", zoneId);
    return ResponseEntity.ok(sousZones);
}

    /**
     * Add a new Sous Zone to a given Zone.
     */
    @PostMapping("/{zoneId}/sous-zones")
    public ResponseEntity<SousZoneDTO> addSousZone(
            @PathVariable Long zoneId,
            @RequestBody SousZoneDTO sousZoneDTO) throws TechnicalException {
        log.info("Start resource addSousZone for zoneId: {}", zoneId);
        SousZoneDTO createdSousZone = sousZoneService.addOrUpdateSousZone(zoneId, sousZoneDTO);
        log.info("End resource addSousZone for zoneId: {}", zoneId);
        return ResponseEntity.status(HttpStatus.CREATED).body(createdSousZone);
    }

    /**
     * Delete a Sous Zone by its ID for a given Zone.
     */
    @DeleteMapping("/sous-zones/{sousZoneId}")
    public ResponseEntity<Object> deleteSousZone(
            @PathVariable Long sousZoneId) throws TechnicalException {
        log.info("Start resource deleteSousZone for  sousZoneId: {}", sousZoneId);
        sousZoneService.deleteSousZone(sousZoneId);
        log.info("End resource deleteSousZone for sousZoneId: {}", sousZoneId);
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }






}




