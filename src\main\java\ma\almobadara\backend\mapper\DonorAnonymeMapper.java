package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.beneficiary.BeneficiaryDTO;
import ma.almobadara.backend.dto.beneficiary.PersonDTO;
import ma.almobadara.backend.dto.donation.DonationDTO;
import ma.almobadara.backend.dto.donation.DonationProductNatureDTO;
import ma.almobadara.backend.dto.donor.DonorAnonymeDTO;
import ma.almobadara.backend.dto.takenInCharge.*;
import ma.almobadara.backend.model.beneficiary.Beneficiary;
import ma.almobadara.backend.model.beneficiary.Person;
import ma.almobadara.backend.model.donation.Donation;
import ma.almobadara.backend.model.donation.DonationProductNature;
import ma.almobadara.backend.model.donor.DonorAnonyme;
import ma.almobadara.backend.model.takenInCharge.*;
import org.mapstruct.*;

import java.util.List;

@Mapper(componentModel = "spring")
public interface DonorAnonymeMapper {

    @Mapping(source = "donations", target = "donations", qualifiedByName = "mapDonationForList")
    @Mapping(source = "donorStatusId", target = "status.id")
    @Mapping(source = "cityId", target = "city.id")
    DonorAnonymeDTO toDto(DonorAnonyme donorAnonyme);

    @Mapping(source = "status.id", target = "donorStatusId")
    @Mapping(source = "city.id", target = "cityId")
    DonorAnonyme toModel(DonorAnonymeDTO donorAnonymeDTO);

    @Named("mapDonationForList")
    @Mapping(target = "donor", ignore = true) // Break the circular reference
    @Mapping(source = "canalDonationId", target = "canalDonation.id")
    DonationDTO donationModelToDto(Donation donation);

    List<DonationDTO> donationModelToDto(List<Donation> donations);

    @IterableMapping(qualifiedByName = "mapWithoutNesting")
    List<DonorAnonymeDTO> toDto(List<DonorAnonyme> donorAnonymes);

    // Break the circular reference here as well
    @Named("mapWithoutNesting")
    @Mapping(target = "takenInChargeDonors", ignore = true)
    @Mapping(target = "donations", ignore = true) // Avoid recursion here
    @Mapping(source = "status", target = "status")
    @Mapping(source = "cityId", target = "city.id")
    DonorAnonymeDTO donorAnonymeModelToDtoForList(DonorAnonyme donorAnonyme);

    @Mapping(target = "id", ignore = true) // Ignore ID if updating
    @Mapping(source = "status.id", target = "donorStatusId")
    void updateModelFromDto(DonorAnonymeDTO donorAnonymeDTO, @MappingTarget DonorAnonyme donorAnonyme);

    @Mapping(target = "donation", ignore = true)
    @Mapping(source = "productNatureId", target = "productNature.id")
    @Mapping(source = "productUnitId", target = "productUnit.id")
    DonationProductNatureDTO donationProductNatureToDonationProductNatureDTO(DonationProductNature donationProductNature);
    @Mapping(target = "donor", ignore = true)
    TakenInChargeDonorDTO takenInChargeDonorDTOToTakenInChargeDonor(TakenInChargeDonor takenInChargeDonor);

    @Mapping(target = "donor", ignore = true)
    TakenInChargeDonor takenInChargeDonorToTakenInChargeDonorDTO(TakenInChargeDonorDTO takenInChargeDonorDTO);

    @Mapping(target = "takenInChargeDonor", ignore = true)
    TakenInChargeOperationDTO takenInChargeOperationToTakenInChargeOperationDTO(TakenInChargeOperation takenInChargeOperation);

    @Mapping(target = "takenInChargeDonor", ignore = true)
    TakenInChargeOperation takenInChargeOperationDTOToTakenInChargeOperation(TakenInChargeOperationDTO takenInChargeOperationDTO);

    @Mapping(target = "takenInChargeDonors", ignore = true)
    TakenInChargeDTO takenInChargeDTOToTakenInCharge(TakenInCharge takenInCharge);

    @Mapping(target = "takenInChargeDonors", ignore = true)
    TakenInCharge takenInChargeToTakenInChargeDTO(TakenInChargeDTO takenInChargeDTO);

    NoteTakenInChargeDTO takenInChargeNoteToTakenInChargeNoteDTO(NoteTakenInCharge takenInChargeNote);
    @Mapping(target = "takenInCharge", ignore = true)
    NoteTakenInCharge takenInChargeNoteDTOToTakenInChargeNote(NoteTakenInChargeDTO takenInChargeNoteDTO);

    DocumentTakenInChargeDTO takenInChargeDocumentToTakenInChargeDocumentDTO(DocumentTakenInCharge takenInChargeDocument);

    @Mapping(target = "takenInCharge", ignore = true)
    DocumentTakenInCharge takenInChargeDocumentDTOToTakenInChargeDocument(DocumentTakenInChargeDTO takenInChargeDocumentDTO);

    @Mapping(target = "takenInCharge", ignore = true)
    @Mapping(target = "bankCards", ignore = true)
    TakenInChargeBeneficiaryDTO takenInChargeBeneficiaryDTOToTakenInChargeBeneficiary(TakenInChargeBeneficiary takenInChargeBeneficiary);

    @Mapping(target = "takenInCharge", ignore = true)
    @Mapping(target = "bankCards", ignore = true)
    TakenInChargeBeneficiary takenInChargeBeneficiaryToTakenInChargeBeneficiaryDTO(TakenInChargeBeneficiaryDTO takenInChargeBeneficiaryDTO);

    @Mapping(target = "takenInChargeBeneficiaries", ignore = true)
    @Mapping(target = "notes", ignore = true)
    @Mapping(target = "beneficiaryServices", ignore = true)
    @Mapping(target = "scholarshipBeneficiaries", ignore = true)
    @Mapping(target = "epsResidents", ignore = true)
    @Mapping(target = "diseaseTreatments", ignore = true)
    @Mapping(target = "educations", ignore = true)
    @Mapping(target = "documents", ignore = true)
    BeneficiaryDTO beneficiaryToBeneficiaryDTO(Beneficiary beneficiary);

    @Mapping(target = "beneficiary", ignore = true)
    @Mapping(target = "bankCards", ignore = true)
    @Mapping(target = "familyMember", ignore = true)
    PersonDTO personToPersonDTO(Person person);


}


