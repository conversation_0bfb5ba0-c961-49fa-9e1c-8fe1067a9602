package ma.almobadara.backend.service.communs;

import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityNotFoundException;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.Audit.AuditApplicationService;
import ma.almobadara.backend.clients.RefFeignClient;
import ma.almobadara.backend.config.Messages;
import ma.almobadara.backend.dto.administration.TagDTO;
import ma.almobadara.backend.dto.communs.DocumentAndEntityDto;
import ma.almobadara.backend.dto.communs.DocumentDTO;
import ma.almobadara.backend.dto.communs.DocumentRenewDTO;
import ma.almobadara.backend.dto.referentiel.TypeDocumentDonorDTO;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.mapper.DocumentMapper;
import ma.almobadara.backend.model.administration.Tag;
import ma.almobadara.backend.model.administration.Taggable;
import ma.almobadara.backend.model.aideComplemenatire.AideComplementaire;
import ma.almobadara.backend.model.beneficiary.Beneficiary;
import ma.almobadara.backend.model.beneficiary.DocumentBeneficiary;
import ma.almobadara.backend.model.communs.Document;
import ma.almobadara.backend.model.donation.DocumentDonation;
import ma.almobadara.backend.model.donation.Donation;
import ma.almobadara.backend.model.donor.*;
import ma.almobadara.backend.model.family.DocumentFamily;
import ma.almobadara.backend.model.family.Family;
import ma.almobadara.backend.model.family.FamilyMember;
import ma.almobadara.backend.model.takenInCharge.DocumentTakenInCharge;
import ma.almobadara.backend.model.takenInCharge.TakenInCharge;
import ma.almobadara.backend.repository.administration.TagRepository;
import ma.almobadara.backend.repository.administration.TaggableRepository;
import ma.almobadara.backend.repository.aideComplemenatire.AideComplementaireRepository;
import ma.almobadara.backend.repository.beneficiary.BeneficiaryRepository;
import ma.almobadara.backend.repository.beneficiary.DocumentBeneficiaryRepository;
import ma.almobadara.backend.repository.communs.DocumentDonorRepository;
import ma.almobadara.backend.repository.communs.DocumentRepository;
import ma.almobadara.backend.repository.donation.DocumentDonationRepository;
import ma.almobadara.backend.repository.donation.DonationRepository;
import ma.almobadara.backend.repository.donor.DonorAnonymeRepository;
import ma.almobadara.backend.repository.donor.DonorMoralRepository;
import ma.almobadara.backend.repository.donor.DonorPhysicalRepository;
import ma.almobadara.backend.repository.donor.DonorRepository;
import ma.almobadara.backend.repository.family.FamilyDocumentRepository;
import ma.almobadara.backend.repository.family.FamilyRepository;
import ma.almobadara.backend.repository.takenInCharge.DocumentTakenInChargeRepository;
import ma.almobadara.backend.repository.takenInCharge.TakenInChargeRepository;
import ma.almobadara.backend.service.ReferentialService;
import ma.almobadara.backend.service.donor.MinioService;
import ma.almobadara.backend.service.donor.TypeDocumentDonorService;
import ma.almobadara.backend.util.times.TimeWatch;
import org.apache.commons.io.FilenameUtils;
import org.jfree.util.Log;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import static ma.almobadara.backend.service.administration.CacheAdUserService.getUsernameFromJwt;
import static ma.almobadara.backend.util.constants.GlobalConstants.*;

@Service
@RequiredArgsConstructor
@Slf4j
public class DocumentService {

    private final DocumentMapper documentMapper;
    private final RefFeignClient refFeignClient;
    private final MinioService minioService;
    private final DocumentRepository documentRepository;
    private final DocumentDonorRepository documentDonorRepository;
    private final DocumentBeneficiaryRepository documentBeneficiaryRepository;
    private final FamilyDocumentRepository familyDocumentRepository;
    private final DocumentTakenInChargeRepository documentTakenInChargeRepository;
    private final DonorRepository donorRepository;
    private final DonorPhysicalRepository donorPhysicalRepository;
    private final DonorMoralRepository donorMoralRepository;
    private final BeneficiaryRepository beneficiaryRepository;
    private final DonationRepository donationRepository;
    private final DocumentDonationRepository documentDonationRepository;
    private final TakenInChargeRepository takenInChargeRepository;
    private final FamilyRepository familyRepository;
    private final AideComplementaireRepository aideComplementaireRepository;
    private final Messages messages;
    private final EntityManager entityManager;
    private final AuditApplicationService auditApplicationService;
    private final DonorAnonymeRepository donorAnonymeRepository;
    private final TaggableRepository taggableRepository;
    private final ReferentialService referentialService;
    private final TypeDocumentDonorService typeDocumentDonorService;
    private final TagRepository tagRepository;


    @Value("${minio.donorsFolder}")
    private String donorsFolder;

    @Value("${minio.beneficiariesFolder}")
    private String beneficiariesFolder;

    @Value("${minio.donationsFolder}")
    private String donationsFolder;

    @Value("${minio.takenInChargesFolder}")
    private String takenInChargesFolder;

    @Value("${minio.familiesFolder}")
    private String familiesFolder;

    @Value("${minio.campagnesFolder}")
    private String campagnesFolder;

        public DocumentDTO createDocumentAndAssignToEntity(DocumentAndEntityDto documentAndEntityDto) throws TechnicalException, IOException {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service createDocumentAndAssignToEntity {}", documentAndEntityDto);

        if (documentAndEntityDto == null) {
            throw new TechnicalException(NULL_ENTITY);
        }

        Document oldExistingDocument = null;
        Document existingDocument = new Document();

        if (documentAndEntityDto.getDocumentDTO().getId() != null) {
            oldExistingDocument = documentRepository.findById(documentAndEntityDto.getDocumentDTO().getId()).orElseThrow();
            BeanUtils.copyProperties(oldExistingDocument, existingDocument);
        }

        DocumentDTO createdDocument = addDocument(documentAndEntityDto);
        documentAndEntityDto.getDocumentDTO().setId(createdDocument.getId());
        affectDocumentToEntity(documentAndEntityDto, existingDocument);
        taggableRepository.deleteAllByTaggableIdAndTaggableType(createdDocument.getId(),"document");
        if (documentAndEntityDto.getDocumentDTO().getTags() != null) {
            documentAndEntityDto.getDocumentDTO().getTags()

                     .forEach(tagDTO -> {
                            Tag tag = tagRepository.findById(tagDTO.getId()).orElseThrow(() -> new EntityNotFoundException("Tag not found with ID: " + tagDTO.getId()));
                         Taggable taggable = new Taggable();
                         taggable.setTag(tag);
                         taggable.setTaggableType("document");
                         taggable.setTaggableId(createdDocument.getId());
                         taggableRepository.save(taggable);
                    });
        }
        log.debug("End service createDocumentAndAssignToEntity with name {}, took {}", createdDocument.getFileName(), watch.toMS());
        return createdDocument;
    }

    public DocumentDTO addDocument(DocumentAndEntityDto documentAndEntityDto) throws TechnicalException, IOException {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service addDocument {}", documentAndEntityDto);
        DocumentDTO documentDTO = documentAndEntityDto.getDocumentDTO();

        String folderPath;
        String subFolderName;
        String folderName;
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.BASIC_ISO_DATE;
        if (documentDTO.getType() == null) {
            if (documentDTO.getDocumentDate() == null) {
                documentDTO.setDocumentDate(Date.from(LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant()));
            }
            folderName = "NormalDocument" + documentAndEntityDto.getEntityType() + "_" + documentDTO.getDocumentDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(dateTimeFormatter);
        } else {
            if(documentDTO.getId() == null) {
            documentDTO.setCode(generatePieceJointCodeBeneficiary(documentDTO.getType().getId(), documentAndEntityDto.getEntityId()));
            }

            folderName = getFolderName(documentDTO.getType().getId(), documentAndEntityDto.getEntityType());
        }
        folderPath = getFolderPath(documentAndEntityDto.getEntityType());

        subFolderName = getSubFolderName(documentAndEntityDto);

        String uniqueId = UUID.randomUUID().toString().substring(0, 4);
        String fileExtension = null;
        if (documentDTO.getFile() != null) {
            fileExtension = FilenameUtils.getExtension(documentDTO.getFile().getOriginalFilename());
            String fileName = "";
            if (documentDTO.getType() == null) {
                fileName = uniqueId + "_" + "Document" + "_" +
                        documentDTO.getDocumentDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(dateTimeFormatter) +
                        "." + fileExtension;
            } else {
                if (documentDTO.getDocumentDate() == null) {
                    documentDTO.setDocumentDate(Date.from(LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant()));
                }
                fileName = uniqueId + "_" + refFeignClient.getConsTypeDonorDocument(documentDTO.getType().getId()).getName().replace(" ", "-") + "_" +
                        documentDTO.getDocumentDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(dateTimeFormatter) +
                        "." + fileExtension;
            }

            minioService.WriteToMinIO(documentDTO.getFile(), folderPath  + subFolderName + "/" + folderName + "/", fileName);
            documentDTO.setFileUrl(folderPath  + subFolderName + "/" + folderName + "/" + fileName);
            documentDTO.setFileName(fileName);

        }
        Document document = documentMapper.documentToModelToModel(documentDTO);

        Document newDocument = documentRepository.save(document);

        // Tags will be handled in createDocumentAndAssignToEntity method

        DocumentDTO newDocumentDTO = documentMapper.documentModelToDto(newDocument);

        if (documentDTO.getFile() != null && documentDTO.getFile().getBytes().length > 0) {
            String base64 = Arrays.toString(minioService.ReadFromMinIO(newDocument.getFileUrl(),null));
            newDocumentDTO.setFile64(base64);
        }

        if (newDocumentDTO.getType().getId() != null) {
            TypeDocumentDonorDTO typeDocumentDonorDTO = refFeignClient.getConsTypeDonorDocument(newDocumentDTO.getType().getId());
            newDocumentDTO.setType(typeDocumentDonorDTO);
        }

        log.debug("End service addDocument with name {}, took {}", newDocumentDTO.getFileName(), watch.toMS());
        return newDocumentDTO;
    }

    public String generatePieceJointCodeBeneficiary(Long idTypeDocument, Long idBeneficiary) {
        Optional<Beneficiary> beneficiary = beneficiaryRepository.findById(idBeneficiary);
        if (beneficiary.isEmpty()) {
            return null;
        }
        String code = "P";

        // Récupération de l'année seulement
        DocumentDTO documentDTO = new DocumentDTO();
        documentDTO.setDocumentDate(new Date());
        // documentDTO.setType(refFeignClient.getConsTypeDonorDocument(idTypeDocument));

        // Ajouter l'année
        code += documentDTO.getDocumentDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().getYear();

        // Ajouter les deux premières lettres du type de document (ex: AU)
        //  code += documentDTO.getType().getName().substring(0, 2).toUpperCase();

        // Formater le nombre total de documents avec 4 chiffres
        long totalDocumentCount = documentRepository.count();
        code += String.format("%04d", totalDocumentCount);

        // Ajouter "_V" suivi du nombre de documents pour ce bénéficiaire et ce type (format 3 chiffres)
        long documentCountForBeneficiary = documentBeneficiaryRepository.countByBeneficiaryIdAndTypeDocumentId(idBeneficiary, idTypeDocument);
        //code += "_V" + String.format("%03d", documentCountForBeneficiary);

        return code;
    }


    private String getFolderPath(String entityType) throws TechnicalException {
        return switch (entityType.toLowerCase()) {
            case DONOR -> donorsFolder;
            case BENEFICIARY -> beneficiariesFolder;
            case DONATION -> donationsFolder;
            case TAKENINCHARGE -> takenInChargesFolder;
            case FAMILY -> familiesFolder;
            case CAMPAGNE -> campagnesFolder;
            default -> throw new TechnicalException(messages.get(ENTITY_TYPE_NOT_FOUND));
        };
    }


    private String getSubFolderName(DocumentAndEntityDto documentAndEntityDto) throws TechnicalException {
        switch (documentAndEntityDto.getEntityType().toLowerCase()) {
            case DONOR: {
                Donor donor = donorRepository.findById(documentAndEntityDto.getEntityId())
                        .orElseThrow(() -> new EntityNotFoundException("Donor not found with ID: " + documentAndEntityDto.getEntityId()));
                Optional<DonorPhysical> donorPhysical = donorPhysicalRepository.findById(donor.getId());
                Optional<DonorMoral> donorMoral = donorMoralRepository.findById(donor.getId());
                Optional<DonorAnonyme> donorAnonyme = donorAnonymeRepository.findById(donor.getId());
                if (donorPhysical.isPresent()) {
                    return donorPhysical.get().getLastName().toUpperCase() + '-' + donorPhysical.get().getFirstName().toUpperCase() + '_' + donorPhysical.get().getCode();
                } else if (donorMoral.isPresent()) {
                    return donorMoral.get().getCompany().toUpperCase() + '_' + donorMoral.get().getCode();
                } else if (donorAnonyme.isPresent()) {
                    return donorAnonyme.get().getName().toUpperCase() + '_' + donorAnonyme.get().getCode();
                }
            }
            case BENEFICIARY: {
                Beneficiary beneficiary = beneficiaryRepository.findById(documentAndEntityDto.getEntityId())
                        .orElseThrow(() -> new EntityNotFoundException("Beneficiary not found with ID: " + documentAndEntityDto.getEntityId()));
                return beneficiary.getPerson().getLastName().toUpperCase() + '-' + beneficiary.getPerson().getFirstName().toUpperCase() + '_' + beneficiary.getCode();
            }
            case DONATION: {
                Donation donation = donationRepository.findById(documentAndEntityDto.getEntityId())
                        .orElseThrow(() -> new EntityNotFoundException("Donation not found with ID: " + documentAndEntityDto.getEntityId()));
                return donation.getCode();
            }
            case TAKENINCHARGE: {
                TakenInCharge takenInCharge = takenInChargeRepository.findById(documentAndEntityDto.getEntityId())
                        .orElseThrow(() -> new EntityNotFoundException("TakenInCharge not found with ID: " + documentAndEntityDto.getEntityId()));
                return takenInCharge.getCode();
            }
            case FAMILY: {
                Family family = familyRepository.findById(documentAndEntityDto.getEntityId())
                        .orElseThrow(() -> new EntityNotFoundException("Family not found with ID: " + documentAndEntityDto.getEntityId()));
                return family.getCode();
            }
            case CAMPAGNE: {
                AideComplementaire aideComplementaire = aideComplementaireRepository.findById(documentAndEntityDto.getEntityId())
                        .orElseThrow(() -> new EntityNotFoundException("aideComplementaire not found with ID: " + documentAndEntityDto.getEntityId()));
                return aideComplementaire.getCode();
            }
            default:
                throw new TechnicalException(messages.get(ENTITY_TYPE_NOT_FOUND));
        }
    }

    private String getFolderName(Long typeId, String entityType) throws TechnicalException {
        return switch (entityType.toLowerCase()) {
            case DONOR, DONATION, BENEFICIARY, TAKENINCHARGE, FAMILY ->
                    refFeignClient.getConsTypeDonorDocument(typeId).getFolderName();
            default -> throw new TechnicalException(messages.get(ENTITY_TYPE_NOT_FOUND));
        };
    }

//    public List<DocumentRenewDTO> getToRenewDocumentsBackup(){
//        List<Document> documentList = documentRepository.findAll();
//        List<DocumentRenewDTO> documentRenewDTOList = new ArrayList<>();
//
//        //Remove docs where date is null or greater than one month
//        documentList.removeIf(item -> item.getExpiryDate() == null);
//        Date currentDate = new Date();
//        LocalDate currentDateTwo = LocalDate.now();
//        LocalDate oneMonthFromNow = currentDateTwo.plusMonths(1);
//        Date futureDate = Date.from(oneMonthFromNow.atStartOfDay(ZoneId.systemDefault()).toInstant());
//        documentList.removeIf(item ->
//                        item.getExpiryDate().after(futureDate)
//        );
//
//        //Create DTO to send to FrontEnd
//        currentDate.toInstant();
//        if(!documentList.isEmpty()){
//            for (Document document:documentList){
//                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
//                DocumentRenewDTO documentRenewDTO = new DocumentRenewDTO(null,document.getCode(),document.getLabel(),(document.getTypeDocumentId()!=null ?document.getTypeDocumentId().toString():null),document.getExpiryDate(),null);
//                String formattedDate = formatter.format(document.getExpiryDate());
//                List<DocumentBeneficiary> documentBeneficiary = documentBeneficiaryRepository.findByDocumentId(document.getId());
//                List<DocumentFamily> documentFamilies = familyDocumentRepository.findByDocumentId(document.getId());
//                if (!documentBeneficiary.isEmpty()){
//                    documentRenewDTO.setModule(BENEFICIARY);
//
//                }else if (!documentFamilies.isEmpty()){
//                    documentRenewDTO.setModule(FAMILY);
//                }else{
//                    documentRenewDTO = null;
//                    Log.debug("not in benef or family");
//                }
//                if (documentRenewDTO!=null) documentRenewDTO.setExpiryDateString(formattedDate);
//                documentRenewDTOList.add(documentRenewDTO);
//                documentRenewDTOList.removeIf(Objects::isNull);
//            }
//        }
//
//        Log.debug(documentRenewDTOList.toString());
//
//        return documentRenewDTOList;
//    }

    public Page<DocumentRenewDTO> getToRenewDocuments(int page, int size, Long zoneId, String searchByName, String searchByModule, String searchByType, String searchByExpiryDate) {
        List<Document> documentList = documentRepository.findAll();
        List<DocumentRenewDTO> documentRenewDTOList = new ArrayList<>();

        // Parse searchByExpiryDate into a Date object
        Date expiryDateFilter = null;
        if (searchByExpiryDate != null) {
            try {
                expiryDateFilter = new SimpleDateFormat("yyyy-MM-dd").parse(searchByExpiryDate);
            } catch (ParseException e) {
                throw new IllegalArgumentException("Invalid date format for searchByExpiryDate. Expected format: YYYY-MM-DD", e);
            }
        }

        // Remove documents where expiry date is null or greater than one month from now
        documentList.removeIf(item -> item.getExpiryDate() == null);
        Date currentDate = new Date();
        LocalDate currentDateTwo = LocalDate.now();
        LocalDate oneMonthFromNow = currentDateTwo.plusMonths(1);
        Date futureDate = Date.from(oneMonthFromNow.atStartOfDay(ZoneId.systemDefault()).toInstant());
        documentList.removeIf(item -> item.getExpiryDate().after(futureDate));

        // Filter documents based on searchByExpiryDate
        if (expiryDateFilter != null) {
            Date finalExpiryDateFilter = expiryDateFilter;
            documentList.removeIf(item -> item.getExpiryDate().after(finalExpiryDateFilter));
        }

        // Create DTOs for the remaining documents
        if (!documentList.isEmpty()) {
            for (Document document : documentList) {
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
                DocumentRenewDTO documentRenewDTO = new DocumentRenewDTO(document.getId(), null, null, null, null, document.getLabel(), null, document.getExpiryDate(), null, null);
                String formattedDate = formatter.format(document.getExpiryDate());
                List<DocumentBeneficiary> documentBeneficiary = documentBeneficiaryRepository.findByDocumentId(document.getId());
                List<DocumentFamily> documentFamilies = familyDocumentRepository.findByDocumentId(document.getId());
                if (!documentBeneficiary.isEmpty()) {
                    if (documentBeneficiary.get(0).getBeneficiary() != null) {
                        if (!documentBeneficiary.get(0).getBeneficiary().getArchived()){
                            if (documentBeneficiary.get(0).getBeneficiary().getBeneficiaryStatut().getId() == 6 ||
                                    documentBeneficiary.get(0).getBeneficiary().getBeneficiaryStatut().getId() == 10||
                                    documentBeneficiary.get(0).getBeneficiary().getBeneficiaryStatut().getId() == 13) {
                                documentRenewDTO.setCodeEntity(documentBeneficiary.get(0).getBeneficiary().getCode());
                                if (documentRenewDTO.getZoneId() != null)
                                    documentRenewDTO.setZoneId(documentBeneficiary.get(0).getBeneficiary().getZone().getId());
                                String entityName = "";
                                if (documentBeneficiary.get(0).getBeneficiary().getPerson() != null) {
                                    entityName = documentBeneficiary.get(0).getBeneficiary().getPerson().getFirstName() + " " + documentBeneficiary.get(0).getBeneficiary().getPerson().getLastName();
                                }
                                documentRenewDTO.setNomEntity(entityName);
                                documentRenewDTO.setIdEntity(documentBeneficiary.get(0).getBeneficiary().getId());
                                if (document.getTypeDocumentId() != null) {
                                    TypeDocumentDonorDTO beneficiaryDocumentTypeDTO = refFeignClient.getConsTypeDonorDocument(document.getTypeDocumentId());
                                    documentRenewDTO.setTypeDocument(beneficiaryDocumentTypeDTO.getName());
                                } else {
                                    documentRenewDTO.setTypeDocument("Normal");
                                }
                                documentRenewDTO.setModule("Beneficiaire");
                            }else{
                                documentRenewDTO = null;
                            }
                        }else{
                            documentRenewDTO = null;
                        }
                    }

                } else if (!documentFamilies.isEmpty()) {
                    if (documentFamilies.get(0).getFamily() != null && !documentFamilies.get(0).getFamily().getFamilyMembers().isEmpty()) {
                        documentRenewDTO.setCodeEntity(documentFamilies.get(0).getFamily().getCode());
                        documentRenewDTO.setZoneId(documentFamilies.get(0).getFamily().getZone().getId());
                        List<FamilyMember> familyMembers = documentFamilies.get(0).getFamily().getFamilyMembers();
                        familyMembers.removeIf(items -> !items.isTutor());
                        documentRenewDTO.setNomEntity(familyMembers.get(0).getPerson().getLastName());
                        documentRenewDTO.setIdEntity(documentFamilies.get(0).getFamily().getId());
                        if (document.getTypeDocumentId() !=null){
                            TypeDocumentDonorDTO familyDocumentTypeDTO = refFeignClient.getConsTypeDonorDocument(document.getTypeDocumentId());
                            documentRenewDTO.setTypeDocument(familyDocumentTypeDTO.getName());
                        }else{
                            documentRenewDTO.setTypeDocument("Normal");
                        }

                    }
                    documentRenewDTO.setModule("Famille");
                } else {
                    documentRenewDTO = null;
                    Log.debug("not in benef or family");
                }
                if (documentRenewDTO != null) documentRenewDTO.setExpiryDateString(formattedDate);
                documentRenewDTOList.add(documentRenewDTO);
                documentRenewDTOList.removeIf(Objects::isNull);
                if (zoneId != 0) {
                    documentRenewDTOList.removeIf(item -> !Objects.equals(item.getZoneId(), zoneId));
                }
            }
        }

        if (searchByName != null) {
            documentRenewDTOList.removeIf(item -> !item.getNomEntity().toLowerCase().contains(searchByName.toLowerCase()));
        }
        if (searchByType != null) {
            documentRenewDTOList.removeIf(item -> !item.getTypeDocument().toLowerCase().contains(searchByType.toLowerCase()));
        }
        if (searchByModule != null) {
            documentRenewDTOList.removeIf(item -> !Objects.equals(item.getModule().toLowerCase(), searchByModule.toLowerCase()));
        }
        Log.debug(documentRenewDTOList.toString());

        Pageable pageable = PageRequest.of(page, size);

        // Convert list to pageable
        return toPage(documentRenewDTOList, pageable);
    }
    private Page<Document> filterDocuments(String searchByName, String searchByNameAr, String searchByStatus, String searchByCity, Pageable pageable) {
        log.debug("Start service filterEps with searchByName: {}, searchByNameAr: {}, searchByStatus: {}",
                searchByName, searchByNameAr, searchByStatus);

        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Document> criteriaQuery = criteriaBuilder.createQuery(Document.class);
        Root<Document> rootDocument = criteriaQuery.from(Document.class);
        Predicate predicate = buildPredicate(criteriaBuilder, rootDocument, criteriaQuery, searchByName, searchByNameAr, searchByStatus, searchByCity);
        criteriaQuery.where(predicate);

        TypedQuery<Document> typedQuery = entityManager.createQuery(criteriaQuery);
        long totalCount = typedQuery.getResultList().size();
        typedQuery.setFirstResult((int) pageable.getOffset());
        typedQuery.setMaxResults(pageable.getPageSize());
        List<Document> resultList = typedQuery.getResultList();
        log.debug("End service filterZones with {} zones found", totalCount);
        return new PageImpl<>(resultList, pageable, totalCount);
    }

    private Predicate buildPredicate(CriteriaBuilder criteriaBuilder, Root<Document> root, CriteriaQuery<?> criteriaQuery,
                                     String searchByName, String searchByNameAr, String searchByStatus, String searchByCity) {
        Predicate predicate = criteriaBuilder.conjunction();

        if (searchByName != null) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(criteriaBuilder.lower(root.get("name")),
                    "%" + searchByName.toLowerCase() + "%"));
        }

        if (searchByNameAr != null) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(criteriaBuilder.lower(root.get("nameAr")),
                    "%" + searchByNameAr.toLowerCase() + "%"));
        }

        if (searchByStatus != null) {
            // the actif  is just have active ( the zone that have a assistant)
            log.info("searchByStatus value : " + searchByStatus);
            if (searchByStatus.equals("Actif")) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.isTrue(root.get("status")));
            } else if (searchByStatus.equals("Inactif")) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.isFalse(root.get("status")));
            }
        }

        if (searchByCity != null) {

            predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(criteriaBuilder.lower(root.get("cityIds")),
                    "%" + searchByCity + "%"));
        }


        return predicate;
    }

    public static <T> Page<T> toPage(List<T> list, Pageable pageable) {
        int start = (int) pageable.getOffset();
        int end = Math.min(start + pageable.getPageSize(), list.size());

        List<T> sublist = (start >= list.size()) ? new ArrayList<>() : list.subList(start, end);
        return new PageImpl<>(sublist, pageable, list.size());
    }

    public void affectDocumentToEntity(DocumentAndEntityDto documentAndEntityDto, Document existingDocument) throws TechnicalException {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service affectDocumentToEntity with document ID  {}, and entity ID {}",
                documentAndEntityDto.getDocumentDTO().getId(), documentAndEntityDto.getEntityId());

        switch (documentAndEntityDto.getEntityType().toLowerCase()) {
            case DONOR -> {
                affectDocumentToDonor(documentAndEntityDto);
                Document findDocument = documentRepository.findById(documentAndEntityDto.getDocumentDTO().getId()).orElseThrow(() -> new TechnicalException("Document not found"));
                TypeDocumentDonorDTO typeDocumentDonorDTO = refFeignClient.getConsTypeDonorDocument(findDocument.getTypeDocumentId());
                String documentName = typeDocumentDonorDTO.getName();
                String entityCode = "";
                if (existingDocument != null && existingDocument.getId() != null) {
                    TypeDocumentDonorDTO typeDocumentDonorDTO2 = refFeignClient.getConsTypeDonorDocument(existingDocument.getTypeDocumentId());
                    String documentName2 = typeDocumentDonorDTO2.getName();
                    if (!documentDonorRepository.findByDocumentId(existingDocument.getId()).isEmpty()) entityCode = documentDonorRepository.findByDocumentId(existingDocument.getId()).get(0).getDonor().getCode();

                    auditApplicationService.audit("Modification document pour donateur : "+entityCode, getUsernameFromJwt(), "Modification du document pour Donation",
                            existingDocument.toAuditString(documentName2), findDocument.toAuditString(documentName), DONATEUR, UPDATE);
                } else {
                        if (!documentDonorRepository.findByDocumentId(findDocument.getId()).isEmpty())
                            entityCode = documentDonorRepository.findByDocumentId(findDocument.getId()).get(0).getDonor().getCode();
                        auditApplicationService.audit("Ajout document pour donateur : "+entityCode, getUsernameFromJwt(), "Ajout du document pour Donation",
                                null, findDocument.toAuditString(documentName), DONATEUR, CREATE);
                    }

            }
            case BENEFICIARY -> {
                affectDocumentToBeneficiary(documentAndEntityDto);
                Document findDocument = documentRepository.findById(documentAndEntityDto.getDocumentDTO().getId()).orElseThrow(() -> new TechnicalException("Document not found"));
                TypeDocumentDonorDTO typeDocumentDonorDTO = null;
                String documentName;

                if (findDocument.getTypeDocumentId() != null) {
                    typeDocumentDonorDTO = refFeignClient.getConsTypeDonorDocument(findDocument.getTypeDocumentId());
                    documentName = typeDocumentDonorDTO.getName();
                } else {
                    documentName = "NormalDocument";
                }
                String entityCode = "";
                if (existingDocument != null && existingDocument.getId() != null) {
                    TypeDocumentDonorDTO typeDocumentDonorDTO2 = null;
                    String documentName2 ;
                    if (!documentBeneficiaryRepository.findByDocumentId(existingDocument.getId()).isEmpty()) entityCode = documentBeneficiaryRepository.findByDocumentId(existingDocument.getId()).get(0).getBeneficiary().getCode();
                    if (existingDocument.getTypeDocumentId() != null) {
                        typeDocumentDonorDTO2 = refFeignClient.getConsTypeDonorDocument(existingDocument.getTypeDocumentId());
                        documentName2 = typeDocumentDonorDTO2.getName();
                    } else {
                        documentName2 = "Autre Document";
                    }

                    auditApplicationService.audit("Modification document pour beneficiare :"+entityCode, getUsernameFromJwt(), "Modification du document pour Donation",
                            existingDocument.toAuditString(documentName2), findDocument.toAuditString(documentName), BENEFICIAIRE, UPDATE);
                } else {

                    if (!documentBeneficiaryRepository.findByDocumentId(findDocument.getId()).isEmpty()) entityCode = documentBeneficiaryRepository.findByDocumentId(findDocument.getId()).get(0).getBeneficiary().getCode();
                    auditApplicationService.audit("Ajout document pour beneficiare : "+entityCode, getUsernameFromJwt(), "Ajout du document pour Donation",
                            null, findDocument.toAuditString(documentName), BENEFICIAIRE, CREATE);
                }
            }
            case DONATION -> {
                affectDocumentToDonation(documentAndEntityDto);
                Document findDocument = documentRepository.findById(documentAndEntityDto.getDocumentDTO().getId()).orElseThrow(() -> new TechnicalException("Document not found"));
                TypeDocumentDonorDTO typeDocumentDonorDTO = refFeignClient.getConsTypeDonorDocument(findDocument.getTypeDocumentId());
                String documentName = typeDocumentDonorDTO.getName();
                String entityCode = "";
                if (existingDocument != null && existingDocument.getId() != null) {
                    if (!documentDonationRepository.findByDocumentId(existingDocument.getId()).isEmpty()) entityCode = documentDonationRepository.findByDocumentId(existingDocument.getId()).get(0).getDonation().getCode();
                    TypeDocumentDonorDTO typeDocumentDonorDTO2 = refFeignClient.getConsTypeDonorDocument(existingDocument.getTypeDocumentId());
                    String documentName2 = typeDocumentDonorDTO2.getName();
                    auditApplicationService.audit("Modification document pour donation : "+entityCode, getUsernameFromJwt(), "Modification du document pour Donation",
                            existingDocument.toAuditString(documentName2), findDocument.toAuditString(documentName), DONATION2, UPDATE);
                } else {
                    if (!documentDonationRepository.findByDocumentId(findDocument.getId()).isEmpty()) entityCode = documentDonationRepository.findByDocumentId(findDocument.getId()).get(0).getDonation().getCode();
                    auditApplicationService.audit("Ajout document pour donation : "+entityCode, getUsernameFromJwt(), "Ajout du document pour Donation",
                            null, findDocument.toAuditString(documentName), DONATION2, CREATE);
                }
            }
            case TAKENINCHARGE -> {
                affectDocumentToTakenInCharge(documentAndEntityDto);
                Document findDocument = documentRepository.findById(documentAndEntityDto.getDocumentDTO().getId()).orElseThrow(() -> new TechnicalException("Document not found"));
                TypeDocumentDonorDTO typeDocumentDonorDTO = refFeignClient.getConsTypeDonorDocument(findDocument.getTypeDocumentId());
                String documentName = typeDocumentDonorDTO.getName();
                String entityCode = "";
                if (existingDocument != null && existingDocument.getId() != null) {
                    TypeDocumentDonorDTO typeDocumentDonorDTO2 = refFeignClient.getConsTypeDonorDocument(existingDocument.getTypeDocumentId());
                    String documentName2 = typeDocumentDonorDTO2.getName();
                    if (!documentTakenInChargeRepository.findByDocumentId(existingDocument.getId()).isEmpty()) entityCode = documentTakenInChargeRepository.findByDocumentId(existingDocument.getId()).get(0).getTakenInCharge().getCode();

                    auditApplicationService.audit("Modification document pour take in charge : "+entityCode, getUsernameFromJwt(), "Modification du document pour Donation",
                            existingDocument.toAuditString(documentName2), findDocument.toAuditString(documentName), TAKENINCHARGE, UPDATE);
                } else {
                    if (!documentTakenInChargeRepository.findByDocumentId(findDocument.getId()).isEmpty()) entityCode = documentTakenInChargeRepository.findByDocumentId(findDocument.getId()).get(0).getTakenInCharge().getCode();
                    auditApplicationService.audit("Ajout document pour take in charge : "+entityCode, getUsernameFromJwt(), "Ajout du document pour Donation",
                            null, findDocument.toAuditString(documentName), TAKENINCHARGE, CREATE);
                }
            }
            case FAMILY -> {
                affectDocumentToFamily(documentAndEntityDto);
                Document findDocument = documentRepository.findById(documentAndEntityDto.getDocumentDTO().getId()).orElseThrow(() -> new TechnicalException("Document not found"));
                TypeDocumentDonorDTO typeDocumentDonorDTO = null;
                String documentName;
                if (findDocument.getTypeDocumentId() != null) {
                    typeDocumentDonorDTO = refFeignClient.getConsTypeDonorDocument(findDocument.getTypeDocumentId());
                    documentName = typeDocumentDonorDTO.getName();
                } else {
                    documentName = "NormalDocument";
                }
                String entityCode = "";
                if (existingDocument != null && existingDocument.getId() != null) {
                    String documentName2;
                    if (findDocument.getTypeDocumentId() != null) {
                    TypeDocumentDonorDTO typeDocumentDonorDTO2 = refFeignClient.getConsTypeDonorDocument(existingDocument.getTypeDocumentId());
                        documentName2 = typeDocumentDonorDTO2.getName();
                    } else {
                        documentName2 = "NormalDocument";
                    }
                    if (!familyDocumentRepository.findByDocumentId(existingDocument.getId()).isEmpty()) entityCode = familyDocumentRepository.findByDocumentId(existingDocument.getId()).get(0).getFamily().getCode();

                    auditApplicationService.audit("Modification document pour famille : " +entityCode, getUsernameFromJwt(), "Modification du document pour Donation",
                            existingDocument.toAuditString(documentName2), findDocument.toAuditString(documentName), FAMILLE, UPDATE);
                } else {
                    if (!familyDocumentRepository.findByDocumentId(findDocument.getId()).isEmpty()) entityCode = familyDocumentRepository.findByDocumentId(findDocument.getId()).get(0).getFamily().getCode();

                    auditApplicationService.audit("Ajout document pour famille : "+entityCode, getUsernameFromJwt(), "Ajout du document pour Donation",
                            null, findDocument.toAuditString(documentName), FAMILLE, CREATE);
                }
            }
            case CAMPAGNE -> {

            }
            default -> throw new TechnicalException(messages.get(ENTITY_TYPE_NOT_FOUND));
        }
        log.debug("End service affectDocumentToEntity {}, took {}", documentAndEntityDto.getEntityType(), watch.toMS());
    }

    private void affectDocumentToDonor(DocumentAndEntityDto documentAndEntityDto) {
        Donor donorEntity = donorRepository.findById(documentAndEntityDto.getEntityId())
                .orElseThrow(() -> new EntityNotFoundException("Donor not found with ID: " + documentAndEntityDto.getEntityId()));
        DocumentDTO documentDTO = documentAndEntityDto.getDocumentDTO();
        Document documentEntity = documentRepository.findById(documentDTO.getId())
                .orElseThrow(() -> new EntityNotFoundException("Document not found with ID: " + documentDTO.getId()));

        DocumentDonor documentDonorEntity = documentDonorRepository.findByDonorIdAndDocumentId(
                        donorEntity.getId(), documentEntity.getId())
                .orElse(new DocumentDonor(donorEntity, documentEntity));

        documentDonorRepository.save(documentDonorEntity);
    }

    private void affectDocumentToBeneficiary(DocumentAndEntityDto documentAndEntityDto) {
        Beneficiary beneficiaryEntity = beneficiaryRepository.findById(documentAndEntityDto.getEntityId())
                .orElseThrow(() -> new EntityNotFoundException("Beneficiary not found with ID: " + documentAndEntityDto.getEntityId()));

        DocumentDTO documentDTO = documentAndEntityDto.getDocumentDTO();
        Document documentEntity = documentRepository.findById(documentDTO.getId())
                .orElseThrow(() -> new EntityNotFoundException("Document not found with ID: " + documentDTO.getId()));

        DocumentBeneficiary documentBeneficiaryEntity = documentBeneficiaryRepository.findByBeneficiaryIdAndDocumentId(
                        beneficiaryEntity.getId(), documentEntity.getId())
                .orElse(new DocumentBeneficiary(documentEntity, beneficiaryEntity));

        documentBeneficiaryRepository.save(documentBeneficiaryEntity);
    }

    private void affectDocumentToFamily(DocumentAndEntityDto documentAndEntityDto) {
        Family familyEntity = familyRepository.findById(documentAndEntityDto.getEntityId())
                .orElseThrow(() -> new EntityNotFoundException("Family not found with ID: " + documentAndEntityDto.getEntityId()));

        DocumentDTO documentDTO = documentAndEntityDto.getDocumentDTO();
        Document documentEntity = documentRepository.findById(documentDTO.getId())
                .orElseThrow(() -> new EntityNotFoundException("Document not found with ID: " + documentDTO.getId()));

        DocumentFamily documentFamilyEntity = familyDocumentRepository.findByFamilyIdAndDocumentId(
                        familyEntity.getId(), documentEntity.getId())
                .orElse(new DocumentFamily(familyEntity, documentEntity));

        familyDocumentRepository.save(documentFamilyEntity);
    }

    private void affectDocumentToDonation(DocumentAndEntityDto documentAndEntityDto) {
        Donation donationEntity = donationRepository.findById(documentAndEntityDto.getEntityId())
                .orElseThrow(() -> new EntityNotFoundException("Donation not found with ID: " + documentAndEntityDto.getEntityId()));

        DocumentDTO documentDTO = documentAndEntityDto.getDocumentDTO();
        Document documentEntity = documentRepository.findById(documentDTO.getId())
                .orElseThrow(() -> new EntityNotFoundException("Document not found with ID: " + documentDTO.getId()));

        DocumentDonation documentDonationEntity = documentDonationRepository.findByDonationIdAndDocumentId(
                        donationEntity.getId(), documentEntity.getId())
                .orElse(new DocumentDonation(donationEntity, documentEntity));

        documentDonationRepository.save(documentDonationEntity);
    }

    private void affectDocumentToTakenInCharge(DocumentAndEntityDto documentAndEntityDto) {
        TakenInCharge takenInChargeEntity = takenInChargeRepository.findById(documentAndEntityDto.getEntityId())
                .orElseThrow(() -> new EntityNotFoundException("Donor not found with ID: " + documentAndEntityDto.getEntityId()));

        DocumentDTO documentDTO = documentAndEntityDto.getDocumentDTO();
        Document documentEntity = documentRepository.findById(documentDTO.getId())
                .orElseThrow(() -> new EntityNotFoundException("Document not found with ID: " + documentDTO.getId()));

        DocumentTakenInCharge documentTakenInChargeEntity = documentTakenInChargeRepository.findByTakenInChargeIdAndDocumentId(
                        takenInChargeEntity.getId(), documentEntity.getId())
                .orElse(new DocumentTakenInCharge(takenInChargeEntity, documentEntity));

        documentTakenInChargeRepository.save(documentTakenInChargeEntity);
    }


    public List<DocumentDTO> getAllDocuments() {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service getAllDocuments");
        Iterable<Document> documents = documentRepository.findAll();
        Iterable<DocumentDTO> documentDTOs = documentMapper.documentListModelToDto(documents);
        List<DocumentDTO> documentList = StreamSupport.stream(documentDTOs.spliterator(), false)
                .collect(Collectors.toList());

        // We don't add tags here as we don't know the entity type for each document
        // Tags will be added when retrieving documents by entity

        auditApplicationService.audit("Consultation du liste des documents", getUsernameFromJwt(), "Liste des documents",
                null, null, DOCUMENT, CONSULTATION);
        log.debug("End service getAllDocuments {} documents found, took {}", documentList.size(), watch.toMS());
        return documentList;
    }

    public List<DocumentDTO> getAllDocumentsByEntity(String entityType, Long entityId) {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service getAllDocumentsByEntity {} and ID {} ", entityType, entityId);
        List<DocumentDTO> documentDTOList = switch (entityType.toLowerCase()) {
            case DONOR -> getAllDocumentsByDonor(entityId);
            case BENEFICIARY -> getAllDocumentsByBeneficiary(entityId);
            case DONATION -> getAllDocumentsByDonation(entityId);
            case TAKENINCHARGE -> getAllDocumentsByTakenInCharge(entityId);
            case FAMILY -> getAllDocumentsByFamily(entityId);
            default -> throw new IllegalArgumentException("Unsupported entity type: " + entityType);
        };
        log.debug("End service getAllDocumentsByEntity for type {} and it has {} documents, took {}", entityType, documentDTOList.size(), watch.toMS());
        return documentDTOList;
    }

    private List<DocumentDTO> getAllDocumentsByBeneficiary(Long beneficiaryId) {
        List<DocumentBeneficiary> documentBeneficiaryList = documentBeneficiaryRepository.findByBeneficiaryId(beneficiaryId);
        List<DocumentDTO> documentDTOList = new ArrayList<>();

        Optional<Beneficiary> beneficiaryTemp = beneficiaryRepository.findById(beneficiaryId);
        Beneficiary beneficiary = beneficiaryTemp.orElse(null);

        for (DocumentBeneficiary documentBeneficiary : documentBeneficiaryList) {
            Document documentEntity = documentBeneficiary.getDocument();

            TypeDocumentDonorDTO typeDocumentDonorDTO = new TypeDocumentDonorDTO();
            typeDocumentDonorDTO.setId(null);
            if (documentEntity.getTypeDocumentId() != null) {
                typeDocumentDonorDTO = refFeignClient.getConsTypeDonorDocument(documentBeneficiary.getDocument().getTypeDocumentId());
            }
            DocumentDTO documentDTO = documentMapper.documentModelToDto(documentEntity);
            documentDTO.setType(typeDocumentDonorDTO);

            // Add tags to the document DTO
            addTagsToDocumentDTO(documentDTO, "document");

            documentDTOList.add(documentDTO);

        }
        assert beneficiary != null;
        auditApplicationService.audit("Consultation des documents dans beneficiaire : "+beneficiary.getCode(), getUsernameFromJwt(), "Liste des documents pour bénéficiaire",
                null, null, BENEFICIAIRE, CONSULTATION);
        return documentDTOList;
    }

    private List<DocumentDTO> getAllDocumentsByFamily(Long familyId) {
        List<DocumentFamily> documentFamilyList = familyDocumentRepository.findByFamilyId(familyId);
        List<DocumentDTO> documentDTOList = new ArrayList<>();
        Optional<Family> familyTemp = familyRepository.findById(familyId);
        Family family = familyTemp.orElse(null);

        for (DocumentFamily documentFamily : documentFamilyList) {
            Document documentEntity = documentFamily.getDocument();

                TypeDocumentDonorDTO typeDocumentDonorDTO = new TypeDocumentDonorDTO();
                typeDocumentDonorDTO.setId(null);
                if (documentEntity.getTypeDocumentId() != null) {
                    typeDocumentDonorDTO = refFeignClient.getConsTypeDonorDocument(documentEntity.getTypeDocumentId());
                }
                DocumentDTO documentDTO = documentMapper.documentModelToDto(documentEntity);
                documentDTO.setType(typeDocumentDonorDTO);

                // Add tags to the document DTO
                addTagsToDocumentDTO(documentDTO, "document");

                documentDTOList.add(documentDTO);

        }
        assert family != null;
        auditApplicationService.audit("Consultation des documents dans famille : "+family.getCode(), getUsernameFromJwt(), "Liste des documents pour famille",
                null, null, FAMILLE, CONSULTATION);
        return documentDTOList;
    }

    private List<DocumentDTO> getAllDocumentsByDonation(Long donationId) {
        List<DocumentDonation> documentDonationList = documentDonationRepository.findByDonationId(donationId);
        List<DocumentDTO> documentDTOList = new ArrayList<>();
        Optional<Donor> donationTemp = donorRepository.findById(donationId);
        Donor donation = donationTemp.orElse(null);
        for (DocumentDonation documentDonation : documentDonationList) {
            Document documentEntity = documentDonation.getDocument();

            TypeDocumentDonorDTO typeDocumentDonorDTO = refFeignClient.getConsTypeDonorDocument(documentDonation.getDocument().getTypeDocumentId());

            DocumentDTO documentDTO = documentMapper.documentModelToDto(documentEntity);
            documentDTO.setType(typeDocumentDonorDTO);

            // Add tags to the document DTO
            addTagsToDocumentDTO(documentDTO, "document");

            documentDTOList.add(documentDTO);
        }
        if (donation!=null) auditApplicationService.audit("Consultation des documents dans donation : "+donation.getCode(), getUsernameFromJwt(), "Liste des documents pour donation", null, null, DONATION2, CONSULTATION);
        return documentDTOList;
    }

    private List<DocumentDTO> getAllDocumentsByDonor(Long donorId) {
        List<DocumentDonor> documentDonorList = documentDonorRepository.findByDonorId(donorId);
        List<DocumentDTO> documentDTOList = new ArrayList<>();
        Optional<Donor> donateurTemp = donorRepository.findById(donorId);
        Donor donateur = donateurTemp.orElse(null);
        for (DocumentDonor documentDonor : documentDonorList) {
            Document documentEntity = documentDonor.getDocument();

            TypeDocumentDonorDTO typeDocumentDonorDTO = refFeignClient.getConsTypeDonorDocument(documentDonor.getDocument().getTypeDocumentId());

            DocumentDTO documentDTO = documentMapper.documentModelToDto(documentEntity);
            documentDTO.setType(typeDocumentDonorDTO);

            // Add tags to the document DTO
            addTagsToDocumentDTO(documentDTO, "document");

            documentDTOList.add(documentDTO);
        }
        auditApplicationService.audit("Consultation des documents du donateur : "+(donateur!=null?donateur.getCode():"-"), getUsernameFromJwt(), "Liste des documents pour donateur",
                null, null, DONATEUR, CONSULTATION);
        return documentDTOList;
    }

    private List<DocumentDTO> getAllDocumentsByTakenInCharge(Long takenInChargeId) {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service Get All Documents By TakenInCharge");
        List<DocumentTakenInCharge> documentTakenInChargeList = documentTakenInChargeRepository.findByTakenInChargeId(takenInChargeId);
        List<DocumentDTO> documentDTOList = new ArrayList<>();

        for (DocumentTakenInCharge documentTakenInCharge : documentTakenInChargeList) {
            Document documentEntity = documentTakenInCharge.getDocument();

            TypeDocumentDonorDTO typeDocumentDonorDTO = refFeignClient.getConsTypeDonorDocument(documentTakenInCharge.getDocument().getTypeDocumentId());

            DocumentDTO documentDTO = documentMapper.documentModelToDto(documentEntity);
            documentDTO.setType(typeDocumentDonorDTO);

            // Add tags to the document DTO
            addTagsToDocumentDTO(documentDTO, "document");

            documentDTOList.add(documentDTO);

        }
        auditApplicationService.audit("Consultation du liste des documents dans take in charge", getUsernameFromJwt(), "Liste des documents pour take in charge",
                null, null, TAKENINCHARGE, CONSULTATION);
        log.debug("End service Get All Documents By TakenInCharge, took {}", watch.toMS());
        return documentDTOList;
    }

    public void deleteDocumentByDocumentId(String target, Long documentId) throws TechnicalException {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service deleteDocumentByDocumentId with target {} and document ID {}", target, documentId);
        switch (target.toLowerCase()) {
            case DONOR -> {
                List<DocumentDonor> documentDonorList = documentDonorRepository.findByDocumentId(documentId);
                documentDonorRepository.deleteAll(documentDonorList);

                Document deleteDocument = documentRepository.findById(documentId).orElseThrow(() -> new TechnicalException("Document not found"));
                TypeDocumentDonorDTO typeDocumentDonorDTO = refFeignClient.getConsTypeDonorDocument(deleteDocument.getTypeDocumentId());
                String documentName = typeDocumentDonorDTO.getName();
                auditApplicationService.audit("Suppression Document pour Donateur", getUsernameFromJwt(), "Delete Donor Document",
                        deleteDocument.toAuditString(documentName), null, DONATEUR, DELETE);
            }
            case BENEFICIARY -> {
                List<DocumentBeneficiary> documentBeneficiarieslist = documentBeneficiaryRepository.findByDocumentId(documentId);
                documentBeneficiaryRepository.deleteAll(documentBeneficiarieslist);
                Document deleteDocument = documentRepository.findById(documentId).orElseThrow(() -> new TechnicalException("Document not found"));
                TypeDocumentDonorDTO typeDocumentDonorDTO = refFeignClient.getConsTypeDonorDocument(deleteDocument.getTypeDocumentId());
                String documentName = typeDocumentDonorDTO.getName();
                auditApplicationService.audit("Suppression Document pour Bénéficiaire", getUsernameFromJwt(), "Delete Beneficiary Document",
                        deleteDocument.toAuditString(documentName), null, BENEFICIAIRE, DELETE);
            }
            case TAKENINCHARGE -> {
                List<DocumentTakenInCharge> documentTakenInChargeslist = documentTakenInChargeRepository.findByDocumentId(documentId);
                documentTakenInChargeRepository.deleteAll(documentTakenInChargeslist);
                Document deleteDocument = documentRepository.findById(documentId).orElseThrow(() -> new TechnicalException("Document not found"));
                TypeDocumentDonorDTO typeDocumentDonorDTO = refFeignClient.getConsTypeDonorDocument(deleteDocument.getTypeDocumentId());
                String documentName = typeDocumentDonorDTO.getName();
                auditApplicationService.audit("Suppression Document pour Prise en Charge", getUsernameFromJwt(), "Delete TakenInCharge Document",
                        deleteDocument.toAuditString(documentName), null, DOCUMENT, DELETE);
            }
            case DONATION -> {
                List<DocumentDonation> documentDonationsList = documentDonationRepository.findByDocumentId(documentId);
                documentDonationRepository.deleteAll(documentDonationsList);
                Document deleteDocument = documentRepository.findById(documentId).orElseThrow(() -> new TechnicalException("Document not found"));
                TypeDocumentDonorDTO typeDocumentDonorDTO = refFeignClient.getConsTypeDonorDocument(deleteDocument.getTypeDocumentId());
                String documentName = typeDocumentDonorDTO.getName();
                auditApplicationService.audit("Suppression Document pour Donation", getUsernameFromJwt(), "Delete Donation Document",
                        deleteDocument.toAuditString(documentName), null, DONATION2, DELETE);
            }
            case FAMILY -> {
                List<DocumentFamily> documentFamiliesList = familyDocumentRepository.findByDocumentId(documentId);
                familyDocumentRepository.deleteAll(documentFamiliesList);
                Document deleteDocument = documentRepository.findById(documentId).orElseThrow(() -> new TechnicalException("Document not found"));
                TypeDocumentDonorDTO typeDocumentDonorDTO = refFeignClient.getConsTypeDonorDocument(deleteDocument.getTypeDocumentId());
                String documentName = typeDocumentDonorDTO.getName();
                auditApplicationService.audit("Suppression Document pour Famille", getUsernameFromJwt(), "Delete Family Document",
                        deleteDocument.toAuditString(documentName), null, FAMILLE, DELETE);
            }
            default -> throw new IllegalArgumentException("Unsupported target type: " + target);
        }
        documentRepository.deleteById(documentId);

        log.debug("End service deleteDocumentByDocumentId with target {} and document ID {}, took {}", target, documentId, watch.toMS());
    }

    public byte[] downloadDocumentById(Long documentId) throws TechnicalException {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service downloadDocumentById with document ID {}", documentId);
        if (documentId == null) {
            throw new TechnicalException(NULL_ENTITY);
        }
        Optional<Document> findDocumentOpt = documentRepository.findById(documentId);
        Document findDocument = findDocumentOpt.orElse(null);
        String documentName = null;
        assert findDocument != null;
        if(findDocument.getTypeDocumentId() !=null) {
            TypeDocumentDonorDTO typeDocumentDonorDTO = refFeignClient.getConsTypeDonorDocument(findDocument.getTypeDocumentId());
            documentName = typeDocumentDonorDTO.getName();
        }

        String entityCode = "";
        String entityName = DOCUMENT;
        if (!documentDonorRepository.findByDocumentId(documentId).isEmpty()){
            entityName = DONATEUR;
            entityCode = documentDonorRepository.findByDocumentId(documentId).get(0).getDonor().getCode();
            assert findDocument != null;
        }
        if (!familyDocumentRepository.findByDocumentId(documentId).isEmpty()){
            entityCode = familyDocumentRepository.findByDocumentId(documentId).get(0).getFamily().getCode();
            entityName = FAMILLE;
        }
        if (!documentBeneficiaryRepository.findByDocumentId(documentId).isEmpty()){
            entityCode = documentBeneficiaryRepository.findByDocumentId(documentId).get(0).getBeneficiary().getCode();
            entityName = BENEFICIAIRE;
        }
        if (!documentDonationRepository.findByDocumentId(documentId).isEmpty()){
            entityCode = documentDonationRepository.findByDocumentId(documentId).get(0).getDonation().getCode();
            entityName = DONATION2;
        }
        if (!documentTakenInChargeRepository.findByDocumentId(documentId).isEmpty()){
            entityCode = documentTakenInChargeRepository.findByDocumentId(documentId).get(0).getTakenInCharge().getCode();
            entityName = TAKENINCHARGE;
        }

        Document documentEntity = documentRepository.findById(documentId)
                .orElseThrow(() -> new EntityNotFoundException("Document not found with ID: " + documentId));

        String fileUrl = documentEntity.getFileUrl();
        byte[] documentBytes = minioService.downloadFromMinIO(fileUrl);
        auditApplicationService.audit("Téléchargement du document dans "+entityName+" : "+entityCode, getUsernameFromJwt(), "Téléchargement du document",
                findDocument.toAuditString(documentName),null, entityName, TELECHARGEMENT);

        log.debug("End service downloadDocumentById with document ID {}, took {}", documentId, watch.toMS());
        return documentBytes;
    }

    public String getDocumentFileNameOrUrl(Long idDocument) throws TechnicalException {
        if (idDocument == null) {
            throw new TechnicalException(NULL_ENTITY);
        }
        Document document = documentRepository.findById(idDocument)
                .orElseThrow(() -> new EntityNotFoundException("Document not found with ID: " + idDocument));

        if (StringUtils.hasText(document.getFileName())) {
            document.setFileName(idDocument + document.getFileName());
            return document.getFileName();
        }
        if (StringUtils.hasText(document.getFileUrl())) {
            return extractFileNameFromUrl(document.getFileUrl());
        }
        throw new IllegalStateException("Document does not have a file name or URL");
    }

    private String extractFileNameFromUrl(String url) {
        String[] pathSegments = url.split("/");
        return pathSegments[pathSegments.length - 1];
    }

    public void save(DocumentBeneficiary documentBeneficiary) {
        documentBeneficiaryRepository.save(documentBeneficiary);
    }

    /**
     * Retrieves tags for a document and adds them to the DocumentDTO
     * @param documentDTO The DocumentDTO to add tags to
     * @param entityType The type of entity (DONOR, BENEFICIARY, etc.)
     */
    private void addTagsToDocumentDTO(DocumentDTO documentDTO, String entityType) {
        if (documentDTO.getId() == null) {
            return;
        }

        List<Taggable> taggables = taggableRepository.findByTaggableIdAndTaggableType(documentDTO.getId(), entityType);

        if (taggables != null && !taggables.isEmpty()) {
            List<TagDTO> tagDTOs = taggables.stream()
                    .map(taggable -> {
                        Tag tag = taggable.getTag();
                        TagDTO tagDTO = new TagDTO();
                        tagDTO.setId(tag.getId());
                        tagDTO.setName(tag.getName());
                        tagDTO.setColor(tag.getColor());
                        if (tag.getTypeTag() != null) {
                            tagDTO.setTypeTagId(tag.getTypeTag().getId());
                            tagDTO.setTypeTagName(tag.getTypeTag().getName());
                        }
                        return tagDTO;
                    })
                    .collect(Collectors.toList());

            documentDTO.setTags(tagDTOs);
        }
    }

    public String getContentType(String fileName) {
        String extension = fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase();
        return switch (extension) {
            case "png" -> "image/png";
            case "jpg", "jpeg" -> "image/jpeg";
            case "gif" -> "image/gif";
            case "pdf" -> "application/pdf";
            case "txt" -> "text/plain";
            case "csv" -> "text/csv";  // For CSV files
            case "xls", "xlsx" ->
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";  // For Excel files (XLS, XLSX)
            case "doc", "docx" ->
                    "application/vnd.openxmlformats-officedocument.wordprocessingml.document";  // For Word documents (DOC, DOCX)
            case "ppt", "pptx" ->
                    "application/vnd.openxmlformats-officedocument.presentationml.presentation";  // For PowerPoint files
            case "zip" -> "application/zip";  // For ZIP files
            case "json" -> "application/json";  // For JSON files
            case "html" -> "text/html";  // For HTML files
            case "xml" -> "application/xml";  // For XML files
            case "mp3" -> "audio/mpeg";  // For MP3 files
            case "mp4" -> "video/mp4";  // For MP4 files
            // Add more file types as necessary
            default -> "application/octet-stream";  // Default to binary stream if type is unknown
        };
    }

}
