package ma.almobadara.backend.model.aideComplemenatire;


import jakarta.persistence.*;
import lombok.*;
import ma.almobadara.backend.model.beneficiary.Beneficiary;
import ma.almobadara.backend.model.donor.Donor;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AideComplementaireDonorBeneficiary {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne
    @JoinColumn(name = "aide_complementaire_id", nullable = false)
    private AideComplementaire aideComplementaire;

    @ManyToOne
    @JoinColumn(name = "donor_id", nullable = true)
    private Donor donor;

    @ManyToOne
    @JoinColumn(name = "beneficiary_id", nullable = true)
    private Beneficiary beneficiary;

    private Double montantAffecter;
    private Double montantTotalDuDonateur;
    private Double montantPoserDuDonateur;
    private Double montantRestantDuDonateur;
    private Boolean statutValidation;
    private Boolean isNature;

}
