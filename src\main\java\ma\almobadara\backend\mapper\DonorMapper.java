package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.communs.DocumentDTO;
import ma.almobadara.backend.dto.donor.*;
import ma.almobadara.backend.dto.exportentities.DonorExportDTO;
import ma.almobadara.backend.model.communs.Document;
import ma.almobadara.backend.model.donor.DocumentDonor;
import ma.almobadara.backend.model.donor.Donor;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;


@Mapper(componentModel = "spring")
public interface DonorMapper {

	@Mapping(source = "donorStatusId", target = "status.id")
	@Mapping(source = "cityId", target = "city.id")
	@Mapping(source = "documentsDonors", target = "documentDonors")
	DonorDTO donorModelToDto(Donor donor);

	Iterable<DonorDTO> donorListModelToDto(Iterable<Donor> donors);

	@Mapping(source = "status.id", target = "donorStatusId")
	@Mapping(source = "city.id", target = "cityId")
	Donor donorDtoToModel(DonorDTO donor);

	Iterable<Donor> donorListDtoToModal(Iterable<DonorDTO> donors);


	DocumentDTO documentDonorModelToDto(DocumentDonor documentDonor);

	Iterable<DocumentDTO> documentDonorListModelToDto(Iterable<DocumentDonor> documentDonors);


	DocumentDonor documentDonorDtoModelToModel(DocumentDTO documentDonor);

	@Mapping(source = "document.id", target = "document.id")
	DocumentDonorDto documentDonorEntityToDto(DocumentDonor documentDonor);

	Iterable<DocumentDonorDto> documentDonorEntityListToDtoList(Iterable<DocumentDonor> documentDonors);

	// Mappez le typeDocumentDonor depuis Document vers DocumentDonorDto
	@Mapping(source = "typeDocumentId", target = "document.id")
	DocumentDonorDto documentToDocumentDonorDto(Document document);

	static DonorExportDTO toDonorExportDTO(DonorDTO donorDTO) {
		if (donorDTO instanceof DonorMoralDTO) {
			return mapDonorMoral((DonorMoralDTO) donorDTO);
		} else if (donorDTO instanceof DonorPhysicalDTO) {
			return mapDonorPhysical((DonorPhysicalDTO) donorDTO);
		}
		else if (donorDTO instanceof DonorAnonymeDTO ) {
			return mapDonorAnonyme((DonorAnonymeDTO) donorDTO);
		}
		else {
			return mapDonor(donorDTO);
		}
	}

	private static DonorExportDTO mapDonor(DonorDTO donorDTO) {
		return DonorExportDTO.builder()
				.code(donorDTO.getCode())
				.createdAt(donorDTO.getCreatedAt())
				.balance(donorDTO.getBalance())
				.address(donorDTO.getAddress())
				.city(donorDTO.getCity() != null ? donorDTO.getCity().getName() : null)
				.region(donorDTO.getInfo() != null ? donorDTO.getInfo().getRegion().getName() : null)
				.country(donorDTO.getInfo() != null ? donorDTO.getInfo().getCountry().getName() : null)
				.status(donorDTO.getStatus() != null ? donorDTO.getStatus().getName() : null)
				.build();
	}

	private static DonorExportDTO mapDonorMoral(DonorMoralDTO donorMoralDTO) {
		String email = null;
		String phoneNumber = null;
		if (donorMoralDTO.getDonorContacts() != null && !donorMoralDTO.getDonorContacts().isEmpty()) {
			email = donorMoralDTO.getDonorContacts().get(0).getEmail();
			phoneNumber = donorMoralDTO.getDonorContacts().get(0).getPhoneNumber();
		}
		return DonorExportDTO.builder()
				.code(donorMoralDTO.getCode())
				.createdAt(donorMoralDTO.getCreatedAt())
				.balance(donorMoralDTO.getBalance())
				.address(donorMoralDTO.getAddress())
				.city(donorMoralDTO.getCity() != null ? donorMoralDTO.getCity().getName() : null)
				.region(donorMoralDTO.getInfo() != null ? donorMoralDTO.getInfo().getRegion().getName() : null)
				.country(donorMoralDTO.getInfo() != null ? donorMoralDTO.getInfo().getCountry().getName() : null)
				.status(donorMoralDTO.getStatus() != null ? donorMoralDTO.getStatus().getName() : null)
				.company(donorMoralDTO.getCompany())
				.donorType("Moral")
				.phoneNumber(phoneNumber)
				.email(email)
				.build();
	}

	private static DonorExportDTO mapDonorPhysical(DonorPhysicalDTO donorPhysicalDTO) {
		return DonorExportDTO.builder()
				.code(donorPhysicalDTO.getCode())
				.createdAt(donorPhysicalDTO.getCreatedAt())
				.balance(donorPhysicalDTO.getBalance())
				.address(donorPhysicalDTO.getAddress())
				.city(donorPhysicalDTO.getCity() != null ? donorPhysicalDTO.getCity().getName() : null)
				.region(donorPhysicalDTO.getInfo() != null ? donorPhysicalDTO.getInfo().getRegion().getName() : null)
				.country(donorPhysicalDTO.getInfo() != null ? donorPhysicalDTO.getInfo().getCountry().getName() : null)
				.status(donorPhysicalDTO.getStatus() != null ? donorPhysicalDTO.getStatus().getName() : null)
				.firstName(donorPhysicalDTO.getFirstName())
				.lastName(donorPhysicalDTO.getLastName())
				.phoneNumber(donorPhysicalDTO.getPhoneNumber())
				.email(donorPhysicalDTO.getEmail())
				.sex(donorPhysicalDTO.getSex())
				.donorType("Physique")
				.build();
	}
	private static DonorExportDTO mapDonorAnonyme(DonorAnonymeDTO donorAnonymeDTO) {
		return DonorExportDTO.builder()
				.code(donorAnonymeDTO.getCode())
				.createdAt(donorAnonymeDTO.getCreatedAt())
				.status(donorAnonymeDTO.getStatus() != null ? donorAnonymeDTO.getStatus().getName() : null)
				.firstName(donorAnonymeDTO.getName())
				.donorType("Anonyme")
				.build();
	}

}


