package ma.almobadara.backend.controller.aideComplemenatire;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.persistence.EntityNotFoundException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.aideComplemenatire.*;
import ma.almobadara.backend.dto.beneficiary.AideComplementaireListDto;
import ma.almobadara.backend.dto.beneficiary.BeneficiaryForAideComplementaireDTO;
import ma.almobadara.backend.dto.beneficiary.BeneficiaryForTypeKafalatDTO;
import ma.almobadara.backend.dto.communs.DocumentAndEntityDto;
import ma.almobadara.backend.dto.donation.BudgetLineForAideComplementaireDTO;
import ma.almobadara.backend.dto.donor.DonorForAideComplementaireDTO;
import ma.almobadara.backend.dto.referentiel.TypePriseEnChargeDTO;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.service.aideComplemenatire.AideComplementaireService;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

@RefreshScope
@RestController
@AllArgsConstructor
@Slf4j
@RequestMapping("/aide-complementaire")
public class AideComplementaireController {
    private final AideComplementaireService aideComplementaireService;

    @PostMapping(value = "/create")
    @Operation(summary = "Create an aideComplementaire", description = "add a new aideComplementaire", tags = {"aideComplementaire"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = AideComplementaireDTO.class))))})
    public ResponseEntity<AideComplementaireDTO> addAideComplementaire(@RequestBody AddAideComplementaireDTO aideComplementaireDTO) {
        return ResponseEntity.ok(aideComplementaireService.addAideComplementaire(aideComplementaireDTO));
    }

    @Operation(summary = "Find AideComplementaire by ID", description = "Returns a single AideComplementaire", tags = {"AideComplementaire"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(schema = @Schema(implementation = AideComplementaireDTO.class))),
            @ApiResponse(responseCode = "404", description = "AideComplementaire not found")})
    @GetMapping(value = "/{idAideComplementaire}", produces = {"application/json"})
    public ResponseEntity<AideComplementaireDTO> getAideComplementaireById(@PathVariable Long idAideComplementaire) {


        AideComplementaireDTO aideComplementaireDTO;
        HttpStatus status;
        try {
            aideComplementaireDTO = aideComplementaireService.getAideComplementaireById(idAideComplementaire);
            status = HttpStatus.OK;
            log.info("End resource getAideComplementaireById  : {}, OK", idAideComplementaire);
        } catch (Exception e) {
            aideComplementaireDTO = null;
            status = HttpStatus.NOT_FOUND;
            log.error("End resource getAideComplementaireById  : {}, KO: {}", idAideComplementaire, e.getMessage());
        }

        return new ResponseEntity<>(aideComplementaireDTO, new HttpHeaders(), status);
    }

    @GetMapping(value = "/findAll", produces = {"application/json"})
    @Operation(summary = "Find All aideComplementaire", description = "Find All aideComplementaire", tags = {"aideComplementaire"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = AideComplementaireDTO.class))))})
    public ResponseEntity<Page<AideComplementaireDTO>> getAllAideComplementaire(
            @RequestParam(defaultValue = "0") final Integer page,
            @RequestParam(defaultValue = "10") final Integer size,
            @RequestParam(required = false) final String searchByNom,
            @RequestParam(required = false) final String searchByStatut,
            @RequestParam(required = false) final Long searchByMontant,
            @RequestParam(required = false) final Long searchByTypePriseEnChargeId,
            @RequestParam(required = false) final Long searchByTagId,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") final Date minDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") final Date maxDate
    ) {

        HttpStatus status;
        Page<AideComplementaireDTO> aideComplementaireDTOS;
        try {
            aideComplementaireDTOS = aideComplementaireService.getAllAideComplementaire(page, size,  searchByNom, searchByTypePriseEnChargeId,searchByStatut , searchByMontant, minDate, maxDate,searchByTagId);
            status = HttpStatus.OK;
            log.info("End resource getAllAideComplementaire with size: {}, OK", aideComplementaireDTOS.getTotalElements());
        } catch (Exception e) {
            aideComplementaireDTOS = new PageImpl<>(Collections.emptyList());
            status = HttpStatus.INTERNAL_SERVER_ERROR;
            log.error("End resource getAllAideComplementaire, KO: {}", e.getMessage());
        }

        return new ResponseEntity<>(aideComplementaireDTOS, status);
    }

    @GetMapping("/beneficiaries_type-kafalat")
    public ResponseEntity<List<BeneficiaryForTypeKafalatDTO>> getBeneficiariesWithTypeKafalatId(
            @RequestParam Long typePriseEnChargeId) {
        List<BeneficiaryForTypeKafalatDTO> beneficiaries = aideComplementaireService.getBeneficiariesWithTypeKafalatId(typePriseEnChargeId);
        return ResponseEntity.ok(beneficiaries);
    }

    @GetMapping("/type-prise-en-charge-with-caisse")
    public ResponseEntity<List<TypePriseEnChargeDTO>> loadTypePriseEnChargeWithCaisse() {
        List<TypePriseEnChargeDTO> availableTypes = aideComplementaireService.loadTypePriseEnChargeWithCaisse();
        return ResponseEntity.ok(availableTypes);
    }

    @GetMapping(value = "/{aideComplementaireId}/active")
    @Operation(summary = "Get active beneficiaries for a given aideComplementaire",
            description = "Retrieve a list of active beneficiaries that are not archived and not associated with the specified aideComplementaire.",
            tags = {"beneficiary"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successful operation",
                    content = @Content(array = @ArraySchema(schema = @Schema(implementation = BeneficiaryForAideComplementaireDTO.class)))),
            @ApiResponse(responseCode = "404", description = "AideComplementaire not found"),
            @ApiResponse(responseCode = "500", description = "Internal server error")})
    public ResponseEntity<Page<BeneficiaryForAideComplementaireDTO>> getActiveBeneficiariesForAideComplementaire(@PathVariable Long aideComplementaireId,
                                                                                                                 @RequestParam(required = false) String beneficiaryStatut,
                                                                                                                 @RequestParam(required = false) String type,
                                                                                                                 @RequestParam(defaultValue = "0") Integer page,
                                                                                                                 @RequestParam(defaultValue = "5") Integer size,
                                                                                                                 @RequestParam(required = false) Boolean withParticipatingMembers,
                                                                                                                 @RequestParam(required = false) Boolean withDonor,
                                                                                                                 @RequestParam(required = false) Boolean withParticipatingDonor,
                                                                                                                 @RequestParam(required = false) Boolean withOldCampagne,
                                                                                                                 @RequestParam(required = false) String text) {
        Pageable pageable = PageRequest.of(page, size);
        Page<BeneficiaryForAideComplementaireDTO> beneficiaries = aideComplementaireService.getActiveBeneficiariesForAideComplementaire(
                aideComplementaireId, beneficiaryStatut, type, withParticipatingMembers, withDonor, withParticipatingDonor, withOldCampagne,text,pageable);

        return ResponseEntity.ok(beneficiaries);
    }


    @GetMapping("/{id}/beneficiaries")
    public ResponseEntity<Page<BeneficiaryAideComplemenatireDTO>> getBeneficiariesForAide(
            @PathVariable Long id,
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(defaultValue = "5") Integer size,
            @RequestParam(required = false) String searchFullName,
            @RequestParam(required = false) String category,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) Boolean relatedToDonor,
            @RequestParam(required = false) Boolean validationStatus
    ) throws TechnicalException {

        Pageable pageable = PageRequest.of(page, size);
        Page<BeneficiaryAideComplemenatireDTO> beneficiariesPage = aideComplementaireService.getBeneficiariesForAide(id, pageable,searchFullName,category,type,relatedToDonor,validationStatus);

        return ResponseEntity.ok(beneficiariesPage);
    }


    @GetMapping("/groupe-beneficiaries")
    public ResponseEntity<GroupeAndBeneficiariesDTO> getGroupeAdHocBeneficiaries(
            @RequestParam Long aideComplementaireId,
            @RequestParam Long groupeId) {
        GroupeAndBeneficiariesDTO result = aideComplementaireService.getGroupeAdHocBeneficiries(aideComplementaireId, groupeId);
        return ResponseEntity.ok(result);
    }


    @GetMapping(value = "/budgetLines/{serviceId}/{aideComplementaireId}/budgetLines")
    @Operation(summary = "Get budgetLines for a given aideComplementaire",
            description = "Retrieve a list of budgetLines with the specified aideComplementaire.",
            tags = {"budgetLine"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successful operation",
                    content = @Content(array = @ArraySchema(schema = @Schema(implementation = DonorAideComplemenatireDTO.class)))),
            @ApiResponse(responseCode = "404", description = "budgetLines not found"),
            @ApiResponse(responseCode = "500", description = "Internal server error")})
    public ResponseEntity<List<DonorAideComplemenatireDTO>> getBudgetLineForAideComplementaire(@PathVariable Long serviceId, @PathVariable Long aideComplementaireId) {
        List<DonorAideComplemenatireDTO> budgetLines = aideComplementaireService.getBudgetLineForAideComplementaire(serviceId, aideComplementaireId);
        return ResponseEntity.ok(budgetLines);
    }

    @GetMapping(value = "/{aideComplementaireId}/activeDonors")
    @Operation(summary = "Get active donors for a given aideComplementaire",
            description = "Retrieve a list of active donors that are not archived and not associated with the specified aideComplementaire.",
            tags = {"donor"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successful operation",
                    content = @Content(array = @ArraySchema(schema = @Schema(implementation = DonorForAideComplementaireDTO.class)))),
            @ApiResponse(responseCode = "404", description = "AideComplementaire not found"),
            @ApiResponse(responseCode = "500", description = "Internal server error")})
    public ResponseEntity<List<DonorForAideComplementaireDTO>> getActiveDonorsForAideComplementaire(@PathVariable Long aideComplementaireId) {
        List<DonorForAideComplementaireDTO> donors = aideComplementaireService.getActiveDonorsForAideComplementaire(aideComplementaireId);
        return ResponseEntity.ok(donors);
    }

    @PostMapping(value = "/processDonorsAndBeneficiaries")
    @Operation(summary = "Process donors and beneficiaries", description = "Link donors and beneficiaries to an aideComplementaire", tags = {"aideComplementaire"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = Void.class)))),
            @ApiResponse(responseCode = "400", description = "Invalid input")})
    public ResponseEntity<Void> processDonorsAndBeneficiaries(
            @RequestParam Long idAideComplementaire,
            @RequestBody List<String> beneficiaryIds) {

        aideComplementaireService.processDonorAndBeneficiaries(idAideComplementaire, beneficiaryIds);

        return ResponseEntity.ok().build();
    }

    @DeleteMapping("/remove-beneficiary-from-aide")
    @Operation(summary = "removeBeneficiaryFromAideComplementaire", description = "Remove beneficiary From aideComplementaire", tags = {"aideComplementaire"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = Void.class)))),
            @ApiResponse(responseCode = "400", description = "Invalid input")})
    public ResponseEntity<Void> removeBeneficiaryFromAideComplementaire(
            @RequestParam Long idAideComplementaire,
            @RequestParam Long idBeneficiary,
            @RequestParam(required = false) Long idDonor,
            @RequestParam String type) {


        aideComplementaireService.removeBeneficiaryFromAideComplementaire(idAideComplementaire, idBeneficiary, idDonor, type);

        return ResponseEntity.noContent().build();
    }


    @DeleteMapping("/remove-donor-from-aide")
    @Operation(summary = "removeDonorFromAideComplementaire", description = "Remove donor From aideComplementaire", tags = {"aideComplementaire"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = Void.class)))),
            @ApiResponse(responseCode = "400", description = "Invalid input")})
    public ResponseEntity<Void> removeDonorFromAideComplementaire(
            @RequestParam Long idAideComplementaire,
            @RequestParam Long idDonor) {

        aideComplementaireService.removeDonorFromAideComplementaire(idAideComplementaire, idDonor);

        return ResponseEntity.noContent().build();
    }


    @PutMapping("/update-montant-beneficiary")
    @Operation(summary = "updateMontantParBeneficiary", description = "update Montant for beneficiary in aideComplementaire", tags = {"aideComplementaire"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = Void.class)))),
            @ApiResponse(responseCode = "400", description = "Invalid input")})
    public ResponseEntity<Void> updateMontantParBeneficiary(
            @RequestParam Long idAideComplementaire,
            @RequestParam Long idBeneficiary,
            @RequestParam(required = false) Long idDonor,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) Double newMontant,
            @RequestParam(required = false) Long numberOfMembers
    ) {
        try {
            aideComplementaireService.updateMontantParBeneficiary(idAideComplementaire, idBeneficiary,idDonor, type,newMontant,numberOfMembers);
            return ResponseEntity.noContent().build();

        } catch (EntityNotFoundException e) {
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @PostMapping("/update-montant-for-groupe")
    public ResponseEntity<Double> updateMontantParBeneficiaryAdHocGroupe(
            @RequestParam Long idAideComplementaire,
            @RequestParam Long idGroupe,
            @RequestParam String type,
            @RequestBody Map<Long, Double> beneficiaryAmounts) {
        try {
            Double totalAmountAffecter = aideComplementaireService.updateMontantParBeneficiaryAdHocGroupe(
                    idAideComplementaire, idGroupe, type, beneficiaryAmounts);
            return ResponseEntity.ok(totalAmountAffecter);

        } catch (EntityNotFoundException e) {
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @PutMapping("/update-reservedAmount-budgetLine")
    @Operation(summary = "updateReservedAmountForBudgetLine", description = "update Reserved Amount For BudgetLine in aideComplementaire", tags = {"aideComplementaire"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = Void.class)))),
            @ApiResponse(responseCode = "400", description = "Invalid input")})
    public ResponseEntity<Void> updateReservedAmountForBudgetLine(
            @RequestParam Long budgetLineId,
            @RequestParam Double newAmount
    ) {
        try {
            aideComplementaireService.updateReservedAmountForBudgetLine(budgetLineId, newAmount);
            return ResponseEntity.noContent().build();

        } catch (EntityNotFoundException e) {
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }


    @PostMapping("/refresh-donors")
    @Operation(summary = "refreshDonorsForAideComplementaire", description = "refresh Donors For AideComplementaire", tags = {"aideComplementaire"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = Void.class)))),
            @ApiResponse(responseCode = "400", description = "Invalid input")})
    public ResponseEntity<Void> refreshDonorsForAideComplementaire(
            @RequestParam Long idAideComplementaire,@RequestParam boolean priority, @RequestBody List<Long> idDonors) {
        try {
            aideComplementaireService.refreshDonorsForAideComplementaire(idAideComplementaire, idDonors, priority);
            return ResponseEntity.ok().build();
        } catch (EntityNotFoundException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }


    @PutMapping("/{idAideComplementaire}/beneficiary/{idBeneficiary}/update-statut")
    public ResponseEntity<String> updateStatutValidationBenenficiary(
            @PathVariable Long idAideComplementaire,
            @PathVariable Long idBeneficiary,
            @RequestParam(required = false) Long idDonor,
            @RequestParam String type) {
        try {
            aideComplementaireService.updateStatutValidationBenenficiaryInAide(idAideComplementaire, idBeneficiary,idDonor, type);

            return ResponseEntity.ok("Statut validation updated successfully.");
        } catch (EntityNotFoundException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(e.getMessage());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("An error occurred while updating statut validation.");
        }
    }


    @PutMapping("/reserve-budget-line/{budgetLineId}/{aideComplementaireId}")
    public ResponseEntity<String> reserveBudgetLineForAideComplementaire(
            @PathVariable Long budgetLineId,
            @PathVariable Long aideComplementaireId
    ) {
        try {
            aideComplementaireService.reserveBudgetLineForAideComplementaire(budgetLineId, aideComplementaireId);

            return ResponseEntity.ok("Statut reservation updated successfully.");
        } catch (EntityNotFoundException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(e.getMessage());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("An error occurred while updating statut reservation.");
        }
    }

    @PostMapping("/reserve-by-donor")
    public ResponseEntity<Void> reserveBudgetLine(
            @RequestParam Long donorId,
            @RequestParam Long aideComplementaireId,
            @RequestParam(required = false) Double reservedAmount,
            @RequestParam String operation,
            @RequestParam Boolean isNature) {
        try {
            aideComplementaireService.reserveBudgetLineForAideComplementaireByDonor(donorId, aideComplementaireId, reservedAmount, operation,isNature);
            return ResponseEntity.ok().build();
        } catch (EntityNotFoundException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(null);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @PostMapping("/reserve-all-budget-lines/{idAideComplementaire}")
    public ResponseEntity<?> reserveAllBudgetLines(
            @PathVariable("idAideComplementaire") Long idAideComplementaire,
            @RequestParam("operation") String operation,
            @RequestBody Map<Long, Double> donorAmounts) {

        try {
            aideComplementaireService.reserveAllBudgetLines(idAideComplementaire, donorAmounts, operation);
            return ResponseEntity.ok("Opération réussie.");
        } catch (Exception e) {
            return ResponseEntity.status(500).body("Erreur lors de l'opération : " + e.getMessage());
        }
    }

    @GetMapping("/all")
    public ResponseEntity<List<AideComplementaireListDto>> getAllAideComplementaire() {
        List<AideComplementaireListDto> aideComplementaires = aideComplementaireService.loadAllAideComplementaire();
        return ResponseEntity.ok(aideComplementaires);
    }

    @PostMapping("/execute")
    public ResponseEntity<String> executeAideComplementaire(
            @RequestParam Long aideComplementaireId,
            @RequestParam Double validatedAmount) {

        try {
            aideComplementaireService.executeAideComplementaire(aideComplementaireId, validatedAmount);
            return ResponseEntity.ok("Aide complémentaire exécutée avec succès.");
        } catch (EntityNotFoundException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(e.getMessage());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Une erreur est survenue lors de l'exécution de l'aide complémentaire.");
        }
    }

    @PostMapping("/unexecute")
    public ResponseEntity<String> unExecuteAideComplementaire(
            @RequestParam Long aideComplementaireId) {

        try {
            aideComplementaireService.unExecuteAideComplementaire(aideComplementaireId);
            return ResponseEntity.ok("Aide complémentaire unexécutée avec succès.");
        } catch (EntityNotFoundException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(e.getMessage());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Une erreur est survenue lors de l'unexécution de l'aide complémentaire.");
        }
    }


    @PostMapping(value = "/close" , consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    public ResponseEntity<Void> closeAideComplementaire(@ModelAttribute DocumentAndEntityDto documentAndEntityDto) {
        try {
            aideComplementaireService.closeAideComplementaire(documentAndEntityDto);
            return ResponseEntity.ok().build();
        } catch (EntityNotFoundException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
        } catch ( TechnicalException | IOException e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }


    @DeleteMapping("/delete/{id}")
    public ResponseEntity<String> deleteAideComplementaire(@PathVariable Long id) {
        try {
            aideComplementaireService.deleteAideComplementaire(id);
            return ResponseEntity.ok("L'aide complémentaire a été supprimée avec succès.");
        } catch (EntityNotFoundException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body("Aide complémentaire non trouvée avec l'ID: " + id);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Une erreur est survenue lors de la suppression de l'aide complémentaire.");
        }
    }

    @PutMapping("/{id}/validateAll")
    @Operation(summary = "Validate all beneficiaries for an aide complementaire",
            description = "Validate all beneficiaries for an aide complementaire based on filter criteria",
            tags = {"aideComplementaire"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successful operation"),
            @ApiResponse(responseCode = "404", description = "AideComplementaire not found"),
            @ApiResponse(responseCode = "500", description = "Internal server error")})
    public ResponseEntity<String> validateAllBeneficiaries(
            @PathVariable Long id,
            @RequestParam(required = false) String category,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) Boolean withParticipatingMembers,
            @RequestParam(required = false) Boolean withDonor,
            @RequestParam(required = false) Boolean withParticipatingDonor,
            @RequestParam(required = false) Boolean withOldCampagne,
            @RequestParam(required = false) String text,
            @RequestParam(required = false, defaultValue = "false") Boolean invalidate) {

        try {
            aideComplementaireService.validateAllAideComplementaireDonorBeneficiary(
                    id, category, type, withParticipatingMembers,
                    withDonor, withParticipatingDonor, withOldCampagne, text, invalidate);

            String action = invalidate ? "invalidated" : "validated";
            return ResponseEntity.ok("All beneficiaries " + action + " successfully.");
        } catch (EntityNotFoundException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body("Aide complémentaire not found with ID: " + id);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("An error occurred while " + (invalidate ? "invalidating" : "validating") + " beneficiaries: " + e.getMessage());
        }
    }

    @GetMapping("/{id}/check-all-valids")
    @Operation(summary = "Check if all beneficiaries and ad hoc groups are valid",
            description = "Checks if all beneficiaries and ad hoc groups in an aide complementaire have valid status",
            tags = {"aideComplementaire"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successful operation"),
            @ApiResponse(responseCode = "404", description = "AideComplementaire not found"),
            @ApiResponse(responseCode = "500", description = "Internal server error")})
    public ResponseEntity<Map<String, Object>> checkAllValids(@PathVariable Long id) {
        try {
            Map<String, Object> result = aideComplementaireService.checkAllValids(id);
            return ResponseEntity.ok(result);
        } catch (EntityNotFoundException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(
                    Collections.singletonMap("error", "Aide complémentaire not found with ID: " + id));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
                    Collections.singletonMap("error", "An error occurred while checking validation status: " + e.getMessage()));
        }
    }

}
