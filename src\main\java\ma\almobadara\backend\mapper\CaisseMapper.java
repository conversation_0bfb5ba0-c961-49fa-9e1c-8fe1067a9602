package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.caisse.CaisseDto;
import ma.almobadara.backend.model.caisse.Caisse;
import org.mapstruct.*;

@Mapper(componentModel = "spring")
public interface CaisseMapper {
/*
    // Map from Caisse entity to CaisseDto
    @Mappings({
            @Mapping(source = "id", target = "id"),
            @Mapping(source = "code", target = "code"),
            @Mapping(source = "name", target = "name"),
            @Mapping(source = "typePriseEnChargeId", target = "typePriseEnChargeId"),
            @Mapping(source = "comment", target = "comment"),
            @Mapping(source = "createdAt", target = "createdAt"),
            @Mapping(source = "updatedAt", target = "updatedAt"),
           // @Mapping(source = "creationDateCaisse", target = "creationDateCaisse"),
            @Mapping(target ="aidesComplementaires", ignore = true)
    })
    CaisseDto toDTO(Caisse caisse);

    // Map from CaisseDto to Caisse entity
    @Mappings({
            @Mapping(source = "id", target = "id"),
            @Mapping(source = "code", target = "code"),
            @Mapping(source = "name", target = "name"),
            @Mapping(source = "typePriseEnChargeId", target = "typePriseEnChargeId"),
            @Mapping(source = "comment", target = "comment"),
            @Mapping(source = "createdAt", target = "createdAt"),
            @Mapping(source = "updatedAt", target = "updatedAt"),
           // @Mapping(source = "creationDateCaisse", target = "creationDateCaisse"),
            //@Mapping(target ="aidesComplementaires", ignore = true)
    })
    Caisse toEntity(CaisseDto caisseDto);

    // Update an existing Caisse entity with the new values from CaisseDto
    @Mappings({
            @Mapping(source = "id", target = "id"),
            @Mapping(source = "code", target = "code"),
            @Mapping(source = "name", target = "name"),
            @Mapping(source = "typePriseEnChargeId", target = "typePriseEnChargeId"),
            @Mapping(source = "comment", target = "comment"),
            @Mapping(source = "createdAt", target = "createdAt"),
            @Mapping(source = "updatedAt", target = "updatedAt"),
            @Mapping(source = "creationDateCaisse", target = "creationDateCaisse"),
            @Mapping(target ="aidesComplementaires", ignore = true)
    })
    void updateCaisseFromDTO(CaisseDto caisseDto, @MappingTarget Caisse caisse);

 */
}
