package ma.almobadara.backend.controller.administration;

import com.azure.core.annotation.Get;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.administration.*;
import ma.almobadara.backend.dto.donation.DonationServiceCollectEpsDto;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.service.administration.ServiceCollectEpsService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static ma.almobadara.backend.service.administration.CacheAdUserService.logUserInfo;

@RestController
@RequestMapping("/service-collect-eps")
@RequiredArgsConstructor
@Slf4j
public class ServiceCollectEpsController {
    private final ServiceCollectEpsService serviceCollectEpsService;

    @GetMapping
    public ResponseEntity<Page<ConsultServiceCollectEpsDTO>> getAllServiceEpsCollectWithFilter(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "5") int size,
            @RequestParam(required = false) final Integer searchByMonth,
            @RequestParam(required = false) final Integer searchByYear,
            @RequestParam(required = false) final String searchByName,
            @RequestParam(required = false) final String searchByStatut,
            @RequestParam(required = false) final Integer searchByEps,
            @RequestParam(required = false) final Integer searchByService
    ){
        Page<ConsultServiceCollectEpsDTO> serviceCollectEpsDTOS = serviceCollectEpsService.getAllServiceCollectEps(
                page,
                size,
                searchByMonth,
                searchByYear,
                searchByName,
                searchByStatut,
                searchByEps,
                searchByService
        );
        return ResponseEntity.ok(serviceCollectEpsDTOS);
    }



    @GetMapping("/list")
    public ResponseEntity<List<ServiceCollectEpsDTO>> getAllServiceEpsCollect(
    ) {
        log.info("Start resource getAllZones");
        List<ServiceCollectEpsDTO> serviceCollectEpsDTOS = serviceCollectEpsService.getAllServiceCollectEpsList();
        return ResponseEntity.ok(serviceCollectEpsDTOS);
    }

    @GetMapping("/{id}/fiche")
    public ResponseEntity<ConsultServiceCollectEpsDTO> getServiceEpsCollect(@PathVariable Long id){
        ConsultServiceCollectEpsDTO consultServiceCollectEpsDTO = serviceCollectEpsService.getServiceCollectEpsDTO(id);

        return ResponseEntity.ok(consultServiceCollectEpsDTO);
    }

    @PostMapping
    public ResponseEntity<?> createEpsAndServices(@RequestBody ServiceCollectEpsDTO serviceCollectEpsDTO) throws TechnicalException {
        try {
            ServiceCollectEpsDTO createdEps = serviceCollectEpsService.createServiceCollectEps(serviceCollectEpsDTO);
            return ResponseEntity.status(HttpStatus.CREATED).body(createdEps);
        } catch (IllegalArgumentException e) {
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse);
        }
    }



    @GetMapping("/getEpsAndServices")
    public ResponseEntity<List<EpsAndServiceDto>> getEpsAndServices(){
        List<EpsAndServiceDto> epsAndServiceDtos=serviceCollectEpsService.findEpsForServiceCollectEps();
        return ResponseEntity.status(HttpStatus.ACCEPTED).body(epsAndServiceDtos);
    }

    @GetMapping("/{id}")
    public ResponseEntity<ServiceCollectEpsDTO> getAddServiceCollectEps(@PathVariable Long id) {
        logUserInfo("getAddServiceCollectEps", String.valueOf(id));

        try {
            ServiceCollectEpsDTO serviceCollectEpsDTO = serviceCollectEpsService.FindServiceCollectEpsByIdForAdd(id);
            log.info("End resource getAddServiceCollectEps : {}. Retrieved ServiceCollectEps: {}, OK", id, serviceCollectEpsDTO);
            return new ResponseEntity<>(serviceCollectEpsDTO, HttpStatus.OK);

        } catch (Exception e) {
            log.error("End resource getAddServiceCollectEps : {}. KO: {}", id, e.getMessage());

            // Return an empty EpsDto or a custom error EpsDto with appropriate error information
            ServiceCollectEpsDTO errorEpsDto = new ServiceCollectEpsDTO();

            // Return the error EpsDto with a NOT_FOUND status
            return new ResponseEntity<>(errorEpsDto, HttpStatus.NOT_FOUND);
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<?> deleteServiceCollectEps(@PathVariable Long id){
        try {
            serviceCollectEpsService.deleteServiceCollectEps(id);
            return ResponseEntity.status(HttpStatus.ACCEPTED).build();
        } catch (IllegalArgumentException e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(e.getMessage());
        }
    }

    @PostMapping("/cloture/{id}")
    public ResponseEntity<?> clotureServiceCollectEps(@PathVariable Long id){
        serviceCollectEpsService.clotureServiceCollectEps(id);
        return ResponseEntity.status(HttpStatus.ACCEPTED).build();
    }


    @GetMapping("/getForDonation")
    public ResponseEntity<?> getServiceCollectEpsForDonation(){
        List<ServiceCollectEpsLightDto> serviceCollectEpsLightDtos= serviceCollectEpsService.getServiceCollectEpsForDonation();
        return ResponseEntity.ok(serviceCollectEpsLightDtos);
    }

    @GetMapping("/getDonationByServiceCollectEps")
    public ResponseEntity<Page<DonationServiceCollectEpsDto>> getDonationByServiceCollectEps(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "5") int size,
            @RequestParam(required = false) final String searchByDonateur,
            @RequestParam(required = false) final String searchByDonationMinDate,
            @RequestParam(required = false) final String searchByDonationMaxDate,
            @RequestParam(required = false) final Double searchByMinAmount,
            @RequestParam(required = false) final Double searchByMaxAmount,
            @RequestParam(required = false) final String searchByDonationType,
            @RequestParam(required = false) final Long serviceCollectEpsId
    ) {
        if (serviceCollectEpsId == null) {
            return ResponseEntity.badRequest().build();
        }

        // Parse dates safely
        Date minDate = parseDate(searchByDonationMinDate);
        Date maxDate = parseDate(searchByDonationMaxDate);

        // Create pageable object for pagination
        Pageable pageable = PageRequest.of(page, size);

        // Call the service method
        Page<DonationServiceCollectEpsDto> donations = serviceCollectEpsService.getDonationsByServiceCollectEps(
                serviceCollectEpsId,
                minDate,
                maxDate,
                searchByDonationType,
                searchByDonateur,
                searchByMinAmount,
                searchByMaxAmount,
                pageable
        );

        return ResponseEntity.ok(donations);
    }

    private Date parseDate(String dateStr) {
        if (dateStr == null || dateStr.isEmpty()) {
            return null;
        }
        try {
            LocalDate localDate = LocalDate.parse(dateStr, DateTimeFormatter.ISO_DATE);
            return Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
        } catch (DateTimeParseException e) {
            return null; // Handle invalid date format gracefully
        }
    }


}

