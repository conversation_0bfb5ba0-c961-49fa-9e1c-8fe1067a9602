package ma.almobadara.backend.enumeration.EntitiesToExport;

import lombok.Getter;

@Getter
public enum FamilyExportHeaders {
    CODE("Code"),
    DATE_CREATION("Date de création"),
    FAMILY_NAME("Nom de la famille"),
    TUTOR_NAME("Nom du tuteur"),
    TUTOR_PHONE("Téléphone du tuteur"),
    TUTOR_ADDRESS("Adresse du tuteur"),
    TUTOR_CITY("Ville du tuteur"),
    TUTOR_REGION("Région du tuteur"),
    MEMBER_COUNT("Nombre de membres"),
    BENEFICIARY_COUNT("Nombre de bénéficiaires"),
    SERVICES("Services des bénéficiaires");

    private final String headerName;

    FamilyExportHeaders(String headerName) {
        this.headerName = headerName;
    }

}

