package ma.almobadara.backend.repository.administration;

import ma.almobadara.backend.model.administration.Taggable;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface TaggableRepository extends JpaRepository<Taggable,Long> {
    List<Taggable> findByTaggableIdAndTaggableType(Long taggableId, String taggableType);
    List<Taggable> findByTaggableType(String taggableType);

    // Added for mobile beneficiary service
    default List<Taggable> findByEntityIdAndEntityType(Long entityId, String entityType) {
        return findByTaggableIdAndTaggableType(entityId, entityType);
    }

    @Modifying
    @Transactional
    @Query("DELETE FROM Taggable t WHERE t.taggableId = :taggableId AND t.taggableType = :taggableType")
    void deleteAllByTaggableIdAndTaggableType(Long taggableId, String taggableType);



}
