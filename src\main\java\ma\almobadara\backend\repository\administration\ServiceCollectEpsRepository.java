package ma.almobadara.backend.repository.administration;

import ma.almobadara.backend.model.administration.Eps;
import ma.almobadara.backend.model.administration.Role;
import ma.almobadara.backend.model.administration.ServiceCollectEps;
import ma.almobadara.backend.model.service.Services;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Repository
public interface ServiceCollectEpsRepository extends JpaRepository<ServiceCollectEps, Long> {
   Optional<ServiceCollectEps> findByServiceAndMoisAndAnneeAndIsDeletedNull(Services services, int mois, int annee);
   @Query("SELECT s.code FROM ServiceCollectEps s  ORDER BY s.code DESC LIMIT 1")
   String findLastCode();

   Page<ServiceCollectEps> findByIsDeletedNull(Pageable pageable);
   List<ServiceCollectEps> getByIsDeletedNullAndIsClotureNull();

   @Query("SELECT s FROM ServiceCollectEps s WHERE LOWER(s.nom) LIKE LOWER(CONCAT('%', :query, '%')) OR LOWER(s.commentaire) LIKE LOWER(CONCAT('%', :query, '%'))")
   Page<ServiceCollectEps> searchServiceCollectEps(@Param("query") String query, Pageable pageable);

   @Query("""
SELECT
  CASE
    WHEN s.isCloture = true THEN 'cloturé'
    WHEN  (s.annee > :currentYear OR (s.annee = :currentYear AND s.mois > :currentMonth)) THEN 'planifié'
    WHEN  (s.annee < :currentYear OR (s.annee = :currentYear AND s.mois < :currentMonth)) THEN 'fermé'
    ELSE 'en cours'
  END as status,
  COUNT(s)
FROM ServiceCollectEps s
WHERE s.isDeleted IS NULL
GROUP BY status
""")
   List<Object[]> countServiceCollectionByCustomStatus(
           @Param("currentMonth") int currentMonth,
           @Param("currentYear") int currentYear
   );


}
