package ma.almobadara.backend.repository.administration;

import ma.almobadara.backend.model.administration.Eps;
import ma.almobadara.backend.model.administration.Zone;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface EpsRepository extends JpaRepository<Eps, Long>{
    @NotNull Optional<Eps> findById(@NotNull Long epsId);

    @Query("SELECT e FROM Eps e WHERE LOWER(e.name) LIKE LOWER(CONCAT('%', :query, '%')) OR LOWER(e.comment) LIKE LOWER(CONCAT('%', :query, '%'))")
    Page<Eps> searchEps(@Param("query") String query, Pageable pageable);

    @Query("SELECT e FROM Eps e WHERE e.isDeleted = false ORDER BY e.updateDate DESC")
    Page<Eps> findAllWithDeletedIsFalse(Pageable pageable);

    @Query("SELECT e FROM Eps e WHERE e.isDeleted = false")
    List<Eps> findAllByIsDeletedIsFalse();

    Optional<Eps> findByName(String  name);

    Eps findFirstByOrderByCodeDesc();

    List<Eps> findByStatusTrueAndIsDeletedFalse();

    @Query("SELECT e FROM Eps e WHERE e.status = true AND e.isDeleted = false AND SIZE(e.services) > 0")
    List<Eps> findByStatusTrueAndServicesSizeGreaterThan0();
    
    @Query("SELECT e.status, COUNT(e) FROM Eps e WHERE e.isDeleted = false GROUP BY e.status")
    List<Object[]> countEpsByStatus();


}
