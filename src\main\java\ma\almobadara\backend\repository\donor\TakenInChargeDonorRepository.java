package ma.almobadara.backend.repository.donor;

import ma.almobadara.backend.model.donor.Donor;
import ma.almobadara.backend.model.takenInCharge.TakenInChargeDonor;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Set;

@Repository
public interface TakenInChargeDonorRepository extends JpaRepository<TakenInChargeDonor, Long> {

    List<TakenInChargeDonor> findByDonor(Donor donor);
    List<TakenInChargeDonor> findByTakenInChargeId(Long takenInChargeId);
    List<TakenInChargeDonor> findByTakenInChargeIdIn(Set<Long> takenInChargeIds);
    List<TakenInChargeDonor> findByDonorId(Long donorId);
    @Modifying
    @Transactional

    @Query("UPDATE TakenInChargeDonor t SET t.donor.id = :newDonorId WHERE t.donor.id = :oldDonorId")
    void updateDonorId(@Param("oldDonorId") Long oldDonorId, @Param("newDonorId") Long newDonorId);
    // we need to get the donor balance by donor id and service id by default the status of budgetLine should soule be the "DISPONIBLE"
    @Query("SELECT COALESCE(SUM(bl.amount), 0) " +
            "FROM Donor d " +
            "JOIN d.donations don " +
            "JOIN don.budgetLines bl " +
            "WHERE d.id = :donorId " +
            "AND bl.service.id = :serviceId " +
            "AND bl.status = 'DISPONIBLE'")
    Double findAvailableBalanceByDonorAndService(@Param("donorId") Long donorId, @Param("serviceId") Long serviceId);

    //i nedd a funtions that retuen a boolean if the si ther a takeinchargeOpeartion by takeinchargeDOnor id if the is at leat one of thsoperation that is not executed
    @Query("SELECT CASE WHEN COUNT(tio) > 0 THEN true ELSE false END " +
            "FROM TakenInChargeDonor tid " +
            "JOIN tid.takenInChargeOperations tio " +
            "WHERE tid.id = :takenInChargeDonorId " +
            "AND tio.executionDate IS NULL")
    Boolean hasNotExecutedOperations(@Param("takenInChargeDonorId") Long takenInChargeDonorId);


}
