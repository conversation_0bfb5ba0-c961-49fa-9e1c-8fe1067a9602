package ma.almobadara.backend.controller.communs;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.communs.ActionAndEntityDto;
import ma.almobadara.backend.dto.communs.ActionDTO;
import ma.almobadara.backend.dto.communs.ActionModuleDTO;
import ma.almobadara.backend.dto.donor.DonorDTO;
import ma.almobadara.backend.exceptions.FunctionalException;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.service.communs.ActionService;
import ma.almobadara.backend.service.notification.NotificationService;
import org.springframework.data.domain.Page;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping("/actions")
public class ActionController {

    private final ActionService actionService;

    @PostMapping
    @Operation(summary = "Create a Action and Assign to Entity", tags = {"SIG"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = ActionDTO.class))))})
    public ResponseEntity<ActionDTO> createActionAndAssignToEntity(@RequestBody @Valid ActionAndEntityDto actionAndEntityDto) throws TechnicalException {
        log.info("Start resource Create Action and Assign to Entity {}",  actionAndEntityDto);

        ActionDTO created = actionService.addActionAndAssignToEntity(actionAndEntityDto);


        log.info("End resource Create Action and Assign to Entity {}", actionAndEntityDto);
        return new ResponseEntity<>(created, HttpStatus.OK);
    }

    @GetMapping("/{entityType}/{entityId}")
    @Operation(summary = "Get All Actions By Entity", description = "get all Actions associated with a specific entity", tags = {"SIG"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = ActionDTO.class))))})
    public ResponseEntity<List<ActionDTO>> getAllActionsByEntity(
            @PathVariable String entityType,
            @PathVariable Long entityId) throws TechnicalException {
        log.info("Start resource Get All Actions By Entity");

        List<ActionDTO> actionDTOList = actionService.getAllActionsByEntity(entityType, entityId);

        log.info("End resource Get All Actions By Entity");
        return new ResponseEntity<>(actionDTOList, new HttpHeaders(), HttpStatus.OK);
    }

    @GetMapping("/all")
    public ResponseEntity<List<ActionAndEntityDto>> getAllActionsByUserConnected(
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(defaultValue = "5") Integer size) {
        List<ActionAndEntityDto> allActionsByUserConnected = actionService.getAllActionsByUserConnected(page, size);
        return new ResponseEntity<>(allActionsByUserConnected, HttpStatus.OK);
    }

    @DeleteMapping(value = "/{entityType}/{idAction}", headers = "Accept=application/json")
    @Operation(summary = "Delete action", description = "delete action", tags = {"SIG"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = ActionDTO.class))))})

    public ResponseEntity<Void> deleteAction(@PathVariable Long idAction, @PathVariable String entityType) throws TechnicalException {
        log.info("Start resource delete Action {}", idAction);
        actionService.deleteAction(idAction, entityType);
        log.info("End resource delete Action {}", "");
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @GetMapping("/{userId}")
    @Operation(summary = "Get All Actions for User", tags = {"SIG"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = ActionDTO.class))))})
    public ResponseEntity<List<ActionDTO>> getAllActionsByUser(@PathVariable Long userId) throws FunctionalException {
        log.info("Start resource Get All Actions for User with ID: {}", userId);
        List<ActionDTO> userActions = actionService.getAllActionsBy(userId);
        log.info("End resource Get All Actions for User with ID: {}", userId);
        return new ResponseEntity<>(userActions, HttpStatus.OK);
    }

    @GetMapping("/allModuls")
    @Operation(summary = "Get All Actions of all entities", description = "get all Actions of all entities", tags = {"SIG"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = ActionModuleDTO.class))))})
    public ResponseEntity<Page<ActionAndEntityDto>> getAllActionsWithModules(@RequestParam(defaultValue = "0") int page,
                                                                             @RequestParam(defaultValue = "10") int size,
                                                                             @RequestParam(required = false) String searchByValue,
                                                                             @RequestParam(required = false) String searchByModule,
                                                                             @RequestParam(required = false) Long searchByStatus,
                                                                             @RequestParam(required = false) String searchByUser,
                                                                             @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date minDate,
                                                                             @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date maxDate) throws TechnicalException {
        log.info("Start resource Get All Actions of all entities");

        Page<ActionAndEntityDto> actionModelDTOList = actionService.getAllActionsWithModules(page, size, searchByValue, searchByModule, searchByStatus, searchByUser, minDate, maxDate);

        log.info("End resource Get All Actions of all entities");
        return new ResponseEntity<>(actionModelDTOList, new HttpHeaders(), HttpStatus.OK);
    }


    @GetMapping("/send-reminder-emails")
    @Operation(summary = "Send reminder emails for actions", description = "Send reminder emails for actions with a deadline of tomorrow", tags = {"SIG"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Reminder emails sent successfully"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<Object> sendReminderEmailsForActions() {
        log.debug("Start resource sendReminderEmailsForActions");
        try {
            actionService.sendReminderEmailsForActions();
            return ResponseEntity.ok(Boolean.TRUE);
        } catch (RuntimeException e) {
            String errorMessage = e.getMessage();
            log.error(errorMessage, e);
            // Retournez un message d'erreur détaillé avec un code d'erreur 500
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorMessage);
        }
    }


}
