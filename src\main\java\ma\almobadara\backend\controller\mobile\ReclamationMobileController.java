package ma.almobadara.backend.controller.mobile;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.reclamation.ReclamationDTO;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.service.reclamation.ReclamationService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static ma.almobadara.backend.service.administration.CacheAdUserService.logUserInfo;

@RestController
@RequestMapping("/mobile/reclamation")
@CrossOrigin(origins = "*")
@RequiredArgsConstructor
@Slf4j
public class ReclamationMobileController {

    private final ReclamationService reclamationService;

    /**
     * Create a new reclamation
     * @param reclamationDTO The reclamation data
     * @return The created reclamation
     */
    @PostMapping
    @Operation(summary = "Create a new reclamation", description = "Creates a new reclamation with the provided data", tags = {"Mobile"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "Reclamation created successfully",
                    content = @Content(schema = @Schema(implementation = ReclamationDTO.class))),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ReclamationDTO> createReclamation(@Valid @RequestBody ReclamationDTO reclamationDTO) {
        logUserInfo("mobile_createReclamation", "title: " + reclamationDTO.getTitle());

        try {
            ReclamationDTO createdReclamation = reclamationService.createReclamation(reclamationDTO);
            log.info("End resource mobile_createReclamation. Created reclamation with ID: {}, OK", createdReclamation.getId());
            return new ResponseEntity<>(createdReclamation, new HttpHeaders(), HttpStatus.CREATED);
        } catch (Exception e) {
            log.error("End resource mobile_createReclamation. KO: {}", e.getMessage());
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get all reclamations with pagination
     * @param page Page number
     * @param size Page size
     * @return Page of reclamations
     */
    @GetMapping
    @Operation(summary = "Get all reclamations", description = "Returns a paginated list of all reclamations", tags = {"Mobile"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved reclamations"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<Page<ReclamationDTO>> getAllReclamations(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        logUserInfo("mobile_getAllReclamations", "page: " + page + ", size: " + size);

        try {
            Pageable pageable = PageRequest.of(page, size, Sort.by("createdAt").descending());
            Page<ReclamationDTO> reclamations = reclamationService.getAllReclamations(pageable);
            log.info("End resource mobile_getAllReclamations. Retrieved reclamations: {}, OK", reclamations.getTotalElements());
            return new ResponseEntity<>(reclamations, HttpStatus.OK);
        } catch (Exception e) {
            log.error("End resource mobile_getAllReclamations. KO: {}", e.getMessage());
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get a reclamation by ID
     * @param id The reclamation ID
     * @return The reclamation
     */
    @GetMapping("/{id}")
    @Operation(summary = "Get a reclamation by ID", description = "Returns a single reclamation by ID", tags = {"Mobile"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved reclamation",
                    content = @Content(schema = @Schema(implementation = ReclamationDTO.class))),
            @ApiResponse(responseCode = "404", description = "Reclamation not found"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ReclamationDTO> getReclamationById(@PathVariable Long id) {
        logUserInfo("mobile_getReclamationById", "id: " + id);

        try {
            ReclamationDTO reclamation = reclamationService.getReclamationById(id);
            log.info("End resource mobile_getReclamationById. Retrieved reclamation ID: {}, OK", id);
            return new ResponseEntity<>(reclamation, HttpStatus.OK);
        } catch (TechnicalException e) {
            log.error("End resource mobile_getReclamationById. KO: {}", e.getMessage());
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        } catch (Exception e) {
            log.error("End resource mobile_getReclamationById. KO: {}", e.getMessage());
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Update a reclamation
     * @param reclamationDTO The updated reclamation data with ID
     * @return The updated reclamation
     */
    @PostMapping("/update")
    @Operation(summary = "Update a reclamation", description = "Updates an existing reclamation with the provided data", tags = {"Mobile"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Reclamation updated successfully",
                    content = @Content(schema = @Schema(implementation = ReclamationDTO.class))),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "404", description = "Reclamation not found"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ReclamationDTO> updateReclamation(@Valid @RequestBody ReclamationDTO reclamationDTO) {
        Long id = reclamationDTO.getId();
        logUserInfo("mobile_updateReclamation", "id: " + id);

        if (id == null) {
            log.error("End resource mobile_updateReclamation. KO: ID is required");
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        }

        try {
            ReclamationDTO updatedReclamation = reclamationService.updateReclamation(id, reclamationDTO);
            log.info("End resource mobile_updateReclamation. Updated reclamation ID: {}, OK", id);
            return new ResponseEntity<>(updatedReclamation, HttpStatus.OK);
        } catch (TechnicalException e) {
            log.error("End resource mobile_updateReclamation. KO: {}", e.getMessage());
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        } catch (Exception e) {
            log.error("End resource mobile_updateReclamation. KO: {}", e.getMessage());
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Delete a reclamation
     * @param id The reclamation ID
     * @return No content
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "Delete a reclamation", description = "Deletes a reclamation by ID", tags = {"Mobile"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204", description = "Reclamation deleted successfully"),
            @ApiResponse(responseCode = "404", description = "Reclamation not found"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<Void> deleteReclamation(@PathVariable Long id) {
        logUserInfo("mobile_deleteReclamation", "id: " + id);

        try {
            reclamationService.deleteReclamation(id);
            log.info("End resource mobile_deleteReclamation. Deleted reclamation ID: {}, OK", id);
            return new ResponseEntity<>(HttpStatus.NO_CONTENT);
        } catch (TechnicalException e) {
            log.error("End resource mobile_deleteReclamation. KO: {}", e.getMessage());
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        } catch (Exception e) {
            log.error("End resource mobile_deleteReclamation. KO: {}", e.getMessage());
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get all reclamations by donor ID
     * @param donorId The donor ID
     * @return List of reclamations
     */
    @GetMapping("/donor/{donorId}")
    @Operation(summary = "Get all reclamations by donor ID", description = "Returns a list of all reclamations for a specific donor", tags = {"Mobile"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved reclamations"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<List<ReclamationDTO>> getReclamationsByDonorId(@PathVariable Long donorId) {
        logUserInfo("mobile_getReclamationsByDonorId", "donorId: " + donorId);

        try {
            List<ReclamationDTO> reclamations = reclamationService.getReclamationsByDonorId(donorId);
            log.info("End resource mobile_getReclamationsByDonorId. Retrieved reclamations: {}, OK", reclamations.size());
            return new ResponseEntity<>(reclamations, HttpStatus.OK);
        } catch (Exception e) {
            log.error("End resource mobile_getReclamationsByDonorId. KO: {}", e.getMessage());
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get all reclamations by donor ID with pagination
     * @param donorId The donor ID
     * @param page Page number
     * @param size Page size
     * @return Page of reclamations
     */
    @GetMapping("/donor/{donorId}/page")
    @Operation(summary = "Get all reclamations by donor ID with pagination", description = "Returns a paginated list of all reclamations for a specific donor", tags = {"Mobile"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved reclamations"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<Page<ReclamationDTO>> getReclamationsByDonorIdPaginated(
            @PathVariable Long donorId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        logUserInfo("mobile_getReclamationsByDonorIdPaginated", "donorId: " + donorId + ", page: " + page + ", size: " + size);

        try {
            Pageable pageable = PageRequest.of(page, size, Sort.by("createdAt").descending());
            Page<ReclamationDTO> reclamations = reclamationService.getReclamationsByDonorId(donorId, pageable);
            log.info("End resource mobile_getReclamationsByDonorIdPaginated. Retrieved reclamations: {}, OK", reclamations.getTotalElements());
            return new ResponseEntity<>(reclamations, HttpStatus.OK);
        } catch (Exception e) {
            log.error("End resource mobile_getReclamationsByDonorIdPaginated. KO: {}", e.getMessage());
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Search reclamations by title
     * @param keyword The search keyword
     * @param page Page number
     * @param size Page size
     * @return Page of reclamations
     */
    @GetMapping("/search")
    @Operation(summary = "Search reclamations by title", description = "Returns a paginated list of reclamations matching the search keyword", tags = {"Mobile"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved reclamations"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<Page<ReclamationDTO>> searchReclamationsByTitle(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        logUserInfo("mobile_searchReclamationsByTitle", "keyword: " + keyword + ", page: " + page + ", size: " + size);

        try {
            Pageable pageable = PageRequest.of(page, size, Sort.by("createdAt").descending());
            Page<ReclamationDTO> reclamations = reclamationService.searchReclamationsByTitle(keyword, pageable);
            log.info("End resource mobile_searchReclamationsByTitle. Retrieved reclamations: {}, OK", reclamations.getTotalElements());
            return new ResponseEntity<>(reclamations, HttpStatus.OK);
        } catch (Exception e) {
            log.error("End resource mobile_searchReclamationsByTitle. KO: {}", e.getMessage());
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
