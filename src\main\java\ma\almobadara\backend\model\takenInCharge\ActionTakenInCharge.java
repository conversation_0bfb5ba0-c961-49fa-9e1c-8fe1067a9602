package ma.almobadara.backend.model.takenInCharge;

import jakarta.persistence.*;
import lombok.*;
import ma.almobadara.backend.model.communs.Action;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@IdClass(ActionTakenInChargeId.class)
public class ActionTakenInCharge {

    @Id
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "taken_in_charge_id")
    private TakenInCharge takenInCharge;

    @Id
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "action_id")
    private Action action;

}
