package ma.almobadara.backend.service.migration;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.administration.AssistantDTO;
import ma.almobadara.backend.dto.administration.EpsDto;
import ma.almobadara.backend.dto.administration.ZoneDTO;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.service.administration.AssistantService;
import ma.almobadara.backend.service.administration.EpsService;
import ma.almobadara.backend.service.administration.ZoneService;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDate;
import java.time.ZoneId;

@Service
@RequiredArgsConstructor
@Slf4j
public class AssistantZoneMigrationService {

    private final ZoneService zoneService;
    private final AssistantService assistantService;
    private final EpsService epsService;

    public void migrateZoneAssistant(MultipartFile file) throws TechnicalException {
        log.info("Request to migrateZoneAssistant : {}", file);
        try (Workbook workbook = new XSSFWorkbook(file.getInputStream())) {
            Sheet sheet = workbook.getSheetAt(0);
            for (Row row : sheet) {
                if (row.getRowNum() == 0) continue;
                if (row.getCell(0) == null || row.getCell(0).getCellType() == CellType.BLANK) {
                    break;
                }
                Long epsId = null;
                String epsName = getCellValue(row, 4);

                if (epsName != null && epsName.toLowerCase().startsWith("eps")) {
                    EpsDto eps = createEpsDTO(row);
                    EpsDto savedEps = epsService.createEps(eps);
                    epsId = savedEps.getId();
                }

                ZoneDTO zone = createZoneDTO(row, epsId);
                ZoneDTO savedZone = zoneService.createZone(zone);

                AssistantDTO assistant = createAssistantDTO(row, savedZone.getId());
                assistantService.createAssistant(assistant);
            }
        } catch (Exception e) {
            log.error("Error during migration: {}", e.getMessage());
            throw new TechnicalException("Error during migration: " + e.getMessage());
        }
    }


    private EpsDto createEpsDTO(Row row) {
        EpsDto eps = new EpsDto();
        eps.setName(getCellValue(row, 4));
        eps.setNameAr(getCellValue(row, 5));
        eps.setAddress("À compléter");
        eps.setAddressAr("قيد الإكمال");
        return eps;
    }

    private ZoneDTO createZoneDTO(Row row, Long epsId) {
        ZoneDTO zone = new ZoneDTO();
        zone.setEpsId(epsId);
        zone.setName(getCellValue(row, 4));
        zone.setNameAr(getCellValue(row, 5));
        zone.setCode(zoneService.generateZoneCode());
        zone.setOldZone(true);
        return zone;
    }

    private AssistantDTO createAssistantDTO(Row row, Long zoneId) throws TechnicalException {
        AssistantDTO assistant = new AssistantDTO();
        assistant.setZoneId(zoneId);
        String fullName = getCellValue(row, 1);
        if (fullName != null) {
            String[] nameParts = fullName.split(" ", 2);
            assistant.setFirstName(nameParts[0]);
            assistant.setLastName(nameParts.length > 1 ? nameParts[1] : "");
        }
        assistant.setCinNumber(getCellValue(row, 3));
        assistant.setEmail(getCellValue(row, 6));
        assistant.setDateAffectationToZone(getDateCellValue(row));
        assistant.setPhone(getCellValue(row, 10));
        assistant.setOldAssistant(true);
        return assistant;
    }

    private String getCellValue(Row row, int cellIndex) {
        try {
            return row.getCell(cellIndex) != null ? row.getCell(cellIndex).getStringCellValue().trim() : null;
        } catch (Exception e) {
            log.error("Error reading cell at row {} and column {}: {}", row.getRowNum(), cellIndex, e.getMessage());
            return null;
        }
    }

    private LocalDate getDateCellValue(Row row) throws TechnicalException {
        try {
            if (row.getCell(7) == null || row.getCell(7).getCellType() == CellType.BLANK) {
                return null;
            }
            return row.getCell(7).getDateCellValue() != null
                    ? row.getCell(7).getDateCellValue().toInstant().atZone(ZoneId.systemDefault()).toLocalDate()
                    : null;
        } catch (Exception e) {
            String errorMessage = String.format("Error reading date at row %d and column 7: %s",
                    row.getRowNum(), e.getMessage());
            log.error(errorMessage);
            throw new TechnicalException(errorMessage);
        }
    }
}
