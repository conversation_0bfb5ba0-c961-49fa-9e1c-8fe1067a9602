CREATE TABLE service_collect_eps (
    id BIGSERIAL PRIMARY KEY,
    code VA<PERSON>HAR(20) NOT NULL UNIQUE,
    nom VARCHAR(255) NOT NULL,
    service_id BIGINT NOT NULL,
    mois INT NOT NULL,
    annee INT NOT NULL,
    commentaire TEXT,
    is_cloture Boolean DEFAULT FALSE,
    is_deleted Boolean DEFAULT FALSE,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE CASCADE
);

-- Indexation pour accélérer les recherches
CREATE INDEX idx_service_collecte_periode ON service_collect_eps (mois, annee);