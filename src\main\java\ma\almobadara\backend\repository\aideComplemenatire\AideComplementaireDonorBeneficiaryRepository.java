package ma.almobadara.backend.repository.aideComplemenatire;


import ma.almobadara.backend.model.aideComplemenatire.AideComplementaireDonorBeneficiary;
import ma.almobadara.backend.model.beneficiary.Beneficiary;
import ma.almobadara.backend.model.donor.Donor;
import org.apache.commons.lang3.function.Failable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.Set;

@Repository
public interface AideComplementaireDonorBeneficiaryRepository extends JpaRepository<AideComplementaireDonorBeneficiary, Long> {

    List<AideComplementaireDonorBeneficiary> findByDonor(Donor donor);
    List<AideComplementaireDonorBeneficiary>  findAideComplementaireDonorBeneficiariesByAideComplementaire_Id(Long aideComplementaireid);
    List<AideComplementaireDonorBeneficiary>  findAideComplementaireDonorBeneficiariesBybeneficiary_id(Long beneficiareId);
    void deleteAllByAideComplementaire_Id(Long aideComplementaireid);

    @Modifying
    @Transactional

    @Query("UPDATE AideComplementaireDonorBeneficiary a SET a.donor.id = :newDonorId WHERE a.donor.id = :oldDonorId")
    void updateDonorId(@Param("oldDonorId") Long oldDonorId, @Param("newDonorId") Long newDonorId);

    List<AideComplementaireDonorBeneficiary> findAideComplementaireDonorBeneficiariesByDonorIdAndAideComplementaireId(Long donorId, Long aideComplementaireId);

    Optional<AideComplementaireDonorBeneficiary> findByAideComplementaireIdAndBeneficiaryId(Long idAideComplementaire, Long idBeneficiary);
    Optional<AideComplementaireDonorBeneficiary> findByAideComplementaireIdAndDonorIdAndBeneficiaryId(Long idAideComplementaire,Long donorId, Long idBeneficiary);
    Optional<AideComplementaireDonorBeneficiary> findByAideComplementaireIdAndBeneficiaryIdAndDonorIsNull(Long idAideComplementaire, Long idBeneficiary);
    List<AideComplementaireDonorBeneficiary> findByAideComplementaire_IdAndBeneficiaryIsNull(Long idAideComplementaire);
    List<AideComplementaireDonorBeneficiary> findByAideComplementaire_IdAndDonorIsNull(Long idAideComplementaire);
    List<AideComplementaireDonorBeneficiary> findByAideComplementaire_IdAndDonorIsNotNullAndBeneficiaryIsNotNull(Long idAideComplementaire);

    Optional<AideComplementaireDonorBeneficiary> findAideComplementaireDonorBeneficiariesByAideComplementaire_IdAndDonor_Id(Long idAideComplementaire, Long idDonor);
    @Query(value = "SELECT * FROM aide_complementaire_donor_beneficiary acdb " +
            "WHERE acdb.aide_complementaire_id = :idAideComplementaire " +
            "AND acdb.donor_id = :idDonor " +
            "AND acdb.beneficiary_id IS NULL", nativeQuery = true)
    Optional<AideComplementaireDonorBeneficiary> findAideComplementaireDonorBeneficiariesByAideComplementaire_IdAndDonor_IdAndBeneficiaryIsEmpty(
            @Param("idAideComplementaire") Long idAideComplementaire,
            @Param("idDonor") Long idDonor
    );
    
    @Query(value = "SELECT * FROM aide_complementaire_donor_beneficiary acdb " +
            "WHERE acdb.aide_complementaire_id = :idAideComplementaire " +
            "AND acdb.beneficiary_id IS NULL", nativeQuery = true)
    List<AideComplementaireDonorBeneficiary> findAideComplementaireDonorBeneficiariesByAideComplementaire_IdAndBeneficiaryIsEmpty(
            @Param("idAideComplementaire") Long idAideComplementaire
    );

    @Query(value = "SELECT * FROM aide_complementaire_donor_beneficiary acdb " +
            "WHERE acdb.aide_complementaire_id = :idAideComplementaire " +
            "AND acdb.donor_id = :idDonor " +
            "AND acdb.is_nature = :isNature " +
            "AND acdb.beneficiary_id IS NULL", nativeQuery = true)
    Optional<AideComplementaireDonorBeneficiary> findAideComplementaireDonorBeneficiariesByAideComplementaire_IdAndDonor_IdAndBeneficiaryIsEmptyAndIsNature(
            @Param("idAideComplementaire") Long idAideComplementaire,
            @Param("idDonor") Long idDonor,
            @Param("isNature") Boolean isNature
    );


    void deleteAllByAideComplementaire_IdAndDonor_Id(Long id, Long id1);
    List<AideComplementaireDonorBeneficiary> findByAideComplementaire_Id(Long aLong);

    List<AideComplementaireDonorBeneficiary> findByBeneficiaryId(Long beneficiaryId);
    List<AideComplementaireDonorBeneficiary> findByBeneficiaryIdIn(Set<Long> beneficiaryIds);
}
