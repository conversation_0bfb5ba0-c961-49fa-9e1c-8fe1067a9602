package ma.almobadara.backend.dto.administration;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FeatureDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private Long id;
    private String label;
    private String code;
    private Long moduleId;
    private LocalDateTime creationDate;
    private LocalDateTime updateDate;
}

