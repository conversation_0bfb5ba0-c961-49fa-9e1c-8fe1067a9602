package ma.almobadara.backend.controller.communs;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.communs.NoteAndEntityDto;
import ma.almobadara.backend.dto.communs.NoteDTO;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.service.communs.NoteService;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping("/note")
public class NoteController {

    private final NoteService noteService;

    @PostMapping
    @Operation(summary = "Create a Note and Assign to Entity", tags = {"note"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = NoteDTO.class))))})
    public ResponseEntity<NoteDTO> createNoteAndAssignToEntity(@RequestBody NoteAndEntityDto noteAndEntityDto) throws TechnicalException {
        log.info("Start resource createNoteAndAssignToEntity {}",  noteAndEntityDto);

        NoteDTO created = noteService.addNoteAndAssignToEntity(noteAndEntityDto);

         log.info("End resource createNoteAndAssignToEntity with ID {}", created.getId());
        return new ResponseEntity<>(created, HttpStatus.OK);
    }

    @GetMapping("/{entityType}/{entityId}")
    @Operation(summary = "Get All Notes By Entity", description = "get all Notes associated with a specific entity", tags = {"note"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = NoteDTO.class))))})
    public ResponseEntity<List<NoteDTO>> getAllNotesByEntity(
            @PathVariable String entityType,
            @PathVariable Long entityId) throws TechnicalException {
        log.info("Start resource getAllNotesByEntity with entityType {} and entityId {}", entityType, entityId);

        List<NoteDTO> documentDTOList = noteService.getAllNotesByEntity(entityType, entityId);

        log.info("End resource getAllNotesByEntity with size {}", documentDTOList.size());
        return new ResponseEntity<>(documentDTOList, new HttpHeaders(), HttpStatus.OK);
    }

   @DeleteMapping(value = "/{entityType}/{idNote}", headers = "Accept=application/json")
    @Operation(summary = "Delete note", description = "delete note", tags = {"note"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = NoteDTO.class))))})

    public ResponseEntity<Void> deleteNote(@PathVariable Long idNote, @PathVariable String entityType) throws TechnicalException {
        log.info("Start resource deleteNote {}", idNote);
        noteService.deleteNote(idNote, entityType);
        log.info("End resource deleteNote {}", idNote);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

}
