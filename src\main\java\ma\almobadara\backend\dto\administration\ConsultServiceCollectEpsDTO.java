package ma.almobadara.backend.dto.administration;

import lombok.Getter;
import lombok.Setter;
import ma.almobadara.backend.dto.service.ServiceLightDTO;
import ma.almobadara.backend.model.administration.Eps;
import ma.almobadara.backend.model.service.Services;

@Getter
@Setter
public class ConsultServiceCollectEpsDTO {
    private Long serviceId;
    private ServiceLightDTO serviceLightDTO;
    private int mois;
    private int annee;
    private String nom;
    private String code;
    private Long id;
    private EpsLightDTO eps;
    private String commentaire;
    private Services services;
    private Boolean isCloture;
    private Boolean isContainDons;


}
