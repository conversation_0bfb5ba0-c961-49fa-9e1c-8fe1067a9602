package ma.almobadara.backend.dto.mobile;

import lombok.*;

import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class DocumentRenewMobileDTO {
    private Long id;
    private String nomEntity;      // Name of the entity (beneficiary or family)
    private Long idEntity;         // ID of the entity
    private String module;         // Module type (Beneficiary or Family)
    private String codeEntity;     // Code of the entity
    private String labelDocument;  // Label of the document
    private String typeDocument;   // Type of the document
    private Date expiryDate;       // Expiry date
    private String expiryDateString; // Formatted expiry date
}
