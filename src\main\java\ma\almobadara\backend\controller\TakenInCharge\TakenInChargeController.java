package ma.almobadara.backend.controller.TakenInCharge;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.persistence.EntityNotFoundException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.exportentities.ExportFileDTO;
import ma.almobadara.backend.dto.takenInCharge.OperationListDto;
import ma.almobadara.backend.dto.takenInCharge.TakenInChargeDTO;
import ma.almobadara.backend.dto.takenInCharge.TakenInChargeOperationDTO;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.service.takenInCharge.TakenInChargeService;
import org.apache.poi.openxml4j.exceptions.InvalidOperationException;
import org.springframework.data.domain.Page;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.ErrorResponse;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

import static ma.almobadara.backend.service.administration.CacheAdUserService.logUserInfo;

//@RefreshScope
@RestController
@AllArgsConstructor
@Slf4j
@RequestMapping("/takenInCharges")
public class TakenInChargeController {

    private final TakenInChargeService takenInChargeService;

    @PostMapping(value = "", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    @Operation(summary = "Create a TakenInCharge", description = "add a new TakenInCharge", tags = {"takenInCharges"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = TakenInChargeDTO.class)))),
            @ApiResponse(responseCode = "400", description = "Bad request, validation or constraint error", content = @Content(schema = @Schema(implementation = ErrorResponse.class))),
            @ApiResponse(responseCode = "500", description = "Internal server error", content = @Content(schema = @Schema(implementation = ErrorResponse.class)))
    })
    public ResponseEntity<?> createTakenInCharge(TakenInChargeDTO takenInChargeDTO) {
        logUserInfo("createTakenInCharge", String.valueOf(takenInChargeDTO));

        TakenInChargeDTO created = new TakenInChargeDTO();
        HttpStatus status = HttpStatus.OK;

        try {
            created = takenInChargeService.addTakenInCharge(takenInChargeDTO);
            log.info("End createTakenInCharge with ID {}, OK", created.getId());
        } catch (Exception e) {
            // Handle the exception and return it in the response
            log.error("Error during createTakenInCharge, OK: {}", e.getMessage());
            String errorMessage = e.getMessage();
            status = HttpStatus.BAD_REQUEST;  // You can choose to return 400 for known conflict issues
            return new ResponseEntity<>(errorMessage, new HttpHeaders(), status);
        }

        return new ResponseEntity<>(created, new HttpHeaders(), status);
    }


    @GetMapping(value = "/findByCriteria", produces = {"application/json"})
    @Operation(summary = "Find All Taken In Charges", description = "Find all Taken In Charges", tags = {"takenInCharges"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = TakenInChargeDTO.class))))})
    public ResponseEntity<Page<TakenInChargeDTO>> findAllTakenInCharges(
            @RequestParam Optional<Integer> page,
            @RequestParam Optional<String> criteria,
            @RequestParam Optional<String> value1,
            @RequestParam Optional<String> value2) {
        logUserInfo("findAllTakenInCharges ");

        Page<TakenInChargeDTO> takenInChargeDTOS = null;
        HttpStatus status;
        try {
            takenInChargeDTOS = takenInChargeService.getAllTakenInCharges(criteria, value1, value2, page);
            status = HttpStatus.OK;
            log.info("End resource findAllTakenInCharges with a total of: {}, OK", takenInChargeDTOS.getTotalElements());
        } catch (Exception e) {
            status = HttpStatus.INTERNAL_SERVER_ERROR;
            log.error("End resource findAllTakenInCharges, KO: {}", e.getMessage());
        }

        return new ResponseEntity<>(takenInChargeDTOS, new HttpHeaders(), status);
    }

    @GetMapping(value = "/findAll", produces = {"application/json"})
    @Operation(summary = "Find All Taken In Charges", description = "Find all Taken In Charges", tags = {"takenInCharges"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = TakenInChargeDTO.class))))})
    public ResponseEntity<Page<TakenInChargeDTO>> findAllTakenInChargesByCriteria(
            @RequestParam(defaultValue = "0") final Integer page,
            @RequestParam(defaultValue = "10") final Integer size,
            @RequestParam(required = false) final String searchByNom,
            @RequestParam(required = false) final String lastNameAr,
            @RequestParam(required = false) final String searchByBeneficiaryAr,
            @RequestParam(required = false) final String searchByBeneficiary,
            @RequestParam(required = false) final Long searchByService,
            @RequestParam(required = false) final String searchByStatus,
            @RequestParam(required = false) final Long searchByTagId,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") final Date minStartDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") final Date maxStartDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") final Date minEndDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") final Date maxEndDate)
    {
        logUserInfo("findAllTakenInCharges ");

        Page<TakenInChargeDTO> takenInChargeDTOS = null;
        HttpStatus status;
        try {
            takenInChargeDTOS = takenInChargeService.getAllTakenInChargesByCriteria(page, size, searchByNom, searchByBeneficiary, searchByBeneficiaryAr, lastNameAr,  searchByService, searchByStatus, minStartDate, maxStartDate, minEndDate, maxEndDate,searchByTagId);
            status = HttpStatus.OK;
            log.info("End resource findAllTakenInCharges with a total of: {}, OK", takenInChargeDTOS.getTotalElements());
        } catch (Exception e) {
            status = HttpStatus.INTERNAL_SERVER_ERROR;
            log.error("End resource findAllTakenInCharges, KO: {}", e.getMessage());
        }

        return new ResponseEntity<>(takenInChargeDTOS, new HttpHeaders(), status);
    }

    @Operation(summary = "Find Taken In Charge by ID", description = "Returns a single Taken In Charge", tags = {"takenInCharges"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(schema = @Schema(implementation = TakenInChargeDTO.class))),
            @ApiResponse(responseCode = "404", description = "Taken In Charge not found")})
    @GetMapping(value = "/{idTakenInCharge}", produces = {"application/json"})
    public TakenInChargeDTO getTakenInChargeByID(@PathVariable Long idTakenInCharge) {

        logUserInfo("getTakenInChargeByID ", String.valueOf(idTakenInCharge));

        TakenInChargeDTO takenInChargeDTO = null;
        try {
            takenInChargeDTO = takenInChargeService.getTakenInChargeById(idTakenInCharge);
            log.info("End resource getTakenInChargeByID with ID {}, OK", idTakenInCharge);
        } catch (Exception e) {
            log.error("End resource getTakenInChargeByID with ID {}, KO: {}", idTakenInCharge, e.getMessage());
        }

        return takenInChargeDTO;
    }

    @PostMapping(value = "/plan")
    @Operation(summary = "Plan a TakenInCharge", description = "plan an existing TakenInCharge", tags = {"takenInCharges"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = TakenInChargeOperationDTO.class))))})
    public ResponseEntity<List<TakenInChargeOperationDTO>> planTakenInCharge(@RequestBody List<TakenInChargeOperationDTO> takenInChargeOperationDTOS) {

        logUserInfo("planTakenInCharge ", String.valueOf(takenInChargeOperationDTOS));

        List<TakenInChargeOperationDTO> created = null;
        HttpStatus status;
        try {
            created = takenInChargeService.planTakenInCharge(takenInChargeOperationDTOS);
            status = HttpStatus.OK;
            log.info("End resource Plan Taken In Charge with a total of: {}, OK", created.size());
        } catch (Exception e) {
            status = HttpStatus.INTERNAL_SERVER_ERROR;
            log.error("End resource Plan Taken In Charge, KO: {}", e.getMessage());
        }

        return new ResponseEntity<>(created, new HttpHeaders(), status);
    }

    @PostMapping(value = "/update")
    @Operation(summary = "Update a TakenInCharge", description = "Update an existing TakenInCharge", tags = {"takenInCharges"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = TakenInChargeOperationDTO.class))))})
    public ResponseEntity<TakenInChargeOperationDTO> updateOperation(@RequestBody TakenInChargeOperationDTO takenInChargeOperationDTO) {

        logUserInfo("updateOperation ", String.valueOf(takenInChargeOperationDTO));

        TakenInChargeOperationDTO updatedOperationDTO = new TakenInChargeOperationDTO();
        HttpStatus status;
        try {
            updatedOperationDTO = takenInChargeService.updateOperation(takenInChargeOperationDTO);
            status = HttpStatus.OK;
            log.info("End resource Enclose Taken In Charge with ID {}, OK", takenInChargeOperationDTO.getId());
        } catch (Exception e) {
            status = HttpStatus.INTERNAL_SERVER_ERROR;
            log.error("End resource Enclose Taken In Charge with ID {}, KO: {}", takenInChargeOperationDTO.getId(), e.getMessage());
        }

        return new ResponseEntity<>(updatedOperationDTO, new HttpHeaders(), status);
    }

    /// Reserve operation
    @PostMapping(value = "/reserve")
    @Operation(summary = "Update a TakenInCharge", description = "Update an existing TakenInCharge", tags = {"takenInCharges"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = TakenInChargeOperationDTO.class)))),
            @ApiResponse(responseCode = "500", description = "Internal Server Error", content = @Content(schema = @Schema(implementation = String.class))) // Keeping the error response as String
    })
    public ResponseEntity<?> reserveOperation(@RequestBody TakenInChargeOperationDTO takenInChargeOperationDTO) {

        logUserInfo("reserveOperation ", String.valueOf(takenInChargeOperationDTO));

        TakenInChargeOperationDTO updatedOperationDTO = new TakenInChargeOperationDTO();
        HttpStatus status;
        try {
            updatedOperationDTO = takenInChargeService.reserveOperation(takenInChargeOperationDTO);
            status = HttpStatus.OK;
            log.info("End resource Reserve Taken In Charge with ID {}, OK", takenInChargeOperationDTO.getId());
        } catch (TechnicalException e) {
            status = HttpStatus.INTERNAL_SERVER_ERROR;
            log.error("End resource Reserve Taken In Charge with ID {}, KO: {}", takenInChargeOperationDTO.getId(), e.getMessage());

            // Return a map with detailedMessage directly
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("detailedMessage", e.getMessage());  // This will include the message like "Donor has insufficient balance"
            return new ResponseEntity<>(errorResponse, new HttpHeaders(), status);
        }

        return new ResponseEntity<>(updatedOperationDTO, new HttpHeaders(), status);
    }


    //cancelReservation

    @PostMapping(value = "/cancelReservation")
    @Operation(summary = "Update a TakenInCharge", description = "Update an existing TakenInCharge", tags = {"takenInCharges"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = TakenInChargeOperationDTO.class))))})
    public ResponseEntity<TakenInChargeOperationDTO> cancelReservationOperation(@RequestBody TakenInChargeOperationDTO takenInChargeOperationDTO) {

        logUserInfo("cancelReservationOperation ", String.valueOf(takenInChargeOperationDTO));

            TakenInChargeOperationDTO updatedOperationDTO = new TakenInChargeOperationDTO();
            HttpStatus status;
            try {
                updatedOperationDTO = takenInChargeService.cancelReservationOperation(takenInChargeOperationDTO);
                status = HttpStatus.OK;
                log.info("End resource Cancel Reservation Taken In Charge with ID {}, OK", takenInChargeOperationDTO.getId());
            } catch (Exception e) {
                status = HttpStatus.INTERNAL_SERVER_ERROR;
                log.error("End resource Cancel Reservation Taken In Charge with ID {}, KO: {}", takenInChargeOperationDTO.getId(), e.getMessage());
            }

            return new ResponseEntity<>(updatedOperationDTO, new HttpHeaders(), status);
    }

    ///cancelExecution
    @PostMapping(value = "/cancelExecution")
    @Operation(summary = "Update a TakenInCharge", description = "Update an existing TakenInCharge", tags = {"takenInCharges"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = TakenInChargeOperationDTO.class))))})

    public ResponseEntity<TakenInChargeOperationDTO> cancelExecutionOperation(@RequestBody TakenInChargeOperationDTO takenInChargeOperationDTO) {

                logUserInfo("cancelExecutionOperation ", String.valueOf(takenInChargeOperationDTO));

                TakenInChargeOperationDTO updatedOperationDTO = new TakenInChargeOperationDTO();
                HttpStatus status;
                try {
                    updatedOperationDTO = takenInChargeService.cancelExecutionOperation(takenInChargeOperationDTO);
                    status = HttpStatus.OK;
                    log.info("End resource Cancel Execution Taken In Charge with ID {}, OK", takenInChargeOperationDTO.getId());
                } catch (Exception e) {
                    status = HttpStatus.INTERNAL_SERVER_ERROR;
                    log.error("End resource Cancel Execution Taken In Charge with ID {}, KO: {}", takenInChargeOperationDTO.getId(), e.getMessage());
                }

                return new ResponseEntity<>(updatedOperationDTO, new HttpHeaders(), status);
    }

    @PostMapping(value = "/enclose")
    @Operation(summary = "Enclose a TakenInCharge", description = "Enclose an existing TakenInCharge", tags = {"takenInCharges"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = TakenInChargeOperationDTO.class))))})
    public ResponseEntity<TakenInChargeOperationDTO> encloseOperation(@RequestBody TakenInChargeOperationDTO takenInChargeOperationDTO) {

        logUserInfo("encloseOperation ", String.valueOf(takenInChargeOperationDTO));

        TakenInChargeOperationDTO enclosedOperationDTO = new TakenInChargeOperationDTO();
        HttpStatus status;
        try {
            enclosedOperationDTO = takenInChargeService.encloseOperation(takenInChargeOperationDTO);
            status = HttpStatus.OK;
            log.info("End resource Enclose Taken In Charge with code {}, OK", takenInChargeOperationDTO.getCode());
        } catch (Exception e) {
            status = HttpStatus.INTERNAL_SERVER_ERROR;
            log.error("End resource Enclose Taken In Charge with code {}, KO: {}", takenInChargeOperationDTO.getCode(), e.getMessage());
        }

        return new ResponseEntity<>(enclosedOperationDTO, new HttpHeaders(), status);
    }

    @DeleteMapping(value = "delete/{idOperation}", produces = {"application/json"})
    @Operation(summary = "Delete operation", description = "delete an existing operation of taken in charge", tags = {"takenInCharges"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = TakenInChargeOperationDTO.class))))})
    public ResponseEntity<Void> deleteOperation(@PathVariable Long idOperation) {
        logUserInfo("deleteOperation ", String.valueOf(idOperation));
        try {
            takenInChargeService.deleteOperation(idOperation);
            log.info("End resource Delete operation {}, OK", idOperation);
        } catch (Exception e) {
            log.error("End resource Delete operation {}, KO: {}", idOperation, e.getMessage());
        }

        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
    @PostMapping(value = "close/{id}", produces = {"application/json"})
    @Operation(summary = "Close TakenInCharge", description = "Close an existing TakenInCharge operation", tags = {"takenInCharges"})
    public ResponseEntity<Object> closeTakenInCharge(
            @PathVariable Long id,
            @RequestParam String comment,
            @RequestParam Long motifTypeId) {

        logUserInfo("closeTakenInCharge", String.valueOf(id));
        try {
            takenInChargeService.closeTakenInCharge(id, comment, motifTypeId);
            log.info("End resource Close TakenInCharge {}, OK", id);
            return new ResponseEntity<>(HttpStatus.OK);
        } catch (EntityNotFoundException e) {
            log.error("Close TakenInCharge failed, ID {} not found", id);
            Map<String, String> error = Map.of("message", "TakenInCharge not found", "details", e.getMessage());
            return new ResponseEntity<>(error, HttpStatus.NOT_FOUND);
        } catch (InvalidOperationException e) {
            log.error("Close TakenInCharge failed, invalid operation: {}", e.getMessage());
            Map<String, String> error = Map.of("message", "Invalid operation", "details", e.getMessage());
            return new ResponseEntity<>(error, HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            log.error("End resource Close TakenInCharge {}, KO: {}", id, e.getMessage());
            Map<String, String> error = Map.of("message", "Internal server error", "details", e.getMessage());
            return new ResponseEntity<>(error, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }


    @PostMapping(value = "/execute")
    @Operation(summary = "Execute a TakenInCharge", description = "Execute an existing TakenInCharge", tags = {"takenInCharges"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = TakenInChargeOperationDTO.class))))})
    public ResponseEntity<TakenInChargeOperationDTO> executeOperation(@RequestBody TakenInChargeOperationDTO takenInChargeOperationDTO) {

        logUserInfo("executeOperation ", String.valueOf(takenInChargeOperationDTO));

        TakenInChargeOperationDTO executedOperationDTO = new TakenInChargeOperationDTO();
        HttpStatus status;
        try {
            executedOperationDTO = takenInChargeService.executeOperation(takenInChargeOperationDTO);
            status = HttpStatus.OK;
            log.info("End resource Execute Taken In Charge with id: {}, OK", takenInChargeOperationDTO.getId());
        } catch (Exception e) {
            status = HttpStatus.INTERNAL_SERVER_ERROR;
            log.error("End resource Execute Taken In Charge with id: {}, KO: {}", takenInChargeOperationDTO.getId(), e.getMessage());
        }

        return new ResponseEntity<>(executedOperationDTO, new HttpHeaders(), status);
    }


    @DeleteMapping("/{id}")
    public ResponseEntity<String> deleteTakenInCharge(@PathVariable Long id) {
        try {
            takenInChargeService.deleteTakenInCharge(id);
            return ResponseEntity.ok("Prise en charge a été supprimé avec succès");
        } catch (IllegalStateException e) {
            return ResponseEntity.status(HttpStatus.OK).body(e.getMessage());
        } catch (IllegalArgumentException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(e.getMessage());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Une erreur s'est produite");
        }
    }


    @PostMapping(value = "/batch-action")
    @Operation(summary = "Batch operation on TakenInCharge", description = "Perform batch operations on multiple TakenInCharge items", tags = {"takenInCharges"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(array = @ArraySchema(schema = @Schema(implementation = TakenInChargeOperationDTO.class)))),
            @ApiResponse(responseCode = "500", description = "Internal Server Error", content = @Content(schema = @Schema(implementation = String.class)))})
    public ResponseEntity<?> handleBatchOperation(@RequestBody Map<String, Object> request) {

        @SuppressWarnings("unchecked")
        List<Integer> operationIdsAsIntegers = (List<Integer>) request.get("operationIds");
        List<Long> operationIds = operationIdsAsIntegers.stream().map(Long::valueOf).collect(Collectors.toList());
        String actionType = (String) request.get("actionType");

        logUserInfo("handleBatchOperation ", "operationIds: " + operationIds + ", actionType: " + actionType);

        List<TakenInChargeOperationDTO> updatedOperations;
        HttpStatus status;
        try {
            updatedOperations = takenInChargeService.handleBatchOperation(operationIds, actionType);
            status = HttpStatus.OK;
            log.info("End resource Batch Operation with action type: {}, OK", actionType);
        } catch (TechnicalException e) {
            status = HttpStatus.INTERNAL_SERVER_ERROR;
            log.error("End resource Batch Operation with action type: {}, KO: {}", actionType, e.getMessage());

            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("detailedMessage", e.getMessage());
            return new ResponseEntity<>(errorResponse, new HttpHeaders(), status);
        }

        return new ResponseEntity<>(updatedOperations, new HttpHeaders(), status);
    }

    @GetMapping("/operations")
    public ResponseEntity<Page<OperationListDto>> getAllOperations(
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String searchByNameDonor,
            @RequestParam(required = false) String searchByNameDonorAr,
            @RequestParam(required = false) String searchByBeneficiary,
            @RequestParam(required = false) String searchByNameBeneficiaryAr,
            @RequestParam(required = false) String searchByStatusOperation,
            @RequestParam(required = false) Long searchByServiceId,
            @RequestParam(required = false) Long searchByStatusId,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date minPlanningDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date maxPlanningDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date minExecutionDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date maxExecutionDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date minClosureDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date maxClosureDate) {

        Page<OperationListDto> operations = takenInChargeService.getAllOperations(page, size, searchByNameDonor, searchByNameDonorAr, searchByBeneficiary,searchByNameBeneficiaryAr,
                searchByStatusOperation,searchByServiceId,searchByStatusId,minPlanningDate, maxPlanningDate, minExecutionDate,
                maxExecutionDate, minClosureDate, maxClosureDate);
        System.out.println(operations);
        return new ResponseEntity<>(operations, HttpStatus.OK);
    }

    @GetMapping(value = "/csv", produces = {"application/json"})
    public ResponseEntity<ExportFileDTO>  exportTakenIchargeToCSV(
            @RequestParam(required = false) final String searchByNom,
            @RequestParam(required = false) final String lastNameAr,
            @RequestParam(required = false) final String searchByBeneficiaryAr,
            @RequestParam(required =false) final String searchByBeneficiary,
            @RequestParam(required = false) final Long searchByService,
            @RequestParam(required = false) final String searchByStatus,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") final Date minStartDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") final Date maxStartDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") final Date minEndDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") final Date maxEndDate) {
        logUserInfo("exportTakenIchargeToCSV ");
        HttpStatus status;
        try {
            ExportFileDTO exportFileDTO = takenInChargeService.exportFileWithName(searchByNom, lastNameAr, searchByBeneficiaryAr, searchByBeneficiary, searchByService, searchByStatus, minStartDate, maxStartDate, minEndDate, maxEndDate);
            status = HttpStatus.OK;
            log.info("End resource exportTakenIchargeToCSV, OK");
            return new ResponseEntity<>(exportFileDTO, status);
        } catch (Exception ex) {
            log.error("End resource exportTakenIchargeToCSV, KO: {}", ex.getMessage());
            status = HttpStatus.INTERNAL_SERVER_ERROR;
            return new ResponseEntity<>(null, status);
        }
    }

    // get Opearation by take in charge id
    @GetMapping(value = "/operations/{id}", produces = {"application/json"})
    public ResponseEntity<List<TakenInChargeOperationDTO>> getOperationsByTakenInChargeId(@PathVariable Long id) {
        List<TakenInChargeOperationDTO> operations = takenInChargeService.getOperationsByTakenInChargeId(id);
        return new ResponseEntity<>(operations, HttpStatus.OK);

    }


}
