package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.service.AddServicesDTO;
import ma.almobadara.backend.dto.service.ServicesDTO;
import ma.almobadara.backend.model.service.Services;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface ServicesMapper {

    @Mapping(target = "epsId", source = "eps.id")
    ServicesDTO toDto(Services services);


    @Mapping(target = "eps", ignore = true)
    Services toEntity(ServicesDTO servicesDTO);


    @Mapping(target = "eps.id", source = "epsId")
    Services AddDtotoEntity(AddServicesDTO addServicesDTO);
}
