package ma.almobadara.backend.repository.beneficiary;

import ma.almobadara.backend.dto.dashboard.ActiveKafalatesByMonthAndAge;
import ma.almobadara.backend.dto.dashboard.ActiveKafalatesByMonthAndCityDTO;
import ma.almobadara.backend.dto.dashboard.ActiveKafalatesByMonthAndTypeDTO;
import ma.almobadara.backend.model.administration.Eps;
import ma.almobadara.backend.model.administration.Zone;
import ma.almobadara.backend.model.beneficiary.Beneficiary;
import ma.almobadara.backend.model.beneficiary.BeneficiaryStatut;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.Set;

@Repository
public interface BeneficiaryRepository extends JpaRepository<Beneficiary, Long> {


    @Query("SELECT count(b) FROM Beneficiary b WHERE b.archived IS NULL or b.archived = false and b.independent = true")
    long countBeneficiaryIndependent();
    @Query("SELECT count(b) FROM Beneficiary b WHERE b.archived IS NULL or b.archived = false and b.independent = false")
    long countBeneficiaryNotIndependent();

    @Query(value = """
    SELECT
        TO_CHAR(created_at, 'YYYY-MM') AS month,
        COUNT(*) AS count
    FROM beneficiary
    WHERE created_at >= CURRENT_DATE - INTERVAL '12 months' and archived IS NULL or archived = false
    GROUP BY TO_CHAR(created_at, 'YYYY-MM')
    ORDER BY month ASC
    """, nativeQuery = true)
    List<Object[]> countDonorsByMonthLastYear();
    @Query("SELECT count(b) , b.zone.name FROM Beneficiary b WHERE b.archived = false OR b.archived IS NULL group by b.zone.name order by count(b) desc")
    List<Object[]> countBeneficiariesGroupedByZone();
    @Query("SELECT count(b), b.beneficiaryStatut FROM Beneficiary b WHERE (b.archived = false OR b.archived IS NULL) AND b.beneficiaryStatut.nameStatut != 'beneficiary_ad_hoc_individual' GROUP BY b.beneficiaryStatut")
    List<Object[]> countBeneficiariesGroupedByType();


    @Query("SELECT count(b) FROM Beneficiary b WHERE (b.archived = false OR b.archived IS NULL) AND b.beneficiaryStatut.nameStatut = 'beneficiary_ad_hoc_individual'")
    long countBeneficiaryAdHocIndividual();
    @Query("SELECT count(b) , b.person.sex FROM Beneficiary b WHERE b.archived = false OR b.archived IS NULL group by b.person.sex")
    List<Object[]> countBeneficiariesGroupedBySex();

    @Query("SELECT count(b) , b.person.categoryBeneficiaryId FROM Beneficiary b WHERE b.archived = false OR b.archived IS NULL group by b.person.categoryBeneficiaryId")
    List<Object[]> countBeneficiariesGroupedByCategory();

    @Query("SELECT count(b) FROM Beneficiary b WHERE b.archived IS NULL or b.archived = false")
    long countBeneficiary();

    @Query("SELECT b FROM Beneficiary b WHERE LOWER(b.person.firstName) LIKE LOWER(CONCAT('%', :query, '%')) OR LOWER(b.person.lastName) LIKE LOWER(CONCAT('%', :query, '%'))  OR LOWER(b.person.email) LIKE LOWER(CONCAT('%', :query, '%')) OR LOWER(b.person.phoneNumber) LIKE LOWER(CONCAT('%', :query, '%')) OR LOWER(b.code) LIKE LOWER(CONCAT('%', :query, '%'))")
    Page<Beneficiary> searchBeneficiaries(@Param("query") String query, Pageable pageable);

    @Query(value = "SELECT b from Beneficiary b where b.archived = false and b.beneficiaryStatut.id IN (6,10,13)")
    Page<Beneficiary> findBeneficiaryByArchivedIsFalse(Pageable pageable);

    @Query(value = "SELECT b from Beneficiary b where b.archived = false and b.beneficiaryStatut.id IN (6,10,13)")
    Page<Beneficiary> findBeneficiaryByArchivedIsFalseAndZoneId(Pageable pageable, Long zoneId);

    @Query(value = "SELECT b from Beneficiary b where b.beneficiaryStatut.id IN (11,12)")
    Page<Beneficiary> findBeneficiaryByBeneficiaryStatut(Pageable pageable);

    @Query("SELECT b FROM Beneficiary b WHERE b.beneficiaryStatut.id IN (11, 12) AND b.id NOT IN (SELECT bg.id FROM BeneficiaryAdHocGroup g JOIN g.beneficiaries bg)")
    List<Beneficiary> findBeneficiariesWithoutAdHocGroup();

    @Query(value = "SELECT b from Beneficiary b where b.beneficiaryStatut.id IN (11)")
    List<Beneficiary> findBeneficiaryByBeneficiaryStatutId();

    boolean existsByCode(String code);

    Optional<Beneficiary> findByCode(String code);
    Optional<Beneficiary> findByAccountingCode(String accountingCode);
    Optional<Beneficiary> findByCodeBeneficiary(String codeBeneficiary);

    List<Beneficiary> getBeneficiariesById(Long id);

    Optional<Beneficiary> findByPersonId(Long id);

    List<Beneficiary> findByPersonIdIn(Set<Long> personIds);

    @Query(value = "SELECT b from Beneficiary b where b.independent = true")
    Page<Beneficiary> searchBeneficiaryByTypeIndependent(Pageable page);

    @Query(value = "SELECT b from Beneficiary b where b.independent = false")
    Page<Beneficiary> searchBeneficiaryByTypeFamilyMember(Pageable page);

    @Query(value = "SELECT b from Beneficiary b where lower(b.person.firstName) like lower(concat('%', :value1,'%')) and lower(b.person.lastName) like lower(concat('%', :value2,'%'))")
    Page<Beneficiary> searchBeneficiaryByName(@Param("value1") String value1, @Param("value2") String value2, Pageable page);

    @Query(value = "SELECT b from Beneficiary b where b.person.cityId = :value1")
    Page<Beneficiary> searchBeneficiaryByCity(@Param("value1") Long value1, Pageable page);

    @Query(value = "SELECT b from Beneficiary b JOIN b.beneficiaryServices s WHERE s.serviceId = :value1")
    Page<Beneficiary> searchBeneficiaryByService(@Param("value1") Long value1, Pageable page);

    @Query(value = "SELECT b from Beneficiary b JOIN b.beneficiaryServices s WHERE s.statusId = :value1")
    Page<Beneficiary> searchBeneficiaryByStatus(@Param("value1") Long value1, Pageable page);

    @Query("SELECT COUNT(b) FROM Beneficiary b WHERE b.archived = false AND b.beneficiaryStatut.id IN :statusIds")
    long countByArchivedFalseAndBeneficiaryStatutIn(@Param("statusIds") List<Long> statusIds);

//    @Query(value = "SELECT \n" + "new ma.almobadara.backend.dto.dashboard.ActiveKafalatesByMonthAndTypeDTO(" +
//            "    s.serviceId," +
//            "    EXTRACT(MONTH FROM s.createdAt) AS month," +
//            "    EXTRACT(YEAR FROM s.createdAt) AS year," +
//            "    COUNT(*) AS totalActiveKafalates)" +
//            "FROM \n" +
//            "    Beneficiary b \n" +
//            "INNER JOIN\n" +
//            "    BeneficiaryService s ON b.id = s.beneficiary.id\n" +
//            "WHERE \n" +
//            "    s.statusId = 2 \n" +
//            "    AND s.serviceId IN (1,2,3,4)\n" +
//            "    AND s.createdAt >= :maxDate\n" +
//            "GROUP BY \n" +
//            "    s.serviceId, \n" +
//            "    EXTRACT(month FROM s.createdAt),\n" +
//            "    EXTRACT(year FROM s.createdAt)\n" +
//            "ORDER BY \n" +
//            "    year DESC, month DESC")
//    List<ActiveKafalatesByMonthAndTypeDTO> getActiveKafalatesByMonthAndType(Instant maxDate);


    @Query(value = "SELECT new ma.almobadara.backend.dto.dashboard.ActiveKafalatesByMonthAndTypeDTO(" +
            "    s.serviceId," +
            "    EXTRACT(MONTH FROM s.createdAt) AS month," +
            "    EXTRACT(YEAR FROM s.createdAt) AS year," +
            "    COUNT(*) AS totalActiveKafalates)" +
            "FROM Beneficiary b " +
            "INNER JOIN BeneficiaryService s ON b.id = s.beneficiary.id " +
            "WHERE s.statusId = 2 " +
            "    AND s.serviceId IN (1,2,3,4) " +
            "    AND s.createdAt >= :maxDate " +
            "GROUP BY s.serviceId, EXTRACT(month FROM s.createdAt), EXTRACT(year FROM s.createdAt) " +
            "ORDER BY year DESC, month DESC")
    List<ActiveKafalatesByMonthAndTypeDTO> getActiveKafalatesByMonthAndType(Instant maxDate);



//    @Query(value = "SELECT \n" + "new ma.almobadara.backend.dto.dashboard.ActiveKafalatesByMonthAndTypeDTO(" +
//            "    s.serviceId," +
//            "    EXTRACT(MONTH FROM s.createdAt) AS month," +
//            "    EXTRACT(YEAR FROM s.createdAt) AS year," +
//            "    COUNT(*) AS totalActiveKafalates)" +
//            "FROM \n" +
//            "    Beneficiary b \n" +
//            "INNER JOIN\n" +
//            "    BeneficiaryService s ON b.id = s.beneficiary.id\n" +
//            "WHERE \n" +
//            "    s.statusId = 2 \n" +
//            "    AND s.serviceId IN (1,2,3,4)\n" +
//            "    AND s.createdAt >= :maxDate\n" +
//            "    AND NOT EXISTS (" +
//            "        SELECT 1 " +
//            "        FROM TakenInChargeBeneficiary t " +
//            "        WHERE t.beneficiary.id = b.id " +
//            "    ) " +
//            "GROUP BY \n" +
//            "    s.serviceId, \n" +
//            "    EXTRACT(month FROM s.createdAt),\n" +
//            "    EXTRACT(year FROM s.createdAt)\n" +
//            "ORDER BY \n" +
//            "    year DESC, month DESC")
//    List<ActiveKafalatesByMonthAndTypeDTO> getActiveKafalatesByMonthAndTypeWithoutTakenInCharge(Instant maxDate);



    @Query(value = "SELECT new ma.almobadara.backend.dto.dashboard.ActiveKafalatesByMonthAndTypeDTO(" +
            "    s.serviceId," +
            "    EXTRACT(MONTH FROM s.createdAt) AS month," +
            "    EXTRACT(YEAR FROM s.createdAt) AS year," +
            "    COUNT(*) AS totalActiveKafalates)" +
            "FROM Beneficiary b " +
            "INNER JOIN BeneficiaryService s ON b.id = s.beneficiary.id " +
            "WHERE s.statusId = 2 " +
            "    AND s.serviceId IN (1,2,3,4) " +
            "    AND s.createdAt >= :maxDate " +
            "AND NOT EXISTS (SELECT 1 FROM TakenInChargeBeneficiary t WHERE t.beneficiary.id = b.id  )"+
            "GROUP BY s.serviceId, EXTRACT(month FROM s.createdAt), EXTRACT(year FROM s.createdAt) " +
            "ORDER BY year DESC, month DESC")
    List<ActiveKafalatesByMonthAndTypeDTO> getActiveKafalatesByMonthAndTypeWithoutTakenInCharge(Instant maxDate);


    @Query(value = "SELECT \n" + "new ma.almobadara.backend.dto.dashboard.ActiveKafalatesByMonthAndTypeDTO(" +
            "    p.sex AS gender," +
            "    EXTRACT(MONTH FROM s.createdAt) AS month," +
            "    EXTRACT(YEAR FROM s.createdAt) AS year," +
            "    COUNT(*) AS totalActiveKafalates)" +
            "FROM \n" +
            "    Beneficiary b \n" +
            "INNER JOIN\n" +
            "    BeneficiaryService s ON b.id = s.beneficiary.id\n" +
            "INNER JOIN\n" +
            "    Person p ON b.person.id = p.id\n" +
            "WHERE \n" +
            "    s.statusId = 2 \n" +
            "    AND s.serviceId = 1 \n" +
            "    AND s.createdAt >= :maxDate\n" +
            "GROUP BY \n" +
            "    p.sex, \n" +
            "    EXTRACT(month FROM s.createdAt),\n" +
            "    EXTRACT(year FROM s.createdAt)\n" +
            "ORDER BY \n" +
            "    year DESC, month DESC, gender")
    List<ActiveKafalatesByMonthAndTypeDTO> getActiveKafalatesOrphansByMonthAndSex(Instant maxDate);

    @Query(value = "SELECT \n" + "new ma.almobadara.backend.dto.dashboard.ActiveKafalatesByMonthAndCityDTO(" +
            "    p.cityId AS ville," +
            "    EXTRACT(MONTH FROM s.createdAt) AS month," +
            "    EXTRACT(YEAR FROM s.createdAt) AS year," +
            "    COUNT(*) AS totalActiveKafalates)" +
            "FROM \n" +
            "    Beneficiary b \n" +
            "INNER JOIN\n" +
            "    BeneficiaryService s ON b.id = s.beneficiary.id\n" +
            "INNER JOIN\n" +
            "    Person p ON b.person.id = p.id\n" +
            "WHERE \n" +
            "    s.statusId = 2 \n" +
            "    AND s.serviceId = 1 \n" +
            "    AND s.createdAt >= :maxDate\n" +
            "GROUP BY \n" +
            "    p.cityId, \n" +
            "    EXTRACT(month FROM s.createdAt),\n" +
            "    EXTRACT(year FROM s.createdAt)\n" +
            "ORDER BY \n" +
            "    year DESC, month DESC, ville")
    List<ActiveKafalatesByMonthAndCityDTO> getActiveKafalatesOrphansByMonthAndCity(Instant maxDate);

    @Query(value = "SELECT new ma.almobadara.backend.dto.dashboard.ActiveKafalatesByMonthAndAge( " +
            "    p.birthDate AS birthDate," +
            "    EXTRACT(MONTH FROM s.createdAt) AS month," +
            "    EXTRACT(YEAR FROM s.createdAt) AS year, " +
            "    COUNT(*) AS totalActiveKafalates ) " +
            "FROM Beneficiary b " +
            "INNER JOIN BeneficiaryService s ON b.id = s.beneficiary.id " +
            "INNER JOIN Person p ON b.person.id = p.id " +
            "WHERE s.statusId = 2 " +
            "AND s.serviceId = 1 " +
            "AND s.createdAt >= :maxDate " +
            "GROUP BY p.birthDate, " +
            "EXTRACT(month FROM s.createdAt), " +
            "EXTRACT(year FROM s.createdAt) " +
            "ORDER BY year DESC, month DESC, birthDate")
    List<ActiveKafalatesByMonthAndAge> getActiveKafalatesOrphansByMonthAndAge(Instant maxDate);


    @Query(value = "SELECT \n" + "new ma.almobadara.backend.dto.dashboard.ActiveKafalatesByMonthAndCityDTO(" +
            "    p.cityId AS ville," +
            "    EXTRACT(MONTH FROM s.createdAt) AS month," +
            "    EXTRACT(YEAR FROM s.createdAt) AS year," +
            "    COUNT(*) AS totalActiveKafalates)" +
            "FROM \n" +
            "    Beneficiary b \n" +
            "INNER JOIN\n" +
            "    BeneficiaryService s ON b.id = s.beneficiary.id\n" +
            "INNER JOIN\n" +
            "    Person p ON b.person.id = p.id\n" +
            "WHERE \n" +
            "    s.statusId = 2 \n" +
            "    AND s.serviceId = 1 \n" +
            "    AND s.createdAt >= :maxDate\n" +
            "GROUP BY \n" +
            "    p.cityId, \n" +
            "    EXTRACT(month FROM s.createdAt),\n" +
            "    EXTRACT(year FROM s.createdAt)\n" +
            "ORDER BY \n" +
            "    year DESC, month DESC, ville")
    List<ActiveKafalatesByMonthAndCityDTO> getActiveKafalatesOrphansByMonthAndCityAndRegion(Instant maxDate);


    Page<Beneficiary> findByZoneId(Long zoneId, Pageable pageable);

    //List<Beneficiary> findAllByArchivedFalseOrArchivedNullAndBeneficiaryStatutIdIn(List<Long> longs);

    @Query("SELECT b FROM Beneficiary b WHERE (b.archived = false OR b.archived IS NULL) AND b.beneficiaryStatut.id IN :statutIds")
    List<Beneficiary> findAllByArchivedFalseOrArchivedNullAndBeneficiaryStatutIdIn(@Param("statutIds") List<Long> statutIds);


    @Query("SELECT b FROM Beneficiary b WHERE b.beneficiaryStatut.id IN (:statusIds)")
    Page<Beneficiary> findBeneficiariesByStatusIds(List<Long> statusIds, Pageable pageable);

    //countByAddedYearAndBeneficiaryStatutIdIn

    @Query("SELECT COUNT(b) FROM Beneficiary b WHERE b.addedYear = :addedYear AND b.beneficiaryStatut.id IN :statuses")
    Long countByAddedYearAndBeneficiaryStatutIdIn(@Param("addedYear") String addedYear, @Param("statuses") List<Long> statuses);

    // cehck if exie beneficire wth the sousZoneId

    boolean existsBySousZoneId(Long sousZoneId);


    List<Beneficiary> findByZone(Zone zone);
}
