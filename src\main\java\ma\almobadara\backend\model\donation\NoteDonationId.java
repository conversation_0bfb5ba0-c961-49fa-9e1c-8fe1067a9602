package ma.almobadara.backend.model.donation;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Objects;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class NoteDonationId implements Serializable {

    private Long donation;
    private Long note;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        NoteDonationId that = (NoteDonationId) o;
        return Objects.equals(donation, that.donation) && Objects.equals(note, that.note);
    }

    @Override
    public int hashCode() {
        return Objects.hash(donation, note);
    }

}
