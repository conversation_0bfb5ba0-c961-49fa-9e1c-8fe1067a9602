package ma.almobadara.backend.controller.donor;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.donor.DocumentDonorDto;
import ma.almobadara.backend.dto.donor.DonorDocumentDetailsDTO;
import ma.almobadara.backend.service.donor.DocumentDonorService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping("/donors")
public class DocumentDonorController {

    private final DocumentDonorService documentDonorService;

    @GetMapping("/getDonorAndDocument/{documentDonorId}")
    @Operation(summary = "Get Donor and Document", description = "Get Donor and Document by DocumentDonor ID", tags = {"SIG"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successful operation"),
            @ApiResponse(responseCode = "404", description = "DocumentDonor not found")})
    public ResponseEntity<DocumentDonorDto> getDonorAndDocument(@PathVariable Long documentDonorId) {
        log.info("Start resource Get Donor and Document for DocumentDonor ID: {}", documentDonorId);

        DocumentDonorDto documentDonorDto = documentDonorService.getDocumentDonorById(documentDonorId);

        log.info("End resource Get Donor and Document");
        return ResponseEntity.ok(documentDonorDto);
    }

    @GetMapping("/getDocumentsByDonorId/{donorId}")
    public List<DonorDocumentDetailsDTO> getDocumentsByDonorId(@PathVariable Long donorId) {
        List<DonorDocumentDetailsDTO> documentDTOList = documentDonorService.getDocumentsByDonorId(donorId);
        return ResponseEntity.ok(documentDTOList).getBody();
    }

}
