package ma.almobadara.backend.service.administration;

import jakarta.persistence.EntityManager;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.Audit.AuditApplicationService;
import ma.almobadara.backend.clients.RefFeignClient;
import ma.almobadara.backend.controller.referentiel.RefController;
import ma.almobadara.backend.dto.administration.EpsDto;
import ma.almobadara.backend.dto.administration.ResponsableEpsDto;
import ma.almobadara.backend.enumeration.AgeGroup;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.mapper.EpsMapper;
import ma.almobadara.backend.mapper.ResponsableEpsMapper;
import ma.almobadara.backend.mapper.ZoneMapper;
import ma.almobadara.backend.model.administration.Eps;
import ma.almobadara.backend.model.administration.ResponsableEps;
import ma.almobadara.backend.model.beneficiary.Person;
import ma.almobadara.backend.repository.administration.EpsRepository;
import ma.almobadara.backend.repository.administration.ResponsableEpsRepository;
import ma.almobadara.backend.repository.beneficiary.BeneficiaryRepository;
import ma.almobadara.backend.service.donor.MinioService;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.crossstore.ChangeSetPersister;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.awt.print.Pageable;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static ma.almobadara.backend.service.administration.CacheAdUserService.getUsernameFromJwt;
import static ma.almobadara.backend.util.constants.GlobalConstants.*;

@Service
@RequiredArgsConstructor
@Slf4j
public class ResponsableEpsService {

    private final EpsRepository epsRepository;
    private final ResponsableEpsRepository responsableEpsRepository;
    private final ResponsableEpsMapper responsableEpsMapper;
    private final ZoneMapper zoneMapper;
    private final RefController refController;
    private final RefFeignClient refFeignClient;
    private final BeneficiaryRepository beneficiaryRepository;
    private final EntityManager entityManager;
    private final AuditApplicationService auditApplicationService;
    private final MinioService minioService;



    @Value("${minio.epsResponsableFolder}")
    private String epsResponsableFolder;

    @Value("${minio.profilePicture.folder}")
    private String folderPathPicture;

    @Value("${minio.profilePicture.abv}")
    private String abv;

    public ResponsableEpsDto createResponsableEps(ResponsableEpsDto responsableEpsDto)  {
        log.info("Creating ResponsableEps for: {} {}", responsableEpsDto.getFirstName(), responsableEpsDto.getLastName());

        ResponsableEps responsableEps = responsableEpsMapper.responsableEpsDtoToResponsableEps(responsableEpsDto);

        if(responsableEpsDto.getFunctionContact()!=null){
            responsableEps.setFunctionContactId(responsableEpsDto.getFunctionContact().getId());
        }
        if(responsableEpsDto.getTypeIdentity()!=null){
            responsableEps.setTypeIdentityId(responsableEpsDto.getTypeIdentity().getId());
        }

        responsableEps.setEps(responsableEpsDto.getEps());
        responsableEps.setPictureUrl(responsableEpsDto.getPictureUrl());
        Eps eps=epsRepository.findById(responsableEpsDto.getEps().getId()).get();

        if (responsableEpsDto.getPicture() != null) {
            Instant instant = Instant.now();
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.BASIC_ISO_DATE;
            String epsResponsablePath = epsResponsableFolder + responsableEps.getLastName().toUpperCase() + "-" + responsableEps.getFirstName().substring(0, 1).toUpperCase() + responsableEps.getFirstName().substring(1) + "_" + eps.getCode();
            String fileName = responsableEps.getLastName().toUpperCase() + "-" + responsableEps.getFirstName().substring(0, 1).toUpperCase() + responsableEps.getFirstName().substring(1) + "_" + abv + "_" + instant.atZone(ZoneId.systemDefault()).toLocalDate().format(dateTimeFormatter) + "." + FilenameUtils.getExtension(responsableEpsDto.getPicture().getOriginalFilename());
            responsableEps.setPictureUrl(epsResponsablePath + "/" + folderPathPicture + "/" + fileName);
            log.info("Uploading picture to MinIO: {}", fileName);
            minioService.WriteToMinIO(responsableEpsDto.getPicture(), epsResponsablePath + "/" + folderPathPicture + "/", fileName);
        }
        responsableEps.setEps(eps);

        ResponsableEps responsableEps1= responsableEpsRepository.save(responsableEps);
        log.info("ResponsableEps created successfully with ID: {}", responsableEps1.getId());
        return responsableEpsMapper.responsableEpsToResponsableEpsDto(responsableEps1);
    }

    public void deleteResponsableEps(Long id) throws TechnicalException {
        log.info("Deleting ResponsableEps with ID: {}", id);
        ResponsableEps responsableEps = responsableEpsRepository.findById(id)
                .orElseThrow(() -> new TechnicalException("Responsable Eps with ID " + id + " Not Found"));

        if (!responsableEps.isDeleted()) {
            responsableEps.setDeleted(true);
            responsableEpsRepository.save(responsableEps);
            log.info("ResponsableEps with ID {} marked as deleted.", id);

        }
    }

    public List<ResponsableEpsDto> findAllResponsableEps(Long id) {
        log.info("Retrieving all ResponsableEps for Eps ID: {}", id);
        Eps eps = epsRepository.findById(id).get();
        return responsableEpsRepository.findByEpsAndIsDeletedFalse(eps).stream()
                .map(responsableEps -> {
                    ResponsableEpsDto dto = responsableEpsMapper.responsableEpsToResponsableEpsDto(responsableEps);
                    if (responsableEps.getFunctionContactId() != null) {
                        dto.setFunctionContact(refFeignClient.getMetDonorContactFunction(responsableEps.getFunctionContactId()));
                    }
                    if (responsableEps.getTypeIdentityId() != null) {
                        dto.setTypeIdentity(refFeignClient.getParTypeIdentity(responsableEps.getTypeIdentityId()));
                    }
                    if (responsableEps.getPictureUrl() != null) {
                        try {
                            byte[] imageData = minioService.ReadFromMinIO(responsableEps.getPictureUrl(), null);
                            String base64Image = Base64.getEncoder().encodeToString(imageData);
                            dto.setPictureBase64(base64Image);
                        } catch (TechnicalException ex) {
                            ex.printStackTrace();
                        }
                    }
                    eps.setZone(null);
                    eps.setServices(null);
                    dto.setEps(responsableEps.getEps());
                    dto.setPictureUrl(responsableEps.getPictureUrl());
                    return dto;
                })
                .collect(Collectors.toList());
    }


}
