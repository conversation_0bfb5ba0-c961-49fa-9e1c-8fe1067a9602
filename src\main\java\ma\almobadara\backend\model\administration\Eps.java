package ma.almobadara.backend.model.administration;

import com.fasterxml.jackson.annotation.JsonManagedReference;
import jakarta.persistence.*;
import lombok.*;
import ma.almobadara.backend.model.service.Services;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import com.google.gson.Gson;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;

import static ma.almobadara.backend.util.strings.HandleSpecialChars.escapeSpecialChars;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Eps {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @Column(unique = true)
    private String code;
    private String name;
    private String nameAr;
    private String address;
    private String addressAr;
    private boolean isDeleted;
    private boolean status;
    private String beneficiaryType;
    private String ageGroupIds;
    private String comment;
    private String cityIds;
    private Long capacity;
    private Date dateCreated;


    @CreationTimestamp
    @Column(updatable = false)
    private LocalDateTime creationDate;

    @UpdateTimestamp
    private LocalDateTime updateDate;

    @OneToOne(mappedBy = "eps")
    private Zone zone;

    @OneToMany(mappedBy = "eps")
    private List<Services> services;

    // add a set of city ids
    public List<Long> getCityIdList() {
        if (cityIds == null || cityIds.isEmpty()) {
            return Collections.emptyList();
        }
        return Arrays.stream(cityIds.split(",\\s*"))
                .map(Long::parseLong)  // Convert String to Long
                .toList();
    }

    // add a set of age group ids
    public List<Long> getAgeGroupIdList() {
        if (ageGroupIds == null || ageGroupIds.isEmpty()) {
            return Collections.emptyList();
        }
        return Arrays.stream(ageGroupIds.split(",\\s*"))
                .map(Long::parseLong)  // Convert String to Long
                .toList();
    }

    public void setCityIdList(List<Long> cityIds) {
        if (cityIds == null || cityIds.isEmpty()) {
            this.cityIds = "";
        } else {
            this.cityIds = String.join(", ", cityIds.stream()
                    .map(String::valueOf)
                    .toList());
        }

    }

    public String toAuditString(String zoneName, String ageGroups) {
        Gson gson = new Gson();
        SimpleDateFormat formatter = new SimpleDateFormat("dd-MM-yyyy");

        // Use LinkedHashMap to preserve the order of insertion
        Map<String, Object> jsonMap = new LinkedHashMap<>();
        jsonMap.put("Code", escapeSpecialChars(code));
        jsonMap.put("Nom", escapeSpecialChars(name));
        jsonMap.put("Nom Arabe", escapeSpecialChars(nameAr));
        jsonMap.put("Adresse", escapeSpecialChars(address));
        jsonMap.put("Adresse Arabe", escapeSpecialChars(addressAr));
        jsonMap.put("Statut", status ? "Actif" : "Inactif");
        jsonMap.put("Type de bénéficiaire", escapeSpecialChars(beneficiaryType));
        jsonMap.put("Commentaire", escapeSpecialChars(comment));
        jsonMap.put("Zone", escapeSpecialChars(zoneName));
        jsonMap.put("Groupes d'âge", escapeSpecialChars(ageGroups));

        return gson.toJson(jsonMap);
    }

    // Method to set age group IDs from a list
    public void setAgeGroupIdList(List<Long> ageGroupIds) {
        if (ageGroupIds == null || ageGroupIds.isEmpty()) {
            this.ageGroupIds = "";
        } else {
            this.ageGroupIds = String.join(", ", ageGroupIds.stream()
                    .map(String::valueOf)
                    .toList());
        }
    }


}
