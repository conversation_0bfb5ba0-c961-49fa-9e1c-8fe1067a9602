package ma.almobadara.backend.service.administration;

import com.microsoft.graph.http.GraphServiceException;
import com.microsoft.graph.models.Invitation;
import com.microsoft.graph.models.SignIn;
import com.microsoft.graph.requests.GraphServiceClient;
import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityNotFoundException;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.*;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.administration.CacheAdUserDTO;
import ma.almobadara.backend.dto.administration.RolePrivilegeDTO;
import ma.almobadara.backend.enumeration.ProfilesId;
import ma.almobadara.backend.enumeration.RoleCode;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.exceptions.UserAlreadyExistsException;
import ma.almobadara.backend.exceptions.UserNotFoundException;
import ma.almobadara.backend.mapper.CacheAdUserDTOMapper;
import ma.almobadara.backend.mapper.CacheAdUserMapper;
import ma.almobadara.backend.model.administration.*;
import ma.almobadara.backend.repository.administration.*;
import ma.almobadara.backend.service.SecurityService;
import jakarta.servlet.http.HttpServletRequest;
import okhttp3.Request;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static ma.almobadara.backend.util.constants.GlobalConstants.NULL_ENTITY;

@Service
@RequiredArgsConstructor
@Slf4j
public class CacheAdUserService {
    private final RoleRepository roleRepository;
    private final RolePrivilegeRepository rolePrivilegeRepository;
    private final AssistantRepository assistantRepository;
    private final CacheAdUserRepository cacheAdUserRepository;
    private final CacheAdUserDTOMapper cacheAdUserDTOMapper;
    private final SecurityService securityService;
    private final ProfileRepository profileRepository;
    private final GraphServiceClient<Request> graphClient;
    private final CacheAdUserMapper cacheAdUserMapper;
    private final CacheManager cacheManager;
    private final EntityManager entityManager;
    private final HttpServletRequest request;

    private TokenImpersonationService tokenImpersonationService; // Will be set after construction to avoid circular dependency


    public static String getUsernameFromJwt() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        // Check if the principal is of type Jwt
        if (authentication.getPrincipal() instanceof Jwt jwtToken) {
            Map<String, Object> claims = jwtToken.getClaims();
            return (String) claims.get("name");
        }
        // Handle the case where the principal is a CacheAdUser
        else if (authentication.getPrincipal() instanceof CacheAdUser user) {
            return user.getFirstName() + " " + user.getLastName();
        }

        return null; // If the principal is neither a Jwt nor a CacheAdUser
    }

    // getttinh Role and his permissions of the connected user from the Authentication authentication
    public static CacheAdUser getRoleFromJwt() {
        // Check if the principal is of type Jwt
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if(authentication == null) {
            return null;
        }
        if (authentication.getPrincipal() instanceof CacheAdUser user) {
            return user;
        }
        return null; // If the principal is neither a Jwt nor a CacheAdUser
    }

    public CacheAdUserDTO getConnectedUser() {
        log.debug("Start service getConnectedUser");

        // Check for impersonation token in the request header
        String impersonationToken = request.getHeader("Impersonation-Token");
        if (impersonationToken != null && !impersonationToken.isEmpty() && tokenImpersonationService != null) {
            log.debug("Impersonation token found in request header");
            CacheAdUserDTO impersonatedUser = tokenImpersonationService.getImpersonatedUser(impersonationToken);
            if (impersonatedUser != null) {
                log.debug("Using impersonated user with ID: {}", impersonatedUser.getId());
                return impersonatedUser;
            }
            log.warn("Invalid impersonation token, falling back to authenticated user");
        }

        // If no impersonation token or invalid token, use the authenticated user
        String azureId = securityService.getAuthenticatedAzureId();
        if (azureId == null) {
            log.error("No authenticated user found.");
            return null;
        }

        // If not found in cache, retrieve the user from the repository
        CacheAdUser connectedUser = cacheAdUserRepository.findByAzureDirectoryIdAndIsDeletedIsFalse(azureId);
        if (connectedUser == null) {
            // If no user found, check for master admin and update Azure ID
            connectedUser = findMasterAdminByEmailAndUpdateAzureId();
            if (connectedUser == null) {
                log.error("No user found with Azure ID or email.");
                return null;
            }
        }

        // Initialize rolePrivilegeDTOS only if a role exists
        List<RolePrivilegeDTO> rolePrivilegeDTOS = new ArrayList<>();
        if (connectedUser.getRole() != null) {
            Role role = connectedUser.getRole();
            List<RolePrivilege> rolePrivileges = rolePrivilegeRepository.findByRole_Id(role.getId());

            // Map rolePrivileges to RolePrivilegeDTO
            rolePrivilegeDTOS = rolePrivileges.stream().map(rolePrivilege -> {
                RolePrivilegeDTO rolePrivilegeDTO = new RolePrivilegeDTO();
                rolePrivilegeDTO.setCodeRole(rolePrivilege.getRole().getCode());
                rolePrivilegeDTO.setCodeFeature(rolePrivilege.getFeature().getCode());
                rolePrivilegeDTO.setCodePrivilege(rolePrivilege.getPrivilege().getCode());
                return rolePrivilegeDTO;
            }).collect(Collectors.toList());
        }

        // Map CacheAdUser to CacheAdUserDTO
        // wech if that user is a assistant if yes we should get his id with us
        CacheAdUserDTO connectedUserDTO = cacheAdUserDTOMapper.mapToDTO(connectedUser);
        if(connectedUser.getRole().getCode().equals(RoleCode.ASSISTANT.getCode())){
            Assistant assistant = assistantRepository.findByCacheAdUserId(connectedUser.getId());
            if(assistant != null){
                connectedUserDTO.setAssistantId(assistant.getId());
                if(assistant.getZone() != null){
                    connectedUserDTO.setZoneId(assistant.getZone().getId());
                }
            }
        }
        connectedUserDTO.getRole().setRolePrivileges(rolePrivilegeDTOS);

        log.debug("End service getConnectedUser");
        return connectedUserDTO;
    }


    public static void logUserInfo(String methodName, String... args) {
        String username = getUsernameFromJwt(); // Use the new method
        if (username != null) {
            String logMessage = "User '{}' is accessing {} with parameters: {}";
            log.info(logMessage, username, methodName, Arrays.toString(args));
        } else {
            log.warn("Unable to determine username for logging.");
        }
    }

    @Transactional
    public Page<CacheAdUserDTO> getAllCachedUsers(int page, int size, String searchByNom, String searchByPrenom, String searchRole, String searchByEmail, Date minDate, Date maxDate) {
        log.debug("Start service getAllCachedUsers page: {}, size: {}, searchRole: {}, firstName: {}, lastName: {}", page, size, searchByNom, searchByPrenom, searchByEmail);
        Pageable pageable = PageRequest.of(page, size);
        Page<CacheAdUser> cachedUserPage;

        if (searchByNom != null || searchByPrenom != null || searchByEmail != null || searchRole != null || minDate != null || maxDate != null) {
            cachedUserPage = filterCacheAdusers(searchByNom, searchByPrenom, searchByEmail, searchRole, minDate, maxDate, pageable);
        } else {
            cachedUserPage = cacheAdUserRepository.findAll(pageable);
        }


        List<CacheAdUserDTO> cacheAdUserDTOList = cachedUserPage.getContent().stream()
                .map(cacheAdUserDTOMapper::mapToDTO)
                .collect(Collectors.toList());

        log.debug("End service getAllCachedUsers with {} users found", cachedUserPage.getTotalElements());
        return new PageImpl<>(cacheAdUserDTOList, pageable, cachedUserPage.getTotalElements());
    }


    public Page<CacheAdUser> filterCacheAdusers(String searchByNom, String searchByPrenom, String searchByEmail, String searchRole, Date minDate, Date maxDate, Pageable pageable) {
        log.debug("Start service filterDonations with searchByNom: {}, searchByPrenom: {}, searchByEmail: {}, minDate: {}, maxDate: {}", searchByNom, searchByPrenom, searchByEmail, minDate, maxDate);

        // Create criteria builder and query
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<CacheAdUser> criteriaQuery = criteriaBuilder.createQuery(CacheAdUser.class);
        Root<CacheAdUser> root = criteriaQuery.from(CacheAdUser.class);

        // Create predicate for filtering
        Predicate predicate = criteriaBuilder.conjunction();
        predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("isDeleted"), false));

        // Apply filters
        if (searchByPrenom != null) {
            predicate = criteriaBuilder.and(predicate,
                    criteriaBuilder.like(
                            criteriaBuilder.lower(root.get("firstName")),
                            "%" + searchByPrenom.toLowerCase() + "%"
                    )
            );
        }
        if (searchByNom != null) {
            predicate = criteriaBuilder.and(predicate,
                    criteriaBuilder.like(
                            criteriaBuilder.lower(root.get("lastName")),
                            "%" + searchByNom.toLowerCase() + "%"
                    )
            );
        }
        if (minDate != null) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.greaterThanOrEqualTo(root.get("creationDate"), minDate));
        }
        if (maxDate != null) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.lessThanOrEqualTo(root.get("creationDate"), maxDate));
        }
        if (searchByEmail != null) {
            predicate = criteriaBuilder.and(predicate,
                    criteriaBuilder.like(
                            criteriaBuilder.lower(root.get("mail")),
                            "%" + searchByEmail.toLowerCase() + "%"
                    )
            );
        }

        // Join with Profile entity
        Join<CacheAdUser, Role> roleJoin = root.join("role", JoinType.LEFT);
        if (searchRole != null) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(roleJoin.get("id"), searchRole));
        }

        // Apply predicate to criteria query
        criteriaQuery.where(predicate);

        // Execute query and get results
        TypedQuery<CacheAdUser> typedQuery = entityManager.createQuery(criteriaQuery);
        typedQuery.setFirstResult((int) pageable.getOffset());
        typedQuery.setMaxResults(pageable.getPageSize());
        List<CacheAdUser> resultList = typedQuery.getResultList();

        // Count total number of results
        CriteriaQuery<Long> countQuery = criteriaBuilder.createQuery(Long.class);
        countQuery.select(criteriaBuilder.count(countQuery.from(CacheAdUser.class)));
        long totalCount = entityManager.createQuery(countQuery).getSingleResult();

        log.debug("End service filterDonations with {} users found", totalCount);
        return new PageImpl<>(resultList, pageable, totalCount);
    }


    private CacheAdUser findMasterAdminByEmailAndUpdateAzureId() {
        String email = securityService.getAuthenticatedUserEmail();

        if (email == null) {
            log.error("No email found for the connected user.");
            return null;
        }
        CacheAdUser connectedUser = cacheAdUserRepository.findByMail(email);
        if (connectedUser != null && connectedUser.getProfile() != null && Objects.equals(connectedUser.getProfile().getNameProfile(), "Master Admin")) {
            connectedUser = loadAndSyncConnectedUser(email);
            return cacheAdUserRepository.save(connectedUser);
        }

        return null;
    }

    public void adddUserToCache(CacheAdUser cacheAdUser) {
        log.debug("Start service addConnectedUserToCache for user with Azure ID: {}", cacheAdUser.getAzureDirectoryId());
        CacheAdUserDTO cacheAdUserDTO = cacheAdUserDTOMapper.mapToDTO(cacheAdUser);
        Cache cache = cacheManager.getCache("connectedUserCache");
        if (cache != null) {
            cache.put(cacheAdUser.getAzureDirectoryId(), cacheAdUserDTO);
            log.debug("Connected user with Azure ID {} added to cache", cacheAdUser.getAzureDirectoryId());
        }
        log.debug("End service addConnectedUserToCache");
    }

    @Transactional
    @CacheEvict(value = {"userDetails", "userId"}, allEntries = true)
    public void loadAndSaveAdUserByEmail(String email, Long roleId) {
        log.debug("Start service loadAndSaveAdUserByEmail for email: {}", email);
        Role role = roleRepository.findById(roleId).orElseThrow(() ->
                new EntityNotFoundException("Role with ID " + roleId + " not found"));

        // Check if the user already exists in the database with isDeleted flag set to true so just make false no need to add again from azure
        CacheAdUser cacheAdUserFromDB = cacheAdUserRepository.findByMail(email);
        if (cacheAdUserFromDB != null && cacheAdUserFromDB.isDeleted()) {
            cacheAdUserFromDB.setDeleted(false);
            cacheAdUserFromDB.setRole(role);
            cacheAdUserFromDB.setProfile(profileRepository.findById(ProfilesId.ADMIINMASTER.getId()).orElseThrow(() -> new EntityNotFoundException("Profile not found")));
            cacheAdUserRepository.save(cacheAdUserFromDB);
            handleAssistantRole(cacheAdUserFromDB, email);

            // add the cacheADUserDTO to the cache
            adddUserToCache(cacheAdUserFromDB);
            log.debug("User with mail: {} found in the database and isDeleted flag set to false", email);
            return;
        }
        // If the user already exists in the database with isDeleted flag set to false, throw exception
        if (cacheAdUserFromDB != null) {
            log.error("User with mail: {} already exists in the database", email);
            throw new UserAlreadyExistsException("User with email: " + email + " already exists");
        }
        try {
            var user = graphClient.users(email).buildRequest().get();
            if (user != null && user.id != null) {
                CacheAdUser cacheAdUser = cacheAdUserMapper.mapGraphUserToCacheAdUser(user);
                if ((cacheAdUser.getFirstName() == null && cacheAdUser.getLastName() == null)
                        && user.displayName != null) {

                    // If givenName and surname are null, extract from displayName
                    String[] nameParts = user.displayName.trim().split("\\s+", 2);
                    cacheAdUser.setFirstName(nameParts[0]);

                    if (nameParts.length > 1) {
                        cacheAdUser.setLastName(nameParts[1]);
                    } else {
                        cacheAdUser.setLastName(""); // Handle cases where only one name exists
                    }
                }
                cacheAdUser.setRole(role);
                cacheAdUser.setProfile(profileRepository.findById(ProfilesId.ADMIINMASTER.getId()).orElseThrow(() -> new EntityNotFoundException("Profile not found")));
                cacheAdUserRepository.save(cacheAdUser);
                handleAssistantRole(cacheAdUser, email);
                // add the cacheADUserDTO to the cache
                adddUserToCache(cacheAdUser);
                log.debug("Saved user with mail: {}", cacheAdUser.getMail());

                log.debug("End service loadAndSaveAdUserByEmail for email: {}", email);
            }
        } catch (GraphServiceException ex) {
            log.error("Error loading user with email {}: {}", email, ex.getMessage());
            throw new UserNotFoundException("Error loading user with email: " + email, 404);
        }
    }

    private void handleAssistantRole(CacheAdUser cacheAdUser, String email) {
        if (cacheAdUser.getRole().getCode().equals(RoleCode.ASSISTANT.getCode())) {
            Assistant assistant = new Assistant();
            assistant.setCode(generateAssistantCode());
            assistant.setCacheAdUser(cacheAdUser);
            assistant.setEmail(email);
            assistantRepository.save(assistant);
        }
    }

    public String generateAssistantCode() {
        // Get the last assistant code
        Optional<Assistant> lastAssistant = assistantRepository.findLastAssistantCode();
        String lastCode = lastAssistant.map(Assistant::getCode).orElse(null);
        String currentYear = String.valueOf(LocalDateTime.now().getYear());
        String newCode;
        if (lastCode != null && lastCode.substring(1, 5).equals(currentYear)) {
            int lastCodeNumber = Integer.parseInt(lastCode.substring(5));
            newCode = "A" + currentYear + String.format("%04d", lastCodeNumber + 1);
        } else {
            newCode = "A" + currentYear + "0001";
        }
        return newCode;
    }


    @Transactional
    @CacheEvict(value = {"userDetails", "userId"}, allEntries = true)
    public Long checkAndSaveAdUserByEmail(String email) {
        log.debug("Start service checkAndSaveAdUserByEmail for email: {}", email);

        // Check if the user already exists in the database
        CacheAdUser cacheAdUserFromDB = cacheAdUserRepository.findByMail(email);
        if (cacheAdUserFromDB != null) {
            if (cacheAdUserFromDB.isDeleted()) {
                // User exists but is marked as deleted, so reactivate
                cacheAdUserFromDB.setDeleted(false);
                Role role = roleRepository.findByCode(RoleCode.ASSISTANT.getCode());
                cacheAdUserFromDB.setRole(role);
                Profile profile = profileRepository.findById(ProfilesId.ADMIINMASTER.getId()).orElseThrow(() -> new EntityNotFoundException("Profile not found"));
                cacheAdUserFromDB.setProfile(profile);
                cacheAdUserRepository.save(cacheAdUserFromDB);
                // Add the CacheAdUserDTO to the cache
                adddUserToCache(cacheAdUserFromDB);
                log.debug("User with mail: {} found in the database and isDeleted flag set to false", email);
                return cacheAdUserFromDB.getId(); // Return the existing user's ID
                // also if the user is not deleted and the profile is not a master or  a assistant admin so we can liek overide the profile of the user by a assistant profile
            } else {
                log.error("User with mail: {} already exists in the database", email);
                throw new UserAlreadyExistsException("Assistant with email: " + email + " already exists.");
            }
        }

        // Load user from external source (e.g., Graph API)
        try {
            var user = graphClient.users(email).buildRequest().get();
            if (user != null && user.id != null) {
                CacheAdUser cacheAdUser = cacheAdUserMapper.mapGraphUserToCacheAdUser(user);
                Optional<Profile> optionalProfile = profileRepository.findById(ProfilesId.ADMIINMASTER.getId());
                optionalProfile.ifPresent(cacheAdUser::setProfile);
                Role role = roleRepository.findByCode(RoleCode.ASSISTANT.getCode());
                cacheAdUser.setRole(role);
                if ((cacheAdUser.getFirstName() == null && cacheAdUser.getLastName() == null)
                        && user.displayName != null) {

                    // If givenName and surname are null, extract from displayName
                    String[] nameParts = user.displayName.trim().split("\\s+", 2);
                    cacheAdUser.setFirstName(nameParts[0]);

                    if (nameParts.length > 1) {
                        cacheAdUser.setLastName(nameParts[1]);
                    } else {
                        cacheAdUser.setLastName(""); // Handle cases where only one name exists
                    }
                }
                cacheAdUserRepository.save(cacheAdUser);
                // Add the CacheAdUserDTO to the cache
                adddUserToCache(cacheAdUser);
                log.debug("Saved user with mail: {}", cacheAdUser.getMail());

                return cacheAdUser.getId(); // Return the new user's ID
            }
        } catch (GraphServiceException ex) {
            log.error("Error loading user with email {}: {}", email, ex.getMessage());
            throw new UserNotFoundException("Error loading user with email: " + email, 404);
        }

        log.debug("End service checkAndSaveAdUserByEmail for email: {}", email);
        return null; // Return null if the user is not found in Azure
    }


    public CacheAdUser loadAndSyncConnectedUser(String email) {
        log.debug("Start service loadAndSyncConnectedUser for email: {}", email);
        if (email == null) {
            log.error("No email found for the connected user.");
            return null;
        }
        try {
            var user = graphClient.users(email).buildRequest().get();

            if (user != null) {
                CacheAdUser cacheAdUser = cacheAdUserMapper.mapGraphUserToCacheAdUser(user);
                CacheAdUser connectedUser = cacheAdUserRepository.findByMailOrMailNullAndAzureDirectoryIdFromAll(email);
                if (connectedUser != null) {
                    // this is case it just one time for the admin wo added manually by script
                    connectedUser.setAzureDirectoryId(cacheAdUser.getAzureDirectoryId());
                    return cacheAdUserRepository.save(connectedUser);
                }
            } else {
                log.error("No user found in Azure AD with email: {}", email);
            }
        } catch (GraphServiceException ex) {
            log.error("Error loading user with email {}: {}", email, ex.getMessage());
        }
        return null;
    }

    @Transactional
    public void deleteUserById(Integer userId) throws TechnicalException {
        log.debug("Start service deleteUserById for userId: {}", userId);

        Optional<CacheAdUser> optionalUser = cacheAdUserRepository.findByIdAndIsDeletedIsFalse(userId);
        if (optionalUser.isPresent()) {
            // we should check first if teh user is lied to a asssitant
            Assistant assistant = assistantRepository.findByCacheAdUserId(optionalUser.get().getId());
            if (assistant != null) {
                // if the user is a assistant we should delete the assistant entity
                // before delteing we shulld check if it has a relation with the one of the zones
                assistantRepository.delete(assistant);
            }
            CacheAdUser user = optionalUser.get();
            user.setDeleted(true);
            cacheAdUserRepository.save(user);

            // Check if the user exists in the cache and evict them if they do
            String azureId = user.getAzureDirectoryId();
            Cache cache = cacheManager.getCache("connectedUserCache");
            if (cache != null) {
                Cache.ValueWrapper valueWrapper = cache.get(azureId);
                if (valueWrapper != null) {
                    cache.evict(azureId);
                    log.debug("User with Azure ID {} evicted from cache.", azureId);
                }
            }

            log.debug("User with ID {} marked as deleted successfully", userId);
        } else {
            log.error("User with ID {} not found", userId);
            throw new NoSuchElementException("User with ID " + userId + " not found");
        }

        log.debug("End service deleteUserById");
    }

    @Transactional
    public void changeUserRole(Long userId, Long newRoleId) throws TechnicalException {
        log.debug("Start service changeUserRole with userId: {} and newRoleId: {}", userId, newRoleId);

        CacheAdUser cacheAdUser = cacheAdUserRepository.findById(userId)
                .orElseThrow(() -> new TechnicalException(NULL_ENTITY));
        String azureId = cacheAdUser.getAzureDirectoryId();
        String azureIdConnectedUser = securityService.getAuthenticatedAzureId();

        if (azureId.equals(azureIdConnectedUser)) {
            log.error("Cannot change the role of the connected user.");
            throw new TechnicalException("Cannot change the role of the connected user.");
        }
       // we should store ina  variable the old role of the user to check if the user is a assistant or not
        // because if the user is a assistant we should delete the assistant entity
        Role oldRole = cacheAdUser.getRole();
        Role newRole = roleRepository.findById(newRoleId)
                .orElseThrow(() -> new TechnicalException("Role not found"));
        cacheAdUser.setRole(newRole);
        cacheAdUserRepository.save(cacheAdUser);
        // add to cache the new version of the user
        if (oldRole.getCode().equals(RoleCode.ASSISTANT.getCode())) {
            Assistant assistant = assistantRepository.findByCacheAdUserId(userId);
            if (assistant != null) {
                assistantRepository.delete(assistant);
            }
        }
        else if (newRole.getCode().equals(RoleCode.ASSISTANT.getCode())) {
           handleAssistantRole(cacheAdUser, cacheAdUser.getMail());
        }

        adddUserToCache(cacheAdUser);
        log.debug("End service changeUserRole with userId: {}, New RoleId: {}", userId, newRoleId);
    }


    public Iterable<CacheAdUser> getLast() {
        Iterable<CacheAdUser> beneficiaryLevels = cacheAdUserRepository.findAll();


        return beneficiaryLevels;
    }

    public void logLastSignIn() {
        String azureId = securityService.getAuthenticatedAzureId();
        if (azureId != null) {
            try {
                // Fetch the sign-in activity for the user
                List<SignIn> signIns = graphClient.auditLogs().signIns()
                        .buildRequest()
                        .filter("userId eq '" + azureId + "'")
                        .orderBy("createdDateTime desc")
                        .top(1)
                        .get()
                        .getCurrentPage();

                if (!signIns.isEmpty()) {
                    assert signIns.get(0).createdDateTime != null;
                    LocalDateTime lastSignInDate = signIns.get(0).createdDateTime.toLocalDateTime();

                    // Log the sign-in activity
                    log.info("Last sign-in activity for user with Azure ID {}: {}", azureId, lastSignInDate);

                    // Update CacheAdUserDTO with the last sign-in date
                    CacheAdUser userDTO = cacheAdUserRepository.findByAzureDirectoryIdAndIsDeletedIsFalse(azureId);
                    if (userDTO != null) {
                        userDTO.setLastLoginInDate(lastSignInDate);
                        cacheAdUserRepository.save(userDTO);
                    }
                }
            } catch (GraphServiceException ex) {
                // Handle exception
                ex.printStackTrace();
            }
        }
    }


    public LocalDateTime getLastLoginInDate(String azureDirectoryId) {
        CacheAdUser user = cacheAdUserRepository.findByAzureDirectoryId(azureDirectoryId)
                .orElseThrow(() -> new EntityNotFoundException("User not found with id " + azureDirectoryId));
        return user.getLastLoginInDate();
    }

    /**
     * Convert a CacheAdUser entity to a CacheAdUserDTO
     * @param cacheAdUser The entity to convert
     * @return The converted DTO
     */
    public CacheAdUserDTO convertToDTO(CacheAdUser cacheAdUser) {
        if (cacheAdUser == null) {
            return null;
        }
        return cacheAdUserDTOMapper.mapToDTO(cacheAdUser);
    }

    /**
     * Load a user with all their role privileges
     * @param userId The ID of the user to load
     * @return The user DTO with role privileges loaded
     */
    public CacheAdUserDTO loadUserWithRolePrivileges(Long userId) {
        log.debug("Start service loadUserWithRolePrivileges for userId: {}", userId);

        // Get the user from repository
        CacheAdUser user = cacheAdUserRepository.findById(userId)
                .orElseThrow(() -> new EntityNotFoundException("User not found with id " + userId));

        // Initialize rolePrivilegeDTOS only if a role exists
        List<RolePrivilegeDTO> rolePrivilegeDTOS = new ArrayList<>();
        if (user.getRole() != null) {
            Role role = user.getRole();
            List<RolePrivilege> rolePrivileges = rolePrivilegeRepository.findByRole_Id(role.getId());

            // Map rolePrivileges to RolePrivilegeDTO
            rolePrivilegeDTOS = rolePrivileges.stream().map(rolePrivilege -> {
                RolePrivilegeDTO rolePrivilegeDTO = new RolePrivilegeDTO();
                rolePrivilegeDTO.setCodeRole(rolePrivilege.getRole().getCode());
                rolePrivilegeDTO.setCodeFeature(rolePrivilege.getFeature().getCode());
                rolePrivilegeDTO.setCodePrivilege(rolePrivilege.getPrivilege().getCode());
                return rolePrivilegeDTO;
            }).collect(Collectors.toList());
        }

        // Map CacheAdUser to CacheAdUserDTO
        CacheAdUserDTO userDTO = cacheAdUserDTOMapper.mapToDTO(user);
        if(user.getRole().getCode().equals(RoleCode.ASSISTANT.getCode())){
            Assistant assistant = assistantRepository.findByCacheAdUserId(user.getId());
            if(assistant != null){
                userDTO.setAssistantId(assistant.getId());
                if(assistant.getZone() != null){
                    userDTO.setZoneId(assistant.getZone().getId());
                }
            }
        }
        userDTO.getRole().setRolePrivileges(rolePrivilegeDTOS);

        log.debug("End service loadUserWithRolePrivileges for userId: {}", userId);
        return userDTO;
    }

    @Transactional
    public void inviteExternalUserByEmail(String email, Long roleId, String redirectUrl) throws TechnicalException {
        log.debug("Start service inviteExternalUserByEmail for email: {}", email);

        // Check if user already exists in our system
        CacheAdUser existingUser = cacheAdUserRepository.findByMail(email);
        if (existingUser != null && !existingUser.isDeleted()) {
            log.error("User with email: {} already exists in the database", email);
            throw new UserAlreadyExistsException("User with email: " + email + " already exists");
        }

        try {
            // Create invitation object
            Invitation invitation = new Invitation();
            invitation.invitedUserEmailAddress = email;
            invitation.inviteRedirectUrl = redirectUrl;
            invitation.sendInvitationMessage = true;

            // Optional: Set additional properties
            invitation.invitedUserDisplayName = email.split("@")[0]; // Use part of email as display name

            // Send invitation via Graph API
            Invitation createdInvitation = graphClient.invitations()
                    .buildRequest()
                    .post(invitation);

            if (createdInvitation != null && createdInvitation.invitedUser != null) {
                // Map and save the invited user to our database
                CacheAdUser cacheAdUser = new CacheAdUser();
                cacheAdUser.setAzureDirectoryId(createdInvitation.invitedUser.id);
                cacheAdUser.setMail(email);
                cacheAdUser.setFirstName(invitation.invitedUserDisplayName);

                // Set role
                Role role = roleRepository.findById(roleId).orElseThrow(() ->
                        new EntityNotFoundException("Role with ID " + roleId + " not found"));
                cacheAdUser.setRole(role);

                // Set profile (assuming you want to set a default profile)
                cacheAdUser.setProfile(profileRepository.findById(ProfilesId.ADMIINMASTER.getId())
                        .orElseThrow(() -> new EntityNotFoundException("Profile not found")));

                cacheAdUserRepository.save(cacheAdUser);

                // Add to cache
                adddUserToCache(cacheAdUser);

                log.debug("External user invited and saved with email: {}", email);
            } else {
                throw new TechnicalException("Failed to invite user: No user data returned from invitation");
            }
        } catch (GraphServiceException | TechnicalException ex) {
            log.error("Error inviting user with email {}: {}", email, ex.getMessage());
            throw new TechnicalException("Error inviting user with email: " + email + ": " + ex.getMessage());
        }
    }

    public void setTokenImpersonationService(TokenImpersonationService tokenImpersonationService) {
        this.tokenImpersonationService = tokenImpersonationService;
    }

}
