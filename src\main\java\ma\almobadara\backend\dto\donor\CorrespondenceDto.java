package ma.almobadara.backend.dto.donor;

import jakarta.validation.constraints.NotNull;
import lombok.*;
import ma.almobadara.backend.dto.administration.CacheAdUserDTO;
import ma.almobadara.backend.dto.referentiel.CanalCommunicationDTO;
import ma.almobadara.backend.enumeration.Direction;

import java.time.LocalDate;

import static ma.almobadara.backend.util.constants.GlobalConstants.USER_ID_AFFECTED_BY_NOT_FOUND;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class CorrespondenceDto {

    private Long id;
    private LocalDate date;
    private Direction direction;
    private String content;
    private String subject;
    @NotNull(message = USER_ID_AFFECTED_BY_NOT_FOUND)
    private CacheAdUserDTO affectedTo;
    private CanalCommunicationDTO canalCommunication;
    private DocumentCorrespondenceDTO documents;
    private Long donorId;

}
