package ma.almobadara.backend.model.takenInCharge;

import jakarta.persistence.*;
import lombok.*;
import ma.almobadara.backend.model.donor.Donor;

import java.util.List;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TakenInChargeDonor {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private Boolean keepanonymous;
    @ManyToOne
    @JoinColumn(name = "donor_id")
    private Donor donor;
    @ManyToOne
    @JoinColumn(name = "taken_in_charge_id")
    private TakenInCharge takenInCharge;
    @OneToMany(mappedBy = "takenInChargeDonor")
    private List<TakenInChargeOperation> takenInChargeOperations;

}
