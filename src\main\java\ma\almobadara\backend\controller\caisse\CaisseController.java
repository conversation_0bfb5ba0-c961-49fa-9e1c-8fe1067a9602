package ma.almobadara.backend.controller.caisse;

import jakarta.persistence.EntityNotFoundException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.caisse.CaisseDataDTO;
import ma.almobadara.backend.dto.caisse.CaisseDto;
import ma.almobadara.backend.dto.referentiel.TypePriseEnChargeDTO;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.service.caisse.CaisseService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.data.domain.Pageable;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/caisses")
@AllArgsConstructor
@Slf4j
public class CaisseController {

   /* private final CaisseService caisseService;

    @PostMapping
    public ResponseEntity<CaisseDto> addCaisse(@RequestBody CaisseDto caisseDto) {
        log.info("Adding new Caisse: {}", caisseDto);
        CaisseDto createdCaisse = caisseService.addCaisse(caisseDto);
        return new ResponseEntity<>(createdCaisse, HttpStatus.CREATED);
    }

    @GetMapping
    public ResponseEntity<Page<CaisseDto>> getAllCaisses(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "5") int size,
            @RequestParam(required = false) final String searchBycode,
            @RequestParam(required = false) final String searchByName
    ) {
        log.info("Start resource getAllCaisses");
        Page<CaisseDto> caisses = caisseService.getAllCaisses(page, size, searchBycode, searchByName);
        return ResponseEntity.ok(caisses);
    }

    @GetMapping("/{id}")
    public ResponseEntity<CaisseDto> getCaisseById(
            @PathVariable Long id,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        Pageable pageable = PageRequest.of(page, size);
        CaisseDto caisseDto = caisseService.getCaisseById(id, pageable);
        return ResponseEntity.ok(caisseDto);
    }


    @PutMapping("/{id}")
    public ResponseEntity<Object> updateCaisse(@PathVariable Long id, @RequestBody CaisseDto caisseDto) {
        log.debug("Start resource update caisse with id {}", id);
        try {
            caisseService.updateCaisse(id, caisseDto);
            log.debug("End resource update caisse with id {}", id);
            return ResponseEntity.ok().body(Map.of("message", "Caisse updated successfully with id " + id));
        } catch (EntityNotFoundException ex) {
            log.error("Caisse not found with id: {}", id, ex);
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(Map.of("error", "Caisse not found", "message", "Caisse not found with id: " + id));
        } catch (TechnicalException ex) {
            log.error("Error updating caisse with id {}: {}", id, ex.getMessage(), ex);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("error", "Technical error", "message", "Error updating caisse: " + ex.getMessage()));
        } catch (RuntimeException ex) {
            log.error("Unexpected error while updating caisse with id {}: {}", id, ex.getMessage(), ex);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Unexpected error", "message", "An unexpected error occurred: " + ex.getMessage()));
        }
    }

    @GetMapping("/type-prise-en-charge-disponibles")
    public ResponseEntity<List<TypePriseEnChargeDTO>> getAvailableTypePriseEnCharges() {
        List<TypePriseEnChargeDTO> availableTypes = caisseService.getAvailableTypePriseEnCharges();
        return ResponseEntity.ok(availableTypes);
    }

    @GetMapping("/data/{id}")
    public ResponseEntity<List<CaisseDataDTO>> getCaisseData(@PathVariable Long id) {
        List<CaisseDataDTO> caisseDataList = caisseService.getCaisseData(id);

        if (caisseDataList.isEmpty()) {
            return new ResponseEntity<>(HttpStatus.NO_CONTENT);
        }
        return new ResponseEntity<>(caisseDataList, HttpStatus.OK);
    }
*/
}
