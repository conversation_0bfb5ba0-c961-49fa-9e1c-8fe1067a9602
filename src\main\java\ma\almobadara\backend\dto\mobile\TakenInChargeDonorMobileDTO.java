package ma.almobadara.backend.dto.mobile;

import lombok.*;
import lombok.experimental.SuperBuilder;
import ma.almobadara.backend.dto.beneficiary.BeneficiaryDTO;
import ma.almobadara.backend.dto.donor.DonorDTO;
import ma.almobadara.backend.dto.takenInCharge.TakenInChargeDTO;
import ma.almobadara.backend.dto.takenInCharge.TakenInChargeOperationDTO;
import ma.almobadara.backend.model.beneficiary.Beneficiary;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@SuperBuilder
@ToString
@AllArgsConstructor
public class TakenInChargeDonorMobileDTO {
    private Long id;

    private Boolean keepanonymous;

    private DonorDTO donor;

    private TakenInChargeDTO takenInCharge;

    private List<TakenInChargeOperationDTO> takenInChargeOperations;

    private Double DonorBalance;

    private BeneficiaryDTO beneficiary;
}
