package ma.almobadara.backend.service.donor;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.Audit.AuditApplicationService;
import ma.almobadara.backend.clients.RefFeignClient;
import ma.almobadara.backend.controller.referentiel.RefController;
import ma.almobadara.backend.dto.donor.CorrespondenceDto;
import ma.almobadara.backend.dto.donor.DocumentCorrespondenceDTO;
import ma.almobadara.backend.dto.referentiel.CanalCommunicationDTO;
import ma.almobadara.backend.mapper.CorrespondenceMapper;
import ma.almobadara.backend.mapper.DocumentMapper;
import ma.almobadara.backend.model.communs.Document;
import ma.almobadara.backend.model.donor.*;
import ma.almobadara.backend.repository.communs.DocumentRepository;
import ma.almobadara.backend.repository.donor.CorrespondenceRepository;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static ma.almobadara.backend.service.administration.CacheAdUserService.getUsernameFromJwt;
import static ma.almobadara.backend.util.constants.GlobalConstants.*;
import static ma.almobadara.backend.util.strings.HandleSpecialChars.escapeSpecialChars;

@Service
@RequiredArgsConstructor
@Slf4j
public class CorrespondenceService {

    private final CorrespondenceRepository correspondenceRepository;
    private final CorrespondenceMapper correspondenceMapper;
    private final DocumentMapper documentMapper;
    private final DocumentRepository documentRepository;
    private final RefController refController;
    private final RefFeignClient refFeignClient;
    private final AuditApplicationService auditApplicationService;



    public CorrespondenceDto addCorrespondence(CorrespondenceDto correspondenceDto) {
        Correspondence correspondence = correspondenceMapper.correspondenceDtoModelToModel(correspondenceDto);

        if(correspondenceDto.getId() != null) {
            Optional<Correspondence> correspondenceOptional = correspondenceRepository.findById(correspondenceDto.getId());
            correspondence.setId(correspondenceOptional.get().getId());
        }

        // Vérifie si le document doit être sauvegardé
        if (correspondenceDto.getDocuments() != null) {
            DocumentCorrespondenceDTO documentDto = correspondenceDto.getDocuments();
            Document document = documentMapper.documentToModelToModel(documentDto);
            // Sauvegarde le document
            Document savedDocument = documentRepository.save(document);
            // Associe le document sauvegardé à la correspondance
            correspondence.setDocuments(savedDocument);
        }

        // Enregistre la correspondance
        Correspondence addedCorrespondence = correspondenceRepository.save(correspondence);
        return correspondenceMapper.correspondenceModelToDto(addedCorrespondence);
    }

    public List<CorrespondenceDto> getCorrespondenceByIdDonor(Long id) {
        List<Correspondence> correspondenceList = correspondenceRepository.findByDonorId(id);
        return correspondenceList.stream()
                .map(correspondenceMapper::correspondenceModelToDto)
                .collect(Collectors.toList());
    }

    public List<CorrespondenceDto> getAllCorrespondences() {
        Iterable<Correspondence> correspondences = correspondenceRepository.findAll();
        Iterable<CorrespondenceDto> correspondenceDtos = correspondenceMapper.correspondenceListModelToDto(correspondences);
        List<CorrespondenceDto> correspondenceDtoList = new ArrayList<>();
        correspondenceDtos.forEach(correspondenceDtoList::add);
        return correspondenceDtoList;
    }

    public void deleteCorrespondence(Long id) {
        Correspondence correspondence = correspondenceRepository.findById(id).get();
        Donor donor = correspondence.getDonor();
        CanalCommunicationDTO canalCommunication=null;
        if(correspondence.getCanalCommunicationId() != null) {
            canalCommunication = refFeignClient.getMetCanalCommunication(correspondence.getCanalCommunicationId());
        }

        if (donor instanceof DonorPhysical){
            DonorPhysical donorPhysical = (DonorPhysical) donor;
            StringBuilder columnToAppend = new StringBuilder();
            columnToAppend.append("\"Donateur type\": \"" + "Physique" + "\",");
            columnToAppend.append("\"Donateur Code\": \"" + escapeSpecialChars(donorPhysical.getCode()) + "\",");
            columnToAppend.append("\"Donateur name\": \"" + escapeSpecialChars(donorPhysical.getLastName() +" "+donorPhysical.getFirstName()) + "\"");
            auditApplicationService.audit("Suppression du Correspondance du Donateur : "+donor.getCode(), getUsernameFromJwt(), "Suppression du Correspondance",
                    correspondence.getAudit(canalCommunication.getName(),columnToAppend), null, DONATEUR, DELETE);
        }
        else if(donor instanceof DonorMoral){
            DonorMoral donorMoral = (DonorMoral) donor;

            StringBuilder columnToAppend = new StringBuilder();
            columnToAppend.append("\"Donateur type\": \"" + "Moral" + "\",");
            columnToAppend.append("\"Donateur Code\": \"" + escapeSpecialChars(donorMoral.getCode()) + "\",");
            columnToAppend.append("\"Donateur name\": \"" + escapeSpecialChars(donorMoral.getCompany()) + "\"");

            auditApplicationService.audit("Suppression du Correspondance du Donateur : "+donor.getCode(), getUsernameFromJwt(), "Suppression du Correspondance",
                    correspondence.getAudit(canalCommunication.getName(),columnToAppend), null, DONATEUR, DELETE);
        }
        else{
            DonorAnonyme donorAnonyme = (DonorAnonyme) donor;

            StringBuilder columnToAppend = new StringBuilder();
            columnToAppend.append("\"Donateur type\": \"" + "Anonyme" + "\",");
            columnToAppend.append("\"Donateur Code\": \"" + escapeSpecialChars(donorAnonyme.getCode()) + "\",");
            columnToAppend.append("\"Donateur name\": \"" + escapeSpecialChars(donorAnonyme.getName()) + "\"");
            auditApplicationService.audit("Suppression du Correspondance du Donateur : "+donor.getCode(), getUsernameFromJwt(), "Suppression du Correspondance",
                    correspondence.getAudit(canalCommunication.getName(),columnToAppend), null, DONATEUR, DELETE);
        }

        correspondenceRepository.deleteById(id);
    }


}
