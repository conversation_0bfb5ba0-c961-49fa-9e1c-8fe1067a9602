package ma.almobadara.backend.service.family;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.*;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.Audit.AuditApplicationService;
import ma.almobadara.backend.clients.RefFeignClient;
import ma.almobadara.backend.config.Messages;
import ma.almobadara.backend.controller.referentiel.RefController;
import ma.almobadara.backend.dto.CityWithRegionAndCountryDTO;
import ma.almobadara.backend.dto.ExternalIncomeDTO;
import ma.almobadara.backend.dto.administration.CacheAdUserDTO;
import ma.almobadara.backend.dto.administration.SousZoneDTO;
import ma.almobadara.backend.dto.administration.TagDTO;
import ma.almobadara.backend.dto.administration.ZoneDTO;
import ma.almobadara.backend.dto.beneficiary.BankCardDTO;
import ma.almobadara.backend.dto.beneficiary.BeneficiaryDTO;
import ma.almobadara.backend.dto.beneficiary.BeneficiaryServiceDTO;
import ma.almobadara.backend.dto.beneficiary.PersonDTO;
import ma.almobadara.backend.dto.donor.DonorDTO;
import ma.almobadara.backend.dto.exportentities.ExportFileDTO;
import ma.almobadara.backend.dto.exportentities.FamilyExportDTO;
import ma.almobadara.backend.dto.family.FamilyDTO;
import ma.almobadara.backend.dto.family.FamilyMemberDTO;
import ma.almobadara.backend.dto.family.TutorHistoryDTO;
import ma.almobadara.backend.dto.referentiel.*;
import ma.almobadara.backend.dto.service.ServicesDTO;
import ma.almobadara.backend.dto.takenInCharge.TakenInChargeBeneficiaryDTO;
import ma.almobadara.backend.dto.takenInCharge.TakenInChargeDonorDTO;
import ma.almobadara.backend.enumeration.EntitiesToExport.FamilyExportHeaders;
import ma.almobadara.backend.enumeration.RoleCode;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.mapper.*;
import ma.almobadara.backend.model.administration.*;
import ma.almobadara.backend.model.beneficiary.Beneficiary;
import ma.almobadara.backend.model.family.*;
import ma.almobadara.backend.model.service.Services;
import ma.almobadara.backend.model.takenInCharge.TakenInCharge;
import ma.almobadara.backend.repository.administration.AssistantRepository;
import ma.almobadara.backend.repository.administration.SousZoneRepository;
import ma.almobadara.backend.repository.administration.TaggableRepository;
import ma.almobadara.backend.repository.administration.ZoneRepository;
import ma.almobadara.backend.repository.beneficiary.BeneficiaryRepository;
import ma.almobadara.backend.repository.communs.DocumentRepository;
import ma.almobadara.backend.repository.family.*;
import ma.almobadara.backend.repository.services.ServicesRepository;
import ma.almobadara.backend.repository.takenInCharge.TakenInChargeRepository;
import ma.almobadara.backend.service.administration.TokenImpersonationService;
import jakarta.servlet.http.HttpServletRequest;
import ma.almobadara.backend.service.communs.ExportService;
import ma.almobadara.backend.service.donor.DonorService;
import ma.almobadara.backend.service.donor.MinioService;
import ma.almobadara.backend.util.times.TimeWatch;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.*;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import static ma.almobadara.backend.Audit.ObjectConverter.convertObjectToJson;
import static ma.almobadara.backend.service.administration.CacheAdUserService.getRoleFromJwt;
import static ma.almobadara.backend.service.administration.CacheAdUserService.getUsernameFromJwt;
import static ma.almobadara.backend.util.constants.GlobalConstants.*;

@Service
@RequiredArgsConstructor
@Slf4j
public class FamilyService {
    private final AssistantRepository assistantRepository;
    private final ServicesRepository servicesRepository;

    private final FamilyMapper familyMapper;
    private final FamilyRepository familyRepository;
    private final FamilyYearCountRepository familyYearCountRepository;
    private final FamilyMemberMapper memberMapper;
    private final FamilyMemberRepository memberRepository;
    private final ExternalIncomeRepository externalIncomeRepository;
    private final MinioService minioService;
    private final RefController refController;
    private final BeneficiaryRepository beneficiaryRepository;
    private final BeneficiaryMapper beneficiaryMapper;
    private final DonorService donorService;
    private final RefFeignClient refFeignClient;
    private final Messages messages;
    private final EntityManager entityManager;
    private final AuditApplicationService auditApplicationService;
    private final ExportService exportService;
    private final TutorHistoryRepository tutorHistoryRepository;
    private final TutorHistoryMapper tutorHistoryMapper;
    private final ServicesMapper servicesMapper;
    private final FamilyDocumentRepository familyDocumentRepository;
    private final DocumentRepository documentRepository;
    private final TakenInChargeRepository takenInChargeRepository;
    private final TokenImpersonationService tokenImpersonationService;
    private final ZoneRepository zoneRepository;
    private final SousZoneRepository sousZoneRepository;
    private final SousZoneMapper sousZoneMapper;
    private final ZoneMapper zoneMapper = ZoneMapper.INSTANCE;
    private final TaggableRepository taggableRepository;
    private final HttpServletRequest request;


    @Value("${minio.familiesFolder}")
    private String familiesFolder;

    @Value("${minio.membersFolder}")
    private String membersFolder;

    @Value("${minio.profilePicture.folder}")
    private String folderPathPicture;

    @Value("${minio.profilePicture.abv}")
    private String abv;

    public FamilyDTO addFamily(FamilyDTO familyRequest) {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service Add Family");

        Family family = familyMapper.familyDTOToFamily((familyRequest));
        List<FamilyMember> familyMembers = family.getFamilyMembers();
        family.setFamilyMembers(null);
        Family savedFamily = familyRepository.save(family);
        savedFamily.setFamilyMembers(familyMembers);
        saveFamilyMembers(familyRequest.getFamilyMembers(), savedFamily);
        familyRepository.save(savedFamily);
        FamilyDTO savedFamilyDTO = familyMapper.familyToFamilyDTO(savedFamily);

        log.debug("End service Add Family, took {}", watch.toMS());
        return savedFamilyDTO;
    }

    public Family createNewFamily(String addressFamily, String addressFamilyAr, String generalCommentFamily, String phoneNumberFamily , Long cityId, Long accommodationTypeId, Long accommodationNatureId, Long zoneId, Long sousZoneId, List<TagDTO> tags) throws TechnicalException {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service Add Family");

        Family family = Family.builder().build();
        family.setAddressFamily(addressFamily);
        family.setAddressFamilyAr(addressFamilyAr);
        family.setGeneralCommentFamily(generalCommentFamily);
        family.setPhoneNumberFamily(phoneNumberFamily);
        family.setCityId(cityId);
        family.setAccommodationTypeId(accommodationTypeId);
        family.setAccommodationNatureId(accommodationNatureId);

        if (zoneId != null) {
            Zone zone = zoneRepository.findById(zoneId)
                    .orElseThrow(() -> new TechnicalException("Zone not found with id: " + zoneId));
            family.setZone(zone);
            Family finalFamily = family;
            Optional.ofNullable(sousZoneId).ifPresent(id -> {
                SousZone sousZone = null;
                try {
                    sousZone = sousZoneRepository.findById(id)
                            .orElseThrow(() -> new TechnicalException("SousZone not found with id: " + id));
                } catch (TechnicalException e) {
                    throw new RuntimeException(e);
                }
                finalFamily.setSousZone(sousZone);
            });
        } else {
            family.setSousZone(null);
        }

        family = familyRepository.save(family);
        generateFamilyCode(family);
        taggableRepository.deleteAllByTaggableIdAndTaggableType(family.getId(),"family");
        if(tags!=null){
            Family finalFamily1 = family;
            tags.forEach(tagDTO -> {
                Taggable taggable = new Taggable();
                Tag tag = new Tag();
                tag.setId(tagDTO.getId());
                taggable.setTag(tag);
                taggable.setTaggableType("family");
                taggable.setTaggableId(finalFamily1.getId());
                taggableRepository.save(taggable);
            });
        }
        log.debug("End service Add Family, took {}", watch.toMS());
        return family;
    }

    void generateFamilyCode(Family family) {
        String code;
        Optional<FamilyYearCount> familyYearCount = familyYearCountRepository.findByYear(String.valueOf(family.getCreatedAt().atZone(ZoneId.systemDefault()).toLocalDate().getYear()));
        if (familyYearCount.isPresent()) {
            code = F_CODE + familyYearCount.get().getYear();
            StringBuilder zeros = new StringBuilder();
            Long count = familyYearCount.get().getCount() + 1L;
            if (count.toString().length() < 4) {
                for (int i = 0; i < 4 - count.toString().length(); i++) {
                    zeros.append(ZEROS);
                }
            }
            code += zeros.toString() + count;
            FamilyYearCount familyYearCount1 = familyYearCount.get();
            familyYearCount1.setCount(familyYearCount.get().getCount() + 1L);
            familyYearCountRepository.save(familyYearCount1);
        } else {
            code = "F" + family.getCreatedAt().atZone(ZoneId.systemDefault()).toLocalDate().getYear() + "0001";
            FamilyYearCount familyYearCount1 = new FamilyYearCount();
            familyYearCount1.setCount(1L);
            familyYearCount1.setYear(String.valueOf(family.getCreatedAt().atZone(ZoneId.systemDefault()).toLocalDate().getYear()));
            familyYearCountRepository.save(familyYearCount1);
        }
        code += "XXX";
        family.setCode(code);
        familyRepository.save(family);
    }

    public void saveFamilyMembers(List<FamilyMemberDTO> memberDTOS, Family savedFamily) {

        List<FamilyMember> familyMembers = savedFamily.getFamilyMembers();
        List<FamilyMember> members = new ArrayList<>();
        if (memberDTOS != null && !memberDTOS.isEmpty()) {
            for (FamilyMemberDTO memberDTO : memberDTOS) {
                //Set member picture
                FamilyMember member = memberMapper.familyMemberDTOToFamilyMember(memberDTO);
                member.setFamily(savedFamily);
                //Generate code
                String code = generateFamilyMemberCode(member);
                member.setCode(code);
                saveMemberPicture(member, memberDTO.getPerson(), savedFamily);
                savedFamily.setFamilyMembers(null);
                member.setFamily(savedFamily);
                //Save member
                memberRepository.save(member);
                savedFamily.setFamilyMembers(familyMembers);
                //Save ExternalIncomes
                if (member.getExternalIncomes() != null) {
                    for (ExternalIncome externalIncome : member.getExternalIncomes()) {
                        externalIncome.setFamilyMember(member);
                        externalIncomeRepository.save(externalIncome);
                    }
                }
                members.add(member);
            }
            savedFamily.setFamilyMembers(members);
        }
    }

    public Family findFamilyById(Long id) throws TechnicalException {
        TimeWatch watch = TimeWatch.start();
        log.debug("start service findFamilyById with id: {}", id);
        var family = familyRepository.findById(id);
        if (family.isEmpty()) {
            throw new TechnicalException(messages.get(FAMILY_NOT_FOUND));
        }
        log.debug("End service findFamilyById, took {}", watch.toMS());
        return family.get();
    }

    public String generateFamilyMemberCode(FamilyMember familyMember) {
        String code = "";
        if (familyMember.getFamily().getCode() != null && familyMember.getCode() == null) {
            List<FamilyMember> familyMembers = memberRepository.findAllByFamily(familyMember.getFamily());
            String zeros = "";
            if (familyMembers.size() < 10) {
                zeros = "0";
            }
            code = M_CODE + zeros + (familyMembers.size() + 1) + familyMember.getFamily().getCode();
        } else if (familyMember.getFamily().getCode() != null && !familyMember.getCode().substring(2).equals(familyMember.getFamily().getCode())) {
            code = familyMember.getCode().substring(0, 3) + familyMember.getFamily().getCode();
        } else {
            code = familyMember.getCode();
        }
        return code;
    }

    public void saveMemberPicture(FamilyMember familyMember, PersonDTO personDTO, Family savedFamily) {
        String memberPath = familiesFolder + savedFamily.getFamilyMembers().get(0).getPerson().getLastName().toUpperCase() + "_" + savedFamily.getCode() + "/" + membersFolder + personDTO.getLastName().toUpperCase() + "-" + personDTO.getFirstName().substring(0, 1).toUpperCase() + personDTO.getFirstName().substring(1) + "_" + familyMember.getCode();
        if (personDTO.getPicture() != null) {
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.BASIC_ISO_DATE;
            Instant instant = Instant.now();
            String fileName = personDTO.getLastName().toUpperCase() + "-" + personDTO.getFirstName().substring(0, 1).toUpperCase() + personDTO.getFirstName().substring(1) + "_" + abv + "_" + instant.atZone(ZoneId.systemDefault()).toLocalDate().format(dateTimeFormatter) + "." + FilenameUtils.getExtension(personDTO.getPicture().getOriginalFilename());
            familyMember.getPerson().setPictureUrl(memberPath + "/" + folderPathPicture + "/" + fileName);
            minioService.WriteToMinIO(personDTO.getPicture(), memberPath + "/" + folderPathPicture + "/", fileName);
        }
    }
    private String convertMapToString(Map<String, String> map) {
        StringBuilder stringBuilder = new StringBuilder();
        for (Map.Entry<String, String> entry : map.entrySet()) {
            stringBuilder.append(entry.getKey()).append(": ").append(entry.getValue()).append(", ");
        }
        return stringBuilder.toString();
    }

    private String convertMapToJsonString(Map<String, String> map) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.writeValueAsString(map);
        } catch (Exception e) {
            // Gérer les exceptions de sérialisation JSON ici
            e.printStackTrace();
            return "{}"; // Retourne une chaîne JSON vide en cas d'erreur
        }
    }

    public void cleanListFamilyAfterGet() {
        List<Family> families = familyRepository.findAll();
        Iterator<Family> iterator = families.iterator();
        while (iterator.hasNext()) {
            Family family = iterator.next();
            List<FamilyMember> familyMembers = family.getFamilyMembers();
            if (familyMembers != null && !familyMembers.isEmpty()) {
                if (familyMembers.size() < 2 || familyMembers.stream().noneMatch(FamilyMember::isTutor)) {
                    List<DocumentFamily> documentFamilies = familyDocumentRepository.findByFamilyId(family.getId());
                    List<Long> documentsIds = documentFamilies.stream()
                            .map(documentFamily -> documentFamily.getDocument().getId())
                            .collect(Collectors.toList());
                    familyDocumentRepository.deleteAll(documentFamilies);
                    documentsIds.forEach(documentRepository::deleteById);
                    iterator.remove();
                    familyRepository.delete(family);
                }
            }else {
                List<DocumentFamily> documentFamilies = familyDocumentRepository.findByFamilyId(family.getId());
                List<Long> documentsIds = documentFamilies.stream()
                        .map(documentFamily -> documentFamily.getDocument().getId())
                        .collect(Collectors.toList());
                familyDocumentRepository.deleteAll(documentFamilies);
                documentsIds.forEach(documentRepository::deleteById);
                iterator.remove();
                familyRepository.delete(family);
            }
        }
    }


    public Page<FamilyDTO> getAllFamilies(Optional<Integer> page, String searchByTutorName, String lastNameAr, Long minNumber, Long maxNumber, String numTel, Date minDate, Date maxDate,Long searchByTagId) {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service Get All Families {}", "");
        // before everything, we need to do a get all families to clean the nested objects who are the family without any tutor or the one that have less than 2 members
        cleanListFamilyAfterGet();
        int pageNumber = 0;
        int pageSize = 10;
        if (page.isPresent()) {
            pageNumber = page.get();
        }

        Sort.Direction sortDirection = Sort.Direction.DESC;
        String sortBy = "createdAt";

        Sort sort = Sort.by(sortDirection, sortBy);
        Pageable pageable = PageRequest.of(pageNumber, pageSize, sort);
        Map<String, String> searchParams = new HashMap<>();
        if (searchByTutorName != null) {
            searchParams.put("Nom du Tuteur", searchByTutorName);
        }
        if (lastNameAr != null) {
            searchParams.put("Nom arabe du Tuteur", lastNameAr);
        }
        if (minNumber != null) {
            searchParams.put("Nombre des membres minimum", String.valueOf(minNumber));
        }
        if (maxNumber != null) {
            searchParams.put("Nombre des membres maximum", String.valueOf(maxNumber));
        }
        if (numTel != null) {
            searchParams.put("Numéro Téléphone", numTel);
        }
        if (minDate != null) {
            searchParams.put("Date de creation de famille minimale", String.valueOf(minDate));
        }
        if (maxDate != null) {
            searchParams.put("Date de creation de famille maximale", String.valueOf(maxDate));
        }

        String jsonSearchParams = convertMapToJsonString(searchParams);


        Long zoneId = null;
        CacheAdUser cacheAdUser  =  getRoleFromJwt();
        assert cacheAdUser != null;
        // Get impersonation token from request header
        String impersonationToken = request.getHeader("Impersonation-Token");
        CacheAdUserDTO cacheAdUser1 = null;
        if (impersonationToken != null && !impersonationToken.isEmpty()) {
            cacheAdUser1 = tokenImpersonationService.getImpersonatedUser(impersonationToken);
        }
        if (cacheAdUser1 != null && cacheAdUser1.getRole().getCode().equals(RoleCode.ASSISTANT.getCode())) {
            Assistant assistant = assistantRepository.findByCacheAdUserId(cacheAdUser1.getId());
            if (assistant != null) {
                if (assistant.getZone() != null && assistant.getZone().getId() != null) {
                    zoneId = assistant.getZone().getId();
                } else {
                    return new PageImpl<>(Collections.emptyList(), pageable, 0);
                }
            } else {
                return new PageImpl<>(Collections.emptyList(), pageable, 0);
            }
        }
        else
        if(cacheAdUser.getRole().getCode().equals(RoleCode.ASSISTANT.getCode())){
            Assistant assistant = assistantRepository.findByCacheAdUserId(cacheAdUser.getId());
            if (assistant != null){
                if (assistant.getZone() != null && assistant.getZone().getId() != null ){
                    zoneId = assistant.getZone().getId();
                }else {
                    return new PageImpl<>(Collections.emptyList(), pageable, 0);
                }
            }else {
                return new PageImpl<>(Collections.emptyList(), pageable, 0);
            }
        }

        Page<Family> listFamilies;
        if (searchByTutorName != null || lastNameAr != null || minNumber != null || maxNumber != null || numTel != null || minDate != null || maxDate != null || searchByTagId != null) {
            listFamilies = filterFamillies(zoneId, searchByTutorName, lastNameAr, minNumber, maxNumber, numTel, minDate, maxDate,searchByTagId, pageable);
            auditApplicationService.audit("Recherche par filtre dans la liste des familles", getUsernameFromJwt(), "Liste des familles",
                    jsonSearchParams, null, FAMILLE, VIEW);
        } else {
            if (zoneId != null) {
                listFamilies = familyRepository.findFamilyByZoneId(pageable, zoneId);
            }else {
                listFamilies = familyRepository.findAll(pageable);
            }
            auditApplicationService.audit("Consultation de la liste des familles globale", getUsernameFromJwt(), "Liste des familles",
                    null, null, FAMILLE, CONSULTATION);
        }


        Iterable<FamilyDTO> familyDTOIterable = familyMapper.familyToFamilyDTO(listFamilies);
        List<FamilyDTO> familyDTOS = StreamSupport.stream(familyDTOIterable.spliterator(), false).collect(Collectors.toList());
        List<FamilyDTO> familyDTOS1 = familyDTOS.stream().map(familyDTO -> {

            List<FamilyMemberDTO> familyMemberDTOS = familyDTO.getFamilyMembers().stream().map((e) -> {
                //Setting familyRelationship full info
                if (e.getFamilyRelationship().getId() != null) {
                    List<FamilyRelationshipDTO> familyRelationshipDTOS = refController.getAllMetFamilyRelationships().getBody();
                    for (FamilyRelationshipDTO familyRelationshipDTO : familyRelationshipDTOS) {
                        if (e.getFamilyRelationship().getId() == 1L && Objects.equals(familyRelationshipDTO.getName(), "Père")) {
                            e.setFamilyRelationship(familyRelationshipDTO);
                        } else if (e.getFamilyRelationship().getId() == 2L && Objects.equals(familyRelationshipDTO.getName(), "Mère")) {
                            e.setFamilyRelationship(familyRelationshipDTO);
                        } else if (e.getFamilyRelationship().getId().equals(familyRelationshipDTO.getId())) {
                            e.setFamilyRelationship(familyRelationshipDTO);
                        }
                    }
                }
                if (e.getPerson().getCity().getId() != null) {
                    CityDTO cityDTO = refController.getParCity(e.getPerson().getCity().getId()).getBody();
                    e.getPerson().setCity(cityDTO);
                }
                if (e.getPerson().getProfession().getId() != null) {

                    ProfessionDTO professionDTO = refController.getMetProfession(e.getPerson().getProfession().getId()).getBody();
                    e.getPerson().setProfession(professionDTO);

                }
                if (e.getPerson().getTypeIdentity().getId() != null) {

                    TypeIdentityDTO typeIdentityDTO = refController.getParTypeIdentity(e.getPerson().getTypeIdentity().getId()).getBody();
                    e.getPerson().setTypeIdentity(typeIdentityDTO);

                }
                if (e.getPerson().getSchoolLevel().getId() != null) {

                    SchoolLevelDTO schoolLevelDTO = refController.getParSchoolLevel(e.getPerson().getSchoolLevel().getId()).getBody();
                    e.getPerson().setSchoolLevel(schoolLevelDTO);

                }
                Optional<Beneficiary> beneficiary = beneficiaryRepository.findByPersonId(e.getPerson().getId());
                if (beneficiary.isPresent()) {
                    BeneficiaryDTO beneficiaryDTO = beneficiaryMapper.beneficiaryToBeneficiaryDTO(beneficiary.get());
                    if (beneficiaryDTO.getBeneficiaryServices() != null) {
                        Set<BeneficiaryServiceDTO> beneficiaryServiceDTOS = beneficiaryDTO.getBeneficiaryServices().stream().map((b) -> {
                            ServiceDTO serviceDTO = b.getService();
                            StatusDTO statusDTO = b.getStatus();
                            ServiceDTO fullServiceDTO = new ServiceDTO();
                            StatusDTO fullStatusDTO = new StatusDTO();
                            if (serviceDTO.getId() != null && statusDTO != null) {

                                fullServiceDTO = refController.getMetService(serviceDTO.getId()).getBody();
                                fullStatusDTO = refController.getParStatus(statusDTO.getId()).getBody();

                                b.setService(fullServiceDTO);
                                b.setStatus(fullStatusDTO);
                            }
                            return b;
                        }).collect(Collectors.toSet());
                        beneficiaryDTO.setBeneficiaryServices(beneficiaryServiceDTOS);
                    }
                    e.getPerson().setBeneficiary(beneficiaryDTO);
                }

                return e;
            }).collect(Collectors.toList());
            familyDTO.setFamilyMembers(familyMemberDTOS);
            return familyDTO;
        }).collect(Collectors.toList());
        familyDTOS1= familyDTOS1.stream().peek(dto->{
            List<Taggable> taggables = taggableRepository.findByTaggableIdAndTaggableType(dto.getId(), "family");
            List<TagDTO> tagDTOs = new ArrayList<>();
            for (Taggable taggable : taggables) {
                Tag tag = taggable.getTag();
                TagDTO tagDTO = new TagDTO();
                tagDTO.setId(tag.getId());
                tagDTO.setName(tag.getName());
                tagDTO.setColor(tag.getColor());
                tagDTOs.add(tagDTO);
            }
            dto.setTags(tagDTOs);
        }).collect(Collectors.toList());
        log.debug("End service Get All Families , took {}", watch.toMS());
        return new PageImpl<>(familyDTOS1, pageable, listFamilies.getTotalElements());

    }

    public Page<Family> filterFamillies(Long zoneId, String searchByTutorName, String lastNameAr, Long minNumber, Long maxNumber, String numTel, Date minDate, Date maxDate,Long searchByTagId, Pageable pageable) {
        // Create the criteria query using the helper method
        TypedQuery<Family> typedQuery = createFamilyCriteriaQuery(zoneId,searchByTutorName, lastNameAr, minNumber, maxNumber, numTel, minDate, maxDate,searchByTagId);

        //var result = typedQuery.getResultList();
        // Get the total count of records
        long totalCount = typedQuery.getResultList().size();

        // Set pagination parameters
        typedQuery.setFirstResult((int) pageable.getOffset());
        typedQuery.setMaxResults(pageable.getPageSize());

        // Retrieve the paginated results
        List<Family> resultList = typedQuery.getResultList();

        return new PageImpl<>(resultList, pageable, totalCount);
    }

    public FamilyDTO getFamilyById(Long id) throws TechnicalException {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service Get Family by ID: {}", id);

        // get zoneId from jwt
        Long zoneId;
        CacheAdUser cacheAdUser  =  getRoleFromJwt();
        Assistant assistant =null ;

        // Get impersonation token from request header
        String impersonationToken = request.getHeader("Impersonation-Token");
        CacheAdUserDTO cacheAdUser1 = null;
        if (impersonationToken != null && !impersonationToken.isEmpty()) {
            cacheAdUser1 = tokenImpersonationService.getImpersonatedUser(impersonationToken);
        }
        if (cacheAdUser1 != null && cacheAdUser1.getRole().getCode().equals(RoleCode.ASSISTANT.getCode())) {
             assistant = assistantRepository.findByCacheAdUserId(cacheAdUser1.getId());
            if (assistant != null) {
                if (assistant.getZone() != null && assistant.getZone().getId() != null) {
                    zoneId = assistant.getZone().getId();
                } else {
                    zoneId = null;
                }
            } else {
                zoneId = null;
            }
        }
        else
        if(cacheAdUser!=null && cacheAdUser.getRole().getCode().equals(RoleCode.ASSISTANT.getCode())){
            assistant = assistantRepository.findByCacheAdUserId(cacheAdUser.getId());
            if (assistant != null){
                if (assistant.getZone() != null && assistant.getZone().getId() != null ){
                    zoneId = assistant.getZone().getId();
                } else {
                    zoneId = null;
                }
            } else {
                zoneId = null;
            }
        } else {
            zoneId = null;
        }

        Optional<Family> family = familyRepository.findById(id);

        Family familyAudit = family.orElse(null);


        FamilyDTO familyDTO = familyMapper.familyToFamilyDTO(family.get());
        if (zoneId != null) {
            List<FamilyMemberDTO> membersToRemove = new ArrayList<>();

            for (FamilyMemberDTO familyMemberDTO : familyDTO.getFamilyMembers()) {
                if (familyMemberDTO.getPerson() != null && familyMemberDTO.getPerson().getId() != null) {
                    Optional<Beneficiary> optionalBeneficiary = beneficiaryRepository.findByPersonId(familyMemberDTO.getPerson().getId());

                    if (optionalBeneficiary.isPresent()) {
                        Beneficiary beneficiary = optionalBeneficiary.get();
                        // we should add teh case when it as assistant and the zone is null
                        if (!Objects.equals(beneficiary.getZone().getId(), zoneId)) {
                            familyMemberDTO.setCanBeUpdatedByAssistant(false);
                        }
                    }
                }
            }

            // Supprimer les membres en dehors de la boucle d'itération
            familyDTO.getFamilyMembers().removeAll(membersToRemove);
        }

        String cityName = "";
        String natureAccomName = "";
        String typeAccomName = "";
        if (family.get() != null && family.get().getCityId() != null) {
            CityDTO fullCityDTO = refFeignClient.getParCity(family.get().getCityId());
            familyDTO.setCity(fullCityDTO);
            cityName = (familyDTO.getCity() != null ? familyDTO.getCity().getName() : "-");

            CityWithRegionAndCountryDTO fullCountryDto = refController.getCityWithRegionAndCountry(family.get().getCityId()).getBody();

            if (fullCountryDto != null) {
                RegionDTO regionDTO = fullCountryDto.getRegion();
                fullCityDTO.setRegion(regionDTO);
                if (regionDTO != null) {
                    CountryDTO countryDTO = regionDTO.getCountry();
                    fullCityDTO.getRegion().setCountry(countryDTO);
                    familyDTO.setCity(fullCityDTO);
                }
            }

            familyDTO.setCity(fullCityDTO);
        }

        if (family.get() != null && family.get().getAccommodationTypeId() != null) {
            AccommodationTypeDTO fullAccommodationTypeDTO = refFeignClient.getMetAccommodationType(family.get().getAccommodationTypeId());
            familyDTO.setAccommodationType(fullAccommodationTypeDTO);
            typeAccomName = (familyDTO.getAccommodationType() != null ? familyDTO.getAccommodationType().getName() : "");
        }

        if (family.get() != null && family.get().getAccommodationNatureId() != null) {
            AccommodationNatureDTO fullAccommodationNatureDTO = refFeignClient.getParAccommodationNature(family.get().getAccommodationNatureId());
            familyDTO.setAccommodationNature(fullAccommodationNatureDTO);
            natureAccomName = (familyDTO.getAccommodationNature() != null ? familyDTO.getAccommodationNature().getName() : "");
        }

        if (family.get() != null && family.get().getZone() != null) {
            ZoneDTO zoneDTO = zoneMapper.zoneToLightZoneDTO(family.get().getZone());
            zoneDTO.setEps(null);
            familyDTO.setZoneDTO(zoneDTO);

            if (family.get().getSousZone() != null){
                SousZoneDTO sousZoneDTO = sousZoneMapper.toDto(family.get().getSousZone());
                zoneDTO.setEps(null);
                familyDTO.setSousZoneDTO(sousZoneDTO);
            }
        }

        Zone zone = zoneMapper.zoneDTOToZone(familyDTO.getZoneDTO());
        familyAudit.setZone(zone);
        String nomFamille = familyDTO.getFamilyMembers().stream()
                .filter(fm -> fm.getFamilyRelationship().getId() == 1L)
                .findFirst()
                .map(fm -> fm.getPerson().getLastName())
                .orElse("N/A");
        String familyAuditString = familyAudit.getAuditForConsultation(nomFamille,cityName,natureAccomName,typeAccomName);
        //Get FamilyMembers Pictures
        List<FamilyMemberDTO> familyMemberDTOS = familyDTO.getFamilyMembers();

        if (familyMemberDTOS != null) {
            for (int i = 0; i < familyMemberDTOS.size(); i++) {
                if (familyMemberDTOS.get(i).getPerson().getPictureUrl() != null) {
                    try {
                        // Retrieve the image from MinIO
                        byte[] imageData = minioService.ReadFromMinIO(familyMemberDTOS.get(i).getPerson().getPictureUrl(),null);
                        String base64Image = Base64.getEncoder().encodeToString(imageData);
                        // Set the base64 string to the DonorPhysicalDTO object
                        familyMemberDTOS.get(i).getPerson().setPictureBase64(base64Image);
                    } catch (TechnicalException ex) {
                        ex.printStackTrace();
                    }
                }
                if(zoneId==null && assistant != null){
                    familyMemberDTOS.get(i).setCanBeUpdatedByAssistant(false);
                }
            }
            familyDTO.setFamilyMembers(familyMemberDTOS);
        }

        List<FamilyMemberDTO> FamilyMemberDTO = familyDTO.getFamilyMembers().stream().map((e) -> {
            //Setting familyRelationship full info
            if (e.getFamilyRelationship().getId() != null) {
                List<FamilyRelationshipDTO> familyRelationshipDTOS = refController.getAllMetFamilyRelationships().getBody();
                for (FamilyRelationshipDTO familyRelationshipDTO : familyRelationshipDTOS) {
                    if (e.getFamilyRelationship().getId() == 1L && Objects.equals(familyRelationshipDTO.getName(), "Père") || e.getFamilyRelationship().getId() == 2L && Objects.equals(familyRelationshipDTO.getName(), "Mère") || e.getFamilyRelationship().getId().equals(familyRelationshipDTO.getId())) {
                        e.setFamilyRelationship(familyRelationshipDTO);
                    }
                }
            }
            if (e.getPerson().getCity().getId() != null) {
                CityDTO cityDTO = e.getPerson().getCity();
                if (cityDTO.getId() != null) {
                    CityDTO fullCityDTO = refFeignClient.getParCity(cityDTO.getId());
                    e.getPerson().setCity(fullCityDTO);

                    CityWithRegionAndCountryDTO fullCountryDto = refController.getCityWithRegionAndCountry(cityDTO.getId()).getBody();
                    e.getPerson().setInfo(fullCountryDto);

                    if (fullCountryDto != null) {
                        RegionDTO regionDTO = fullCountryDto.getRegion();
                        if (regionDTO != null) {
                            CountryDTO countryDTO = regionDTO.getCountry();
                            fullCityDTO.getRegion().setCountry(countryDTO);
                            e.getPerson().setCity(fullCityDTO);
                        }
                    }
                }

            }

            if (e.getPerson().getProfession().getId() != null) {
                ProfessionDTO professionDTO = refController.getMetProfession(e.getPerson().getProfession().getId()).getBody();
                e.getPerson().setProfession(professionDTO);

            }
            if (e.getPerson().getTypeIdentity().getId() != null) {

                TypeIdentityDTO typeIdentityDTO = refController.getParTypeIdentity(e.getPerson().getTypeIdentity().getId()).getBody();
                e.getPerson().setTypeIdentity(typeIdentityDTO);

            }
            if (e.getPerson().getSchoolLevel().getId() != null) {

                SchoolLevelDTO schoolLevelDTO = refController.getParSchoolLevel(e.getPerson().getSchoolLevel().getId()).getBody();
                e.getPerson().setSchoolLevel(schoolLevelDTO);
                assert schoolLevelDTO != null;
                e.getPerson().setSchoolLevelType(schoolLevelDTO.getType());

            }
            if (e.getPerson().getDeathReasonSelected().getId() != null) {
                DeathReasonDTO deathReasonDTO = refController.getMetDeathReason(e.getPerson().getDeathReasonSelected().getId()).getBody();
                e.getPerson().setDeathReasonSelected(deathReasonDTO);
            }
            if (!e.getPerson().getBankCards().isEmpty()) {
                List<BankCardDTO> bankCardDTOS = e.getPerson().getBankCards().stream().map((card -> {
                    if (card.getCardType().getId() != null) {

                        CardTypeDTO cardTypeDTO = refController.getConsCardType(card.getCardType().getId()).getBody();
                        card.setCardType(cardTypeDTO);

                    }
                    return card;
                })).collect(Collectors.toList());
                e.getPerson().setBankCards(bankCardDTOS);
            }
            if (!e.getExternalIncomes().isEmpty()) {
                List<ExternalIncomeDTO> externalIncomeDTOS = e.getExternalIncomes().stream().map((income) -> {
                    if (income.getIncomeSource().getId() != null) {

                        IncomeSourceDTO incomeSourceDTO = refController.getMetIncomeSource(income.getIncomeSource().getId()).getBody();
                        income.setIncomeSource(incomeSourceDTO);

                    }
                    return income;
                }).collect(Collectors.toList());
            }
            Optional<Beneficiary> beneficiary = beneficiaryRepository.findByPersonId(e.getPerson().getId());
            if (beneficiary.isPresent()) {
                BeneficiaryDTO beneficiaryDTO = beneficiaryMapper.beneficiaryToBeneficiaryDTO(beneficiary.get());
                if(beneficiaryDTO.getTakenInChargeBeneficiaries()!=null) {
                    beneficiaryDTO.getTakenInChargeBeneficiaries().forEach(takenInChargeBeneficiaryDTO -> {
                        if (takenInChargeBeneficiaryDTO.getTakenInCharge() != null && takenInChargeBeneficiaryDTO.getTakenInCharge().getId() != null) {
                            Optional<TakenInCharge> takenInCharge = takenInChargeRepository.findById(takenInChargeBeneficiaryDTO.getTakenInCharge().getId());
                            if (takenInCharge.isPresent()) {
                                ServicesDTO servicesDTO = servicesMapper.toDto(takenInCharge.get().getService());
                                takenInChargeBeneficiaryDTO.getTakenInCharge().setServices(servicesDTO);
                            }
                        }
                        {

                        }
                    });
                }
                if (beneficiaryDTO.getBeneficiaryServices() != null) {
                    Set<BeneficiaryServiceDTO> beneficiaryServiceDTOS = beneficiaryDTO.getBeneficiaryServices().stream().map((b) -> {
                        ServiceDTO serviceDTO = b.getService();
                        StatusDTO statusDTO = b.getStatus();
                        ServiceDTO fullServiceDTO = new ServiceDTO();
                        StatusDTO fullStatusDTO = new StatusDTO();
                        if (serviceDTO.getId() != null && statusDTO != null) {

                            fullServiceDTO = refController.getMetService(serviceDTO.getId()).getBody();
                            fullStatusDTO = refController.getParStatus(statusDTO.getId()).getBody();

                            b.setService(fullServiceDTO);
                            b.setStatus(fullStatusDTO);
                        }
                        return b;
                    }).collect(Collectors.toSet());
                    beneficiaryDTO.setBeneficiaryServices(beneficiaryServiceDTOS);
                }
                if (!beneficiaryDTO.getTakenInChargeBeneficiaries().isEmpty()) {
                    List<TakenInChargeBeneficiaryDTO> takenInChargeBeneficiaryDTOS = beneficiaryDTO.getTakenInChargeBeneficiaries().stream().map(takenInChargeBeneficiaryDTO -> {
                        if (takenInChargeBeneficiaryDTO.getTakenInCharge().getServices() != null) {
                            Services services = servicesRepository.findById(takenInChargeBeneficiaryDTO.getTakenInCharge().getServices().getId()).get();
                            ServicesDTO servicesDTO = servicesMapper.toDto(services);
                            takenInChargeBeneficiaryDTO.getTakenInCharge().setServices(servicesDTO);

                        }

                        if (!takenInChargeBeneficiaryDTO.getTakenInCharge().getTakenInChargeDonors().isEmpty()) {
                            List<TakenInChargeDonorDTO> takenInChargeDonorDTOS = takenInChargeBeneficiaryDTO.getTakenInCharge().getTakenInChargeDonors().stream().map(takenInChargeDonorDTO -> {
                                DonorDTO donorDTO = null;
                                try {
                                    donorDTO = donorService.getDonorById(takenInChargeDonorDTO.getDonor().getId());
                                    takenInChargeDonorDTO.setDonor(donorDTO);
                                } catch (TechnicalException ex) {
                                    log.warn("Donor with id '{}' not found !", takenInChargeDonorDTO.getDonor().getId());
                                }
                                return takenInChargeDonorDTO;
                            }).collect(Collectors.toList());
                            takenInChargeBeneficiaryDTO.getTakenInCharge().setTakenInChargeDonors(takenInChargeDonorDTOS);
                        }
                        return takenInChargeBeneficiaryDTO;
                    }).collect(Collectors.toList());
                    beneficiaryDTO.setTakenInChargeBeneficiaries(takenInChargeBeneficiaryDTOS);
                }
                e.getPerson().setBeneficiary(beneficiaryDTO);
            }
            return e;
        }).collect(Collectors.toList());
        familyDTO.setFamilyMembers(FamilyMemberDTO);



        auditApplicationService.audit("Consultation de la Famille : " + familyDTO.getCode(), getUsernameFromJwt(), "Liste des familles",
                familyAuditString, null, FAMILLE, CONSULTATION);

        log.debug("End service Get Family by ID: {}, took {}", id, watch.toMS());
        return familyDTO;
    }

    public FamilyDTO getFamilyForOneBeneficiary(Long beneficiaryId) {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service Get One Family For One Beneficiary {}", "");
        Family family = familyRepository.getFamilyByBeneficiaryId(beneficiaryId);
        FamilyDTO familyDTO = familyMapper.familyToFamilyDTOWithoutNotesDocuments(family);
        if (familyDTO != null) {

            List<FamilyMemberDTO> familyMemberDTOS = familyDTO.getFamilyMembers().stream().map((e) -> {

                //Setting member picture
                if (e.getPerson().getPictureUrl() != null) {
                    String base64 = null;
                    try {
                        base64 = Arrays.toString(minioService.ReadFromMinIO(e.getPerson().getPictureUrl(),null));
                        e.getPerson().setPictureBase64(base64);
                    } catch (TechnicalException ex) {
                        ex.printStackTrace();
                    }
                }

                //Setting familyRelationship full info
                if (e.getFamilyRelationship().getId() != null) {
                    List<FamilyRelationshipDTO> familyRelationshipDTOS = refFeignClient.getAllMetFamilyRelationships();
                    for (FamilyRelationshipDTO familyRelationshipDTO : familyRelationshipDTOS) {
                        if (e.getFamilyRelationship().getId() == 1L && Objects.equals(familyRelationshipDTO.getName(), "Père")) {
                            e.setFamilyRelationship(familyRelationshipDTO);
                        } else if (e.getFamilyRelationship().getId() == 2L && Objects.equals(familyRelationshipDTO.getName(), "Mère")) {
                            e.setFamilyRelationship(familyRelationshipDTO);
                        } else if (e.getFamilyRelationship().getId().equals(familyRelationshipDTO.getId())) {
                            e.setFamilyRelationship(familyRelationshipDTO);
                        }
                    }
                }
                if (e.getPerson().getCity().getId() != null) {
                    CityDTO cityDTO = refFeignClient.getParCity(e.getPerson().getCity().getId());
                    e.getPerson().setCity(cityDTO);
                }
                if (e.getPerson().getProfession().getId() != null) {

                    ProfessionDTO professionDTO = refFeignClient.getMetProfession(e.getPerson().getProfession().getId());
                    e.getPerson().setProfession(professionDTO);

                }
                if (e.getPerson().getTypeIdentity().getId() != null) {

                    TypeIdentityDTO typeIdentityDTO = refFeignClient.getParTypeIdentity(e.getPerson().getTypeIdentity().getId());
                    e.getPerson().setTypeIdentity(typeIdentityDTO);

                }
                if (e.getPerson().getSchoolLevel().getId() != null) {

                    SchoolLevelDTO schoolLevelDTO = refFeignClient.getParSchoolLevel(e.getPerson().getSchoolLevel().getId());
                    e.getPerson().setSchoolLevel(schoolLevelDTO);

                }
                if (e.getPerson().getPictureUrl() != null) {
                    try {
                        byte[] imageData = minioService.ReadFromMinIO(e.getPerson().getPictureUrl(),null);
                        String base64Image = Base64.getEncoder().encodeToString(imageData);
                        e.getPerson().setPictureBase64(base64Image);
                    } catch (TechnicalException ex) {
                        ex.printStackTrace();
                    }
                }

                if (!e.getPerson().getBankCards().isEmpty()) {
                    List<BankCardDTO> bankCardDTOS = e.getPerson().getBankCards().stream().map(bankCardDTO -> {
                        if (bankCardDTO.getCardType().getId() != null) {

                            CardTypeDTO cardTypeDTO = refFeignClient.getConsCardType(bankCardDTO.getCardType().getId());
                            bankCardDTO.setCardType(cardTypeDTO);

                        }
                        return bankCardDTO;
                    }).collect(Collectors.toList());
                    e.getPerson().setBankCards(bankCardDTOS);
                }
                Optional<Beneficiary> beneficiary = beneficiaryRepository.findByPersonId(e.getPerson().getId());
                if (beneficiary.isPresent()) {
                    BeneficiaryDTO beneficiaryDTO = beneficiaryMapper.beneficiaryToBeneficiaryDTO(beneficiary.get());
                    if (beneficiaryDTO.getBeneficiaryServices() != null) {
                        Set<BeneficiaryServiceDTO> beneficiaryServiceDTOS = beneficiaryDTO.getBeneficiaryServices().stream().map((b) -> {
                            ServiceDTO serviceDTO = b.getService();
                            StatusDTO statusDTO = b.getStatus();
                            ServiceDTO fullServiceDTO = new ServiceDTO();
                            StatusDTO fullStatusDTO = new StatusDTO();
                            if (serviceDTO.getId() != null && statusDTO != null) {

                                fullServiceDTO = refFeignClient.getMetService(serviceDTO.getId());
                                fullStatusDTO = refFeignClient.getParStatus(statusDTO.getId());

                                b.setService(fullServiceDTO);
                                b.setStatus(fullStatusDTO);
                            }
                            return b;
                        }).collect(Collectors.toSet());
                        beneficiaryDTO.setBeneficiaryServices(beneficiaryServiceDTOS);
                    }
                    e.getPerson().setBeneficiary(beneficiaryDTO);
                }
                return e;
            }).collect(Collectors.toList());
            familyDTO.setFamilyMembers(familyMemberDTOS);
        }

        log.debug("End service Get One Family For One Beneficiary, took {}", watch.toMS());
        return familyDTO;
    }

    public List<FamilyDTO> getAllFamiliesForBeneficiary() {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service Get All Families For Beneficiary");

        // Fetch families without beneficiary profiles
        List<Family> listFamilies = familyRepository.findFamiliesWithoutBeneficiaryProfile();
        List<FamilyDTO> resultFamilies = new ArrayList<>();

        for (Family family : listFamilies) {
            List<FamilyMember> nonBeneficiaryMembers = new ArrayList<>();
            FamilyMember tutor = null;

            // Identify non-beneficiary members and the tutor
            for (FamilyMember member : family.getFamilyMembers()) {
                if (member.isTutor()) {
                    tutor = member; // Save tutor reference
                }
                if (member.getPerson() != null
                        && member.getPerson().getBeneficiary() == null
                        && !Boolean.TRUE.equals(member.getPerson().isDeceased())) { // Exclude deceased members
                    nonBeneficiaryMembers.add(member);
                }
            }

            // Exclude families where all members are beneficiaries
            if (!nonBeneficiaryMembers.isEmpty()) {
                family.setFamilyMembers(nonBeneficiaryMembers); // Filter non-beneficiary members only
                FamilyDTO familyDTO = familyMapper.familyToFamilyDTO(family);

                // Add tutor details to the FamilyDTO
                if (tutor != null && tutor.getPerson() != null) {
                    familyDTO.setTutorName(tutor.getPerson().getFirstName() + " " + tutor.getPerson().getLastName());
                    familyDTO.setTutorCIN(tutor.getPerson().getIdentityCode()); // Assuming `identityCode` represents CIN
                }

                if(family.getCityId() != null){
                    CityDTO cityDTO = refFeignClient.getParCity(family.getCityId());
                    familyDTO.setCity(cityDTO);
                    CityWithRegionAndCountryDTO fullCountryDto = refController.getCityWithRegionAndCountry(family.getCityId()).getBody();
                    if (fullCountryDto != null) {
                        RegionDTO regionDTO = fullCountryDto.getRegion();
                        if (regionDTO != null) {
                            cityDTO.setRegion(regionDTO);
                            familyDTO.setCity(cityDTO);
                        }
                    }
                }
                if(family.getAccommodationTypeId() != null){
                    AccommodationTypeDTO accommodationTypeDTO = refFeignClient.getMetAccommodationType(family.getAccommodationTypeId());
                    familyDTO.setAccommodationType(accommodationTypeDTO);
                }
                if(family.getAccommodationNatureId() != null){
                    AccommodationNatureDTO accommodationNatureDTO = refFeignClient.getParAccommodationNature(family.getAccommodationNatureId());
                    familyDTO.setAccommodationNature(accommodationNatureDTO);
                }
                if(family.getZone() != null){
                    ZoneDTO zoneDTO = zoneMapper.zoneToLightZoneDTO(family.getZone());
                    zoneDTO.setEps(null);
                    familyDTO.setZoneDTO(zoneDTO);
                    if (family.getSousZone() != null){
                        SousZoneDTO sousZoneDTO = sousZoneMapper.toDto(family.getSousZone());
                        familyDTO.setSousZoneDTO(sousZoneDTO);
                    }
                }

                // Process family members for additional info
                List<FamilyMemberDTO> updatedMembers = familyDTO.getFamilyMembers().stream()
                        .map(member -> processFamilyMember(member))
                        .collect(Collectors.toList());
                familyDTO.setFamilyMembers(updatedMembers);

                resultFamilies.add(familyDTO);
            }
        }

        log.debug("End service Get All Families, took {}", watch.toMS());
        return resultFamilies;
    }

    private FamilyMemberDTO processFamilyMember(FamilyMemberDTO member) {
        // Update FamilyRelationship with full info
        if (member.getFamilyRelationship() != null && member.getFamilyRelationship().getId() != null) {
            List<FamilyRelationshipDTO> relationships = refFeignClient.getAllMetFamilyRelationships();
            for (FamilyRelationshipDTO relation : relationships) {
                if (Objects.equals(member.getFamilyRelationship().getId(), relation.getId())) {
                    member.setFamilyRelationship(relation);
                    break;
                }
            }
        }

        // Fetch city, region, and country info
        if (member.getPerson().getCity() != null && member.getPerson().getCity().getId() != null) {
            CityDTO cityDTO = refFeignClient.getParCity(member.getPerson().getCity().getId());
            member.getPerson().setCity(cityDTO);

            CityWithRegionAndCountryDTO fullInfo = refController
                    .getCityWithRegionAndCountry(member.getPerson().getCity().getId()).getBody();
            member.getPerson().setInfo(fullInfo);
        }

        // Handle picture conversion to Base64
        if (member.getPerson().getPictureUrl() != null) {
            try {
                byte[] imageData = minioService.ReadFromMinIO(member.getPerson().getPictureUrl(), null);
                String base64Image = Base64.getEncoder().encodeToString(imageData);
                member.getPerson().setPictureBase64(base64Image);
            } catch (TechnicalException ex) {
                log.error("Error processing picture for person ID {}: {}", member.getPerson().getId(), ex.getMessage());
            }
        }

        // Map additional details: Profession, TypeIdentity, SchoolLevel
        updatePersonDetails(member);

        // Fetch Beneficiary info if present
        Optional<Beneficiary> beneficiaryOpt = beneficiaryRepository.findByPersonId(member.getPerson().getId());
        beneficiaryOpt.ifPresent(beneficiary -> {
            BeneficiaryDTO beneficiaryDTO = beneficiaryMapper.beneficiaryToBeneficiaryDTO(beneficiary);

            // Populate Beneficiary Services
            if (beneficiaryDTO.getBeneficiaryServices() != null) {
                Set<BeneficiaryServiceDTO> updatedServices = beneficiaryDTO.getBeneficiaryServices().stream()
                        .map(service -> updateServiceAndStatus(service))
                        .collect(Collectors.toSet());
                beneficiaryDTO.setBeneficiaryServices(updatedServices);
            }
            member.getPerson().setBeneficiary(beneficiaryDTO);
        });

        return member;
    }

    private void updatePersonDetails(FamilyMemberDTO member) {
        PersonDTO person = member.getPerson();

        if (person.getProfession() != null && person.getProfession().getId() != null) {
            ProfessionDTO profession = refFeignClient.getMetProfession(person.getProfession().getId());
            person.setProfession(profession);
        }
        if (person.getTypeIdentity() != null && person.getTypeIdentity().getId() != null) {
            TypeIdentityDTO typeIdentity = refFeignClient.getParTypeIdentity(person.getTypeIdentity().getId());
            person.setTypeIdentity(typeIdentity);
        }
        if (person.getSchoolLevel() != null && person.getSchoolLevel().getId() != null) {
            SchoolLevelDTO schoolLevel = refFeignClient.getParSchoolLevel(person.getSchoolLevel().getId());
            person.setSchoolLevel(schoolLevel);
        }
    }

    private BeneficiaryServiceDTO updateServiceAndStatus(BeneficiaryServiceDTO serviceDTO) {
        if (serviceDTO.getService() != null && serviceDTO.getService().getId() != null) {
            ServiceDTO fullService = refFeignClient.getMetService(serviceDTO.getService().getId());
            serviceDTO.setService(fullService);
        }
        if (serviceDTO.getStatus() != null && serviceDTO.getStatus().getId() != null) {
            StatusDTO fullStatus = refFeignClient.getParStatus(serviceDTO.getStatus().getId());
            serviceDTO.setStatus(fullStatus);
        }
        return serviceDTO;
    }



    public Predicate buildPredicate(Long zoneId,CriteriaBuilder criteriaBuilder, CriteriaQuery<?> criteriaQuery, Root<Family> root, String searchByTutorName, String lastNameAr, Long minNumber, Long maxNumber, String numTel, Date minDate, Date maxDate,Long searchByTagId) {
        Predicate predicate = criteriaBuilder.conjunction();

        // Join avec la liste familyMembers
        Join<Family, FamilyMember> familyMemberJoin = root.join("familyMembers");

        if (zoneId != null) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("zone").get("id"), zoneId));
        }


        if (searchByTutorName != null && !searchByTutorName.isEmpty()) {
            Predicate lastNamePredicate = criteriaBuilder.like(
                    criteriaBuilder.lower(familyMemberJoin.get("person").get("lastName")),
                    "%" + searchByTutorName.toLowerCase() + "%");
            Predicate isTutorPredicate = criteriaBuilder.isTrue(familyMemberJoin.get("tutor")); // Vérifie si le membre est un tuteur
            predicate = criteriaBuilder.and(predicate, lastNamePredicate, isTutorPredicate);
        }

        if (lastNameAr != null && !lastNameAr.isEmpty()) {
            Predicate lastNameArPredicate = criteriaBuilder.like(
                    criteriaBuilder.lower(familyMemberJoin.get("person").get("lastNameAr")),
                    "%" + lastNameAr.toLowerCase() + "%");
            Predicate isTutorPredicate = criteriaBuilder.isTrue(familyMemberJoin.get("tutor")); // Vérifie si le membre est un tuteur
            predicate = criteriaBuilder.and(predicate, lastNameArPredicate, isTutorPredicate);
        }

        if (numTel != null) {
            Predicate numTelPredicate = criteriaBuilder.equal(familyMemberJoin.get("person").get("phoneNumber"), numTel);
            predicate = criteriaBuilder.and(predicate, numTelPredicate);
        }

        if (minNumber != null || maxNumber != null) {
            Subquery<Long> subquery = criteriaQuery.subquery(Long.class);
            Root<Family> subRoot = subquery.from(Family.class);
            Join<Family, FamilyMember> subFamilyMemberJoin = subRoot.join("familyMembers");
            subquery.select(criteriaBuilder.count(subFamilyMemberJoin));
            subquery.where(criteriaBuilder.equal(subRoot.get("id"), root.get("id")));

            if (minNumber != null) {
                Predicate minNumberPredicate = criteriaBuilder.greaterThanOrEqualTo(subquery, minNumber);
                predicate = criteriaBuilder.and(predicate, minNumberPredicate);
            }

            if (maxNumber != null) {
                Predicate maxNumberPredicate = criteriaBuilder.lessThanOrEqualTo(subquery, maxNumber);
                predicate = criteriaBuilder.and(predicate, maxNumberPredicate);
            }
        }
        if (searchByTagId != null) {
            // Create a subquery to find donors with the specified tag
            Subquery<Long> subquery = criteriaBuilder.createQuery().subquery(Long.class);
            Root<Taggable> taggableRoot = subquery.from(Taggable.class);
            subquery.select(taggableRoot.get("taggableId"))
                    .where(
                            criteriaBuilder.and(
                                    criteriaBuilder.equal(taggableRoot.get("taggableType"), "family"),
                                    criteriaBuilder.equal(taggableRoot.get("tag").get("id"), searchByTagId)
                            )
                    );

            // Add the subquery condition to the main predicate
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.in(root.get("id")).value(subquery));
        }
        if (minDate != null) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.greaterThanOrEqualTo(root.get("createdAt"), minDate));
        }
        if (maxDate != null) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.lessThanOrEqualTo(root.get("createdAt"), maxDate));
        }

        return predicate;
    }

    private TypedQuery<Family> createFamilyCriteriaQuery(Long zoneId,String searchByTutorName, String lastNameAr, Long minNumber, Long maxNumber, String numTel, Date minDate, Date maxDate,Long searchByTagId) {
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Family> criteriaQuery = criteriaBuilder.createQuery(Family.class);
        Root<Family> root = criteriaQuery.from(Family.class);

        criteriaQuery.select(root).distinct(true);

        Predicate predicate = buildPredicate(zoneId,criteriaBuilder, criteriaQuery, root, searchByTutorName, lastNameAr, minNumber, maxNumber, numTel, minDate, maxDate,searchByTagId);

        criteriaQuery.where(predicate);
        criteriaQuery.orderBy(criteriaBuilder.desc(root.get("createdAt")));

        return entityManager.createQuery(criteriaQuery);
    }


    public List<Family> filterFamilyToExport(Long zoneId , String searchByTutorName, String lastNameAr, Long minNumber, Long maxNumber,
                                             String numTel, Date minDate, Date maxDate) {
        TypedQuery<Family> typedQuery = createFamilyCriteriaQuery(zoneId, searchByTutorName, lastNameAr, minNumber, maxNumber, numTel, minDate, maxDate,null);
        return typedQuery.getResultList();
    }


    public FamilyExportDTO mapFamilyToExportDTO(Family family) {
        FamilyExportDTO familyExportDTO = familyMapper.familyToFamilyExportDTO(family);
        fetchBasicInformation(family, familyExportDTO);
        fetchOtherInformation(family, familyExportDTO);
        return familyExportDTO;
    }

    private void fetchBasicInformation(Family family, FamilyExportDTO familyExportDTO) {
        Optional<FamilyMember> tutor = family.getFamilyMembers().stream().filter(FamilyMember::isTutor).findFirst();
        tutor.ifPresent(t -> {
            familyExportDTO.setTutorName(t.getPerson().getLastName() + " " + t.getPerson().getFirstName());
            familyExportDTO.setNumTel(t.getPerson().getPhoneNumber());

            if (t.getPerson().getCityId() != null) {
                CityWithRegionAndCountryDTO fullCountryDto = getCityWithRegionAndCountry(t.getPerson().getCityId());
                if (fullCountryDto != null) {
                    familyExportDTO.setTutorCountry(fullCountryDto.getCountry().getName());
                    familyExportDTO.setTutorRegion(fullCountryDto.getRegion().getName());
                    familyExportDTO.setTutorCity(fullCountryDto.getName());
                }
            }
        });

        familyExportDTO.setNumberOfFamilyMembers(family.getFamilyMembers().size());
        familyExportDTO.setFamilyName(getFamilyName(family));
        familyExportDTO.setNumberOfBeneficiaries(getNumberOfBeneficiaries(family));
    }

    private void fetchOtherInformation(Family family, FamilyExportDTO familyExportDTO) {
        if (familyExportDTO.getNumberOfBeneficiaries() > 0) {
            Set<Long> serviceIds = new HashSet<>();
            Set<Long> statusIds = new HashSet<>();

            for (FamilyMember familyMember : family.getFamilyMembers()) {
                Optional<Beneficiary> beneficiary = beneficiaryRepository.findByPersonId(familyMember.getPerson().getId());
                beneficiary.ifPresent(b -> {
                    BeneficiaryDTO beneficiaryDTO = beneficiaryMapper.beneficiaryToBeneficiaryDTO(b);
                    if (beneficiaryDTO.getBeneficiaryServices() != null) {
                        for (BeneficiaryServiceDTO beneficiaryServiceDTO : beneficiaryDTO.getBeneficiaryServices()) {
                            serviceIds.add(beneficiaryServiceDTO.getService().getId());
                            statusIds.add(beneficiaryServiceDTO.getStatus().getId());
                        }
                    }
                });
            }

            Map<Long, ServiceDTO> services = getServicesByIds(serviceIds);
            Map<Long, StatusDTO> statuses = getStatusesByIds(statusIds);

            List<String> familyServices = new ArrayList<>();
            for (FamilyMember familyMember : family.getFamilyMembers()) {
                Optional<Beneficiary> beneficiary = beneficiaryRepository.findByPersonId(familyMember.getPerson().getId());
                beneficiary.ifPresent(b -> {
                    BeneficiaryDTO beneficiaryDTO = beneficiaryMapper.beneficiaryToBeneficiaryDTO(b);
                    if (beneficiaryDTO.getBeneficiaryServices() != null) {
                        for (BeneficiaryServiceDTO beneficiaryServiceDTO : beneficiaryDTO.getBeneficiaryServices()) {
                            ServiceDTO serviceDTO = services.get(beneficiaryServiceDTO.getService().getId());
                            StatusDTO statusDTO = statuses.get(beneficiaryServiceDTO.getStatus().getId());
                            if (serviceDTO != null && statusDTO != null) {
                                familyServices.add(serviceDTO.getName() + " - " + statusDTO.getName());
                            }
                        }
                    }
                });
            }
            familyExportDTO.setBeneficiariesServices(Collections.singletonList(String.join(", ", familyServices)));
        }
    }

    public String getFamilyName(Family family) {
        return family.getFamilyMembers().stream()
                .filter(fm -> fm.getFamilyRelationshipId() == 1)
                .findFirst()
                .map(p -> p.getPerson().getLastName())
                .orElse(null);
    }

    private long getNumberOfBeneficiaries(Family family) {
        return family.getFamilyMembers().stream()
                .filter(fm -> fm.getPerson().getBeneficiary() != null)
                .count();
    }

    private CityWithRegionAndCountryDTO getCityWithRegionAndCountry(Long cityId) {
        var response = refController.getCityWithRegionAndCountry(cityId);
        return response != null ? response.getBody() : null;
    }

    private Map<Long, ServiceDTO> getServicesByIds(Set<Long> serviceIds) {
        return serviceIds.stream()
                .map(id -> refController.getMetService(id).getBody())
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(ServiceDTO::getId, Function.identity()));
    }

    private Map<Long, StatusDTO> getStatusesByIds(Set<Long> statusIds) {
        return statusIds.stream()
                .map(id -> refController.getParStatus(id).getBody())
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(StatusDTO::getId, Function.identity()));
    }

    public List<FamilyExportDTO> getAllFamilyToExport(List<Family> listFamilys) {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service Get All Families To Export");

        List<FamilyExportDTO> familyExportDTOS = new ArrayList<>();

        for (Family family : listFamilys) {
            FamilyExportDTO familyExportDTO = mapFamilyToExportDTO(family);
            familyExportDTOS.add(familyExportDTO);
        }

        log.debug("End service Get All Families To Export, took {}", watch.toMS());
        return familyExportDTOS;
    }

    public ExportFileDTO exportFileWithName(String searchByTutorName, String lastNameAr, Long minNumber, Long maxNumber, String numTel, Date minDate, Date maxDate) {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service exportFamilyFile searchByTutorName {}, lastNameAr {}, minNumber {}, maxNumber {}, numTel {}, minDate {}, maxDate {}", searchByTutorName, lastNameAr, minNumber, maxNumber, numTel, minDate, maxDate);
Long zoneId;
        CacheAdUser cacheAdUser  =  getRoleFromJwt();
        assert cacheAdUser != null;
        // Get impersonation token from request header
        String impersonationToken = request.getHeader("Impersonation-Token");
        CacheAdUserDTO cacheAdUser1 = null;
        if (impersonationToken != null && !impersonationToken.isEmpty()) {
            cacheAdUser1 = tokenImpersonationService.getImpersonatedUser(impersonationToken);
        }
        if (cacheAdUser1 != null && cacheAdUser1.getRole().getCode().equals(RoleCode.ASSISTANT.getCode())) {
           Assistant assistant = assistantRepository.findByCacheAdUserId(cacheAdUser1.getId());
            if (assistant != null) {
                if (assistant.getZone() != null && assistant.getZone().getId() != null) {
                    zoneId = assistant.getZone().getId();
                } else {
                    zoneId = null;
                }
            } else {
                zoneId = null;
            }
        }
        else
        if(cacheAdUser.getRole().getCode().equals(RoleCode.ASSISTANT.getCode())){
            Assistant assistant = assistantRepository.findByCacheAdUserId(cacheAdUser.getId());
            if (assistant != null){
                if (assistant.getZone() != null && assistant.getZone().getId() != null ){
                    zoneId = assistant.getZone().getId();
                } else {
                    zoneId = null;
                }
            } else {
                zoneId = null;
            }
        } else {
            zoneId = null;
        }
        // Filter Family entities based on criteria
        List<Family> listFamilies = filterFamilyToExport(zoneId , searchByTutorName, lastNameAr, minNumber, maxNumber, numTel, minDate, maxDate);

        // Convert entities to DTOs
        List<FamilyExportDTO> familyExportDTOS = getAllFamilyToExport(listFamilies);

        // Define export parameters
        String sheetName = "Rappport des Familles";
        String[] headers = Arrays.stream(FamilyExportHeaders.values())
                .map(FamilyExportHeaders::getHeaderName)
                .toArray(String[]::new);

        // Perform export
        return exportService.exportEntities(sheetName, headers, familyExportDTOS, this::mapToExportRow);
    }

    private Object[] mapToExportRow(FamilyExportDTO dto) {
        return new Object[]{
                dto.getCode(),
                dto.getFormattedCreatedAt(),
                dto.getFamilyName(),
                dto.getTutorName(),
                dto.getNumTel(),
                dto.getTutorAdress(),
                dto.getTutorCity(),
                dto.getTutorRegion(),
                dto.getNumberOfFamilyMembers(),
                dto.getNumberOfBeneficiaries(),
                dto.getBeneficiariesServices()
        };
    }

    public List<TutorHistoryDTO> getTutorHistoryByFamilyId(Long familyId) throws TechnicalException {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service getTutorHistoryByFamilyId {}", familyId);
        if (familyId == null) {
            throw new TechnicalException("Family id is required");
        }
        Family family = familyRepository.getFamilyById(familyId);
        if (family == null) {
            throw new TechnicalException("Family not found");
        }
        List<TutorHistory> tutorHistories = tutorHistoryRepository.findByFamilyId(familyId);
        List<TutorHistoryDTO> tutorHistoryDTOS = tutorHistories.stream()
                .map(tutorHistory -> {
                    TutorHistoryDTO dto = tutorHistoryMapper.toDTO(tutorHistory);

                    // Supposons que vous ayez une méthode pour obtenir la relation à partir de l'ID
                    Long relationshipId = tutorHistory.getFamilyMember().getFamilyRelationshipId();
                    if (relationshipId != null) {
                        FamilyRelationshipDTO familyRelationshipDTO = refFeignClient.getMetFamilyRelationship(relationshipId);
                        dto.setFamilyMemberRelationship(familyRelationshipDTO.getName());
                    }

                    return dto;
                })
                .toList();
        log.debug("End service getTutorHistoryByFamilyId, took {}", watch.toMS());
        return tutorHistoryDTOS;
    }

}
