package ma.almobadara.backend.repository.donor;

import ma.almobadara.backend.model.donor.Correspondence;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
public interface CorrespondenceRepository extends JpaRepository<Correspondence, Long> {
    List<Correspondence> findByDonorId(Long donorId);


    @Modifying
    @Transactional

    @Query("UPDATE Correspondence c SET c.donor.id = :newDonorId WHERE c.donor.id = :oldDonorId")
    void updateDonorId(@Param("oldDonorId") Long oldDonorId, @Param("newDonorId") Long newDonorId);
}
