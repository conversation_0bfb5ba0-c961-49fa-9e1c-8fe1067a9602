package ma.almobadara.backend.dto.beneficiary;

import lombok.*;
import ma.almobadara.backend.dto.CityWithRegionAndCountryDTO;
import ma.almobadara.backend.dto.referentiel.*;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class RapportDto {

    private Long id;
    private Boolean archived;
    private String codeRapport;
    private Long numberRapport;
    private Date dateRapport;
    private String reference;
    private String release;

    private Date dateRapportValidate;
    private String detailComplete;

    private Long donorId;
    private List<DonorInfoDto> donors;
    private String donorFirstName;
    private String donorLastName;
    private String donorFirstNameAr;
    private String donorLastNameAr;

    private Long beneficiaryId;
    private Long personBeneficiaryId;
    private String beneficiaryCode;
    private String beneficiaryFirstName;
    private String beneficiaryLastName;
    private String beneficiaryFirstNameAr;
    private String beneficiaryLastNameAr;
    private String beneficiaryAddress;
    private String beneficiaryAddressAr;
    private Date beneficiaryBirthDate;
    private CityDTO city;
    private CityWithRegionAndCountryDTO info;
    private String schoolLevelType;
    private SchoolLevelDTO schoolLevel;
    private String schoolName;
    private Double result;

    private Long familyId;
    private Long personFamilyId;
    private FamilyRelationshipDTO familyRelationship;
    private Long numberOfFamilyMember;
    private String tutorFirstName;
    private String tutorLastName;
    private String tutorFirstNameAr;
    private String tutorLastNameAr;
    private ProfessionDTO profession;
    private String phoneNumber;
    private AccommodationTypeDTO accommodationType;

    private String activityEducationalFr;
    private String activityEducationalEn;
    private String activityEducationalAr;
    private String socialServiceFr;
    private String socialServiceEn;
    private String socialServiceAr;
    private String recommendationFr;
    private String recommendationEn;
    private String recommendationAr;

    private List<RapportPictureDTO> pictures;
}
