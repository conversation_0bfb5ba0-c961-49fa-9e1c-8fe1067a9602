package ma.almobadara.backend.dto.beneficiary;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import static ma.almobadara.backend.util.strings.HandleSpecialChars.escapeSpecialChars;

@Getter
@Setter
@Builder
@AllArgsConstructor
public class BeneficiaryEducation {

    Long beneficiaryId;
    private Long schoolLevelId;
    private String schoolName;
    private String schoolNameAr;
    private boolean educated;
    List<EducationDTO> educations;
    List<ScholarshipBeneficiaryDTO> scholarshipBeneficiaries;

    public String getAudit(Map<String, String> params) {
        return "{"
                + "\"Personne scolarisée\": \"" + escapeSpecialChars(params.getOrDefault("Educated", "-")) + "\""
                + ",\"Niveau scolaire\": \"" + escapeSpecialChars(params.getOrDefault("Niveau", "-")) + "\","
                + "\"Classe\": \"" + escapeSpecialChars(params.getOrDefault("Class", "-")) + "\","
                + "\"Établissement\": \"" + escapeSpecialChars(params.getOrDefault("schoolName", "-")) + "\","
                + "\"Établissement (Arabe)\": \"" + escapeSpecialChars(params.getOrDefault("schoolNameAr", "-")) + "\","
                + "\"Nombre de scolarité\": \"" + escapeSpecialChars(params.getOrDefault("Historique", "-")) + "\","
                + "\"Nombre de bourses\": \"" + escapeSpecialChars(params.getOrDefault("bourses", "-")) + "\""
                + "}";
    }



}
