package ma.almobadara.backend.model.takenInCharge;

import jakarta.persistence.*;
import lombok.*;
import ma.almobadara.backend.model.beneficiary.BankCard;
import ma.almobadara.backend.model.beneficiary.Beneficiary;

import java.util.List;
@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TakenInChargeBeneficiary {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @ManyToOne
    @JoinColumn(name = "beneficiary_id")
    private Beneficiary beneficiary;
    @ManyToOne
    @JoinColumn(name = "taken_in_charge_id")
    private TakenInCharge takenInCharge;
    @ManyToMany
    @JoinTable(name = "taken_in_charge_beneficiary_bank_card",
            joinColumns = @JoinColumn(name = "taken_in_charge_beneficiary_id"),
            inverseJoinColumns = @JoinColumn(name = "bank_card_id"))
    private List<BankCard> bankCards;

}
