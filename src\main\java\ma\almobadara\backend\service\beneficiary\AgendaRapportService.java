package ma.almobadara.backend.service.beneficiary;

import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.*;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.beneficiary.AgendaRapportDto;
import ma.almobadara.backend.dto.beneficiary.AgendaRapportResponseDto;
import ma.almobadara.backend.dto.beneficiary.HistoryRapportDto;
import ma.almobadara.backend.enumeration.BeneficiaryStatus;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.mapper.AgendaRapportMapper;
import ma.almobadara.backend.mapper.HistoryRapportMapper;
import ma.almobadara.backend.model.administration.Assistant;
import ma.almobadara.backend.model.administration.Zone;
import ma.almobadara.backend.model.beneficiary.*;
import ma.almobadara.backend.model.donor.Donor;
import ma.almobadara.backend.repository.beneficiary.AgendaRapportRepository;
import ma.almobadara.backend.repository.beneficiary.BeneficiaryRepository;
import ma.almobadara.backend.repository.beneficiary.RapportRepository;
import ma.almobadara.backend.repository.donor.DonorRepository;
import ma.almobadara.backend.service.communs.MailSenderService;
import ma.almobadara.backend.service.donor.MinioService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Slf4j
@Service
public class AgendaRapportService {

    private final AgendaRapportRepository agendaRapportRepository;
    private final BeneficiaryRepository beneficiaryRepository;
    private final DonorRepository donorRepository;
    private final RapportRepository rapportRepository;
    private final AgendaRapportMapper agendaRapportMapper;
    private final HistoryRapportMapper historyRapportMapper;
    private final MinioService minioService;
    private final MailSenderService mailService;
    private final EntityManager entityManager;

    @Scheduled(cron = "0 0 0 1 1 *")
    @Transactional
    public void planifierRapportsAutomatiquement() {
        log.info("Début de la planification automatique des rapports...");

        // Filtrer uniquement les bénéficiaires ayant le statut BENEFICIAIRE_ACTIF
        List<Beneficiary> beneficiariesActifs = beneficiaryRepository.findAll().stream()
                .filter(beneficiary -> beneficiary.getBeneficiaryStatut() != null &&
                        beneficiary.getBeneficiaryStatut().getId().equals(BeneficiaryStatus.BENEFICIAIRE_ACTIF.getId()))
                .toList();

        if (beneficiariesActifs.isEmpty()) {
            log.info("Aucun bénéficiaire actif trouvé pour la planification.");
            return;
        }

        for (Beneficiary beneficiary : beneficiariesActifs) {
            try {
                log.info("Planification pour le bénéficiaire ID {}", beneficiary.getId());
                if(Boolean.TRUE.equals(beneficiary.getHasRapport())){
                    planifierRapport(beneficiary.getId(), true);
                }
            } catch (Exception e) {
                log.error("Erreur lors de la planification du rapport pour le bénéficiaire ID {}",
                        beneficiary.getId(), e);
            }
        }

        log.info("Fin de la planification automatique des rapports.");
    }

    @Transactional
    public List<AgendaRapportDto> planifierRapport(Long beneficiaryId, boolean hasRapport) {
        Beneficiary beneficiary = beneficiaryRepository.findById(beneficiaryId)
                .orElseThrow(() -> new RuntimeException("Bénéficiaire non trouvé"));

        String currentYear = String.valueOf(LocalDate.now().getYear());
        if (hasRapport) {
            List<AgendaRapport> existingReports = agendaRapportRepository.findByBeneficiaryIdAndYear(beneficiaryId, currentYear);
            if (existingReports.isEmpty()) {
                List<LocalDate> plannedDates = List.of(
                        LocalDate.of(LocalDate.now().getYear(), 4, 15),
                        LocalDate.of(LocalDate.now().getYear(), 8, 15),
                        LocalDate.of(LocalDate.now().getYear(), 12, 15)
                );
                for (LocalDate date : plannedDates) {
                    AgendaRapport agendaRapport = new AgendaRapport();
                    agendaRapport.setBeneficiary(beneficiary);
                    agendaRapport.setDatePlanned(Date.from(date.atStartOfDay(ZoneId.systemDefault()).toInstant()));
                    agendaRapport.setYear(currentYear);
                    agendaRapport.setStatus(String.valueOf(RapportStatus.RAPPORT_PLANIFIER));
                    int currentYears = LocalDate.now().getYear();
                    Long lastNumberRapport = agendaRapportRepository.findLastNumberRapportByBeneficiaryAndYear(beneficiaryId, currentYears);
                    Long newNumberRapport = (lastNumberRapport != null) ? lastNumberRapport + 1 : 1;
                    agendaRapport.setNumberRapport(newNumberRapport);

                    agendaRapportRepository.save(agendaRapport);
                }
            }
            beneficiary.setHasRapport(true);
        } else {
            List<LocalDate> plannedDates = List.of(
                    LocalDate.of(LocalDate.now().getYear(), 4, 15),
                    LocalDate.of(LocalDate.now().getYear(), 8, 15),
                    LocalDate.of(LocalDate.now().getYear(), 12, 15)
            );
            for (LocalDate date : plannedDates) {
                AgendaRapport agendaRapport = new AgendaRapport();
                agendaRapport.setBeneficiary(beneficiary);
                agendaRapport.setDatePlanned(Date.from(date.atStartOfDay(ZoneId.systemDefault()).toInstant()));
                agendaRapport.setYear(currentYear);
                agendaRapport.setStatus(String.valueOf(RapportStatus.RAPPORT_PLANIFIER));

                int currentYears = LocalDate.now().getYear();
                Long lastNumberRapport = agendaRapportRepository.findLastNumberRapportByBeneficiaryAndYear(beneficiaryId, currentYears);
                Long newNumberRapport = (lastNumberRapport != null) ? lastNumberRapport + 1 : 1;
                agendaRapport.setNumberRapport(newNumberRapport);

                agendaRapportRepository.save(agendaRapport);
            }
            beneficiary.setHasRapport(true);
        }

        beneficiaryRepository.save(beneficiary);

        List<AgendaRapport> rapports = agendaRapportRepository.findByBeneficiaryIdAndYear(beneficiaryId, currentYear);
        return agendaRapportMapper.toDtoList(rapports);
    }

    @Transactional
    public Page<AgendaRapportDto> getAllAgendaRapportByBeneficiaryId(Long beneficiaryId, int page, int size, HttpServletResponse response) {
        PageRequest pageRequest = PageRequest.of(page, size);

        Page<AgendaRapport> rapports = agendaRapportRepository.findByBeneficiary_IdOrderByModifiedAtDesc(beneficiaryId, pageRequest);

        if (rapports.isEmpty()) {
            response.setHeader("X-No-AgendaRapports-Found", "No agenda rapports found for beneficiary with id: " + beneficiaryId);
        }

        return rapports.map(agendaRapport -> agendaRapportMapper.toDto(agendaRapport));
    }

    @Transactional
    public Page<AgendaRapportDto> getAllAgendaRapportByBeneficiaryIdFilter(
            Long id, Pageable pageable, String beneficiaryCode, String beneficiaryName,
            Long numberRapport, String reportStatus, LocalDate plannedDateStart, LocalDate plannedDateEnd,
            LocalDate validationDateStart, LocalDate validationDateEnd, HttpServletResponse response
    ) {
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<AgendaRapport> criteriaQuery = criteriaBuilder.createQuery(AgendaRapport.class);
        Root<AgendaRapport> root = criteriaQuery.from(AgendaRapport.class);

        Join<AgendaRapport, Beneficiary> beneficiaryJoin = root.join("beneficiary");

        Join<Beneficiary, Person> personJoin = beneficiaryJoin.join("person");

        Join<AgendaRapport, Rapport> rapportJoin = root.join("rapport", JoinType.LEFT);

        Predicate predicate = criteriaBuilder.conjunction();

        predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(beneficiaryJoin.get("id"), id));

        if (beneficiaryCode != null) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(beneficiaryJoin.get("code"), "%" + beneficiaryCode + "%"));
        }

        if (beneficiaryName != null) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(personJoin.get("firstName"), "%" + beneficiaryName + "%"));
        }

        if (numberRapport != null) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(rapportJoin.get("numberRapport"), numberRapport));
        }

        if (reportStatus != null) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("status"), reportStatus));
        }

        if (plannedDateStart != null ) {
            Date plannedStartDate = Date.from(plannedDateStart.atStartOfDay(ZoneId.systemDefault()).toInstant());
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.greaterThanOrEqualTo(root.get("datePlanned"), plannedStartDate));
        }

        if (plannedDateEnd != null) {
            Date plannedEndDate = Date.from(plannedDateEnd.atStartOfDay(ZoneId.systemDefault()).toInstant());
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.lessThanOrEqualTo(root.get("datePlanned"), plannedEndDate));
        }

        if (validationDateStart != null ) {
            Date validationStartDate = Date.from(validationDateStart.atStartOfDay(ZoneId.systemDefault()).toInstant());
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.greaterThanOrEqualTo(root.get("dateValidate"), validationStartDate));
        }

        if (validationDateEnd != null) {
            Date validationEndDate = Date.from(validationDateEnd.atStartOfDay(ZoneId.systemDefault()).toInstant());
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.lessThanOrEqualTo(root.get("dateValidate"), validationEndDate));
        }

        criteriaQuery.where(predicate);
        criteriaQuery.orderBy(criteriaBuilder.desc(root.get("modifiedAt")));

        TypedQuery<AgendaRapport> query = entityManager.createQuery(criteriaQuery);
        query.setFirstResult((int) pageable.getOffset());
        query.setMaxResults(pageable.getPageSize());

        List<AgendaRapport> rapports = query.getResultList();
        long totalCount = entityManager.createQuery(criteriaQuery).getResultList().size();

        List<AgendaRapportDto> responseDtos = rapports.stream()
                .map(agendaRapportMapper::toDto)
                .collect(Collectors.toList());

        return new PageImpl<>(responseDtos, pageable, totalCount);
    }

    @Transactional
    public void deleteAgendaRapport(Long id) {
        AgendaRapport agendaRapport = agendaRapportRepository.findById(id)
                .orElseThrow(() -> new NoSuchElementException("Agenda rapport non trouvé avec id: " + id));
        if (agendaRapport.getRapport() != null) {
            Rapport rapport = agendaRapport.getRapport();
            if (rapport.getPictures() != null && !rapport.getPictures().isEmpty()) {
                for (RapportPicture picture : rapport.getPictures()) {
                    String filePath = picture.getUrl();
                    try {
                        minioService.DeleteFromMinIo(filePath);
                        log.info("Fichier supprimé de MinIO : {}", filePath);
                    } catch (TechnicalException e) {
                        log.error("Erreur lors de la suppression du fichier dans MinIO : {}", e.getMessage());
                    }
                }
            }
            rapportRepository.delete(rapport);
            log.info("Rapport supprimé avec succès : {}", rapport.getId());
        }
        agendaRapportRepository.delete(agendaRapport);
        log.info("Agenda rapport supprimé avec succès : {}", id);
    }

    @Transactional
    public Page<AgendaRapportResponseDto> getAgendaRapportsByAssistant(Long assistantId, Pageable pageable) {
        Page<AgendaRapport> rapports = agendaRapportRepository.findAllByAssistantId(assistantId, pageable);
    return rapports.map(agendaRapportMapper::toResponseDto);
    }

    @Transactional
    public Page<AgendaRapportResponseDto> getAgendaRapportsByAssistantFilter(
            Long assistantId, Pageable pageable, String beneficiaryCode, Long numberRapport ,String beneficiaryName,
            String reportStatus, LocalDate plannedDateStart, LocalDate plannedDateEnd,
            LocalDate validationDateStart, LocalDate validationDateEnd
    ) {
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<AgendaRapport> criteriaQuery = criteriaBuilder.createQuery(AgendaRapport.class);
        Root<AgendaRapport> root = criteriaQuery.from(AgendaRapport.class);

        Join<AgendaRapport, Beneficiary> beneficiaryJoin = root.join("beneficiary");

        Join<AgendaRapport, Rapport> rapportJoin = root.join("rapport", JoinType.LEFT);

        Predicate predicate = criteriaBuilder.conjunction();

        predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(beneficiaryJoin.get("zone").get("assistant").get("id"), assistantId));

        if (beneficiaryCode != null) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(beneficiaryJoin.get("code"), "%" + beneficiaryCode + "%"));
        }

        if (numberRapport != null) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(rapportJoin.get("numberRapport"), numberRapport));
        }


        if (beneficiaryName != null && !beneficiaryName.isEmpty()) {
            String[] nameParts = beneficiaryName.split(" ", 2);

            Predicate namePredicate = criteriaBuilder.conjunction();

            if (nameParts.length > 1) {
                Predicate firstLastNamePredicate = criteriaBuilder.and(
                        criteriaBuilder.like(criteriaBuilder.lower(beneficiaryJoin.get("person").get("firstName")), "%" + nameParts[0].toLowerCase() + "%"),
                        criteriaBuilder.like(criteriaBuilder.lower(beneficiaryJoin.get("person").get("lastName")), "%" + nameParts[1].toLowerCase() + "%")
                );

                Predicate lastFirstNamePredicate = criteriaBuilder.and(
                        criteriaBuilder.like(criteriaBuilder.lower(beneficiaryJoin.get("person").get("firstName")), "%" + nameParts[1].toLowerCase() + "%"),
                        criteriaBuilder.like(criteriaBuilder.lower(beneficiaryJoin.get("person").get("lastName")), "%" + nameParts[0].toLowerCase() + "%")
                );

                namePredicate = criteriaBuilder.and(namePredicate, criteriaBuilder.or(firstLastNamePredicate, lastFirstNamePredicate));
            } else if (nameParts.length == 1) {
                namePredicate = criteriaBuilder.and(namePredicate, criteriaBuilder.or(
                        criteriaBuilder.like(criteriaBuilder.lower(beneficiaryJoin.get("person").get("firstName")), "%" + nameParts[0].toLowerCase() + "%"),
                        criteriaBuilder.like(criteriaBuilder.lower(beneficiaryJoin.get("person").get("lastName")), "%" + nameParts[0].toLowerCase() + "%")
                ));
            }

            predicate = criteriaBuilder.and(predicate, namePredicate);
        }

        if (reportStatus != null) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("status"), reportStatus));
        }

        if (plannedDateStart != null) {
            Date plannedStartDate = Date.from(plannedDateStart.atStartOfDay(ZoneId.systemDefault()).toInstant());
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.greaterThanOrEqualTo(root.get("datePlanned"), plannedStartDate));
        }

        if (plannedDateEnd != null) {
            Date plannedEndDate = Date.from(plannedDateEnd.atStartOfDay(ZoneId.systemDefault()).toInstant());
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.lessThanOrEqualTo(root.get("datePlanned"), plannedEndDate));
        }

        if (validationDateStart != null && validationDateEnd != null) {
            Date validationStartDate = Date.from(validationDateStart.atStartOfDay(ZoneId.systemDefault()).toInstant());
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.greaterThanOrEqualTo(root.get("dateValidate"), validationStartDate));
        }

        if (validationDateEnd != null) {
            Date validationEndDate = Date.from(validationDateEnd.atStartOfDay(ZoneId.systemDefault()).toInstant());
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.lessThanOrEqualTo(root.get("dateValidate"), validationEndDate));
        }

        criteriaQuery.where(predicate);
        criteriaQuery.orderBy(criteriaBuilder.desc(root.get("modifiedAt")));

        TypedQuery<AgendaRapport> query = entityManager.createQuery(criteriaQuery);
        query.setFirstResult((int) pageable.getOffset());
        query.setMaxResults(pageable.getPageSize());

        List<AgendaRapport> rapports = query.getResultList();
        long totalCount = entityManager.createQuery(criteriaQuery).getResultList().size();

        List<AgendaRapportResponseDto> responseDtos = rapports.stream()
                .map(agendaRapportMapper::toResponseDto)
                .collect(Collectors.toList());

        return new PageImpl<>(responseDtos, pageable, totalCount);
    }

    public void rappelRapport(Long assistantId) {
        List<AgendaRapport> rapports = agendaRapportRepository.findAllByAssistantId(assistantId);

        if (rapports.isEmpty()) {
            return;
        }

        String assistantEmail = rapports.get(0).getBeneficiary().getZone().getAssistant().getEmail();
        if (assistantEmail == null || assistantEmail.isEmpty()) {
            return;
        }

        Set<String> beneficiaryCodes = new HashSet<>();
        StringBuilder beneficiaryNames = new StringBuilder();

        for (AgendaRapport rapport : rapports) {
            Beneficiary beneficiary = rapport.getBeneficiary();

            if (!beneficiaryCodes.contains(beneficiary.getCode())) {
                beneficiaryCodes.add(beneficiary.getCode());
                String beneficiaryFullName = beneficiary.getPerson().getFirstName() + " " + beneficiary.getPerson().getLastName();

                if (!beneficiaryNames.isEmpty()) {
                    beneficiaryNames.append(", ");
                }
                beneficiaryNames.append(beneficiaryFullName);
            }
            rapport.setStatus(String.valueOf(RapportStatus.RAPPORT_A_PREPARE));
        }

        if (beneficiaryNames.isEmpty()) {
            return;
        }

        String subject = "Rappel : Remplissez votre rapport";
        String body = "Bonjour,\n\nVeuillez commencer à remplir votre rapport dès que possible pour le bénéficiaire :\n"
                + beneficiaryNames.toString() + "\n\nMerci.";

        mailService.sendNewMail(assistantEmail, subject, body);
    }

    @Scheduled(cron = "0 0 8 * * *")
    public void rappelRapportAutomatique() {
        List<AgendaRapport> rapports = agendaRapportRepository.findAll();
        for (AgendaRapport rapport : rapports) {
            Date datePlanned = rapport.getDatePlanned();
            LocalDate localDatePlanned = datePlanned.toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDate();
            LocalDate currentDate = LocalDate.now();
            LocalDate targetDate = localDatePlanned.minusMonths(1);
            if (currentDate.isEqual(targetDate)) {
                rappelRapport(rapport.getBeneficiary().getZone().getAssistant().getId());
            }
        }
    }

    @Transactional
    public Page<AgendaRapportResponseDto> getAgendaRapportsByAdmin(
            Pageable pageable, String beneficiaryCode, String beneficiaryName, Long numberRapport,
            String reportStatus, LocalDate plannedDateStart, LocalDate plannedDateEnd,
            LocalDate validationDateStart, LocalDate validationDateEnd
    ) {
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<AgendaRapport> criteriaQuery = criteriaBuilder.createQuery(AgendaRapport.class);
        Root<AgendaRapport> root = criteriaQuery.from(AgendaRapport.class);

        Join<AgendaRapport, Beneficiary> beneficiaryJoin = root.join("beneficiary", JoinType.LEFT);

        Join<AgendaRapport, Rapport> rapportJoin = root.join("rapport", JoinType.LEFT);

        Join<Beneficiary, Zone> zoneJoin = beneficiaryJoin.join("zone", JoinType.LEFT);
        Join<Zone, Assistant> assistantJoin = zoneJoin.join("assistant", JoinType.LEFT);

        Predicate predicate = criteriaBuilder.conjunction();

        if (beneficiaryCode != null) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(beneficiaryJoin.get("code"), "%" + beneficiaryCode + "%"));
        }

        if (beneficiaryName != null && !beneficiaryName.isEmpty()) {
            String[] nameParts = beneficiaryName.split(" ", 2);

            Predicate namePredicate = criteriaBuilder.conjunction();

            if (nameParts.length > 1) {
                Predicate firstLastNamePredicate = criteriaBuilder.and(
                        criteriaBuilder.like(criteriaBuilder.lower(beneficiaryJoin.get("person").get("firstName")), "%" + nameParts[0].toLowerCase() + "%"),
                        criteriaBuilder.like(criteriaBuilder.lower(beneficiaryJoin.get("person").get("lastName")), "%" + nameParts[1].toLowerCase() + "%")
                );

                Predicate lastFirstNamePredicate = criteriaBuilder.and(
                        criteriaBuilder.like(criteriaBuilder.lower(beneficiaryJoin.get("person").get("firstName")), "%" + nameParts[1].toLowerCase() + "%"),
                        criteriaBuilder.like(criteriaBuilder.lower(beneficiaryJoin.get("person").get("lastName")), "%" + nameParts[0].toLowerCase() + "%")
                );

                namePredicate = criteriaBuilder.and(namePredicate, criteriaBuilder.or(firstLastNamePredicate, lastFirstNamePredicate));
            } else if (nameParts.length == 1) {
                namePredicate = criteriaBuilder.and(namePredicate, criteriaBuilder.or(
                        criteriaBuilder.like(criteriaBuilder.lower(beneficiaryJoin.get("person").get("firstName")), "%" + nameParts[0].toLowerCase() + "%"),
                        criteriaBuilder.like(criteriaBuilder.lower(beneficiaryJoin.get("person").get("lastName")), "%" + nameParts[0].toLowerCase() + "%")
                ));
            }

            predicate = criteriaBuilder.and(predicate, namePredicate);
        }

        if (numberRapport != null) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(rapportJoin.get("numberRapport"), numberRapport));
        }

        if (reportStatus != null) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("status"), reportStatus));
        }

        if (plannedDateStart != null) {
            Date plannedStartDate = Date.from(plannedDateStart.atStartOfDay(ZoneId.systemDefault()).toInstant());

            predicate = criteriaBuilder.and(predicate, criteriaBuilder.greaterThanOrEqualTo(root.get("datePlanned"), plannedStartDate));
        }

        if (plannedDateEnd != null) {
            Date plannedEndDate = Date.from(plannedDateEnd.atStartOfDay(ZoneId.systemDefault()).toInstant());
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.lessThanOrEqualTo(root.get("datePlanned"), plannedEndDate));
        }

        if (validationDateStart != null) {
            Date validationStartDate = Date.from(validationDateStart.atStartOfDay(ZoneId.systemDefault()).toInstant());
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.greaterThanOrEqualTo(root.get("dateValidate"), validationStartDate));
        }

        if (validationDateEnd != null) {
            Date validationEndDate = Date.from(validationDateEnd.atStartOfDay(ZoneId.systemDefault()).toInstant());
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.lessThanOrEqualTo(root.get("dateValidate"), validationEndDate));
        }

        criteriaQuery.where(predicate);
        criteriaQuery.orderBy(criteriaBuilder.desc(root.get("modifiedAt")));

        TypedQuery<AgendaRapport> query = entityManager.createQuery(criteriaQuery);
        query.setFirstResult((int) pageable.getOffset());
        query.setMaxResults(pageable.getPageSize());

        List<AgendaRapport> rapports = query.getResultList();
        long totalCount = entityManager.createQuery(criteriaQuery).getResultList().size();

//        List<AgendaRapportResponseDto> responseDtos = rapports.stream()
//                .map(agendaRapportMapper::toResponseDto)
//                .collect(Collectors.toList());

        List<AgendaRapportResponseDto> responseDtos = rapports.stream()
                .map(rapport -> {
                    AgendaRapportResponseDto dto = agendaRapportMapper.toResponseDto(rapport);
                    Beneficiary beneficiary = rapport.getBeneficiary();
                    if (beneficiary != null && beneficiary.getZone() != null && beneficiary.getZone().getAssistant() != null) {
                        String firstNameAssistant = beneficiary.getZone().getAssistant().getFirstName();
                        String lastNameAssistant = beneficiary.getZone().getAssistant().getLastName();
                        dto.setFullNameAssistant((firstNameAssistant != null ? firstNameAssistant : "") + " " + (lastNameAssistant != null ? lastNameAssistant : ""));
                    }

                    return dto;
                })
                .collect(Collectors.toList());

        return new PageImpl<>(responseDtos, pageable, totalCount);
    }

    @Transactional
    public Page<HistoryRapportDto> getAllAgendaRapportHistoryByFilter(
            Long beneficiaryId, Pageable pageable, String communicatedBy, String donorName,
            LocalDate dateCommunicatedStart, LocalDate dateCommunicatedEnd, HttpServletResponse response
    ) {
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<HistoryRapport> criteriaQuery = criteriaBuilder.createQuery(HistoryRapport.class);
        Root<HistoryRapport> root = criteriaQuery.from(HistoryRapport.class);

        // Joins
        Join<HistoryRapport, Beneficiary> beneficiaryJoin = root.join("beneficiary", JoinType.LEFT);
        Join<HistoryRapport, Donor> donorJoin = root.join("donor", JoinType.LEFT);
        Join<HistoryRapport, Rapport> rapportJoin = root.join("rapport", JoinType.LEFT);
        Join<HistoryRapport, AgendaRapport> agendaRapportJoin = root.join("agendaRapport", JoinType.LEFT);

        // Predicate for beneficiaryId
        Predicate predicate = criteriaBuilder.equal(beneficiaryJoin.get("id"), beneficiaryId);

        if (communicatedBy != null && !communicatedBy.isEmpty()) {
            predicate = criteriaBuilder.and(
                    predicate,
                    criteriaBuilder.like(
                            criteriaBuilder.lower(root.get("communicatedBy")),
                            "%" + communicatedBy.toLowerCase() + "%"
                    )
            );
        }

        if (donorName != null && !donorName.isEmpty()) {
            String[] nameParts = donorName.split(" ", 2);
            Predicate namePredicate = criteriaBuilder.conjunction();

            if (nameParts.length > 1) {
                Predicate firstLastNamePredicate = criteriaBuilder.and(
                        criteriaBuilder.like(criteriaBuilder.lower(donorJoin.get("firstName")), "%" + nameParts[0].toLowerCase() + "%"),
                        criteriaBuilder.like(criteriaBuilder.lower(donorJoin.get("lastName")), "%" + nameParts[1].toLowerCase() + "%")
                );

                Predicate lastFirstNamePredicate = criteriaBuilder.and(
                        criteriaBuilder.like(criteriaBuilder.lower(donorJoin.get("firstName")), "%" + nameParts[1].toLowerCase() + "%"),
                        criteriaBuilder.like(criteriaBuilder.lower(donorJoin.get("lastName")), "%" + nameParts[0].toLowerCase() + "%")
                );

                namePredicate = criteriaBuilder.or(firstLastNamePredicate, lastFirstNamePredicate);
            } else if (nameParts.length == 1) {
                namePredicate = criteriaBuilder.or(
                        criteriaBuilder.like(criteriaBuilder.lower(donorJoin.get("firstName")), "%" + nameParts[0].toLowerCase() + "%"),
                        criteriaBuilder.like(criteriaBuilder.lower(donorJoin.get("lastName")), "%" + nameParts[0].toLowerCase() + "%")
                );
            }

            predicate = criteriaBuilder.and(predicate, namePredicate);
        }

        if (dateCommunicatedStart != null) {
            Date startDate = Date.from(dateCommunicatedStart.atStartOfDay(ZoneId.systemDefault()).toInstant());
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.greaterThanOrEqualTo(root.get("dateCommunicated"), startDate));
        }

        if (dateCommunicatedEnd != null) {
            Date endDate = Date.from(dateCommunicatedEnd.atStartOfDay(ZoneId.systemDefault()).toInstant());
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.lessThanOrEqualTo(root.get("dateCommunicated"), endDate));
        }

        criteriaQuery.where(predicate);
        criteriaQuery.orderBy(criteriaBuilder.desc(root.get("dateCommunicated")));

        TypedQuery<HistoryRapport> query = entityManager.createQuery(criteriaQuery);
        query.setFirstResult((int) pageable.getOffset());
        query.setMaxResults(pageable.getPageSize());

        List<HistoryRapport> historyRapports = query.getResultList();
        long totalCount = entityManager.createQuery(criteriaQuery).getResultList().size();

        List<HistoryRapportDto> responseDtos = historyRapportMapper.toDtoList(historyRapports);

        return new PageImpl<>(responseDtos, pageable, totalCount);
    }

    @Transactional
    public ResponseEntity<?> planifierRapportManuell(Long beneficiaryId, boolean hasRapport, Date datePlanned) {

        Beneficiary beneficiary = beneficiaryRepository.findById(beneficiaryId)
                .orElseThrow(() -> new RuntimeException("Bénéficiaire non trouvé"));

        List<AgendaRapport> existingReports = agendaRapportRepository.findByDatePlanned(datePlanned);
        if (!existingReports.isEmpty()) {
            return ResponseEntity.badRequest().body("La date est déjà utilisée pour une planification.");
        }

        LocalDate localDate = datePlanned.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();

        AgendaRapport agendaRapport = new AgendaRapport();
        agendaRapport.setBeneficiary(beneficiary);
        agendaRapport.setDatePlanned(datePlanned);
        //agendaRapport.setYear(String.valueOf(LocalDate.now().getYear()));
        agendaRapport.setYear(String.valueOf(localDate.getYear()));
        agendaRapport.setStatus(String.valueOf(RapportStatus.RAPPORT_PLANIFIER));

        int currentYear = localDate.getYear();
      //  int currentYear = LocalDate.now().getYear();
        Long lastNumberRapport = agendaRapportRepository.findLastNumberRapportByBeneficiaryAndYear(beneficiaryId, currentYear);
        Long newNumberRapport = (lastNumberRapport != null) ? lastNumberRapport + 1 : 1;
        agendaRapport.setNumberRapport(newNumberRapport);

        agendaRapportRepository.save(agendaRapport);

        if (!beneficiary.getHasRapport()) {
            beneficiary.setHasRapport(true);
            beneficiaryRepository.save(beneficiary);
        }

        List<AgendaRapport> rapports = agendaRapportRepository.findByBeneficiaryIdAndYear(beneficiaryId, String.valueOf(LocalDate.now().getYear()));
        return ResponseEntity.ok(agendaRapportMapper.toDtoList(rapports));
    }

    @Transactional
    public void planifierRapportsAutomatiquementWithAllBene() {
        log.info("Début de la planification automatique des rapports...");

        // Filtrer uniquement les bénéficiaires ayant le statut BENEFICIAIRE_ACTIF
        List<Beneficiary> beneficiariesActifs = beneficiaryRepository.findAll().stream()
                .filter(beneficiary -> beneficiary.getBeneficiaryStatut() != null &&
                        beneficiary.getBeneficiaryStatut().getId().equals(BeneficiaryStatus.BENEFICIAIRE_ACTIF.getId()))
                .toList();

        if (beneficiariesActifs.isEmpty()) {
            log.info("Aucun bénéficiaire actif trouvé pour la planification.");
            return;
        }

        for (Beneficiary beneficiary : beneficiariesActifs) {
            try {
                log.info("Planification pour le bénéficiaire ID {}", beneficiary.getId());
                planifierRapport(beneficiary.getId(), true);
            } catch (Exception e) {
                log.error("Erreur lors de la planification du rapport pour le bénéficiaire ID {}",
                        beneficiary.getId(), e);
            }
        }

        log.info("Fin de la planification automatique des rapports.");
    }

}
