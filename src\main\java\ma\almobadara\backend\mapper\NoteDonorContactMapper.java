package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.donor.NoteDonorContactDTO;
import ma.almobadara.backend.model.donor.NoteDonorContact;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface NoteDonorContactMapper {

	NoteDonorContactDTO noteDonorContactModelToDto(NoteDonorContact noteDonorContact);

	Iterable<NoteDonorContactDTO> noteDonorContactListModelToDto(Iterable<NoteDonorContact> noteDonorContacts);

	NoteDonorContact noteDonorContactDtoModelToModel(NoteDonorContactDTO noteDonorContact);

	Iterable<NoteDonorContact> noteDonorContactListDtoToModal(Iterable<NoteDonorContactDTO> noteDonorContacts);

}
