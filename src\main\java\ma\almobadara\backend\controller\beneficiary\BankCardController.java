package ma.almobadara.backend.controller.beneficiary;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.beneficiary.BankCardDTO;
import ma.almobadara.backend.mapper.BankCardMapper;
import ma.almobadara.backend.model.beneficiary.BankCard;
import ma.almobadara.backend.service.beneficiary.BankCardService;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static ma.almobadara.backend.service.administration.CacheAdUserService.logUserInfo;

@RefreshScope
@RestController
@AllArgsConstructor
@Slf4j
@RequestMapping("/bankCards")
public class BankCardController {

    private final BankCardService bankCardService;
    private final BankCardMapper bankCardMapper;


    @PostMapping("/{personId}")
    public ResponseEntity<BankCardDTO> addBankCardToPerson(@PathVariable Long personId, @RequestBody BankCardDTO bankCardDTO) {
        logUserInfo("getBankCardByPersonId", String.valueOf(personId));

        BankCardDTO createdBankCardDTO;
        HttpStatus status = HttpStatus.OK;
        try {
         createdBankCardDTO = bankCardService.addBankCardToPerson(personId, bankCardDTO);

            log.info("End service created BankCard for person ID: {}, OK", personId);
        } catch (Exception e) {
            log.error("End service created BankCard for person ID: {}, KO - {}", personId, e.getMessage());
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }

        return new ResponseEntity<>(createdBankCardDTO, new HttpHeaders(), status);
    }

    @GetMapping("/{personId}/list-bankCards")
    public ResponseEntity<List<BankCardDTO>> getBankCardByPersonId(@PathVariable Long personId) {
        logUserInfo("getBankCardByPersonId", String.valueOf(personId));

        List<BankCardDTO> bankCards = new ArrayList<>();
        HttpStatus status = HttpStatus.OK;
        try {
            bankCards = bankCardService.getBankCardByPersonId(personId);
            log.info("End list Bank Cards for person ID: {}, OK", personId);
        } catch (Exception e) {
            status = HttpStatus.INTERNAL_SERVER_ERROR;
            log.error("End list Bank Cards for person ID: {}, KO - {}", personId, e.getMessage());
        }

        return new ResponseEntity<>(bankCards, status);
    }

    @DeleteMapping(value = "{idBankCard}", headers = "Accept=application/json")
    @Operation(summary = "Delete Bank Card", description = "delete Bank Card", tags = {"SIG"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204", description = "successful operation", content = @Content(schema = @Schema()))})
    public ResponseEntity<Void> deleteBankCard(@PathVariable Long idBankCard)  {
        logUserInfo("deleteBankCard", String.valueOf(idBankCard));

        HttpStatus status = HttpStatus.NO_CONTENT;
        try {
            bankCardService.deleteBankCard(idBankCard);
            log.info("End Delete BankCard {} : OK", idBankCard);
        } catch (Exception e) {
            status = HttpStatus.INTERNAL_SERVER_ERROR;
            log.error("End Delete BankCard {} : KO - {}", idBankCard, e.getMessage());
        }

        return new ResponseEntity<>(status);
    }

}
