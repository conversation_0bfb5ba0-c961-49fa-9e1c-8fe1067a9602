package ma.almobadara.backend.model.beneficiary;

import jakarta.persistence.*;
import lombok.*;

@Getter
@Setter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
public class BeneficiaryHandicap extends BaseEntity{

    private String handicapCause;
    private Double handicapCost;
    private Long handicapTypeId;
    private String comment;
    @ManyToOne
    @JoinColumn(name = "beneficiaryId")
    private Beneficiary beneficiary;

}
