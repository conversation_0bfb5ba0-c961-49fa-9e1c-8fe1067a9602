package ma.almobadara.backend.controller.administration;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.config.HasAccessToModule;
import ma.almobadara.backend.dto.administration.CacheAdUserDTO;
import ma.almobadara.backend.enumeration.Functionality;
import ma.almobadara.backend.enumeration.Module;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.exceptions.UserAlreadyExistsException;
import ma.almobadara.backend.exceptions.UserNotFoundException;
import ma.almobadara.backend.model.administration.CacheAdUser;
import ma.almobadara.backend.service.administration.CacheAdUserService;
import org.springframework.data.domain.Page;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.net.MalformedURLException;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.NoSuchElementException;


@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping(value = "/batch")
public class AdUserBatchController {

    private final CacheAdUserService cacheAdUserService;


    @GetMapping(value = "/AdUsers", produces = MediaType.APPLICATION_JSON_VALUE)
    @HasAccessToModule(modules = {Module.USER}, functionalities = {Functionality.VIEW})
    @Operation(summary = "Get All Cached Users", description = "Retrieve all users cached in the database", tags = {"Cached Users"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(schema = @Schema(implementation = Page.class))),
            @ApiResponse(responseCode = "404", description = "the requested url was not found on this server"),
    })
    public ResponseEntity<Page<CacheAdUserDTO>> getAllCachedUsers(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) final String searchByNom,
            @RequestParam(required = false) final String searchRole,
            @RequestParam(required = false) final String searchByPrenom,
            @RequestParam(required = false) final String searchByEmail,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") final Date minDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") final Date maxDate
            ) {
        log.info("Start resource getAllCachedUsers page: {}, size: {}, searchRole: {}, firstName: {}, lastName: {}", page, size, searchByNom, searchByPrenom, searchByEmail);

        // Fetch users with pagination and optional search parameters
        Page<CacheAdUserDTO> cachedUsers = cacheAdUserService.getAllCachedUsers(page, size, searchByNom, searchByPrenom,searchRole, searchByEmail, minDate, maxDate);

        log.info("End resource getAllCachedUsers with size: {}", cachedUsers.getTotalElements());
        return ResponseEntity.ok(cachedUsers);
    }

    @GetMapping(value = "/ConnectedUser", produces = MediaType.APPLICATION_JSON_VALUE)
    @Operation(summary = "Get Connected User", description = "Retrieve the connected user", tags = {"Connected User"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "successful operation", content = @Content(schema = @Schema(implementation = CacheAdUserDTO.class))),
            @ApiResponse(responseCode = "404", description = "the requested url was not found on this server"),
    })
    public ResponseEntity<CacheAdUserDTO> getConnectedUser() {
        log.info("Start resource getConnectedUser");

        // Retrieve the connected user
        CacheAdUserDTO connectedUser = cacheAdUserService.getConnectedUser();

        if (connectedUser == null) {
            log.error("Connected user not found.");
            throw new UserNotFoundException("Connected user not found.", 404);
        }

        log.info("End resource getConnectedUser");
        return ResponseEntity.ok(connectedUser);
    }

    //changeUserRole
    @PostMapping(value = "/changeUserRole")
    @HasAccessToModule(modules = {Module.USER}, functionalities = {Functionality.UPDATE})
    @Operation(summary = "Change User Role", description = "Change the role of a user by userId", tags = {"User Management"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Role changed successfully"),
            @ApiResponse(responseCode = "404", description = "User not found"),
    })
    public ResponseEntity<String> changeUserRole(
            @RequestParam Long userId,
            @RequestParam Long newRoleId) throws TechnicalException {

        log.info("Start resource changeUserRole with userId: {} and newRoleId: {}", userId, newRoleId);

        cacheAdUserService.changeUserRole(userId, newRoleId);

        log.info("End resource changeUserRole with userId: {} and newRoleId: {}", userId, newRoleId);

        return ResponseEntity.ok("Role changed successfully");
    }


    @PostMapping(value = "/users/loadByEmail", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<String> loadAndSaveAdUserByEmail(
            @RequestParam String email,
            @RequestParam(required = false) Long roleId // New parameter for profile ID
    ) {
        try {
            cacheAdUserService.loadAndSaveAdUserByEmail(email, roleId); // Pass profileId to service method
            return ResponseEntity.ok("User loaded and saved successfully");
        } catch (UserNotFoundException ex) {
            log.error("User not found with email: {}", email);
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body("User not found with email: " + email);
        } catch (UserAlreadyExistsException ex) {
            log.error("User already exists with email: {}", email);
            return ResponseEntity.status(HttpStatus.CONFLICT).body("User already exists with email: " + email);
        } catch (RuntimeException ex) {
            log.error("Error loading or saving user with email {}: {}", email, ex.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Internal server error");
        }
    }

    @DeleteMapping(value = "/users/{userId}")
    @HasAccessToModule(modules = {Module.USER}, functionalities = {Functionality.DELETE})
    @Operation(summary = "Delete User by ID", description = "Delete a user by their ID", tags = {"User Management"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "User deleted successfully"),
            @ApiResponse(responseCode = "404", description = "User not found"),
    })
    public ResponseEntity<String> deleteUserById(@PathVariable Integer userId) {
        log.info("Start resource deleteUserById with userId: {}", userId);

        try {
            cacheAdUserService.deleteUserById(userId);
            log.info("User with ID {} deleted successfully", userId);
            return ResponseEntity.ok("User deleted successfully");
        } catch (NoSuchElementException ex) {
            log.error("User with ID {} not found", userId);
            return ResponseEntity.notFound().build();
        }catch (TechnicalException ex) {
            log.error("Error deleting user with ID {}: {}", userId, ex.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(ex.getMessage());
        } catch (Exception ex) {
            log.error("Error deleting user with ID {}: {}", userId, ex.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Internal server error");
        }
    }

    @GetMapping("/lastSignIn")
    public String getLastSignIn() throws MalformedURLException {
        cacheAdUserService.logLastSignIn();
        return "Last sign-in logged";
    }


    @GetMapping("/getLast")
    public Iterable<CacheAdUser> getLast() {
        return cacheAdUserService.getLast();
    }

    @GetMapping("/user/{azureDirectoryId}/last-login-date")
    public ResponseEntity<LocalDateTime> getLastLoginInDate(@PathVariable String azureDirectoryId) {
        LocalDateTime lastLoginInDate = cacheAdUserService.getLastLoginInDate(azureDirectoryId);
        return ResponseEntity.ok(lastLoginInDate);
    }

    //LoadRoles

}
