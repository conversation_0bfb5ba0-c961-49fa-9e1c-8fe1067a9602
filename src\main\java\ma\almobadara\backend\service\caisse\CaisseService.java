package ma.almobadara.backend.service.caisse;

import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityNotFoundException;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import jakarta.transaction.Transactional;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.clients.RefFeignClient;
import ma.almobadara.backend.dto.aideComplemenatire.AideComplementaireDTO;
import ma.almobadara.backend.dto.caisse.CaisseDataDTO;
import ma.almobadara.backend.dto.caisse.CaisseDto;
import ma.almobadara.backend.dto.caisse.CaisseEntryDTO;
import ma.almobadara.backend.dto.donation.BudgetLineDTO;
import ma.almobadara.backend.dto.referentiel.TypePriseEnChargeDTO;
import ma.almobadara.backend.enumeration.AideComplementaireStatut;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.mapper.AideComplementaireMapper;
import ma.almobadara.backend.mapper.BudgetLineMapper;
import ma.almobadara.backend.mapper.CaisseMapper;
import ma.almobadara.backend.model.aideComplemenatire.AideComplementaire;
import ma.almobadara.backend.model.caisse.Caisse;
import ma.almobadara.backend.model.donation.BudgetLine;
import ma.almobadara.backend.model.donor.Donor;
import ma.almobadara.backend.model.donor.DonorPhysical;
import ma.almobadara.backend.repository.caisse.CaisseRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.ZoneId;
import java.time.format.TextStyle;
import java.util.*;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
@Slf4j
public class CaisseService {

   /* private final CaisseRepository caisseRepository;
    private final CaisseMapper caisseMapper ;
    private final AideComplementaireMapper aideComplementaireMapper ;
    private final BudgetLineMapper budgetLineMapper ;
    private final RefFeignClient refFeignClient;
    private final EntityManager entityManager;


    public CaisseDto addCaisse(CaisseDto caisseDto) {
        String code = generateCode();
        caisseDto.setCode(code);
        Caisse caisse = caisseMapper.toEntity(caisseDto);
        Caisse savedCaisse = caisseRepository.save(caisse);
        return caisseMapper.toDTO(savedCaisse);
    }

    private String generateCode() {
        String newCode;
        int nextCodeNumber;

        Optional<String> lastCodeOpt = caisseRepository.findTopByOrderByCodeDesc().map(Caisse::getCode);
        if (lastCodeOpt.isPresent()) {
            String lastCode = lastCodeOpt.get();
            nextCodeNumber = Integer.parseInt(lastCode.replaceAll("\\D+", "")) + 1;
        } else {
            nextCodeNumber = 1;
        }
        do {
            newCode = String.format("CAISSE%03d", nextCodeNumber);
            nextCodeNumber++;
        } while (caisseRepository.existsByCode(newCode)); // Vérifiez si le code existe déjà

        return newCode;
    }

    public Page<CaisseDto> getAllCaisses(int page, int size, String searchByCode, String searchByName) {

        Pageable pageable = PageRequest.of(page, size);
        Page<Caisse> caisses;

        if (searchByCode != null || searchByName != null) {
            caisses = filterCaisses(searchByCode, searchByName, pageable);
        } else {
            caisses = caisseRepository.findAllWithDeletedIsFalse(pageable);
        }

        List<CaisseDto> caisseDtos = caisses.getContent().stream()
                .map(caisse -> {
                    CaisseDto caisseDto = caisseMapper.toDTO(caisse);
                    if (caisseDto.getTypePriseEnChargeId() != null) {
                        TypePriseEnChargeDTO typePriseEnChargeDTO = refFeignClient.getParTypePriseEnCharge(caisseDto.getTypePriseEnChargeId());
                        caisseDto.setTypePriseEnChargeDescription(typePriseEnChargeDTO);
                    }

                    return caisseDto;
                })
                .collect(Collectors.toList());

        log.debug("End service getAllCaisses with {} caisses found, took {}", caisses.getTotalElements());
        return new PageImpl<>(caisseDtos, pageable, caisses.getTotalElements());
    }

    private Page<Caisse> filterCaisses(String searchByCode, String searchByName, Pageable pageable) {
        log.debug("Start service filterCaisses with searchByCode: {}, searchByAssistantName: {}, searchByName: {}, searchByNameAr: {}, searchByStatus: {}",
                searchByCode, searchByName);

        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Caisse> criteriaQuery = criteriaBuilder.createQuery(Caisse.class);
        Root<Caisse> root = criteriaQuery.from(Caisse.class);

        Predicate predicate = buildPredicate(criteriaBuilder, root, searchByCode, searchByName);
        criteriaQuery.where(predicate);

        TypedQuery<Caisse> typedQuery = entityManager.createQuery(criteriaQuery);
        long totalCount = (long) typedQuery.getResultList().size();
        typedQuery.setFirstResult((int) pageable.getOffset());
        typedQuery.setMaxResults(pageable.getPageSize());
        List<Caisse> resultList = typedQuery.getResultList();

        log.debug("End service filterCaisses with {} Caisses found", totalCount);
        return new PageImpl<>(resultList, pageable, totalCount);
    }

    private Predicate buildPredicate(CriteriaBuilder criteriaBuilder, Root<Caisse> root,
                                     String searchByCode, String searchByName) {
        Predicate predicate = criteriaBuilder.conjunction();
        predicate = criteriaBuilder.and(predicate, criteriaBuilder.isFalse(root.get("isDeleted")));

        if (searchByCode != null) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(criteriaBuilder.lower(root.get("code")),
                    "%" + searchByCode.toLowerCase() + "%"));
        }

        if (searchByName != null) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(criteriaBuilder.lower(root.get("name")),
                    "%" + searchByName.toLowerCase() + "%"));
        }

        return predicate;
    }

    public CaisseDto getCaisseById(Long id, Pageable pageable) {
        Caisse caisse = caisseRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Caisse not found with ID: " + id));

        CaisseDto caisseDto = caisseMapper.toDTO(caisse);

        if (caisseDto.getTypePriseEnChargeId() != null) {
            TypePriseEnChargeDTO fullTypePriseEnChargeDTO = refFeignClient.getParTypePriseEnCharge(caisseDto.getTypePriseEnChargeId());
            caisseDto.setTypePriseEnChargeDescription(fullTypePriseEnChargeDTO);
        }


    List<BudgetLineDTO> budgetLineDTOs;

        if (caisse.getId() == 1) {
            budgetLineDTOs = retrieveBudgetLinesByType("Kafalat", pageable);
        } else if (caisse.getId() == 2) {
            budgetLineDTOs = retrieveBudgetLinesByType("Non Identifié", pageable);
        } else {
            Page<BudgetLine> budgetLinesPage = caisseRepository.findByTypePriseEnChargeAndNotExecuted(caisseDto.getTypePriseEnChargeId(), pageable);
            budgetLineDTOs = budgetLinesPage.getContent().stream()
                    .map(budgetLine -> mapBudgetLineToDTO(budgetLine))
                    .collect(Collectors.toList());
        }

        caisseDto.setBudgetLines(budgetLineDTOs);

//        Page<AideComplementaire> aideComplementairePage = caisseRepository.findByTypePriseEnChargeIdAndStatut(
//                caisseDto.getTypePriseEnChargeId(),
//                AideComplementaireStatut.EXECUTER.getValue(),
//                pageable
//        );
        //List<AideComplementaireDTO> aideComplementaireDTOs = aideComplementaireMapper.toDtoList(aideComplementairePage.getContent());
        //caisseDto.setAidesComplementaires(aideComplementaireDTOs);
        //caisseDto.setTotalAidesComplementaires(aideComplementairePage.getTotalElements());

        return caisseDto;
    }

    private List<BudgetLineDTO> retrieveBudgetLinesByType(String type, Pageable pageable) {
        Page<BudgetLine> budgetLinesPage = caisseRepository.findByType(type, pageable);

        return budgetLinesPage.getContent().stream()
                .map(budgetLine -> mapBudgetLineToDTO(budgetLine))
                .collect(Collectors.toList());
    }

    private BudgetLineDTO mapBudgetLineToDTO(BudgetLine budgetLine) {
        BudgetLineDTO dto = budgetLineMapper.budgetLineToBudgetLineDTO(budgetLine);

        String codeDonation = budgetLine.getDonation() != null ? budgetLine.getDonation().getCode() : null;
        dto.setCodeDonation(codeDonation);

        Long idDonation = budgetLine.getDonation() != null ? budgetLine.getDonation().getId() : null;
        dto.setIdDonation(idDonation);

        Date receptionDateDonation = budgetLine.getDonation() != null ? budgetLine.getDonation().getReceptionDate() : null;
        dto.setReceptionDateDonation(receptionDateDonation);

        Donor donor = budgetLine.getDonation() != null ? budgetLine.getDonation().getDonor() : null;
        String fullNameDonor;
        if (donor instanceof DonorPhysical) {
            DonorPhysical donorPhysical = (DonorPhysical) donor;
            fullNameDonor = donorPhysical.getFirstName() + " " + donorPhysical.getLastName();
        } else {
            fullNameDonor = donor != null ? donor.getCode() : null;
        }
        dto.setFullNameDonor(fullNameDonor);

        return dto;
    }

    @Transactional
    public void updateCaisse(Long id, CaisseDto caisseDto) throws TechnicalException {
        log.debug("Start service update caisse with id {}", id);

        Caisse existingCaisse = caisseRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Caisse not found with ID " + id));
        caisseDto.setCreatedAt(existingCaisse.getCreatedAt());
        caisseMapper.updateCaisseFromDTO(caisseDto, existingCaisse);
        existingCaisse.setUpdatedAt(LocalDateTime.now());

        try {
            caisseRepository.save(existingCaisse);
        } catch (Exception e) {
            log.error("Error occurred while updating the caisse: {}", e.getMessage());
            throw new RuntimeException(e.getMessage(), e);
        }

        log.debug("End service update caisse with id {}", id);
    }

    public List<TypePriseEnChargeDTO> getAvailableTypePriseEnCharges() {
        List<TypePriseEnChargeDTO> allTypes = refFeignClient.getAllParTypePriseEnCharge();
        List<Long> typeIdsLinkedToCaisse = caisseRepository.findAll()
                .stream()
                .map(Caisse::getTypePriseEnChargeId)
                .collect(Collectors.toList());

        return allTypes.stream()
                .filter(type -> !typeIdsLinkedToCaisse.contains(type.getId()))
                .collect(Collectors.toList());
    }

    public List<CaisseDataDTO> getCaisseData(Long id) {
        Caisse caisse = caisseRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Caisse not found with ID: " + id));

        CaisseDto caisseDto = caisseMapper.toDTO(caisse);

//        List<AideComplementaire> aidesComplementaires = caisseRepository.findByTypePriseEnChargeIdAndStatut(
//                caisseDto.getTypePriseEnChargeId(),
//                AideComplementaireStatut.EXECUTER.getValue(),
//                Pageable.unpaged()
//        ).getContent();

        List<BudgetLine> budgetLines = new ArrayList<>(caisseRepository.findByTypePriseEnChargeAndNotExecuted(
                caisseDto.getTypePriseEnChargeId(),
                Pageable.unpaged()
        ).getContent());

        if (caisseDto.getId() == 1) {
            budgetLines.addAll(caisseRepository.findByType("Kafalat"));
        } else if (caisseDto.getId() == 2) {
            budgetLines.addAll(caisseRepository.findByType("Non Identifié"));
        }

        Map<YearMonth, List<CaisseEntryDTO>> groupedEntries = new HashMap<>();

//        for (AideComplementaire aide : aidesComplementaires) {
//            LocalDateTime dateExecution = aide.getDateExecution();
//            if (dateExecution != null) {
//                YearMonth ym = YearMonth.from(dateExecution.toLocalDate());
//                groupedEntries.computeIfAbsent(ym, k -> new ArrayList<>())
//                        .add(new CaisseEntryDTO(
//                                ym.getMonthValue(),
//                                ym.getYear(),
//                                null,
//                                aide.getMontantPrevu(),
//                                null,
//                                aide.getName(),
//                                dateExecution.toString()
//                        ));
//            }
//        }

        for (BudgetLine line : budgetLines) {
            Date receptionDateDonation = line.getDonation() != null ? line.getDonation().getReceptionDate() : null;
            if (receptionDateDonation != null) {
                YearMonth ym = YearMonth.from(receptionDateDonation.toInstant().atZone(ZoneId.systemDefault()).toLocalDate());

                Donor donor = line.getDonation() != null ? line.getDonation().getDonor() : null;
                String fullNameDonor;
                if (donor instanceof DonorPhysical) {
                    DonorPhysical donorPhysical = (DonorPhysical) donor;
                    fullNameDonor = donorPhysical.getFirstName() + " " + donorPhysical.getLastName();
                } else {
                    fullNameDonor = donor != null ? donor.getCode() : null;
                }

                groupedEntries.computeIfAbsent(ym, k -> new ArrayList<>())
                        .add(new CaisseEntryDTO(
                                ym.getMonthValue(),
                                ym.getYear(),
                                fullNameDonor,
                                null,
                                line.getAmount(),
                                null,
                                receptionDateDonation.toString()
                        ));
            }
        }

        List<CaisseDataDTO> caisseDataList = new ArrayList<>();
        for (Map.Entry<YearMonth, List<CaisseEntryDTO>> entry : groupedEntries.entrySet()) {
            caisseDataList.add(new CaisseDataDTO(
                    entry.getKey().getMonthValue(),
                    entry.getKey().getMonth().getDisplayName(TextStyle.FULL, Locale.FRENCH),
                    entry.getKey().getYear(),
                    entry.getValue()
            ));
        }
        return caisseDataList;
    }
*/
}
