package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.beneficiary.BeneficiaryDTO;
import ma.almobadara.backend.dto.beneficiary.PersonDTO;
import ma.almobadara.backend.dto.communs.DocumentDTO;
import ma.almobadara.backend.dto.communs.NoteDTO;
import ma.almobadara.backend.dto.donation.DonationDTO;
import ma.almobadara.backend.dto.donation.DonationProductNatureDTO;
import ma.almobadara.backend.dto.donor.*;
import ma.almobadara.backend.dto.mobile.DonorMoralMobileDTO;
import ma.almobadara.backend.dto.referentiel.CanalCommunicationDTO;
import ma.almobadara.backend.dto.referentiel.LanguageCommunicationDTO;
import ma.almobadara.backend.dto.takenInCharge.*;
import ma.almobadara.backend.model.beneficiary.Beneficiary;
import ma.almobadara.backend.model.beneficiary.Person;
import ma.almobadara.backend.model.donation.Donation;
import ma.almobadara.backend.model.donation.DonationProductNature;
import ma.almobadara.backend.model.donor.*;
import ma.almobadara.backend.model.takenInCharge.*;
import org.mapstruct.IterableMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.List;
import java.util.Optional;

@Mapper(componentModel = "spring")
public interface DonorMoralMapper {

	@Mapping(target = "donorContacts", qualifiedByName = "ListDonorContact")
	@Mapping(source = "activitySectorId", target = "activitySector.id")
	@Mapping(source = "typeDonorMoralId", target = "typeDonorMoral.id")
	@Mapping(source = "donorStatusId", target = "status.id")
	@Mapping(source = "cityId", target = "city.id")
	@Mapping(source = "documentsDonors", target = "documentDonors")
	DonorMoralDTO donorMoralModelToDto(DonorMoral donor);

	@Mapping(target = "donorContacts", qualifiedByName = "ListDonorContact")
	@Mapping(source = "activitySectorId", target = "activitySector.id")
	@Mapping(source = "typeDonorMoralId", target = "typeDonorMoral.id")
	DonorMoralMobileDTO donorMoralModelToMobilDto(DonorMoral donor);

	@IterableMapping(qualifiedByName = "mapWithoutNesting")
	Iterable<DonorMoralDTO> donorMoralListModelToDto(Iterable<DonorMoral> donors);

	@Named("mapWithoutNesting")
	@Mapping(target = "takenInChargeDonors", ignore = true)
	@Mapping(target = "documentDonors", ignore = true)
	@Mapping(target = "notes", ignore = true)
	@Mapping(target = "donorContacts", qualifiedByName = "ListDonorContactForList")
	@Mapping(target = "donations", ignore = true)
	@Mapping(source = "status", target = "status")
	@Mapping(source = "city", target = "city")
	@Mapping(source = "activitySectorId", target = "activitySector.id")
	DonorMoralDTO donorMoralModelToDtoForList(DonorMoral donor);

	@Mapping(target = "takenInChargeDonors", ignore = true)
	@Mapping(target = "documentDonors", ignore = true)
	@Mapping(target = "notes", ignore = true)
	@Mapping(target = "donorContacts", qualifiedByName = "ListDonorContactForList")
	@Mapping(source = "cityId", target = "city.id")
	@Mapping(source = "activitySectorId", target = "activitySector.id")
	DonorMoralDTO donorMoralModelToOneDTO(DonorMoral donor);

	@Mapping(source = "activitySector.id", target = "activitySectorId")
	@Mapping(source = "typeDonorMoral.id", target = "typeDonorMoralId")
	@Mapping(source = "status.id", target = "donorStatusId")
	@Mapping(source = "city.id", target = "cityId")
	@Mapping(target = "donorContacts", qualifiedByName = "ListDonorContactDTOForList")
	DonorMoral donorMoralDtoToModel(DonorMoralDTO donor);

	Iterable<DonorMoral> donorMoralListDtoToModal(Iterable<DonorMoralDTO> donors);

	@Named("ListDonorContact")
	@IterableMapping(qualifiedByName = "DonorContact")
	List<DonorContactDTO> donorContactListModalToDTO(List<DonorContact> donorContacts);

	@Named("ListDonorContactForList")
	@IterableMapping(qualifiedByName = "DonorContactForList")
	List<DonorContactDTO> donorContactListModalToDTOForList(List<DonorContact> donorContacts);


	@Named("ListDonorContactDTOForList")
	@IterableMapping(qualifiedByName = "donorContactDTOtoModelForList")
	List<DonorContact> donorContactDTOtoModel(List<DonorContactDTO> donorContactDTOS);

	@Named("donorContactDTOtoModelForList")
	@IterableMapping(qualifiedByName = "ListLanguageCommunicationsForList")
	//@Mapping(target = "id",ignore = true)
	@Mapping(source = "donorContactFunction.id", target = "donorContactFunctionId")
	@Mapping(source = "languageCommunications", target = "donorContactLanguageCommunications")
	@Mapping(source = "canalCommunications", target = "donorContactCanalCommunications")
	@Mapping(target = "donor", ignore = true)
	DonorContact donorContactDTOtoModel(DonorContactDTO donorContactDTO);

	@Mapping(target = "id", ignore = true)
	@Mapping(source = "id", target = "languageCommunicationId")
	DonorContactLanguageCommunication languageCommunicationDTOToDonorContactLanguageCommunication(LanguageCommunicationDTO languageCommunicationDTO);

	List<DonorContactLanguageCommunication> languageCommunicationDTOToDonorContactLanguageCommunication(List<LanguageCommunicationDTO> languageCommunicationDTO);

	@Mapping(source = "languageCommunicationId", target = "id")
	LanguageCommunicationDTO donorContactLanguageCommunicationToLanguageCommunicationDTO(DonorContactLanguageCommunication donorContactLanguageCommunication);

	List<LanguageCommunicationDTO> donorContactLanguageCommunicationToLanguageCommunicationDTO(List<DonorContactLanguageCommunication> donorContactLanguageCommunications);

	@Mapping(target = "id", ignore = true)
	@Mapping(source = "id", target = "canalCommunicationId")
	DonorContactCanalCommunication canalCommunicationDTOToDonorContactCanalCommunication(CanalCommunicationDTO canalCommunicationDTO);

	List<DonorContactCanalCommunication> canalCommunicationDTOToDonorContactCanalCommunication(List<CanalCommunicationDTO> canalCommunicationDTOS);

	@Mapping(source = "canalCommunicationId", target = "id")
	CanalCommunicationDTO donorContactCanalCommunicationToCanalCommunicationDTO(DonorContactCanalCommunication donorContactCanalCommunication);

	List<CanalCommunicationDTO> donorContactCanalCommunicationToCanalCommunicationDTO(List<DonorContactCanalCommunication> donorContactCanalCommunications);

	@Named("ListLanguageCommunicationsForList")
	@IterableMapping(qualifiedByName = "languageCommunicationsForList")
	List<DonorContactLanguageCommunication> LanguageCommunicationDTOtoModel(List<LanguageCommunicationDTO> languageCommunicationDTO);

	@Named("languageCommunicationsForList")
	//@Mapping(target = "id",ignore = true)
	@Mapping(source = "id", target = "languageCommunicationId")
	DonorContactLanguageCommunication LanguageCommunicationDTOtoModel(LanguageCommunicationDTO languageCommunicationDTO);

	@Named("DonorContact")
	@Mapping(target = "donor", ignore = true)
	@Mapping(source = "donorContactFunctionId", target = "donorContactFunction.id")
	@Mapping(source = "donorContactLanguageCommunications", target = "languageCommunications")
	@Mapping(source = "donorContactCanalCommunications", target = "canalCommunications")
	DonorContactDTO donorContactModelToDto(DonorContact donor);

	@Named("DonorContactForList")
	@Mapping(target = "donor", ignore = true)
	@Mapping(target = "canalCommunications", ignore = true)
	@Mapping(target = "languageCommunications", ignore = true)
	@Mapping(target = "noteDonorContacts", ignore = true)
	@Mapping(source = "donorContactFunctionId", target = "donorContactFunction.id")
	DonorContactDTO donorContactModelToDtoForList(DonorContact donor);

	NoteDTO noteModelToDto(NoteDonor donor);

	@Mapping(target = "donorContact", ignore = true)
	NoteDonorContactDTO donorNoteContactModelToDTO(NoteDonorContact noteDonorContact);

	DocumentDTO documentDonorModelToDto(DocumentDonor donor);

	// we ignore documents of donation
	@Mapping(target = "donor", ignore = true)
	@Mapping(target = "documentDonations", ignore = true)
	@Mapping(source = "canalDonationId", target = "canalDonation.id")
	DonationDTO donationToDonationDTO(Donation donation);

	@Mapping(target = "donation", ignore = true)
	@Mapping(source = "productNatureId", target = "productNature.id")
	@Mapping(source = "productUnitId", target = "productUnit.id")
	DonationProductNatureDTO donationProductNatureToDonationProductNatureDTO(DonationProductNature donationProductNature);

	//****************************************************************************************
	@Mapping(target = "donor", ignore = true)
	TakenInChargeDonorDTO takenInChargeDonorDTOToTakenInChargeDonor(TakenInChargeDonor takenInChargeDonor);

	@Mapping(target = "donor", ignore = true)
	TakenInChargeDonor takenInChargeDonorToTakenInChargeDonorDTO(TakenInChargeDonorDTO takenInChargeDonorDTO);

	@Mapping(target = "takenInChargeDonor", ignore = true)
	TakenInChargeOperationDTO takenInChargeOperationToTakenInChargeOperationDTO(TakenInChargeOperation takenInChargeOperation);

	@Mapping(target = "takenInChargeDonor", ignore = true)
	TakenInChargeOperation takenInChargeOperationDTOToTakenInChargeOperation(TakenInChargeOperationDTO takenInChargeOperationDTO);

	@Mapping(target = "takenInChargeDonors", ignore = true)
	TakenInChargeDTO takenInChargeDTOToTakenInCharge(TakenInCharge takenInCharge);

	DocumentTakenInChargeDTO takenInChargeDocumentToTakenInChargeDocumentDTO(DocumentTakenInCharge takenInChargeDocument);

	@Mapping(target = "takenInCharge", ignore = true)
	DocumentTakenInCharge takenInChargeDocumentDTOToTakenInChargeDocument(DocumentTakenInChargeDTO takenInChargeDocumentDTO);

	NoteTakenInChargeDTO takenInChargeNoteToTakenInChargeNoteDTO(NoteTakenInCharge takenInChargeNote);

	@Mapping(target = "takenInCharge", ignore = true)
	NoteTakenInCharge takenInChargeNoteDTOToTakenInChargeNote(NoteTakenInChargeDTO takenInChargeNoteDTO);

	@Mapping(target = "takenInChargeDonors", ignore = true)
	TakenInCharge takenInChargeToTakenInChargeDTO(TakenInChargeDTO takenInChargeDTO);

	@Mapping(target = "takenInCharge", ignore = true)
	@Mapping(target = "bankCards", ignore = true)
	TakenInChargeBeneficiaryDTO takenInChargeBeneficiaryDTOToTakenInChargeBeneficiary(TakenInChargeBeneficiary takenInChargeBeneficiary);

	@Mapping(target = "takenInCharge", ignore = true)
	TakenInChargeBeneficiary takenInChargeBeneficiaryToTakenInChargeBeneficiaryDTO(TakenInChargeBeneficiaryDTO takenInChargeBeneficiaryDTO);

	@Mapping(target = "takenInChargeBeneficiaries", ignore = true)
	@Mapping(target = "notes", ignore = true)
	@Mapping(target = "beneficiaryServices", ignore = true)
	@Mapping(target = "scholarshipBeneficiaries", ignore = true)
	@Mapping(target = "epsResidents", ignore = true)
	@Mapping(target = "diseaseTreatments", ignore = true)
	@Mapping(target = "educations", ignore = true)
	@Mapping(target = "documents", ignore = true)
	@Mapping(target = "handicapped", ignore = true)
	BeneficiaryDTO beneficiaryToBeneficiaryDTO(Beneficiary beneficiary);

	@Mapping(target = "beneficiary", ignore = true)
	@Mapping(target = "bankCards", ignore = true)
	@Mapping(target = "familyMember", ignore = true)
	PersonDTO personToPersonDTO(Person person);

	@Mapping(source = "company", target = "raisonSociale")
	@Mapping(source = "shortCompany", target = "abreviationNomEntreprise")
	@Mapping(source = "identityCode", target = "identifiantEntreprise")
	@Mapping(source = "address", target = "adresse")
	@Mapping(source = "addressAr", target = "adresseArabe")
	@Mapping(source = "firstDonationYear", target = "AnneePremiereDonation")
	@Mapping(target = "SecteursActivite", ignore = true)
	@Mapping(target = "typeMoral", ignore = true)
	@Mapping(target = "statut", ignore = true)
	DonorMoralDtoAudit donorMoralDtoToDonorMoralDtoAudit(DonorMoralDTO donorMoralDTO);

	DonorDTO donorModeltoModelDto(Donor donor);

	@Mapping(source = "identityCode", target = "Identite")
	@Mapping(source = "address", target = "adresse")
	@Mapping(source = "addressAr", target = "adresseArabe")
	@Mapping(source = "firstDonationYear", target = "anneePremiereDonation")
	DeleteDonorAuditDto donorDtoToDonorAudit(DonorDTO donorDTO);



	@Mapping(target = "fonction", ignore = true)
	@Mapping(target = "canalCommunication", ignore = true)
	@Mapping(target = "languageCommunication", ignore = true)
	@Mapping(target = "contactPourEntreprise", ignore = true)
	@Mapping(source = "firstName", target = "prenom")
	@Mapping(source = "lastName", target = "nom")
	@Mapping(source = "firstNameAr", target = "prenomArabe")
	@Mapping(source = "lastNameAr", target = "nomArabe")
	@Mapping(source = "sex", target = "sexe")
	@Mapping(source = "phoneNumber", target = "telephone")
	@Mapping(source = "mainContact", target = "contactPrincipal")
	DonorContactAuditDTO donorContactDTOtoAudit(DonorContactDTO donorContactDTO);

}
