package ma.almobadara.backend.dto.beneficiary;

import lombok.*;
import java.time.Instant;
import java.util.Date;

@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class AgendaRapportResponseDto {

    private Long id;
    private String status;
    private Long donorId;
    private String donors;
    private Long beneficiaryId;
    private Date dateRapport;
    private Date dateValidate;
    private Date datePlanned;
    private Long rapportId;
    private String year;
    private String detailComplete;
    private Long numberRapport;
    protected Instant modifiedAt;

    private BeneficiaryResponseDTO beneficiary;
    private String fullNameAssistant;

}
