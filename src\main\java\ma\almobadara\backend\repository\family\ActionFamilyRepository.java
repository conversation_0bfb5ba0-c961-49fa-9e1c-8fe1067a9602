package ma.almobadara.backend.repository.family;

import ma.almobadara.backend.model.family.ActionFamily;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ActionFamilyRepository extends JpaRepository<ActionFamily, Long> {

    List<ActionFamily> findByFamilyId(Long familyId);

    List<ActionFamily> findByActionId(Long id);

}
