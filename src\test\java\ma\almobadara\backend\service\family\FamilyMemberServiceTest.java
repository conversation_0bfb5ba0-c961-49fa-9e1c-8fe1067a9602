package ma.almobadara.backend.service.family;

import ma.almobadara.backend.Audit.AuditApplicationService;
import ma.almobadara.backend.clients.RefFeignClient;
import ma.almobadara.backend.dto.family.AddedFamilyMemberResponse;
import ma.almobadara.backend.dto.family.FamilyMemberAddDTO;
import ma.almobadara.backend.dto.family.FamilyMemberDTO;
import ma.almobadara.backend.exceptions.FunctionalException;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.mapper.DocumentMapper;
import ma.almobadara.backend.mapper.FamilyMemberMapper;
import ma.almobadara.backend.mapper.SousZoneMapper;
import ma.almobadara.backend.mapper.TutorHistoryMapper;
import ma.almobadara.backend.model.administration.SousZone;
import ma.almobadara.backend.model.administration.Zone;
import ma.almobadara.backend.model.beneficiary.Person;
import ma.almobadara.backend.model.family.Family;
import ma.almobadara.backend.model.family.FamilyMember;
import ma.almobadara.backend.repository.administration.SousZoneRepository;
import ma.almobadara.backend.repository.administration.ZoneRepository;
import ma.almobadara.backend.repository.beneficiary.PersonRepository;
import ma.almobadara.backend.repository.communs.DocumentRepository;
import ma.almobadara.backend.repository.family.FamilyDocumentRepository;
import ma.almobadara.backend.repository.family.FamilyMemberRepository;
import ma.almobadara.backend.repository.family.FamilyRepository;
import ma.almobadara.backend.repository.family.TutorHistoryRepository;
import ma.almobadara.backend.service.ReferentialService;
import ma.almobadara.backend.service.communs.DocumentService;
import ma.almobadara.backend.service.donor.MinioService;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class FamilyMemberServiceTest {
    //General mocking
    @Mock
    private FamilyMemberRepository familyMemberRepository;
    @Mock
    private FamilyService familyService;
    @Mock
    private PersonRepository personRepository;
    @Mock
    private FamilyMemberMapper familyMemberMapper;
    @Mock
    private FamilyRepository familyRepository;
    @Mock
    private AuditApplicationService auditApplicationService;


    //Inject Mocks
    @InjectMocks
    private FamilyMemberService familyMemberService;

    //Test methods
    //todo: addFamilyMember
    @Test
    void testAddFamilyMember_NewFamilyMember() throws TechnicalException, FunctionalException, IOException {
        FamilyMemberAddDTO familyMemberDTO = FamilyMemberAddDTO
                .builder()
                .generalComment("comment")
                .build();
        // Set up familyMemberAddDTO properties
        Family family = Family
                .builder()
                .build();

        family.setId( 1L);

        when(familyService.createNewFamily(null,null,"comment",null,null,null,null,null,null,null)).thenReturn(family);
        when(familyRepository.findById(anyLong())).thenReturn(Optional.of(family));

        AddedFamilyMemberResponse response = familyMemberService.addFamilyMember(familyMemberDTO);

        assertNotNull(response);
        assertEquals(1L, response.getFamilyId());
        // Add more assertions as needed
    }

    @Test
    void testAddFamilyMember_UpdateExistingFamilyMember() throws TechnicalException, FunctionalException, IOException {
        FamilyMemberAddDTO familyMemberAddDTO = new FamilyMemberAddDTO();
        familyMemberAddDTO.setFamilyId(1L);
        // Set up familyMemberAddDTO properties
        Family family = new Family();
        family.setId(1L);


        when(familyRepository.save(any(Family.class))).thenReturn(family);
        when(familyService.findFamilyById(familyMemberAddDTO.getFamilyId())).thenReturn(family);
        when(familyRepository.findById(anyLong())).thenReturn(Optional.of(family));

        AddedFamilyMemberResponse response = familyMemberService.addFamilyMember(familyMemberAddDTO);

        assertNotNull(response);
        // Add more assertions as needed
    }

    //todo: addListOfFamilyMembers

    @Test
    void addListOfFamilyMembers() throws TechnicalException {

        FamilyMemberAddDTO familyMemberDTO = new FamilyMemberAddDTO();
        familyMemberDTO.setFamilyId(1L);
        FamilyMember familyMember = new FamilyMember();

        Person person = new Person();

        Family family2 = new Family();
        family2.setFamilyMembers(new ArrayList<>(List.of(familyMember)));
        family2.setId(1L);

        when(familyService.findFamilyById(familyMemberDTO.getFamilyId())).thenReturn(family2);
        when(familyMemberRepository.save(any(FamilyMember.class))).thenReturn(new FamilyMember());
        when(familyMemberMapper.mapFamilyMemberAddDTOToPerson(any(FamilyMemberAddDTO.class))).thenReturn(person);
        when(familyMemberMapper.mapFamilyMemberAddDTOToFamilyMember(any(FamilyMemberAddDTO.class))).thenReturn(familyMember);

        Long response = familyMemberService.addListOfFamilyMembers(Collections.singletonList(familyMemberDTO));

        assertNotNull(response);
        // Add more assertions as needed
    }
}