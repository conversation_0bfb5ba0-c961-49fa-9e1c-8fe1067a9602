CREATE TABLE tag
(
    id     BIGSERIAL PRIMARY KEY,
    name   <PERSON><PERSON><PERSON><PERSON>(255),
    entity_type VA<PERSON><PERSON><PERSON>(255),
    color  VARCHAR(255)
);

CREATE TABLE tag_document
(
    document_id  BIGINT,
    tag_id       BIGINT,
    CONSTRAINT fk_document FOREIGN KEY (document_id) REFERENCES document(id) ON DELETE SET NULL,
    CONSTRAINT fk_tag FOREIGN KEY (tag_id) REFERENCES tag(id) ON DELETE CASCADE
);

