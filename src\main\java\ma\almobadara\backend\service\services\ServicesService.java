package ma.almobadara.backend.service.services;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityNotFoundException;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.Audit.AuditApplicationService;
import ma.almobadara.backend.clients.RefFeignClient;
import ma.almobadara.backend.dto.administration.ConsultServiceCollectEpsDTO;
import ma.almobadara.backend.dto.donation.BudgetLineDTO;
import ma.almobadara.backend.dto.referentiel.CategoryDTO;
import ma.almobadara.backend.dto.referentiel.ServiceDTO;
import ma.almobadara.backend.dto.referentiel.TypePriseEnChargeDTO;
import ma.almobadara.backend.dto.service.AddServicesDTO;
import ma.almobadara.backend.dto.service.ServicesDTO;
import ma.almobadara.backend.enumeration.ServicesCategorie;
import ma.almobadara.backend.exceptions.FunctionalException;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.mapper.BudgetLineMapper;
import ma.almobadara.backend.mapper.ServicesMapper;
import ma.almobadara.backend.model.administration.Eps;
import ma.almobadara.backend.model.donation.BudgetLine;
import ma.almobadara.backend.model.service.Services;
import ma.almobadara.backend.repository.administration.EpsRepository;
import ma.almobadara.backend.repository.donation.BudgetLineRepository;
import ma.almobadara.backend.repository.services.ServicesRepository;
import org.springframework.data.domain.*;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

import static ma.almobadara.backend.service.administration.CacheAdUserService.getUsernameFromJwt;
import static ma.almobadara.backend.util.constants.GlobalConstants.*;

@RequiredArgsConstructor
@Slf4j
@Service
public class ServicesService {

    private final ServicesRepository servicesRepository;
    private final BudgetLineRepository budgetLineRepository;
    private final BudgetLineMapper budgetLineMapper;
    private final ServicesMapper servicesMapper;
    private final AuditApplicationService auditApplicationService;
    private final EntityManager entityManager;
    private final RefFeignClient refFeignClient;
    private final EpsRepository epsRepository;


    @Transactional
    public ServicesDTO addService(AddServicesDTO serviceDTO) throws FunctionalException {
        Services service = servicesMapper.AddDtotoEntity(serviceDTO);
        Optional<Services> existingService;

        if (serviceDTO.getId() == null && serviceDTO.getServiceCategoryId() != null && serviceDTO.getServiceCategoryTypeId() != null) {
            if (Boolean.TRUE.equals(serviceDTO.getIsDedicatedToEps())) {
                Eps eps = epsRepository.findById(serviceDTO.getEpsId())
                        .orElseThrow(() -> new EntityNotFoundException("EPS not found with id: " + serviceDTO.getEpsId()));
                existingService = servicesRepository.findByServiceCategoryIdAndServiceCategoryTypeIdAndIsDedicatedToEpsAndEps(
                        serviceDTO.getServiceCategoryId(), serviceDTO.getServiceCategoryTypeId(),
                        true, eps
                );
                if (existingService.isPresent()) {
                    throw new FunctionalException("Ce service existe déjà avec pour cet EPS.");
                }
            } else {
                // Vérifier si un service général existe déjà
                existingService = servicesRepository.findByServiceCategoryIdAndServiceCategoryTypeId(
                        serviceDTO.getServiceCategoryId(), serviceDTO.getServiceCategoryTypeId()
                );
                if (existingService.isPresent()) {
                    if (existingService.get().getStatutIsActif()) {
                        throw new FunctionalException("Ce service existe déjà.");
                    } else {
                        throw new FunctionalException("Ce service existe déjà, veuillez l’activer.");
                    }
                }

            }

            // Génération du code et du nom
            service.setCode(generateServiceCode());
            service.setName(generateServiceName(serviceDTO));

        } else if (serviceDTO.getId() != null) {
            // Modification d'un service existant
            Services existingServiceToUpdate = servicesRepository.findById(serviceDTO.getId())
                    .orElseThrow(() -> new IllegalArgumentException("Service non trouvé pour l'ID: " + serviceDTO.getId()));

            service.setCode(existingServiceToUpdate.getCode());
            service.setName(existingServiceToUpdate.getName());
        }

        // Sauvegarde du service
        if (serviceDTO.getEpsId() != null) {
            Eps eps = epsRepository.findById(serviceDTO.getEpsId())
                    .orElseThrow(() -> new EntityNotFoundException("EPS not found with id: " + serviceDTO.getEpsId()));
            service.setEps(eps);
        } else {
            service.setEps(null);
        }
        service = servicesRepository.save(service);
        return servicesMapper.toDto(service);
    }


    public List<ServicesDTO> getServiceEps(){
        List<Services> services=servicesRepository.findByStatutIsActifTrue();
        List<Services> servicesList=services.stream().filter(services1 -> services1.getEps()!=null).toList();
        return servicesList.stream().map(servicesMapper::toDto).toList();
    }

    private String generateServiceCode() {
        int serviceCodeCounter = serviceCodeCounter() + 1;
        StringBuilder codeBuilder = new StringBuilder();
        codeBuilder.append("S");
        int currentYear = LocalDate.now().getYear();
        codeBuilder.append(currentYear);

        codeBuilder.append(String.format("%05d", serviceCodeCounter));

        return codeBuilder.toString();
    }

    private String generateServiceName(AddServicesDTO serviceDTO) {
        StringBuilder nameBuilder = new StringBuilder();

        if (Boolean.TRUE.equals(serviceDTO.getIsDedicatedToEps())) {
            if (serviceDTO.getEpsId() != null) {
                Eps eps = epsRepository.findById(serviceDTO.getEpsId())
                        .orElseThrow(() -> new EntityNotFoundException("EPS not found with id: " + serviceDTO.getEpsId()));

                nameBuilder.append(eps.getName()).append(" ");
            }
        }

        if (Objects.equals(serviceDTO.getServiceCategoryId(), ServicesCategorie.Kafalat.getId())) {
            nameBuilder.append("Kafalat ");
        }

        if (serviceDTO.getServiceCategoryTypeId() != null) {
            ServiceDTO serviceType = refFeignClient.getMetService(serviceDTO.getServiceCategoryTypeId());
            nameBuilder.append(Character.toUpperCase(serviceType.getName().charAt(0)));
            nameBuilder.append(serviceType.getName().substring(1).toLowerCase());
        }


        return nameBuilder.toString();
    }


    private Integer serviceCodeCounter() {
        List<Services> servicesList = servicesRepository.findAll();
        return servicesList.size();
    }

    public Page<ServicesDTO> getAllServices(Integer page, Integer size, String searchByName, Long searchByCategoryId, Boolean searchByPriority, Boolean searchByStatut, BigDecimal searchByCosts, Date minDate, Date maxDate) throws TechnicalException {
        Sort sort = Sort.by(Sort.Direction.DESC, "modifiedAt");
        Pageable pageable = PageRequest.of(page, size, sort);

        Map<String, String> searchParams = new HashMap<>();
        if (searchByName != null) {
            searchParams.put("Nom du service", searchByName);
        }
        if (searchByCategoryId != null) {
            searchParams.put("Catégorie du service", String.valueOf(searchByCategoryId));
        }

        String jsonSearchParams = convertMapToJsonString(searchParams);

        Page<Services> services;
        if (searchByName != null || searchByCategoryId != null || searchByPriority != null || searchByStatut != null || searchByCosts != null || minDate != null || maxDate != null) {
            services = filterServices(searchByName, searchByCategoryId, searchByPriority, searchByStatut, searchByCosts, minDate, maxDate, pageable);
            auditApplicationService.audit("Recherche par filtre dans la liste des Services", getUsernameFromJwt(), "Liste des Services",
                    jsonSearchParams, null, SERVICE, VIEW);
        } else {
            services = servicesRepository.findAll(pageable);
            auditApplicationService.audit("Consultation de la liste globale des Services", getUsernameFromJwt(), "Liste des Services",
                    null, null, SERVICE, CONSULTATION);
        }

        List<ServicesDTO> serviceDTOList = services.getContent().stream()
                .map(service -> {
                    ServicesDTO servicesDTO = servicesMapper.toDto(service);

                    CategoryDTO categoryDTO = refFeignClient.getMetCategory(service.getServiceCategoryId());
                    servicesDTO.setCategory(categoryDTO.getName());

                    if (Objects.equals(service.getServiceCategoryId(), ServicesCategorie.Kafalat.getId())) {
                        ServiceDTO serviceDTO = refFeignClient.getMetService(service.getServiceCategoryTypeId());
                        servicesDTO.setTypeCategory(serviceDTO.getName());
                    } else if (Objects.equals(service.getServiceCategoryId(), ServicesCategorie.AideComplementaire.getId())) {
                        TypePriseEnChargeDTO typePriseEnChargeDTO = refFeignClient.getParTypePriseEnCharge(service.getServiceCategoryTypeId());
                        servicesDTO.setTypeCategory(typePriseEnChargeDTO.getName());
                    }

                    return servicesDTO;
                })
                .collect(Collectors.toList());


        return new PageImpl<>(serviceDTOList, pageable, services.getTotalElements());
    }

    public Page<Services> filterServices(String searchByName, Long searchByCategoryId, Boolean searchByPriority, Boolean searchByStatut, BigDecimal searchByCosts, Date minDate, Date maxDate, Pageable pageable) {
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Services> criteriaQuery = criteriaBuilder.createQuery(Services.class);
        Root<Services> root = criteriaQuery.from(Services.class);

        Predicate predicate = buildPredicate(criteriaBuilder, root, searchByName, searchByCategoryId, searchByPriority, searchByStatut, searchByCosts, minDate, maxDate);

        criteriaQuery.where(predicate);

        TypedQuery<Services> typedQuery = entityManager.createQuery(criteriaQuery);
        long totalCount = typedQuery.getResultList().size();

        typedQuery.setFirstResult((int) pageable.getOffset());
        typedQuery.setMaxResults(pageable.getPageSize());

        List<Services> resultList = typedQuery.getResultList();
        return new PageImpl<>(resultList, pageable, totalCount);
    }

    private Predicate buildPredicate(CriteriaBuilder criteriaBuilder, Root<Services> root, String searchByName, Long searchByCategoryId, Boolean searchByPriority, Boolean searchByStatut, BigDecimal searchByCosts, Date minDate, Date maxDate) {
        Predicate predicate = criteriaBuilder.conjunction();

        if (searchByName != null && !searchByName.isEmpty()) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(criteriaBuilder.lower(root.get("name")), "%" + searchByName.toLowerCase() + "%"));
        }

        if (searchByCategoryId != null) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("serviceCategoryId"), searchByCategoryId));
        }

        if (searchByPriority != null) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("priority"), searchByPriority));
        }

        if (searchByStatut != null) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("statutIsActif"), searchByStatut));
        }

        if (searchByCosts != null) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("costs"), searchByCosts));
        }

        if (minDate != null) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.greaterThanOrEqualTo(root.get("createdAt"), minDate));
        }

        if (maxDate != null) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.lessThanOrEqualTo(root.get("createdAt"), maxDate));
        }

        return predicate;
    }

    private String convertMapToJsonString(Map<String, String> map) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.writeValueAsString(map);
        } catch (Exception e) {
            e.printStackTrace();
            return "{}";
        }
    }

    public ServicesDTO getServiceById(Long id) throws EntityNotFoundException {
        Services service = servicesRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Service not found with id: " + id));

        ServicesDTO servicesDTO = servicesMapper.toDto(service);

        CategoryDTO categoryDTO = refFeignClient.getMetCategory(service.getServiceCategoryId());
        servicesDTO.setCategory(categoryDTO.getName());

        if (service.getServiceCategoryId() != null) {
            ServiceDTO serviceDTO = refFeignClient.getMetService(service.getServiceCategoryTypeId());
            servicesDTO.setTypeCategory(serviceDTO.getName());
        }

        return servicesDTO;
    }

    @Transactional
    public List<ServicesDTO> getAllActiveServices(Long serviceTypeId) throws TechnicalException {
        List<Services> activeServices = servicesRepository.findByStatutIsActifTrue();

        List<ServicesDTO> serviceDTOList = activeServices.stream()
                .filter(service -> Objects.equals(service.getServiceCategoryId(), serviceTypeId))
                .map(service -> {
                    ServicesDTO servicesDTO = servicesMapper.toDto(service);

                    CategoryDTO categoryDTO = refFeignClient.getMetCategory(service.getServiceCategoryId());
                    servicesDTO.setCategory(categoryDTO.getName());

                    if (Objects.equals(service.getServiceCategoryId(), ServicesCategorie.Kafalat.getId())) {
                        ServiceDTO serviceDTO = refFeignClient.getMetService(service.getServiceCategoryTypeId());
                        servicesDTO.setTypeCategory(serviceDTO.getName());
                    } else if (Objects.equals(service.getServiceCategoryId(), ServicesCategorie.AideComplementaire.getId())) {
                        TypePriseEnChargeDTO typePriseEnChargeDTO = refFeignClient.getParTypePriseEnCharge(service.getServiceCategoryTypeId());
                        servicesDTO.setTypeCategory(typePriseEnChargeDTO.getName());
                    }


                    return servicesDTO;
                })
                .collect(Collectors.toList());

        return serviceDTOList;
    }

    public List<ServicesDTO> getActiveKafalatServices() {
        List<Services> activeKafalatServices = servicesRepository.findByStatutIsActifTrueAndServiceCategoryId(ServicesCategorie.Kafalat.getId());
        return activeKafalatServices.stream()
                .map(service -> {
                    ServicesDTO servicesDTO = servicesMapper.toDto(service);
                    CategoryDTO categoryDTO = refFeignClient.getMetCategory(service.getServiceCategoryId());
                    servicesDTO.setCategory(categoryDTO.getName());
                    ServiceDTO serviceDTO = refFeignClient.getMetService(service.getServiceCategoryTypeId());
                    servicesDTO.setTypeCategory(serviceDTO.getName());

                    return servicesDTO;
                })
                .collect(Collectors.toList());
    }

    public List<ServicesDTO> getActiveAideComplementaireServices() {
        List<Services> activeAideComplementaireServices = servicesRepository.findByStatutIsActifTrueAndServiceCategoryId(ServicesCategorie.AideComplementaire.getId());
        return activeAideComplementaireServices.stream()
                .map(service -> {
                    ServicesDTO servicesDTO = servicesMapper.toDto(service);
                    CategoryDTO categoryDTO = refFeignClient.getMetCategory(service.getServiceCategoryId());
                    servicesDTO.setCategory(categoryDTO.getName());
                    TypePriseEnChargeDTO typePriseEnChargeDTO = refFeignClient.getParTypePriseEnCharge(service.getServiceCategoryTypeId());
                    servicesDTO.setTypeCategory(typePriseEnChargeDTO.getName());

                    return servicesDTO;
                })
                .collect(Collectors.toList());
    }


    // get the budget line by service id
    public List<BudgetLineDTO> getBudgetLinesByServiceId(Long serviceId) {
        List<BudgetLineDTO> budgetLineDTOList = new ArrayList<>();
        List<BudgetLine> budgetLines = budgetLineRepository.findByServiceId(serviceId);
        return budgetLineMapper.budgetLinesToBudgetLineDTOs(budgetLines);
    }


    @Transactional
    public List<ServicesDTO> getAllActiveServicesByCategoryId(Long categoryId) {
        if (categoryId == null || categoryId <= 0) {
            log.warn("Invalid category ID: {}", categoryId);
            return Collections.emptyList();
        }

        List<Services> activeServices = servicesRepository.findByStatutIsActifTrueAndServiceCategoryId(categoryId);

        if (activeServices.isEmpty()) {
            log.info("No active services found for category ID: {}", categoryId);
            return Collections.emptyList();
        }

        List<ServicesDTO> servicesDTOList = activeServices.stream()
                .map(service -> {
                    ServicesDTO servicesDTO = servicesMapper.toDto(service);
                    CategoryDTO categoryDTO = refFeignClient.getMetCategory(service.getServiceCategoryId());
                    servicesDTO.setCategory(categoryDTO.getName());

                    List<ConsultServiceCollectEpsDTO> consultServiceCollectEpsDTOS = service.getServiceCollectEpsList().stream()
                            .filter(serviceCollectEps ->
                                    (Boolean.FALSE.equals(serviceCollectEps.getIsDeleted()) || serviceCollectEps.getIsDeleted() == null) &&
                                            (Boolean.FALSE.equals(serviceCollectEps.getIsCloture()) || serviceCollectEps.getIsCloture() == null) &&
                                            !isFutureMonthAndYear(serviceCollectEps.getMois(), serviceCollectEps.getAnnee())
                            ).map(serviceCollectEps -> {
                                ConsultServiceCollectEpsDTO consultServiceCollectEpsDTO = new ConsultServiceCollectEpsDTO();
                                consultServiceCollectEpsDTO.setNom(serviceCollectEps.getNom());
                                consultServiceCollectEpsDTO.setId(serviceCollectEps.getId());
                                return consultServiceCollectEpsDTO;
                            }).toList();

                    servicesDTO.setConsultServiceCollectEpsDTOS(consultServiceCollectEpsDTOS);

                    ServiceDTO serviceDTO = refFeignClient.getMetService(service.getServiceCategoryTypeId());
                    servicesDTO.setTypeCategory(serviceDTO.getName());

                    return servicesDTO;
                })
                .collect(Collectors.toList());

        // Filter out services with empty ConsultServiceCollectEpsDTOS if categoryId == 6
        if (categoryId == 6L) {
            servicesDTOList = servicesDTOList.stream()
                    .filter(dto -> dto.getConsultServiceCollectEpsDTOS() != null && !dto.getConsultServiceCollectEpsDTOS().isEmpty())
                    .collect(Collectors.toList());
        }

        return servicesDTOList;
    }

    private boolean isFutureMonthAndYear(int mois, int annee) {
         LocalDate now = LocalDate.now();
        int currentYear = now.getYear();
        int currentMonth = now.getMonthValue();

         return annee < currentYear || (annee == currentYear && mois < currentMonth);
    }
}








