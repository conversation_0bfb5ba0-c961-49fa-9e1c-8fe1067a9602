package ma.almobadara.backend.model.family;

import com.google.gson.Gson;
import jakarta.persistence.*;
import lombok.*;
import ma.almobadara.backend.model.beneficiary.BaseEntity;
import ma.almobadara.backend.model.beneficiary.Person;

import java.text.SimpleDateFormat;
import java.util.*;

import static ma.almobadara.backend.util.strings.HandleSpecialChars.escapeSpecialChars;

@Entity
@Getter
@Setter
@EqualsAndHashCode(exclude = {"family"})
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class FamilyMember extends BaseEntity {

    private boolean tutor;
    private String generalComment;

    private Date tutorStartDate;

    private Date tutorEndDate;

    private Long familyRelationshipId;

    @Column(unique = true)
    private String code;

    @OneToOne(cascade = CascadeType.PERSIST)
    @JoinColumn(name = "person_id")
    private Person person;

    @ManyToOne
    @JoinColumn(name = "family_id")
    private Family family;

    @OneToMany(mappedBy = "familyMember", cascade = CascadeType.ALL)
    private List<ExternalIncome> externalIncomes;

    public String getAuditInfos(String address,String profession,String educationLevel){
        Gson gson = new Gson();
        SimpleDateFormat formatter = new SimpleDateFormat("dd-MM-yyyy");
        String date = person.getBirthDate() != null ? formatter.format(person.getBirthDate()):"";
        var date2 = "";
        if (tutorStartDate != null) {
            date2 = formatter.format(tutorStartDate);
        }
        String pictureName = "-";
        if (person.getPictureUrl() !=null){
            String[] elements = person.getPictureUrl().split("/");
            pictureName = elements[elements.length-1];
        }
        Map<String, String> data = new LinkedHashMap<>();
        data.put("Code", escapeSpecialChars(code));

        data.put("Prénom et nom", escapeSpecialChars(person.getFirstName())+" "+escapeSpecialChars(person.getLastName()) +
                (tutor ? " - Tuteur du famille depuis " + date2 : ""));

        data.put("Image fichier", escapeSpecialChars(pictureName));
        data.put("Relation de parenté", getRelationFromId(Math.toIntExact(familyRelationshipId)));
        data.put("Date de naissance", escapeSpecialChars(date));
        data.put("Adresse", escapeSpecialChars(!Objects.equals(address, " null") ?address:"-"));
        data.put("Profession", escapeSpecialChars(profession));
        data.put("Téléphone", escapeSpecialChars(person.getPhoneNumber()));
        data.put("Décédé", person.isDeceased() ? "Oui" : "Non");
        data.put("Scolarisé", person.isEducated() ? "Oui - Niveau : " + escapeSpecialChars(educationLevel) : "Non");
        data.put("Commentaire", escapeSpecialChars(generalComment));
        return gson.toJson(data);
    }

    private String getRelationFromId(int id){

        switch(id){
            case 1:
                return "Père";
            case 2:
                return "Mère";
            case 3:
                return "Fils";
            case 4:
                return "Fille";
            case 5:
                return "Oncle";
            case 6:
                return "Tante";
            case 7:
                return "Grand-père";
            case 8:
                return "Grand-mère";
            default:
                return "Non défini";
        }
    }

}
