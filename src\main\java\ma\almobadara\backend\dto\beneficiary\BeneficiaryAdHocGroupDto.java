package ma.almobadara.backend.dto.beneficiary;

import lombok.*;
import ma.almobadara.backend.dto.CityWithRegionAndCountryDTO;
import ma.almobadara.backend.dto.family.FamilyMemberDTO;
import ma.almobadara.backend.dto.referentiel.CategoryBeneficiaryDTO;
import ma.almobadara.backend.dto.referentiel.CityDTO;
import ma.almobadara.backend.dto.referentiel.ServiceDTO;
import ma.almobadara.backend.dto.referentiel.TypePriseEnChargeDTO;
import ma.almobadara.backend.model.beneficiary.Beneficiary;

import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@Builder
public class BeneficiaryAdHocGroupDto {

    private Long id;
    private String code;
    private String name;
    private Long cityId;
    private CityDTO city;
    private CityWithRegionAndCountryDTO info;
    private String fullNameContact;
    private String phoneNumber;
    private Long numberOfMembers;
    private String comment;
    private String status;
    private LocalDateTime createdAt;
    private List<Beneficiary> beneficiaries;
    private List<TypePriseEnChargeDTO> typePriseEnCharges;
    private List<Long> typePriseEnChargeIds;

}
