package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.administration.ResponsableEpsDto;
import ma.almobadara.backend.model.administration.ResponsableEps;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface ResponsableEpsMapper {

    ResponsableEpsMapper INSTANCE = Mappers.getMapper(ResponsableEpsMapper.class);


    @Mapping(target = "typeIdentity", ignore = true)
    @Mapping(target = "functionContact", ignore = true)
    @Mapping(target = "eps", ignore = true)
    ResponsableEpsDto responsableEpsToResponsableEpsDto(ResponsableEps responsableEps);

    @Mapping(target = "typeIdentityId", ignore = true)
    @Mapping(target = "functionContactId", ignore = true)
    @Mapping(target = "eps", ignore = true)
    ResponsableEps responsableEpsDtoToResponsableEps(ResponsableEpsDto responsableEpsDto);
}
