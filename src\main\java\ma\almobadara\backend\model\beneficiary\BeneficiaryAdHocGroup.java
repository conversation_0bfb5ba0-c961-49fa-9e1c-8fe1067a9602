package ma.almobadara.backend.model.beneficiary;

import jakarta.persistence.*;
import lombok.*;
import ma.almobadara.backend.model.aideComplemenatire.AideComplementaire;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BeneficiaryAdHocGroup {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @Column(unique = true, nullable = false)
    private String code;
    @Column(nullable = false)
    private String name;
    private Long cityId;
    private String fullNameContact;
    private Long numberOfMembers;
    private String phoneNumber;
    private String comment;
    private String status;
    private String typePriseEnChargeIdsList;
    @ManyToOne
    @JoinColumn(name = "beneficiary_statut_id")
    private BeneficiaryStatut beneficiaryStatut;
    @CreationTimestamp
    private LocalDateTime createdAt;
    @UpdateTimestamp
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime updatedAt;
    @Column(name = "is_deleted")
    private boolean isDeleted;

    @ManyToMany
    @JoinTable(
            name = "beneficiary_adhocgroup", // Table d'association
            joinColumns = @JoinColumn(name = "beneficiary_ad_hoc_group_id"), // Clé étrangère pour BeneficiaryAdHocGroup
            inverseJoinColumns = @JoinColumn(name = "beneficiary_id") // Clé étrangère pour Beneficiary
    )
    private List<Beneficiary> beneficiaries;

}
