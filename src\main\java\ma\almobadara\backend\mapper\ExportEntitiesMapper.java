
package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.exportentities.DonationExportDTO;
import ma.almobadara.backend.model.donation.Donation;
import ma.almobadara.backend.model.donor.DonorMoral;
import ma.almobadara.backend.model.donor.DonorPhysical;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;


@Mapper(componentModel = "spring")
public interface ExportEntitiesMapper {

	@Mapping(source = "receptionDate", target = "receptionDate")
	@Mapping(source = "type", target = "typeDonation")
	@Mapping(target = "identifiedDonor", expression = "java(mapIdentifiedDonor(donation.isIdentifiedDonor()))")
	DonationExportDTO donationToDonationExportDTO(Donation donation);

	default String mapIdentifiedDonor(boolean identifiedDonor) {
		return identifiedDonor ? "oui" : "non";
	}

	@Mapping(source = "type", target = "typeDonor")
	@Mapping( target = "donorName", expression = "java(donorPhysical.getLastName() + \" \" + donorPhysical.getFirstName())")
	DonationExportDTO donationPhysicalDonorToDonationExportDTO(DonorPhysical donorPhysical);

	@Mapping(source = "type", target = "typeDonor")
	@Mapping( target = "donorName", expression = "java(donorMoral.getCompany())")
	DonationExportDTO donationMoralDonorToDonationExportDTO(DonorMoral donorMoral);


}

