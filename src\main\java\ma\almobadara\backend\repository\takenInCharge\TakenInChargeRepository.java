package ma.almobadara.backend.repository.takenInCharge;

import ma.almobadara.backend.model.takenInCharge.TakenInCharge;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TakenInChargeRepository extends JpaRepository<TakenInCharge, Long> {

    TakenInCharge getTakenInChargeById(Long id);
    TakenInCharge findByTakenInChargeDonorsId(Long id);
    //existsByCode

    boolean existsByCode(String code);


    @Query("SELECT t FROM TakenInCharge t JOIN t.takenInChargeBeneficiaries b WHERE b.beneficiary.id = :beneficiaryId")
    List<TakenInCharge> findByBeneficiaryId(@Param("beneficiaryId") Long beneficiaryId);

    @Query("SELECT CASE WHEN COUNT(t) > 0 THEN true ELSE false END FROM TakenInCharge t " +
            "JOIN t.takenInChargeBeneficiaries tib " +
            "WHERE tib.beneficiary.id = :beneficiaryId " +
            "AND (t.status = 'Inactif' OR t.status = 'Actif') " +
            "AND t.service.id != :serviceId " +
            "AND (t.id != :takenInChargeId OR :takenInChargeId IS NULL)")
    boolean existsByBeneficiaryIdAndStatusNotClosedAndServiceIdNot(
            @Param("beneficiaryId") Long beneficiaryId,
            @Param("serviceId") Long serviceId,
            @Param("takenInChargeId") Long takenInChargeId);  // Exclude current record during update

    @Query("SELECT CASE WHEN COUNT(t) > 0 THEN true ELSE false END FROM TakenInCharge t " +
            "JOIN t.takenInChargeBeneficiaries tib " +
            "JOIN t.takenInChargeDonors ticd " +
            "WHERE tib.beneficiary.id = :beneficiaryId " +
            "AND (t.status = 'Inactif' OR t.status = 'Actif') " +
            "AND t.service.id = :serviceId " +
            "AND ticd.donor.id = :donorId " +
            "AND (t.id != :takenInChargeId OR :takenInChargeId IS NULL)")
    boolean existsByBeneficiaryIdAndStatusNotClosedAndServiceIdAndDonorId(
            @Param("beneficiaryId") Long beneficiaryId,
            @Param("serviceId") Long serviceId,
            @Param("donorId") Long donorId,
            @Param("takenInChargeId") Long takenInChargeId);  // Exclude current record during update

    @Query("SELECT t.status, COUNT(t) FROM TakenInCharge t GROUP BY t.status")
    List<Object[]> getKafalatsByStatus();
//
//    @Query("SELECT t.closed, COUNT(t) FROM TakenInCharge t GROUP BY t.closed")
//    List<Object[]> getKafalatsByClosure();

    @Query("SELECT s.name, COUNT(t) FROM TakenInCharge t JOIN t.service s GROUP BY s.name")
    List<Object[]> getKafalatsByService();

    @Query(value = """
    SELECT
        TO_CHAR(created_at, 'YYYY-MM') AS month,
        COUNT(*) AS count
    FROM taken_in_charge
    WHERE created_at >= CURRENT_DATE - INTERVAL '12 months'
    GROUP BY TO_CHAR(created_at, 'YYYY-MM')
    ORDER BY month ASC
    """, nativeQuery = true)    List<Object[]> getKafalatsByMonth();

//    @Query("SELECT t.operationStatus, COUNT(t) FROM TakenInCharge t GROUP BY t.operationStatus")
//    List<Object[]> getKafalatOperationsByStatus();
}
