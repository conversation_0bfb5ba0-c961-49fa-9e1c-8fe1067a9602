package ma.almobadara.backend.service;

import ma.almobadara.backend.Audit.AuditApplicationService;
import ma.almobadara.backend.clients.RefFeignClient;
import ma.almobadara.backend.config.Messages;
import ma.almobadara.backend.dto.donation.*;
import ma.almobadara.backend.dto.donor.DonorDTO;
import ma.almobadara.backend.dto.donor.DonorMoralDTO;
import ma.almobadara.backend.dto.exportentities.DonationExportDTO;
import ma.almobadara.backend.dto.referentiel.*;
import ma.almobadara.backend.dto.service.ServicesDTO;
import ma.almobadara.backend.enumeration.BudgetLineStatus;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.mapper.*;
import ma.almobadara.backend.model.donation.BudgetLine;
import ma.almobadara.backend.model.donation.Donation;
import ma.almobadara.backend.model.donation.DonationHistory;
import ma.almobadara.backend.model.donor.DonorMoral;
import ma.almobadara.backend.model.donor.DonorPhysical;
import ma.almobadara.backend.model.service.Services;
import ma.almobadara.backend.repository.donation.BudgetLineRepository;
import ma.almobadara.backend.repository.donation.DonationHistoryRepository;
import ma.almobadara.backend.repository.donation.DonationRepository;
import ma.almobadara.backend.repository.services.ServicesRepository;
import ma.almobadara.backend.service.Donation.DonationService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;


import java.io.IOException;
import java.util.Date;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static ma.almobadara.backend.util.constants.GlobalConstants.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import static org.mockito.Mockito.*;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;


@ExtendWith(MockitoExtension.class)

class DonationServiceTest {

    @Mock
    private DonationRepository donationRepository; // Required to mock findById()

    @Mock
    private DonationMapper donationMapper;

    @Mock
    private DonorMoralMapper donorMoralMapper;

    @Mock
    private DonationHistoryRepository donationHistoryRepository; // Required to mock findByDonationId()

    @Mock
    private DonationHistoryMapper donationHistoryMapper; // Required to mock mapping from entity to DTO

    @Mock
    private BudgetLineRepository budgetLineRepository;

    @Mock
    private ExportEntitiesMapper exportEntitiesMapper;

    @Mock
    private RefFeignClient refFeignClient;

    @Mock
    private BudgetLineMapper budgetLineMapper;

    @Mock
    private AuditApplicationService auditApplicationService;

    @Mock
    private ServicesRepository servicesRepository;

    @Mock
    private Messages messages; // Required to mock exception message retrieval

    @InjectMocks
    private DonationService donationService; // The service being tested

    private DonationDTO donationDTO;
    private CurrencyDTO currencyDTO;

    @BeforeEach
    void setUp() {
    }

    //Add & Update
    @Test
    void addDonation_shouldCreateNewDonationSuccessfully() throws TechnicalException, IOException {

        // Mock the SecurityContext and Authentication objects
        SecurityContext securityContext = Mockito.mock(SecurityContext.class);
        Authentication authentication = Mockito.mock(Authentication.class);

        // Mock the getPrincipal() method to return a username (this simulates the authenticated user)
        Mockito.when(authentication.getPrincipal()).thenReturn("testUser");

        // Set the mocked authentication in the SecurityContext
        Mockito.when(securityContext.getAuthentication()).thenReturn(authentication);
        SecurityContextHolder.setContext(securityContext);
        // Arrange
        DonationDTO donationDTO = new DonationDTO();
        donationDTO.setId(null); // New donation
        donationDTO.setType("Cash");
        donationDTO.setValue(500.0);
        donationDTO.setCreatedAt(LocalDateTime.now());

        // Ensure all fields checked for null are initialized
        CanalDonationDTO canalDonationDTO = new CanalDonationDTO();
        canalDonationDTO.setId(1L);
        canalDonationDTO.setName("Online");
        donationDTO.setCanalDonation(canalDonationDTO);

        CurrencyDTO currencyDTO = new CurrencyDTO();
        currencyDTO.setId(2L);
        currencyDTO.setName("USD");
        donationDTO.setCurrency(currencyDTO);

        DonorDTO donorDTO = new DonorDTO();
        donorDTO.setCode("D123");
        donationDTO.setDonor(donorDTO);

        Donation donation = new Donation();
        donation.setId(1L);
        donation.setCode("D20240210");

        when(donationMapper.donationDTOToDonation(any(DonationDTO.class))).thenReturn(donation);
        when(donationRepository.save(any(Donation.class))).thenReturn(donation);
        when(donationMapper.donationToDonationDTO(any(Donation.class))).thenReturn(donationDTO);
        when(refFeignClient.getMetCanalDonation(1L)).thenReturn(canalDonationDTO);
        when(refFeignClient.getParCurrency(2L)).thenReturn(currencyDTO);

        // Mock audit mapping
        DonationAuditDto donationAuditDto = new DonationAuditDto();
        when(donationMapper.donationDtoToDonationAuditDto(any(DonationDTO.class))).thenReturn(donationAuditDto);

        // Act
        DonationDTO result = donationService.addDonation(donationDTO);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getCanalDonation());
        assertEquals("Online", result.getCanalDonation().getName());
        assertNotNull(result.getCurrency());
        assertEquals("USD", result.getCurrency().getName());
        assertNotNull(result.getDonor());
        assertEquals("D123", result.getDonor().getCode());

        // Verify method calls
        verify(donationRepository, times(1)).save(any(Donation.class));
        verify(refFeignClient, times(2)).getMetCanalDonation(1L);
        verify(refFeignClient, times(1)).getParCurrency(2L);
    }

    //Kafalat or Non Identified
    @Test
    public void testProcessKafalatOrNonIdentified_whenValueIsPositive_shouldAddBudgetLine() {
        // Arrange
        DonationDTO donationDTO = new DonationDTO();
        Double value = 100.0;
        String type = "Kafalat";
        Long budgetLineId = null;
        String comment = "Test comment";
        Double valueCurrency = 100.0;
        String status = "Active";

        // Mock the donationDTO to ensure it doesn't have any budget lines initially
        donationDTO.setBudgetLines(null);

        // Act
        donationService.processKafalatOrNonIdentified(donationDTO, type, value, budgetLineId, currencyDTO, comment, valueCurrency, status);

        // Assert
        assertEquals(1, donationDTO.getBudgetLines().size());
        BudgetLineDTO addedBudgetLine = donationDTO.getBudgetLines().get(0);
        assertEquals(value, addedBudgetLine.getAmount());
        assertEquals(type, addedBudgetLine.getType());
        assertEquals(currencyDTO, addedBudgetLine.getCurrency());
        assertEquals(valueCurrency, addedBudgetLine.getValueCurrency());
        assertEquals(comment, addedBudgetLine.getComment());
        assertEquals(status, addedBudgetLine.getStatus());
    }

    @Test
    public void testProcessKafalatOrNonIdentified_whenUpdatingBudgetLine_shouldUpdateExistingBudgetLine() {
        // Arrange
        DonationDTO donationDTO = new DonationDTO();
        Double value = 100.0;
        String type = "Kafalat";
        Long budgetLineId = 1L;
        String comment = "Updated comment";
        Double valueCurrency = 100.0;
        String status = "Updated";

        BudgetLine existingBudgetLine = new BudgetLine();
        existingBudgetLine.setType(type);
        existingBudgetLine.setAmount(50.0);  // Initial value before update

        BudgetLineDTO expectedBudgetLineDTO = new BudgetLineDTO();
        expectedBudgetLineDTO.setAmount(value);
        expectedBudgetLineDTO.setType(type);
        expectedBudgetLineDTO.setCurrency(currencyDTO);
        expectedBudgetLineDTO.setValueCurrency(valueCurrency);
        expectedBudgetLineDTO.setComment(comment);
        expectedBudgetLineDTO.setStatus(status);

        // Mock repository and mapper
        when(budgetLineRepository.findById(budgetLineId)).thenReturn(Optional.of(existingBudgetLine));
        when(budgetLineMapper.budgetLineToBudgetLineDTO(existingBudgetLine)).thenReturn(expectedBudgetLineDTO);

        // Act
        donationService.processKafalatOrNonIdentified(donationDTO, type, value, budgetLineId, currencyDTO, comment, valueCurrency, status);

        // Assert
        assertEquals(1, donationDTO.getBudgetLines().size());
        BudgetLineDTO updatedBudgetLineDTO = donationDTO.getBudgetLines().get(0);
        assertEquals(value, updatedBudgetLineDTO.getAmount());
        assertEquals(type, updatedBudgetLineDTO.getType());
        assertEquals(currencyDTO, updatedBudgetLineDTO.getCurrency());
        assertEquals(valueCurrency, updatedBudgetLineDTO.getValueCurrency());
        assertEquals(comment, updatedBudgetLineDTO.getComment());
        assertEquals(status, updatedBudgetLineDTO.getStatus());
    }

    //Delete Donation
    @Test
    void testDeleteDonation_whenDonationHasExecutedBudgetLine_shouldThrowTechnicalException() {
        // Arrange
        Long idDonation = 1L;
        Donation donation = mock(Donation.class);
        BudgetLine budgetLine = mock(BudgetLine.class);
        when(budgetLine.getStatus()).thenReturn(BudgetLineStatus.EXECUTED);
        when(donation.getBudgetLines()).thenReturn(List.of(budgetLine));
        when(donationRepository.findById(idDonation)).thenReturn(Optional.of(donation));

        // Act & Assert
        TechnicalException exception = assertThrows(TechnicalException.class, () -> {
            donationService.deleteDonation(idDonation);
        });
        assertEquals("Cannot delete donation with executed budget lines.", exception.getMessage());
    }

    @Test
    void testDeleteDonation_whenDonationHasNoExecutedBudgetLine_shouldDeleteBudgetLinesAndArchiveDonation() throws TechnicalException {
        // Mock the SecurityContext and Authentication objects
        SecurityContext securityContext = Mockito.mock(SecurityContext.class);
        Authentication authentication = Mockito.mock(Authentication.class);

        // Mock the getPrincipal() method to return a username (this simulates the authenticated user)
        Mockito.when(authentication.getPrincipal()).thenReturn("testUser");

        // Set the mocked authentication in the SecurityContext
        Mockito.when(securityContext.getAuthentication()).thenReturn(authentication);
        SecurityContextHolder.setContext(securityContext);
        // Arrange
        Long idDonation = 1L;
        Donation donation = mock(Donation.class);
        BudgetLine budgetLine = mock(BudgetLine.class);
        DonationDTO donationDTO = mock(DonationDTO.class);
        DonationAuditDto donationAuditOld = mock(DonationAuditDto.class);
        CanalDonationDTO canalDonationDTO = mock(CanalDonationDTO.class);
        CurrencyDTO currencyDTO = mock(CurrencyDTO.class);
        DonorDTO donorDTO = mock(DonorDTO.class);

        // Mock Budget Line
        when(budgetLine.getStatus()).thenReturn(BudgetLineStatus.DISPONIBLE); // Not EXECUTED
        when(donation.getBudgetLines()).thenReturn(List.of(budgetLine));

        // Mock Donation Repository
        when(donationRepository.findById(idDonation)).thenReturn(Optional.of(donation));

        // Mock Donation DTO Mapping
        when(donationMapper.donationToDonationDTO(donation)).thenReturn(donationDTO);
        when(donationMapper.donationDtoToDonationAuditDto(donationDTO)).thenReturn(donationAuditOld);

        // Mock Canal Donation
        when(donationDTO.getCanalDonation()).thenReturn(canalDonationDTO);
        when(canalDonationDTO.getId()).thenReturn(1L);

        CanalDonationDTO mockCanalDonationDTO = new CanalDonationDTO();
        mockCanalDonationDTO.setName("Sample Canal");
        when(refFeignClient.getMetCanalDonation(1L)).thenReturn(mockCanalDonationDTO);

        // Mock Currency
        when(donationDTO.getCurrency()).thenReturn(currencyDTO);
        when(currencyDTO.getId()).thenReturn(2L);

        CurrencyDTO mockCurrencyDTO = new CurrencyDTO();
        mockCurrencyDTO.setName("USD");
        when(refFeignClient.getParCurrency(2L)).thenReturn(mockCurrencyDTO);

        // Mock Donor
        when(donationDTO.getDonor()).thenReturn(donorDTO);
        when(donorDTO.getCode()).thenReturn("D123");

        // Act
        donationService.deleteDonation(idDonation);

        // Assert
        verify(budgetLineRepository, times(1)).delete(budgetLine); // Budget line should be deleted
        verify(donationRepository, times(1)).save(donation); // Donation should be archived (soft delete)
        verify(donationAuditOld, times(1)).setCanalDonation("Sample Canal");
        verify(donationAuditOld, times(1)).setDevise("USD");
        verify(donationAuditOld, times(1)).setCodeDonateur("D123");
    }

    //Get by id
    @Test
    void testGetDonationById_whenDonationExists_shouldReturnDonationDTO() throws TechnicalException {
        // Mock the SecurityContext and Authentication objects
        SecurityContext securityContext = Mockito.mock(SecurityContext.class);
        Authentication authentication = Mockito.mock(Authentication.class);

        // Mock the getPrincipal() method to return a username (this simulates the authenticated user)
        Mockito.when(authentication.getPrincipal()).thenReturn("testUser");

        // Set the mocked authentication in the SecurityContext
        Mockito.when(securityContext.getAuthentication()).thenReturn(authentication);
        SecurityContextHolder.setContext(securityContext);
        // Arrange
        Long idDonation = 1L;
        Donation donation = mock(Donation.class);
        DonationDTO donationDTO = mock(DonationDTO.class);
        DonationAuditDto donationAuditOld = mock(DonationAuditDto.class);
        CanalDonationDTO canalDonationDTO = mock(CanalDonationDTO.class);
        CurrencyDTO currencyDTO = mock(CurrencyDTO.class);
        DonorMoral donorMoral = mock(DonorMoral.class);
        DonorMoralDTO donorMoralDTO = mock(DonorMoralDTO.class);

        // Mock donation repository
        when(donationRepository.findById(idDonation)).thenReturn(Optional.of(donation));

        // Mock mapping donation -> DTO
        when(donationMapper.donationToDonationDTO(donation)).thenReturn(donationDTO);
        when(donationMapper.donationDtoToDonationAuditDto(donationDTO)).thenReturn(donationAuditOld);

        // Simuler un donateur de type "DonorMoral"
        when(donation.getDonor()).thenReturn(donorMoral);
        when(donorMoralMapper.donorMoralModelToOneDTO(donorMoral)).thenReturn(donorMoralDTO);

        // Simuler récupération des détails du canal de donation
        when(donationDTO.getCanalDonation()).thenReturn(canalDonationDTO);
        when(canalDonationDTO.getId()).thenReturn(1L);

        CanalDonationDTO mockCanalDonationDTO = new CanalDonationDTO();
        mockCanalDonationDTO.setName("Canal XYZ");
        when(refFeignClient.getMetCanalDonation(1L)).thenReturn(mockCanalDonationDTO);

        // Simuler récupération des détails de la devise
        when(donationDTO.getCurrency()).thenReturn(currencyDTO);
        when(currencyDTO.getId()).thenReturn(2L);

        CurrencyDTO mockCurrencyDTO = new CurrencyDTO();
        mockCurrencyDTO.setName("EUR");
        when(refFeignClient.getParCurrency(2L)).thenReturn(mockCurrencyDTO);

        // Simuler un code donateur
        when(donationDTO.getDonor()).thenReturn(donorMoralDTO);
        when(donorMoralDTO.getCode()).thenReturn("D456");

        // Act
        DonationDTO result = donationService.getDonationById(idDonation);

        // Assert
        assertNotNull(result);
        verify(donationMapper, times(1)).donationToDonationDTO(donation);
        verify(donationMapper, times(1)).donationDtoToDonationAuditDto(donationDTO);
        verify(refFeignClient, times(2)).getMetCanalDonation(1L);
        verify(refFeignClient, times(2)).getParCurrency(2L);
    }

    @Test
    void testGetDonationById_whenDonationNotFound_shouldThrowTechnicalException() {
        // Arrange
        Long idDonation = 1L;
        when(donationRepository.findById(idDonation)).thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(TechnicalException.class, () -> donationService.getDonationById(idDonation));
    }

    //Get Donation history
    @Test
    void getDonationHistory_ThrowsTechnicalException_WhenDonationIdIsNull() {
        when(messages.get(DONATION_NOT_FOUND)).thenReturn("Donation not found");
        TechnicalException exception = assertThrows(TechnicalException.class, () -> donationService.getDonationHistory(null));
        assertEquals("Donation not found", exception.getMessage());
    }

    @Test
    void getDonationHistory_ThrowsTechnicalException_WhenDonationNotFound() {
        Long invalidDonationId = 100L;
        when(donationRepository.findById(invalidDonationId)).thenReturn(Optional.empty());
        when(messages.get(DONATION_NOT_FOUND)).thenReturn("Donation not found");
        TechnicalException exception = assertThrows(TechnicalException.class, () -> donationService.getDonationHistory(invalidDonationId));
        assertEquals("Donation not found", exception.getMessage());
    }

    @Test
    void getDonationHistory_ReturnsDonationHistory_WhenDonationExists() throws TechnicalException {
        Long validDonationId = 1L;
        Donation donation = new Donation(); // Fake donation
        List<DonationHistory> historyList = List.of(new DonationHistory(), new DonationHistory());
        when(donationRepository.findById(validDonationId)).thenReturn(Optional.of(donation));
        when(donationHistoryRepository.findByDonationId(validDonationId)).thenReturn(historyList);
        when(donationHistoryMapper.donationHistoryToDonationHistoryDTO(any(DonationHistory.class)))
                .thenReturn(new DonationHistoryDTO());
        List<DonationHistoryDTO> result = donationService.getDonationHistory(validDonationId);
        assertNotNull(result);
        System.out.println(result.get(0).getAmount());
        assertEquals(2, result.size());
    }

    //get all donation for export
    @Test
    void getAllDonationsToExport_validList_shouldReturnMappedList() {
        // Arrange
        Donation donation1 = new Donation();
        donation1.setReceptionDate(java.util.Date.from(LocalDate.of(2024, 2, 10).atStartOfDay(ZoneId.systemDefault()).toInstant()));

        Donation donation2 = new Donation(); // Another valid donation
        donation2.setReceptionDate(java.util.Date.from(LocalDate.of(2024, 1, 5).atStartOfDay(ZoneId.systemDefault()).toInstant()));

        List<Donation> donations = Arrays.asList(donation1, donation2);

        // Mock Mapper Behavior
        when(exportEntitiesMapper.donationToDonationExportDTO(donation1)).thenReturn(new DonationExportDTO());
        when(exportEntitiesMapper.donationToDonationExportDTO(donation2)).thenReturn(new DonationExportDTO());

        // Act
        List<DonationExportDTO> result = donationService.getAllDonationsToExport(donations);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result).hasSize(2);
        // Verify interactions
        verify(exportEntitiesMapper, times(2)).donationToDonationExportDTO(any(Donation.class));
    }

    @Test
    void getAllDonationsToExport_donationWithNullReceptionDate_shouldHandleGracefully() {
        // Arrange
        Donation donation = new Donation();
        donation.setReceptionDate(null); // No reception date

        List<Donation> donations = List.of(donation);

        when(exportEntitiesMapper.donationToDonationExportDTO(any())).thenReturn(new DonationExportDTO());

        // Act
        List<DonationExportDTO> result = donationService.getAllDonationsToExport(donations);

        // Assert
        assertThat(result).hasSize(1);
        assertNull(result.get(0).getReceptionDate()); // Should handle null receptionDate correctly

        verify(exportEntitiesMapper, times(1)).donationToDonationExportDTO(any());
    }

    @Test
    void getAllDonationsToExport_mapperThrowsException_shouldPropagate() {
        // Arrange
        Donation donation = new Donation();
        List<Donation> donations = List.of(donation);

        when(exportEntitiesMapper.donationToDonationExportDTO(any())).thenThrow(new RuntimeException("Mapping failed"));

        // Act & Assert
        assertThrows(RuntimeException.class, () -> donationService.getAllDonationsToExport(donations));

        verify(exportEntitiesMapper, times(1)).donationToDonationExportDTO(any());
    }

    //Set donor details
    @Test
    void setDonorDetails_physicalDonor_shouldSetCorrectDetails() {
        // Arrange
        Donation donation = new Donation();
        DonorPhysical donorPhysical = new DonorPhysical();
        donation.setDonor(donorPhysical);

        DonationExportDTO mappedDTO = new DonationExportDTO();
        mappedDTO.setDonorName("John Doe");

        when(exportEntitiesMapper.donationPhysicalDonorToDonationExportDTO(donorPhysical)).thenReturn(mappedDTO);

        DonationExportDTO donationExportDTO = new DonationExportDTO();

        // Act
        donationService.setDonorDetails(donation, donationExportDTO);

        // Assert
        assertEquals("John Doe",donationExportDTO.getDonorName());
        assertEquals(TYPE_DONOR_PHYSIQUE,donationExportDTO.getTypeDonor());

        verify(exportEntitiesMapper, times(1)).donationPhysicalDonorToDonationExportDTO(donorPhysical);
    }

    @Test
    void setDonorDetails_moralDonor_shouldSetCorrectDetails() {
        // Arrange
        Donation donation = new Donation();
        DonorMoral donorMoral = new DonorMoral();
        donation.setDonor(donorMoral);

        DonationExportDTO mappedDTO = new DonationExportDTO();
        mappedDTO.setDonorName("Acme Corp");

        when(exportEntitiesMapper.donationMoralDonorToDonationExportDTO(donorMoral)).thenReturn(mappedDTO);

        DonationExportDTO donationExportDTO = new DonationExportDTO();

        // Act
        donationService.setDonorDetails(donation, donationExportDTO);

        // Assert
        assertEquals("Acme Corp", donationExportDTO.getDonorName());
        assertEquals(TYPE_DONOR_MORAL, donationExportDTO.getTypeDonor());

        verify(exportEntitiesMapper, times(1)).donationMoralDonorToDonationExportDTO(donorMoral);
    }

    @Test
    void setDonorDetails_nullDonor_shouldNotModifyDTO() {
        // Arrange
        Donation donation = new Donation();
        donation.setDonor(null);

        DonationExportDTO donationExportDTO = new DonationExportDTO();

        // Act
        donationService.setDonorDetails(donation, donationExportDTO);

        // Assert
        assertNull(donationExportDTO.getDonorName());
        assertNull(donationExportDTO.getTypeDonor());

        verifyNoInteractions(exportEntitiesMapper);
    }

    @Test
    void setDonorDetails_mapperThrowsException_shouldPropagate() {
        // Arrange
        Donation donation = new Donation();
        DonorPhysical donorPhysical = new DonorPhysical();
        donation.setDonor(donorPhysical);

        when(exportEntitiesMapper.donationPhysicalDonorToDonationExportDTO(any())).thenThrow(new RuntimeException("Mapping error"));

        DonationExportDTO donationExportDTO = new DonationExportDTO();

        // Act & Assert
        assertThrows(RuntimeException.class, () -> donationService.setDonorDetails(donation, donationExportDTO));

        verify(exportEntitiesMapper, times(1)).donationPhysicalDonorToDonationExportDTO(donorPhysical);
    }

    //A test of Fetch data
    @Test
    void fetchDonationProductNatureDetails_validList_shouldFetchAndSetDetails() {
        // Arrange
        DonationDTO donationDTO = new DonationDTO();

        ProductNatureDTO productNatureDTO = new ProductNatureDTO();
        productNatureDTO.setId(1L);

        TypeProductNatureDTO typeProductNatureDTO = new TypeProductNatureDTO();
        typeProductNatureDTO.setId(2L);
        productNatureDTO.setTypeProductNature(typeProductNatureDTO);

        ProductUnitDTO productUnitDTO = new ProductUnitDTO();
        productUnitDTO.setId(3L);

        DonationProductNatureDTO donationProductNatureDTO = new DonationProductNatureDTO();
        donationProductNatureDTO.setProductNature(productNatureDTO);
        donationProductNatureDTO.setProductUnit(productUnitDTO);

        List<DonationProductNatureDTO> donationProductNatures = Stream.of(donationProductNatureDTO).collect(Collectors.toList());
        donationDTO.setDonationProductNatures(donationProductNatures);

        // Mock API Calls
        when(refFeignClient.getMetProductNature(1L)).thenReturn(new ProductNatureDTO(1L, "Updated Nature", null, null, null, typeProductNatureDTO));
        when(refFeignClient.getConsTypeProductNature(2L)).thenReturn(new TypeProductNatureDTO(2L, "Updated Type",null,null,null,null));
        when(refFeignClient.getParProductUnit(3L)).thenReturn(new ProductUnitDTO(3L, "Updated Unit",null,null,null));

        // Act
        donationService.fetchDonationProductNatureDetails(donationDTO);

        // Assert
        assertThat(donationDTO.getDonationProductNatures()).hasSize(1);
        assertEquals(1L,donationDTO.getDonationProductNatures().get(0).getProductNature().getId());
        assertEquals(2L,donationDTO.getDonationProductNatures().get(0).getProductNature().getTypeProductNature().getId());
        assertEquals(3L,donationDTO.getDonationProductNatures().get(0).getProductUnit().getId());

        // Verify interactions
        verify(refFeignClient, times(1)).getMetProductNature(1L);
        verify(refFeignClient, times(1)).getConsTypeProductNature(2L);
        verify(refFeignClient, times(1)).getParProductUnit(3L);
    }

    @Test
    void fetchDonationProductNatureDetails_feignClientThrowsException_shouldPropagate() {
        // Arrange
        DonationDTO donationDTO = new DonationDTO();

        ProductNatureDTO productNatureDTO = new ProductNatureDTO();
        productNatureDTO.setId(1L);

        DonationProductNatureDTO donationProductNatureDTO = new DonationProductNatureDTO();
        donationProductNatureDTO.setProductNature(productNatureDTO);

        donationDTO.setDonationProductNatures(List.of(donationProductNatureDTO));

        // Mock API Failure
        when(refFeignClient.getMetProductNature(1L)).thenThrow(new RuntimeException("Feign Client Error"));

        // Act & Assert
        assertThrows(RuntimeException.class, () -> donationService.fetchDonationProductNatureDetails(donationDTO));
        System.out.println();
        // Verify interactions
        verify(refFeignClient, times(1)).getMetProductNature(1L);
    }

    //Processing Budget LInes
    @Test
    void processBudgetLines_validBudgetLines_shouldProcessCorrectly() {
        // Arrange
        Donation donation = new Donation();
        DonationDTO donationDTO = new DonationDTO();

        // Budget Line with currency and execution date
        BudgetLine budgetLine = new BudgetLine();
        budgetLine.setType("Regular");
        budgetLine.setExecutionDate(LocalDateTime.of(2024, 2, 10, 14, 0));

        CurrencyDTO currencyDTO = new CurrencyDTO();
        currencyDTO.setId(1L);
        currencyDTO.setName("USD");

        BudgetLineDTO budgetLineDTO = new BudgetLineDTO();
        budgetLineDTO.setType("Regular");
        budgetLineDTO.setExecutionDate(LocalDateTime.of(2024, 2, 10, 14, 0));
        budgetLineDTO.setCurrency(currencyDTO);

        donation.setBudgetLines(List.of(budgetLine));
        when(budgetLineMapper.budgetLineToBudgetLineDTO(budgetLine)).thenReturn(budgetLineDTO);
        when(refFeignClient.getParCurrency(1L)).thenReturn(new CurrencyDTO(1L, "USD",null,null,null));

        // Act
        donationService.processBudgetLines(donation, donationDTO);

        // Assert
        assertThat(donationDTO.getBudgetLines()).hasSize(1);
        assertEquals("USD",donationDTO.getBudgetLines().get(0).getCurrency().getCode());
        assertNull(donationDTO.getBudgetLines().get(0).getExecutionDate()); // LocalDateTime should be nullified
        assertEquals(Date.from(LocalDateTime.of(2024, 2, 10, 14, 0).atZone(ZoneId.systemDefault()).toInstant()),donationDTO.getBudgetLines().get(0).getExecutionDateBudgetLine());

        verify(budgetLineMapper, times(1)).budgetLineToBudgetLineDTO(budgetLine);
        verify(refFeignClient, times(1)).getParCurrency(1L);
    }

    @Test
    public void testHandleBudgetLines_AddNewBudgetLine() throws TechnicalException {
        // Arrange
        Donation donation;

        donationDTO = new DonationDTO();
        donation = new Donation();
        donation.setId(1L);

        BudgetLineDTO budgetLineDTO = new BudgetLineDTO();
        budgetLineDTO.setId(null); // New budget line
        budgetLineDTO.setService(new ServicesDTO());

        List<BudgetLineDTO> budgetLineDTOs = new ArrayList<>();
        budgetLineDTOs.add(budgetLineDTO);
        donationDTO.setBudgetLines(budgetLineDTOs);
        donationDTO.setReceptionDate(new Date());

        Services service = new Services();
        service.setId(1L);
        service.setPropositionSystem(true);

        BudgetLine budgetLine = new BudgetLine();
        when(budgetLineMapper.budgetLineDTOToBudgetLine(budgetLineDTO)).thenReturn(budgetLine);

        // Act
        donationService.handleBudgetLines(donationDTO, donation);

        // Assert
        verify(budgetLineRepository, never()).delete(any());
        verify(budgetLineMapper).budgetLineDTOToBudgetLine(budgetLineDTO);
    }

    @Test
    void testHandleBudgetLines_UpdateExistingBudgetLine() throws TechnicalException {
        // Arrange

        Donation donation;

        donationDTO = new DonationDTO();
        donation = new Donation();
        donation.setId(1L);

        BudgetLineDTO budgetLineDTO = new BudgetLineDTO();
        budgetLineDTO.setId(1L); // Existing budget line
        budgetLineDTO.setService(new ServicesDTO());

        List<BudgetLineDTO> budgetLineDTOs = new ArrayList<>();
        budgetLineDTOs.add(budgetLineDTO);
        donationDTO.setBudgetLines(budgetLineDTOs);

        BudgetLine existingBudgetLine = new BudgetLine();
        existingBudgetLine.setId(1L);

        List<BudgetLine> existingBudgetLines = new ArrayList<>();
        existingBudgetLines.add(existingBudgetLine);

        Services service = new Services();
        service.setId(1L);
        service.setPropositionSystem(true);

        when(budgetLineRepository.findByDonationId(donation.getId())).thenReturn(existingBudgetLines);

        // Act
        donationService.handleBudgetLines(donationDTO, donation);

        // Assert
        verify(budgetLineRepository).findByDonationId(donation.getId());
        verify(budgetLineMapper).updateBudgetLineFromDTO(budgetLineDTO, existingBudgetLine);
    }


}