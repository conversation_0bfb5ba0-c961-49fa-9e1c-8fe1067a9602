package ma.almobadara.backend.model.donor;

import jakarta.persistence.*;
import lombok.*;
import ma.almobadara.backend.enumeration.Direction;
import ma.almobadara.backend.model.administration.CacheAdUser;
import ma.almobadara.backend.model.communs.Document;
import org.springframework.format.annotation.DateTimeFormat;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;

import static ma.almobadara.backend.util.strings.HandleSpecialChars.escapeSpecialChars;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
public class Correspondence {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate date;
    private Direction direction; // "incoming" or "outgoing"
    private String content;
    private String subject;
    @ManyToOne
    @JoinColumn(name = "affected_to_id")
    private CacheAdUser affectedTo;
    private Long canalCommunicationId;
    @ManyToOne
    @JoinColumn(name = "document_id", nullable = true)
    private Document documents;
    @ManyToOne
    @JoinColumn(name = "donor_id")
    private Donor donor;

    public String getAudit(String canalCommunication,StringBuilder columnToAppend){
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
        date.format(formatter);
        return "{" +
                "\"Date realisation\" : \"" + escapeSpecialChars(date.toString()) + "\"," +
                "\"Canal Communication\" : \"" + escapeSpecialChars(canalCommunication) + "\"," +
                "\"Direction\" : \"" + escapeSpecialChars(direction.name()) + "\"," +
                "\"Type de correspondance\" : \"" + escapeSpecialChars(subject) + "\"," +
                "\"Affecter a\": \"" +(affectedTo !=null ? escapeSpecialChars(affectedTo.getFirstName()+" "+affectedTo.getFirstName()) :"-" ) + "\"," +
                "\"Contenu\": \"" +escapeSpecialChars(content) + "\"," +
                columnToAppend.toString() +
                "}";
    }

}
