package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.beneficiary.DiseaseTreatmentBeneficiaryAddDto;
import ma.almobadara.backend.model.beneficiary.DiseaseTreatment;
import ma.almobadara.backend.dto.beneficiary.DiseaseTreatmentDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;


@Mapper(componentModel = "spring")
public interface DiseaseTreatmentMapper {

	@Mapping(source = "typeId", target = "type.id")
	DiseaseTreatmentDTO diseaseTreatmentToDiseaseTreatmentDTO(DiseaseTreatment diseaseTreatment);

	Iterable<DiseaseTreatmentDTO> diseaseTreatmentToDiseaseTreatmentDTO(Iterable<DiseaseTreatment> diseaseTreatments);

	@Mapping(source = "type.id", target = "typeId")
	DiseaseTreatment diseaseTreatmentDTOToDiseaseTreatment(DiseaseTreatmentDTO diseaseTreatmentDTO);

	Iterable<DiseaseTreatment> diseaseTreatmentDTOToDiseaseTreatment(Iterable<DiseaseTreatmentDTO> diseaseTreatmentDTOS);

	Iterable<DiseaseTreatment> diseaseTreatmentDTOToDiseaseTreatment(List<DiseaseTreatmentBeneficiaryAddDto> diseaseTreatments);

	DiseaseTreatment DiseaseTreatmentBeneficiaryAddDtoToDiseaseTreatment(DiseaseTreatmentBeneficiaryAddDto diseaseTreatmentDTO);

}
