package ma.almobadara.backend.repository.beneficiary;

import ma.almobadara.backend.model.beneficiary.BeneficiaryStatut;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface BeneficiaryStatutRepository extends JpaRepository<BeneficiaryStatut, Long> {

    BeneficiaryStatut findByNameStatut(String nameStatut);

    Long id(Long id);
}
