CREATE TABLE history_rapport (
                                 id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
                                 date_communicated TIMESTAMP,
                                 communicated_by VA<PERSON><PERSON><PERSON>(255),
                                 beneficiary_id BIGINT,
                                 rapport_id BIGINT,
                                 donor_id BIGINT,

                                 created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                                 modified_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,

                                 CONSTRAINT fk_history_rapport_beneficiary FOREIGN KEY (beneficiary_id)
                                     REFERENCES beneficiary(id) ON DELETE SET NULL,
                                 CONSTRAINT fk_history_rapport_rapport FOREIGN KEY (rapport_id)
                                     REFERENCES rapport(id) ON DELETE SET NULL,
                                 CONSTRAINT fk_history_rapport_donor FOREIG<PERSON> KEY (donor_id)
                                     REFERENCES donor(id) ON DELETE SET NULL
);
