package ma.almobadara.backend.model.communs;

import jakarta.persistence.*;
import lombok.*;
import ma.almobadara.backend.model.administration.CacheAdUser;
import ma.almobadara.backend.model.beneficiary.BaseEntity;
import org.hibernate.annotations.DynamicUpdate;
import org.springframework.format.annotation.DateTimeFormat;

import java.text.SimpleDateFormat;
import java.util.Date;

import static ma.almobadara.backend.util.strings.HandleSpecialChars.escapeSpecialChars;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Inheritance(strategy = InheritanceType.JOINED)
@DynamicUpdate
@Builder
public class Note extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String objet;
    private String content;
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date createdDate;
    @ManyToOne
    @JoinColumn(name = "created_by_id")
    private CacheAdUser createdBy;
    public String toDtoString(StringBuilder columnToAppend) {
        SimpleDateFormat formatter = new SimpleDateFormat("dd/MM/yyyy");
        String dateFormatted = formatter.format(createdDate);

        return "{"
                + "\"Date\": \"" + escapeSpecialChars(dateFormatted) + "\","
                + "\"Objet\": \"" + escapeSpecialChars(objet) + "\","
                + "\"Prise Par\": \""
                + (createdBy != null ? escapeSpecialChars(createdBy.getFirstName() + " " + createdBy.getLastName()) : "-") + "\","
                + "\"Commentaire\": \"" + escapeSpecialChars(content) + "\","
                + columnToAppend.toString()
                + "}";
    }




}
