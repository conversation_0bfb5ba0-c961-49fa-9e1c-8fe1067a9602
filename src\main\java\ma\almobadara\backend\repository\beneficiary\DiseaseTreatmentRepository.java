package ma.almobadara.backend.repository.beneficiary;


import ma.almobadara.backend.model.beneficiary.DiseaseTreatment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DiseaseTreatmentRepository extends JpaRepository<DiseaseTreatment, Long> {
    List <DiseaseTreatment> findByBeneficiaryId(Long id);

}
