package ma.almobadara.backend.service.takenInCharge;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityNotFoundException;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.*;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.Audit.AuditApplicationService;
import ma.almobadara.backend.config.Messages;
import ma.almobadara.backend.controller.referentiel.RefController;
import ma.almobadara.backend.dto.administration.TagDTO;
import ma.almobadara.backend.dto.beneficiary.BankCardDTO;
import ma.almobadara.backend.dto.donor.DonorDTO;
import ma.almobadara.backend.dto.donor.DonorMoralDTO;
import ma.almobadara.backend.dto.donor.DonorPhysicalDTO;
import ma.almobadara.backend.dto.exportentities.ExportFileDTO;
import ma.almobadara.backend.dto.exportentities.TakenInChargeExportDTO;
import ma.almobadara.backend.dto.referentiel.ActivitySectorDTO;
import ma.almobadara.backend.dto.referentiel.CityDTO;
import ma.almobadara.backend.dto.referentiel.ClotureMotifTypeDTO;
import ma.almobadara.backend.dto.referentiel.TypeIdentityDTO;
import ma.almobadara.backend.dto.service.ServicesDTO;
import ma.almobadara.backend.dto.takenInCharge.*;
import ma.almobadara.backend.enumeration.BeneficiaryStatus;
import ma.almobadara.backend.enumeration.BudgetLineStatus;
import ma.almobadara.backend.enumeration.EntitiesToExport.TakenInChargeExportHeaders;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.mapper.*;
import ma.almobadara.backend.mapper.operationTakenInCharge.BeneficiaryOperationMapper;
import ma.almobadara.backend.mapper.operationTakenInCharge.DonorOperationMapper;
import ma.almobadara.backend.model.administration.Tag;
import ma.almobadara.backend.model.administration.Taggable;
import ma.almobadara.backend.model.beneficiary.BankCard;
import ma.almobadara.backend.model.beneficiary.Beneficiary;
import ma.almobadara.backend.model.beneficiary.BeneficiaryStatut;
import ma.almobadara.backend.model.beneficiary.Person;
import ma.almobadara.backend.model.donation.BudgetLine;
import ma.almobadara.backend.model.donation.Donation;
import ma.almobadara.backend.model.donor.Donor;
import ma.almobadara.backend.model.donor.DonorAnonyme;
import ma.almobadara.backend.model.donor.DonorMoral;
import ma.almobadara.backend.model.donor.DonorPhysical;
import ma.almobadara.backend.model.family.Family;
import ma.almobadara.backend.model.family.FamilyMember;
import ma.almobadara.backend.model.service.Services;
import ma.almobadara.backend.model.takenInCharge.*;
import ma.almobadara.backend.repository.administration.TaggableRepository;
import ma.almobadara.backend.repository.beneficiary.BankCardRepository;
import ma.almobadara.backend.repository.beneficiary.BeneficiaryRepository;
import ma.almobadara.backend.repository.donation.BudgetLineRepository;
import ma.almobadara.backend.repository.donation.DonationRepository;
import ma.almobadara.backend.repository.donor.DonorRepository;
import ma.almobadara.backend.repository.donor.TakenInChargeDonorRepository;
import ma.almobadara.backend.repository.family.FamilyRepository;
import ma.almobadara.backend.repository.services.ServicesRepository;
import ma.almobadara.backend.repository.takenInCharge.*;
import ma.almobadara.backend.service.ReferentialService;
import ma.almobadara.backend.service.beneficiary.AgendaRapportService;
import ma.almobadara.backend.service.beneficiary.BeneficiaryService;
import ma.almobadara.backend.service.beneficiary.HistoryBeneficiaryService;
import ma.almobadara.backend.service.communs.ExportService;
import ma.almobadara.backend.service.Donation.DonationService;
import ma.almobadara.backend.service.donor.MinioService;
import ma.almobadara.backend.util.times.TimeWatch;
import org.apache.poi.openxml4j.exceptions.InvalidOperationException;
import org.hibernate.Hibernate;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.*;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static ma.almobadara.backend.Audit.ObjectConverter.convertObjectToJson;
import static ma.almobadara.backend.service.administration.CacheAdUserService.getUsernameFromJwt;
import static ma.almobadara.backend.util.constants.GlobalConstants.*;
import static org.apache.poi.ss.usermodel.DateUtil.toLocalDateTime;

@Service
@RequiredArgsConstructor
@Slf4j
public class TakenInChargeService {
    private final BudgetLineRepository budgetLineRepository;
    private final DonationRepository donationRepository;
    private final TakenInChargeOperationHistoriqueRepository takenInChargeOperationHistoriqueRepository;
    private final FamilyRepository familyRepository;
    private final ServicesRepository servicesRepository;
    private final ServicesMapper servicesMapper;
    private final TakenInChargeMapper takenInChargeMapper;
    private final TakenInChargeRepository takenInChargeRepository;
    private final TakenInChargeDonorRepository takenInChargeDonorRepository;
    private final TakenInChargeDonorMapper takenInChargeDonorMapper;
    private final TakenInChargeBeneficiaryRepository takenInChargeBeneficiaryRepository;
    private final TakenInChargeYearCountRepository takenInChargeYearCountRepository;
    private final BankCardRepository bankCardRepository;
    private final BankCardMapper bankCardMapper;
    private final BeneficiaryRepository beneficiaryRepository;
    private final TakenInChargeOperationRepository takenInChargeOperationRepository;
    private final DonorRepository donorRepository;
    private final TakenInChargeBeneficiaryMapper takenInChargeBeneficiaryMapper;
    private final RefController refController;
    private final DonorPhysicalMapper donorPhysicalMapper;
    private final DonorMoralMapper donorMoralMapper;
    private final DonorAnonymeMapper donorAnonymeMapper;
    private final TakenInChargeOperationMapper takenInChargeOperationMapper;
    private final MinioService minioService;
    private final Messages messages;
    private final AuditApplicationService auditApplicationService;
    private final EntityManager entityManager;
    private final DonorOperationMapper donorOperationMapper;
    private final BeneficiaryOperationMapper beneficiaryOperationMapper;
    private final NoteTakenInChargeRepository noteTakenInChargeRepository;
    private final DocumentTakenInChargeRepository documentTakenInChargeRepository;
    private final ActionTakenInChargeRepository actionTakenInChargeRepository;
    private final ExportService exportService;
    private final DonationService donationService;
    private final BeneficiaryService beneficiaryService;
    private final HistoryBeneficiaryService historyBeneficiaryService;
    private final AgendaRapportService agendaRapportService;
    private final ReferentialService referentialService;
    private final TaggableRepository taggableRepository;


    @Transactional
    public TakenInChargeDTO addTakenInCharge(TakenInChargeDTO takenInChargeDTO) throws Exception {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service addTakenInCharge {}", takenInChargeDTO);

        Long takenInChargeId = (takenInChargeDTO.getId() != null) ? takenInChargeDTO.getId() : null;  // Check if it's an update or create


        TakenInChargeDTO takenInChargeOldModifDTO = new TakenInChargeDTO();
        if(takenInChargeDTO.getId()!=null){
          takenInChargeOldModifDTO = getTakenInChargeById(takenInChargeDTO.getId());
        }
        TakenInCharge takenInChargeOldModif = takenInChargeMapper.takenInChargeDTOToTakenInCharge(takenInChargeOldModifDTO);

// Check if the beneficiary has an open TakenInCharge from a different service
        boolean hasOtherServiceTakenInCharge = takenInChargeRepository.existsByBeneficiaryIdAndStatusNotClosedAndServiceIdNot(
                takenInChargeDTO.getTakenInChargeBeneficiaries().get(0).getId(),
                takenInChargeDTO.getServiceId(),
                takenInChargeId  // Pass null for create, or the ID for update
        );

        if (hasOtherServiceTakenInCharge) {
            throw new Exception("The beneficiary already has an open TakenInCharge from a different service.");
        }

// Check if the beneficiary has an open TakenInCharge with the same service and donor
        boolean hasSameServiceAndDonor = takenInChargeRepository.existsByBeneficiaryIdAndStatusNotClosedAndServiceIdAndDonorId(
                takenInChargeDTO.getTakenInChargeBeneficiaries().get(0).getId(),
                takenInChargeDTO.getServiceId(),
                takenInChargeDTO.getTakenInChargeDonors().get(0).getDonor().getId(),
                takenInChargeId  // Pass null for create, or the ID for update
        );

        if (hasSameServiceAndDonor) {
            throw new Exception("The beneficiary already has an open TakenInCharge with this service and donor.");
        }


        takenInChargeDTO.setCreatedAt(LocalDateTime.now());
        TakenInCharge takenInCharge = takenInChargeMapper.takenInChargeDTOToTakenInCharge(takenInChargeDTO);
        if (takenInChargeDTO.getId() != null) {
            Optional<TakenInCharge> existingTakenInChargeOpt = takenInChargeRepository.findById(takenInChargeDTO.getId());
            if (existingTakenInChargeOpt.isPresent()) {
                takenInCharge = existingTakenInChargeOpt.get();
                takenInCharge.setEndDate(takenInChargeDTO.getEndDate());
                takenInCharge.setType(takenInChargeDTO.getType());
                takenInCharge.setStartDate(takenInChargeDTO.getStartDate());
                takenInCharge.setCreatedAt(takenInChargeDTO.getCreatedAt());
                takenInCharge.setService(servicesRepository.findById(takenInChargeDTO.getServiceId()).orElse(null));
                if (takenInChargeDTO.getEndDate() != null) {
                    if (takenInCharge.getEndDate() != null && takenInCharge.getEndDate().before(new Date())) {
                        takenInCharge.setStatus("Expiré");
                    } else if (takenInCharge.getEndDate() != null && takenInCharge.getEndDate().after(new Date())) {
                        takenInCharge.setStatus(
                                (takenInCharge.getTakenInChargeDonors() != null &&
                                        !takenInCharge.getTakenInChargeDonors().isEmpty() &&
                                        takenInCharge.getTakenInChargeDonors().get(0).getTakenInChargeOperations() != null &&
                                        !takenInCharge.getTakenInChargeDonors().get(0).getTakenInChargeOperations().isEmpty())
                                        ? "Actif"
                                        : "Inactif");
                    }
                }

                taggableRepository.deleteAllByTaggableIdAndTaggableType(takenInChargeId,"takenInCharge");
                 takenInChargeRepository.save(takenInCharge);
                if(takenInChargeDTO.getTags() != null) {
                    takenInChargeDTO.getTags().forEach(tagDTO -> {
                        Taggable taggable = new Taggable();
                        taggable.setTaggableType("takenInCharge");
                        taggable.setTaggableId(takenInChargeDTO.getId());
                        Tag tag = new Tag();
                        tag.setId(tagDTO.getId());
                        taggable.setTag(tag);

                taggableRepository.save(taggable);
                    });
                }
            } else {
                throw new TechnicalException("TakenInCharge not found");
            }
        } else {
            handleDonors(takenInChargeDTO, takenInChargeDTO.getTakenInChargeDonors(), takenInChargeDTO.getTakenInChargeBeneficiaries());
        }

        TakenInCharge takenInChargeAudit = takenInChargeMapper.takenInChargeDTOToTakenInCharge(takenInChargeDTO);

        TakenInCharge existingTakenInChargefordto = new TakenInCharge();
        if (takenInChargeDTO.getId() != null) {
            //for audit
            Optional<TakenInCharge> takenInChargeOptional = takenInChargeRepository.findById(takenInChargeDTO.getId());
            TakenInCharge existingTakenInCharge = takenInChargeOptional.orElseThrow(() -> new TechnicalException("TakenInCharge not found"));

            Hibernate.initialize(existingTakenInCharge.getTakenInChargeDonors()); // Initialiser la collection
            Hibernate.initialize(existingTakenInCharge.getTakenInChargeBeneficiaries());


            BeanUtils.copyProperties(existingTakenInCharge, existingTakenInChargefordto);

            // Copy collections
            existingTakenInChargefordto.setTakenInChargeBeneficiaries(new ArrayList<>());
            for (TakenInChargeBeneficiary takenInChargeBeneficiaryDTO : existingTakenInCharge.getTakenInChargeBeneficiaries()) {
                TakenInChargeBeneficiary newTakenInChargeBeneficiary = new TakenInChargeBeneficiary();
                BeanUtils.copyProperties(takenInChargeBeneficiaryDTO, newTakenInChargeBeneficiary);
                existingTakenInChargefordto.getTakenInChargeBeneficiaries().add(newTakenInChargeBeneficiary);
            }

            existingTakenInChargefordto.setTakenInChargeDonors(new ArrayList<>());
            for (TakenInChargeDonor takenInChargeDonor : existingTakenInCharge.getTakenInChargeDonors()) {
                TakenInChargeDonor newTakenInChargeDonor = new TakenInChargeDonor();
                BeanUtils.copyProperties(takenInChargeDonor, newTakenInChargeDonor);
                existingTakenInChargefordto.getTakenInChargeDonors().add(newTakenInChargeDonor);
            }

        }

        TakenInChargeDTO auditTakenInChargeDtoOld = takenInChargeMapper.takenInChargeToTakenInChargeDTO(existingTakenInChargefordto);


        //Audit
        TakenInChargeDTO auditTakenInChargeDto = takenInChargeMapper.takenInChargeToTakenInChargeDTO(takenInCharge);

        String serviceInitialModif = "-";

        if (auditTakenInChargeDto.getServiceId() != null) {
            Services services = servicesRepository.findById(auditTakenInChargeDto.getServiceId()).orElseThrow();
            ServicesDTO servicesDTO = servicesMapper.toDto(services);
            serviceInitialModif = servicesDTO.getName();
        }

        //Beneficiaries
        List<String> beneficiariesNames = new ArrayList<>();
        List<TakenInChargeBeneficiaryDTO> beneficiariesList = auditTakenInChargeDto.getTakenInChargeBeneficiaries();
        Beneficiary beneficiaryTemp = new Beneficiary();
        if (!beneficiariesList.isEmpty() && takenInChargeDTO.getId()!=null) {
            beneficiaryTemp = beneficiaryRepository.findById(beneficiariesList.get(0).getBeneficiary().getId()).orElseThrow();
        }else{
            beneficiaryTemp = beneficiaryRepository.findById(beneficiariesList.get(0).getId()).orElseThrow();
        }
        if (beneficiariesList != null && !beneficiariesList.isEmpty()) {
            for (TakenInChargeBeneficiaryDTO dto : beneficiariesList) {
                if (dto.getId() != null && dto.getBeneficiary() != null && dto.getBeneficiary().getId() != null) {
                    var beneficiary = beneficiaryRepository.findById(dto.getBeneficiary().getId()).orElseThrow();
                    if (beneficiary.getCode() != null) {
                        beneficiariesNames.add(beneficiary.getCode());
                    }
                }

            }
        }

        //Donateurs
        List<String> donateursNames = new ArrayList<>();
        List<TakenInChargeDonorDTO> donateursList = auditTakenInChargeDto.getTakenInChargeDonors();
        Donor donorTemp = new Donor();
        if (!donateursList.isEmpty()) {
            donorTemp = donorRepository.findById(donateursList.get(0).getDonor().getId()).orElseThrow();
        }
        String donorName = getDonorName(donorTemp);

        Services serviceAudit = servicesRepository.findById(takenInChargeAudit.getService().getId()).orElseThrow();



        if (takenInChargeDTO.getId() != null) {
            TakenInCharge takenInChargeNew = takenInChargeMapper.takenInChargeDTOToTakenInCharge(takenInChargeDTO);
            //Beneficiaries
            List<String> beneficiariesNamesOld = new ArrayList<>();
            List<TakenInChargeBeneficiaryDTO> beneficiariesListOld = auditTakenInChargeDtoOld.getTakenInChargeBeneficiaries();
            Beneficiary beneficiaryInitial = new Beneficiary();
            if (!beneficiariesListOld.isEmpty()) {
                beneficiaryInitial = beneficiaryRepository.findById(beneficiariesListOld.get(0).getBeneficiary().getId()).orElseThrow();
            }
            String beneficaryNameInitial = beneficiaryInitial.getPerson().getFirstName()+" "+beneficiaryInitial.getPerson().getLastName();

            List<TakenInChargeDonorDTO> donateursListOld = auditTakenInChargeDtoOld.getTakenInChargeDonors();
            Donor donorInitial = new Donor();
            if (!donateursListOld.isEmpty()) {
                donorInitial = donorRepository.findById(donateursListOld.get(0).getDonor().getId()).orElseThrow();
            }
            String donorNameInitial = getDonorName(donorInitial);
            auditApplicationService.audit("Modification de la Prise en charge", getUsernameFromJwt(), "Update TakeInCharge",
                    takenInChargeOldModif.getAuditModification(donorNameInitial,beneficaryNameInitial,takenInChargeOldModifDTO.getServices().getName(),takenInChargeOldModifDTO.getType(),takenInChargeOldModifDTO.getCode()), takenInChargeNew.getAuditModification(donorNameInitial,beneficaryNameInitial,serviceInitialModif,takenInChargeNew.getType(),takenInChargeOldModifDTO.getCode()), PRISEENCHARGE, UPDATE);
        } else {

            auditApplicationService.audit("Ajout d'une nouvelle Prise en charge", getUsernameFromJwt(), "Add New TakeInCharge",
                    null, takenInChargeAudit.getAuditAjout(donorName,beneficiaryTemp.getPerson().getFirstName()+" "+beneficiaryTemp.getPerson().getLastName(),serviceAudit.getName()), PRISEENCHARGE, CREATE);
        }

        log.debug("End service addTakenInCharge with ID {}, took {}", takenInCharge.getId(), watch.toMS());
        return takenInChargeMapper.takenInChargeToTakenInChargeDTO(takenInCharge);
    }

    private void handleBeneficiaries(TakenInCharge newTakenInCharge, Iterable<TakenInChargeBeneficiaryDTO> beneficiariesDTO) throws TechnicalException {
        if (beneficiariesDTO != null) {
            for (TakenInChargeBeneficiaryDTO beneficiaryDTO : beneficiariesDTO) {
                TakenInChargeBeneficiary beneficiary = takenInChargeBeneficiaryMapper.takenInChargeBeneficiaryDTOToTakenInChargeBeneficiary(beneficiaryDTO);
                Long beneficiaryId = beneficiary.getId();
                Beneficiary beneficiaryEntity = beneficiaryRepository.findById(beneficiaryId)
                        .orElseThrow(() -> new TechnicalException(messages.get(TAKEN_IN_CHARGE_BENEFICIARY_NOT_FOUND)));

                // we shoudl also make the status of the beneficiary to "Actif" since he is related to a takenInCharge but just if the status is not "Actif"
                if (!Objects.equals(beneficiaryEntity.getBeneficiaryStatut().getId(), BeneficiaryStatus.BENEFICIAIRE_ACTIF.getId())) {
                    beneficiaryEntity.setBeneficiaryStatut(new BeneficiaryStatut(BeneficiaryStatus.BENEFICIAIRE_ACTIF.getId()));
//                    if (beneficiaryEntity.getCodeBeneficiary() == null) {
//                        beneficiaryEntity.setCodeBeneficiary(beneficiaryService.generateBeneficiaryCode(beneficiaryEntity));
//                    }
                }
                beneficiaryRepository.save(beneficiaryEntity);

                // we should also save the history of the beneficiary like this   historyBeneficiaryService.saveHistoryBeneficiaryStatus(newStatut.getId(), beneficiary.getId(), getUsernameFromJwt());
                historyBeneficiaryService.saveHistoryBeneficiaryStatus(BeneficiaryStatus.BENEFICIAIRE_ACTIF.getId(), beneficiary.getId(), getUsernameFromJwt());

                beneficiary.setBeneficiary(beneficiaryEntity);
                beneficiary.setTakenInCharge(newTakenInCharge);

                takenInChargeBeneficiaryRepository.save(beneficiary);

                if (beneficiary.getBeneficiary().getHasRapport() != null && !beneficiary.getBeneficiary().getHasRapport()) {
                        agendaRapportService.planifierRapport(beneficiary.getBeneficiary().getId(), false);
                    }

            }
        }
    }


    private void handleDonors(TakenInChargeDTO takenInChargeDTO, Iterable<TakenInChargeDonorDTO> donorsDTO, Iterable<TakenInChargeBeneficiaryDTO> beneficiariesDTO) throws TechnicalException {
        if (donorsDTO != null) {
            Boolean isSaved= false;
            for (TakenInChargeDonorDTO donorDTO : donorsDTO) {
                TakenInCharge newTakenInCharge = takenInChargeMapper.takenInChargeDTOToTakenInCharge(takenInChargeDTO);
                newTakenInCharge.setCode(generateTakenInChargeCodeForDonor(takenInChargeDTO));
                newTakenInCharge = takenInChargeRepository.save(newTakenInCharge);
                TakenInChargeDonor donor = takenInChargeDonorMapper.takenInChargeDonorDTOToTakenInChargeDonor(donorDTO);
                Long donorId = donor.getDonor().getId();

                Donor donorEntity = donorRepository.findById(donorId)
                        .orElseThrow(() -> new TechnicalException(messages.get(TAKEN_IN_CHARGE_DONOR_NOT_FOUND)));

                donor.setDonor(donorEntity);
                donor.setTakenInCharge(newTakenInCharge);

                // Save donor relation
                takenInChargeDonorRepository.save(donor);
                if(!isSaved){
                    taggableRepository.deleteAllByTaggableIdAndTaggableType(newTakenInCharge.getId(),"takenInCharge");
                    if(takenInChargeDTO.getTags() != null) {
                        TakenInCharge finalTakenInCharge1 = newTakenInCharge;
                        takenInChargeDTO.getTags().forEach(tagDTO -> {
                            Taggable taggable = new Taggable();
                            taggable.setTaggableType("takenInCharge");
                            taggable.setTaggableId(finalTakenInCharge1.getId());
                            Tag tag = new Tag();
                            tag.setId(tagDTO.getId());
                            taggable.setTag(tag);

                taggableRepository.save(taggable);
                        });
                    }
                    isSaved=true;
                }

                handleBeneficiaries(newTakenInCharge, beneficiariesDTO);
            }
        }
    }

    private String generateTakenInChargeCodeForDonor(TakenInChargeDTO takenInCharge) {
        String year = String.valueOf(takenInCharge.getStartDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().getYear());
        String code = "";

        // Ensure unique code generation for each donor
        boolean codeUnique = false;
        while (!codeUnique) {
            Optional<TakenInChargeYearCount> takenInChargeYearCount = takenInChargeYearCountRepository.findByYear(year);
            if (takenInChargeYearCount.isPresent()) {
                TakenInChargeYearCount yearCount = takenInChargeYearCount.get();
                Long count = yearCount.getCount() + 1L;

                StringBuilder zeros = new StringBuilder();
                if (count.toString().length() < 5) {
                    for (int i = 0; i < 5 - count.toString().length(); i++) {
                        zeros.append("0");
                    }
                }

                code = "P" + year + zeros + count + "KXX";

                // Check if the generated code already exists, and if so, retry
                if (!takenInChargeRepository.existsByCode(code)) {
                    codeUnique = true;
                    yearCount.setCount(count);
                    takenInChargeYearCountRepository.save(yearCount);
                }
            } else {
                // Initialize the count for the year
                code = "P" + year + "00001KXX";
                TakenInChargeYearCount newYearCount = new TakenInChargeYearCount();
                newYearCount.setCount(1L);
                newYearCount.setYear(year);
                takenInChargeYearCountRepository.save(newYearCount);
                codeUnique = true;
            }
        }

        return code;
    }

    public Page<TakenInChargeDTO> getAllTakenInCharges(Optional<String> criteria, Optional<String> value1, Optional<String> value2, Optional<Integer> page) {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service getAllTakenInCharges with criteria: {} and value1: {} and value2: {}", criteria, value1, value2);

        int pageNumber = page.orElse(0);
        int pageSize = 10;
        Pageable pageable = PageRequest.of(pageNumber, pageSize, Sort.by(Sort.Direction.DESC, "createdAt"));

        Page<TakenInCharge> takenInCharges = getFilteredTakenInCharges(criteria, value1, value2, pageable);

        List<TakenInChargeDTO> takenInChargeDTOS = takenInCharges.getContent().stream()
                .map(takenInCharge -> {
                    TakenInChargeDTO takenInChargeDTO = takenInChargeMapper.takenInChargeToTakenInChargeDTOForList(takenInCharge);
                    setDonorName(takenInCharge, takenInChargeDTO);
                    return takenInChargeDTO;
                })
                .collect(Collectors.toList());

        takenInChargeDTOS.forEach(this::setServiceAndStatus);

        log.debug("End service getAllTakenInCharges with a total of: {}, took {}", takenInChargeDTOS.size(), watch.toMS());

        return new PageImpl<>(takenInChargeDTOS, pageable, takenInCharges.getTotalElements());
    }


    public Page<TakenInCharge> getFilteredTakenInCharges(Optional<String> criteria, Optional<String> value1, Optional<String> value2, Pageable pageable) {
        return takenInChargeRepository.findAll(pageable);
    }

    private void setServiceAndStatus(TakenInChargeDTO takenInChargeDTO) {
        if (takenInChargeDTO.getServices() != null && takenInChargeDTO.getServices().getId() != null) {
            Services services = servicesRepository.findById(takenInChargeDTO.getServices().getId()).orElse(null);
            ServicesDTO servicesDTO = servicesMapper.toDto(services);
            takenInChargeDTO.setServices(servicesDTO);
        }
    }

    public void setDonorName(TakenInCharge takenInCharge, TakenInChargeDTO takenInChargeDTO) {
        if (takenInCharge.getTakenInChargeDonors() != null) {
            List<TakenInChargeDonorDTO> takenInChargeDonorDTOS = new ArrayList<>();
            for (TakenInChargeDonor takenInChargeDonor : takenInCharge.getTakenInChargeDonors()) {
                Donor donor = takenInChargeDonor.getDonor();
                DonorDTO donorDTO = null;
                if (donor instanceof DonorPhysical) {
                    donorDTO = donorPhysicalMapper.donorPhysicalModelToDtoForList((DonorPhysical) donor);
                } else if (donor instanceof DonorMoral) {
                    donorDTO = donorMoralMapper.donorMoralModelToDtoForList((DonorMoral) donor);
                } else if (donor instanceof DonorAnonyme) {
                    donorDTO = donorAnonymeMapper.donorAnonymeModelToDtoForList((DonorAnonyme) donor);
                }
                TakenInChargeDonorDTO takenInChargeDonorDTO = takenInChargeDonorMapper.takenInChargeDonorToTakenInChargeDonorDTO(takenInChargeDonor);
                takenInChargeDonorDTO.setDonor(donorDTO);
                takenInChargeDonorDTOS.add(takenInChargeDonorDTO);
            }
            takenInChargeDTO.setTakenInChargeDonors(takenInChargeDonorDTOS);
        }
    }

    public TakenInChargeDTO getTakenInChargeById(Long id) throws TechnicalException {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service getTakenInChargeById with id: {}", id);

        if (id == null) {
            throw new TechnicalException(messages.get(NULL_ENTITY));
        }

        Optional<TakenInCharge> optionalTakenInCharge = takenInChargeRepository.findById(id);
        TakenInCharge takenInCharge = optionalTakenInCharge.orElseThrow(() -> new TechnicalException(TAKEN_IN_CHARGE_NOT_FOUND));

        TakenInChargeDTO takenInChargeDTO = takenInChargeMapper.takenInChargeToTakenInChargeDTO(takenInCharge);
        takenInChargeDTO.setClotureMotif(takenInCharge.getClotureMotif());
        if (takenInCharge.getClotureMotifTypeId() != null) {
            ClotureMotifTypeDTO clotureMotifTypeDTO = refController.getClotureMotifTypeById(takenInCharge.getClotureMotifTypeId()).getBody();
            takenInChargeDTO.setClotureMotifType(clotureMotifTypeDTO);

        }
        setDonorName(takenInCharge, takenInChargeDTO);
        setFullServiceAndStatus(takenInChargeDTO);

        // Processing donors information
        if (takenInChargeDTO.getTakenInChargeDonors() != null && !takenInChargeDTO.getTakenInChargeDonors().isEmpty()) {
            for (TakenInChargeDonorDTO donorDTO : takenInChargeDTO.getTakenInChargeDonors()) {
                setDonorCityAndInfo(donorDTO);
            }
            // Processing operations history
            for (TakenInChargeDonorDTO takenInChargeDonorDTO : takenInChargeDTO.getTakenInChargeDonors()) {
                if (takenInChargeDonorDTO.getTakenInChargeOperations() != null && !takenInChargeDonorDTO.getTakenInChargeOperations().isEmpty()) {
                    takenInChargeDTO.setHasOperations(true);
                    for (TakenInChargeOperationDTO takenInChargeOperationDTO : takenInChargeDonorDTO.getTakenInChargeOperations()) {
                        setListHistorytoOperations(takenInChargeOperationDTO);
                    }
                }
            }
        }

        Double donorBalance = takenInChargeDonorRepository.findAvailableBalanceByDonorAndService(takenInChargeDTO.getTakenInChargeDonors().get(0).getDonor().getId(), takenInChargeDTO.getServiceId());
        takenInChargeDTO.getTakenInChargeDonors().get(0).setDonorBalance(donorBalance);


        // Process beneficiaries and their bank cards
        if (takenInChargeDTO.getTakenInChargeBeneficiaries() != null) {
            for (TakenInChargeBeneficiaryDTO beneficiaryDTO : takenInChargeDTO.getTakenInChargeBeneficiaries()) {
                processBeneficiary(beneficiaryDTO);
            }
        }

        TakenInCharge takenInChargeAudit = takenInChargeMapper.takenInChargeDTOToTakenInCharge(takenInChargeDTO);
        String donorName = getDonorName(takenInCharge.getTakenInChargeDonors().get(0).getDonor());
        String beneficiaireName = takenInChargeAudit.getTakenInChargeBeneficiaries().get(0).getBeneficiary().getPerson().getFirstName()+" "+takenInChargeAudit.getTakenInChargeBeneficiaries().get(0).getBeneficiary().getPerson().getLastName();
        String comment = takenInChargeAudit.getType();
        String codeKafalat = takenInChargeAudit.getCode();

        // Auditing action
        auditApplicationService.audit("Consultation de la Prise en charge : " + takenInChargeDTO.getCode(), getUsernameFromJwt(), "Consulter TakeInCharge",
                takenInChargeAudit.getAuditModification(donorName,beneficiaireName,takenInCharge.getService().getName(),comment,codeKafalat), null, PRISEENCHARGE, CONSULTATION);
        List<Taggable> taggables=taggableRepository.findByTaggableIdAndTaggableType(takenInChargeDTO.getId(),"takenInCharge");
        List<TagDTO> tags = new ArrayList<>();
        for (Taggable taggable : taggables) {
            TagDTO tagDTO = new TagDTO();
            tagDTO.setId(taggable.getTag().getId());
            tagDTO.setName(taggable.getTag().getName());
            tagDTO.setColor(taggable.getTag().getColor());
            tags.add(tagDTO);
        }
        takenInChargeDTO.setTags(tags);

        log.debug("End service getTakenInChargeById with id: {}, took {}", id, watch.toMS());
        return takenInChargeDTO;
    }


    private void processBeneficiary(TakenInChargeBeneficiaryDTO beneficiaryDTO) {
        if (beneficiaryDTO.getBeneficiary() != null) {
            // Fetch the picture URL for the beneficiary
            fetchBeneficiaryPicture(beneficiaryDTO);

            // Set family details for non-independent beneficiaries
            if (!beneficiaryDTO.getBeneficiary().getIndependent()) {
                Family family = familyRepository.getFamilyByBeneficiaryId(beneficiaryDTO.getBeneficiary().getId());
                if (family != null) {
                    beneficiaryDTO.getBeneficiary().setFamilyId(family.getId());
                    processFamilyMemberDetails(family, beneficiaryDTO);
                }
            }

            // Fetch bank card for the beneficiary
            fetchBankCardForBeneficiary(beneficiaryDTO);
        }
    }

    private void fetchBeneficiaryPicture(TakenInChargeBeneficiaryDTO beneficiaryDTO) {
        if (beneficiaryDTO.getBeneficiary().getPerson().getPictureUrl() != null) {
            try {
                byte[] imageData = minioService.ReadFromMinIO(beneficiaryDTO.getBeneficiary().getPerson().getPictureUrl(), null);
                String base64Image = Base64.getEncoder().encodeToString(imageData);
                beneficiaryDTO.getBeneficiary().getPerson().setPictureBase64(base64Image);
            } catch (TechnicalException ex) {
                log.error("Error reading image from MinIO", ex);
            }
        }
    }

    private void processFamilyMemberDetails(Family family, TakenInChargeBeneficiaryDTO beneficiaryDTO) {
        for (FamilyMember familyMember : family.getFamilyMembers()) {
            if (familyMember.isTutor()) {
                BankCard bankCard = bankCardRepository.findActifBankCardForFamilyMember(familyMember.getPerson().getId());
                if (bankCard != null) {
                    BankCardDTO bankCardDTO = bankCardMapper.bankCardToBankCardDTO(bankCard);
                    beneficiaryDTO.getBankCards().add(bankCardDTO);
                }
            }
        }
    }

    private void fetchBankCardForBeneficiary(TakenInChargeBeneficiaryDTO beneficiaryDTO) {
        BankCard bankCard = null;
        if (beneficiaryDTO.getBeneficiary().getIndependent()) {
            bankCard = bankCardRepository.findActifBankCardForBeneficiary(beneficiaryDTO.getBeneficiary().getId());
        } else {
            bankCard = bankCardRepository.findActifBankCardForFamilyMember(beneficiaryDTO.getBeneficiary().getId());
        }
        if (bankCard != null) {
            BankCardDTO bankCardDTO = bankCardMapper.bankCardToBankCardDTO(bankCard);
            beneficiaryDTO.getBankCards().add(bankCardDTO);
        }
    }

    private void setServiceDetails(TakenInChargeDTO takenInChargeDTO, TakenInChargeAuditDto auditDto) {
        if (takenInChargeDTO.getServiceId() != null) {
            Services service = servicesRepository.findById(takenInChargeDTO.getServiceId()).orElseThrow();
            ServicesDTO serviceDTO = servicesMapper.toDto(service);
            auditDto.setService(serviceDTO.getName());
        }
    }

    public void setBeneficiaryCodes(TakenInChargeDTO takenInChargeDTO, TakenInChargeAuditDto auditDto) {
        List<String> beneficiariesNames = new ArrayList<>();
        for (TakenInChargeBeneficiaryDTO dto : takenInChargeDTO.getTakenInChargeBeneficiaries()) {
            if (dto.getBeneficiary() != null && dto.getBeneficiary().getCode() != null) {
                beneficiariesNames.add(dto.getBeneficiary().getCode());
            }
        }
        auditDto.setCodeBeneficiaire(beneficiariesNames);
    }

    public void setDonorCodes(TakenInChargeDTO takenInChargeDTO, TakenInChargeAuditDto auditDto) {
        List<String> donateursNames = new ArrayList<>();
        for (TakenInChargeDonorDTO dto : takenInChargeDTO.getTakenInChargeDonors()) {
            if (dto.getDonor() != null && dto.getDonor().getCode() != null) {
                donateursNames.add(dto.getDonor().getCode());
            }
        }
        auditDto.setCodeDonateur(donateursNames);
    }


    private void setFullServiceAndStatus(TakenInChargeDTO takenInChargeDTO) {
        if (takenInChargeDTO.getServiceId() != null) {
            Long serviceId = takenInChargeDTO.getServiceId();
            Services fullService = servicesRepository.findById(serviceId).orElse(null);
            ServicesDTO fullServiceDTO = servicesMapper.toDto(fullService);
            takenInChargeDTO.setServices(fullServiceDTO);
        }


    }

    // it should get int he funtion the take incharge and the taken in charge DTO  adn ake a boucle over the operatons and for each we shoul set her list of historique
    private void setListHistorytoOperations(TakenInChargeOperationDTO takenInChargeOperationDTO) {
        List<TakenInChargeOperationHistorique> takenInChargeOperationHistoriques = takenInChargeOperationHistoriqueRepository.findByOperationId(takenInChargeOperationDTO.getId());
        List<TakenInChargeOperationHistoriqueDTO> takenInChargeOperationHistoriqueDTOS = new ArrayList<>();
        for (TakenInChargeOperationHistorique takenInChargeOperationHistorique : takenInChargeOperationHistoriques) {
            TakenInChargeOperationHistoriqueDTO takenInChargeOperationHistoriqueDTO = new TakenInChargeOperationHistoriqueDTO();
            BeanUtils.copyProperties(takenInChargeOperationHistorique, takenInChargeOperationHistoriqueDTO);
            takenInChargeOperationHistoriqueDTOS.add(takenInChargeOperationHistoriqueDTO);
        }
        takenInChargeOperationDTO.setHistoriques(takenInChargeOperationHistoriqueDTOS);
    }

    private void setDonorCityAndInfo(TakenInChargeDonorDTO takenInChargeDonorDTO) {
        DonorDTO donorDTO = takenInChargeDonorDTO.getDonor();
        if (donorDTO != null) {
            if (donorDTO.getCity() != null && donorDTO.getCity().getId() != null) {
                Long cityId = donorDTO.getCity().getId();
                CityDTO fullCityDTO = refController.getParCity(cityId).getBody();
                donorDTO.setCity(fullCityDTO);
            }

            if (donorDTO instanceof DonorMoralDTO donorMoralDTO) {
                if (donorMoralDTO.getLogoUrl() != null) {
                    try {
                        byte[] imageData = minioService.ReadFromMinIO(donorMoralDTO.getLogoUrl(), null);
                        String base64Image = Base64.getEncoder().encodeToString(imageData);
                        donorMoralDTO.setLogo64(base64Image);
                    } catch (TechnicalException ex) {
                        ex.printStackTrace();

                    }
                }

                ActivitySectorDTO activitySectorDTO = donorMoralDTO.getActivitySector();
                if (activitySectorDTO != null && activitySectorDTO.getId() != null) {
                    Long activitySectorId = activitySectorDTO.getId();
                    ActivitySectorDTO fullActivitySectorDTO = refController.getMetActivitySector(activitySectorId).getBody();
                    donorMoralDTO.setActivitySector(fullActivitySectorDTO);
                }
            } else if (donorDTO instanceof DonorPhysicalDTO donorPhysicalDTO) {
                if (donorPhysicalDTO.getPictureUrl() != null) {
                    try {
                        byte[] imageData = minioService.ReadFromMinIO(donorPhysicalDTO.getPictureUrl(), null);
                        String base64Image = Base64.getEncoder().encodeToString(imageData);
                        donorPhysicalDTO.setPicture64(base64Image);
                    } catch (TechnicalException ex) {
                        ex.printStackTrace();
                    }
                }

                TypeIdentityDTO typeIdentityDTO = donorPhysicalDTO.getTypeIdentity();
                if (typeIdentityDTO != null && typeIdentityDTO.getId() != null) {
                    Long typeIdentityId = typeIdentityDTO.getId();
                    TypeIdentityDTO fullTypeIdentityDTO = refController.getParTypeIdentity(typeIdentityId).getBody();
                    donorPhysicalDTO.setTypeIdentity(fullTypeIdentityDTO);
                }
            }


        }
    }

    public List<TakenInChargeOperationDTO> planTakenInCharge(List<TakenInChargeOperationDTO> takenInChargeOperationDTOList) throws TechnicalException {
        List<TakenInChargeOperationDTO> newOperationDTOs = new ArrayList<>();
        Double totalPlanned = 0.0;
        Double balance;

        List<TakenInChargeOperation> takenInChargeOperations = (List<TakenInChargeOperation>) takenInChargeOperationMapper.takenInChargeOperationDTOToTakenInChargeOperation(takenInChargeOperationDTOList);
        if (takenInChargeOperations.get(0) != null && takenInChargeOperations.get(0).getTakenInChargeDonor() != null) {
            // we should get the take incharg en toty and make her stats to Actif
            TakenInCharge takenInCharge = takenInChargeRepository.findByTakenInChargeDonorsId(takenInChargeOperations.get(0).getTakenInChargeDonor().getId());
            if (takenInCharge == null) {
                throw new TechnicalException(messages.get(TAKEN_IN_CHARGE_NOT_FOUND));
            }
            List<TakenInChargeBeneficiary> takenInChargeBeneficiary=takenInChargeBeneficiaryRepository.findByTakenInChargeId(takenInCharge.getId());
            takenInCharge.setStatus("Actif");
            takenInChargeRepository.save(takenInCharge);
            Optional<TakenInChargeDonor> takenInChargeDonorOptional = takenInChargeDonorRepository.findById(takenInChargeOperations.get(0).getTakenInChargeDonor().getId());
            if (!takenInChargeDonorOptional.isPresent()) {
                throw new TechnicalException(messages.get(TAKEN_IN_CHARGE_DONOR_NOT_FOUND));
            }
            TakenInChargeDonor takenInChargeDonor = takenInChargeDonorOptional.get();

            Long idDonor = takenInChargeDonor.getDonor().getId();

            Optional<Donor> donorOptional = donorRepository.findById(idDonor);
            if (!donorOptional.isPresent()) {
                throw new TechnicalException(messages.get(DONOR_NOT_FOUND));
            }
            Donor donor = donorOptional.get();
            balance = donor.getBalance();

            for (TakenInChargeOperation operation : takenInChargeOperations) {

                totalPlanned += (operation.getAmount());
            }
            donor.setBalance(balance - totalPlanned);
            donorRepository.save(donor);

            List<TakenInChargeOperation> newOperations = takenInChargeOperations.stream().map(operation -> {
                //Generate code
                TakenInChargeOperation takenInChargeOperation1 = operation;
                takenInChargeOperation1.setTakenInChargeDonor(takenInChargeDonor);
                String code = generateOperationCode(takenInChargeOperation1);
                operation.setCode(code);
                operation.setStatus("Planifié");
                TakenInChargeOperation takenInChargeOperation = takenInChargeOperationRepository.save(operation);
                createOperationHistory(takenInChargeOperation, "Planification");
                return takenInChargeOperation;
            }).collect(Collectors.toList());
            newOperationDTOs = (List<TakenInChargeOperationDTO>) takenInChargeOperationMapper.takenInChargeOperationToTakenInChargeOperationDTO(newOperations);
            Map<String ,String> params=new HashMap<>();
            params.put("service",takenInCharge.getService().getName());
            params.put("donorCode",donor.getCode());
            params.put("takeInChargeCode",takenInCharge.getCode());
            params.put("beneficiary",takenInChargeBeneficiary.get(0).getBeneficiary().getCode()+" - "+takenInChargeBeneficiary.get(0).getBeneficiary().getPerson().getLastName()+" "+takenInChargeBeneficiary.get(0).getBeneficiary().getPerson().getFirstName());
            params.put("size", String.valueOf(newOperations.size()));
            String audit =newOperations.get(0).getAudit(params);
            auditApplicationService.audit("Ajout d'une opération de prise en charge : " + takenInCharge.getCode(), getUsernameFromJwt(), "Add New TakeInCharge",
                    null, audit, PRISEENCHARGE, CREATE);
        }
        return newOperationDTOs;
    }

    public String generateOperationCode(TakenInChargeOperation takenInChargeOperation) {
        String code = "";
        if (takenInChargeOperation.getTakenInChargeDonor().getTakenInCharge().getCode() != null && takenInChargeOperation.getCode() == null) {
            // Fetch all operations for the donor
            List<TakenInChargeOperation> takenInChargeOperations = takenInChargeOperationRepository.findTakenInChargeOperationsByTakenInChargeDonor(
                    takenInChargeOperation.getTakenInChargeDonor());

            // Find the maximum sequence number already used
            int maxSequence = takenInChargeOperations.stream()
                    .map(op -> {
                        String operationCode = op.getCode();
                        // Extract the numeric part of the code (e.g., "O004")
                        String numericPart = operationCode.substring(1, 4);
                        return Integer.parseInt(numericPart);
                    })
                    .max(Integer::compareTo)
                    .orElse(0); // Default to 0 if no operations exist

            // Increment the sequence number for the new code
            int nextSequence = maxSequence + 1;
            String zeros = String.format("%03d", nextSequence); // Ensure 3-digit padding

            // Generate the new code
            code = "O" + zeros + takenInChargeOperation.getTakenInChargeDonor().getTakenInCharge().getCode();
        } else if (takenInChargeOperation.getCode() != null
                && !takenInChargeOperation.getCode().substring(3).equals(takenInChargeOperation.getTakenInChargeDonor().getTakenInCharge().getCode())) {
            // Update code if it doesn't match the donor's taken-in-charge code
            code = takenInChargeOperation.getCode().substring(0, 4)
                    + takenInChargeOperation.getTakenInChargeDonor().getTakenInCharge().getCode();
        } else {
            // Use existing code if it's already valid
            code = takenInChargeOperation.getCode();
        }

        return code;
    }


    public TakenInChargeOperationDTO updateOperation(TakenInChargeOperationDTO takenInChargeOperationDTO) throws TechnicalException {
        TakenInChargeOperation takenInChargeOperation = takenInChargeOperationMapper.takenInChargeOperationDTOToTakenInChargeOperation(takenInChargeOperationDTO);
        Optional<TakenInChargeOperation> optional = takenInChargeOperationRepository.findById(takenInChargeOperation.getId());
        if (optional.isEmpty()) {
            throw new TechnicalException(messages.get(TAKEN_IN_CHARGE_OPERATION_NOT_FOUND));
        }
        Map<String ,String> params=new HashMap<>();
        TakenInCharge takenInCharge = takenInChargeRepository.findByTakenInChargeDonorsId(takenInChargeOperationDTO.getTakenInChargeDonor().getId());
        Optional<TakenInChargeDonor> takenInChargeDonorOptional = takenInChargeDonorRepository.findById(takenInChargeOperationDTO.getTakenInChargeDonor().getId());
        if (!takenInChargeDonorOptional.isPresent()) {
            throw new TechnicalException(messages.get(TAKEN_IN_CHARGE_DONOR_NOT_FOUND));
        }
        TakenInChargeDonor takenInChargeDonor = takenInChargeDonorOptional.get();

        Long idDonor = takenInChargeDonor.getDonor().getId();

        Donor donor = donorRepository.findById(idDonor).get();
        List<TakenInChargeBeneficiary> takenInChargeBeneficiary=takenInChargeBeneficiaryRepository.findByTakenInChargeId(takenInCharge.getId());

        params.put("service",takenInCharge.getService().getName());
        params.put("donorCode",donor.getCode());
        params.put("takeInChargeCode",takenInCharge.getCode());
        params.put("beneficiary",takenInChargeBeneficiary.get(0).getBeneficiary().getCode()+" - "+takenInChargeBeneficiary.get(0).getBeneficiary().getPerson().getLastName()+" "+takenInChargeBeneficiary.get(0).getBeneficiary().getPerson().getFirstName());
        String oldAudit=optional.get().getAudit(params);
        LocalDateTime newPlanningDate = toLocalDateTime(takenInChargeOperation.getPlanningDate());
        LocalDateTime existingPlanningDate = toLocalDateTime(optional.get().getPlanningDate());
        if ((takenInChargeOperation.getAmount() != optional.get().getAmount()) || !newPlanningDate.equals(existingPlanningDate) || (takenInChargeOperation.getManagementFees() != optional.get().getManagementFees())) {
            createOperationHistory(takenInChargeOperation, "Modification");
        }
        takenInChargeOperation.setTakenInChargeDonor(optional.get().getTakenInChargeDonor());
        TakenInChargeOperation updated = takenInChargeOperationRepository.save(takenInChargeOperation);

        String audit =updated.getAudit(params);
        auditApplicationService.audit("Modification d'une opération de prise en charge : " + takenInCharge.getCode(), getUsernameFromJwt(), "Update New TakeInCharge",
                oldAudit, audit, PRISEENCHARGE, UPDATE);
        return takenInChargeOperationMapper.takenInChargeOperationToTakenInChargeOperationDTO(updated);
    }

    public TakenInChargeOperationDTO reserveOperation(TakenInChargeOperationDTO takenInChargeOperationDTO) throws TechnicalException {
        // Retrieve the operation from DB
        Optional<TakenInChargeOperation> optional = takenInChargeOperationRepository.findById(takenInChargeOperationDTO.getId());
        if (optional.isEmpty()) {
            throw new TechnicalException(messages.get(TAKEN_IN_CHARGE_OPERATION_NOT_FOUND));
        }
        TakenInChargeOperation operation = optional.get();

        // Check if any relevant information has changed in the DTO
        if (operation.getAmount() != takenInChargeOperationDTO.getAmount() ||
                operation.getManagementFees() != takenInChargeOperationDTO.getManagementFees() ||
                !Objects.equals(operation.getComment(), takenInChargeOperationDTO.getComment())) {

            // If changes are found, update the operation and create a history entry
            operation.setAmount(takenInChargeOperationDTO.getAmount());
            operation.setManagementFees(takenInChargeOperationDTO.getManagementFees());
            operation.setComment(takenInChargeOperationDTO.getComment());
            createOperationHistory(operation, "Modification");
            takenInChargeOperationRepository.save(operation);
        }

        // Check if donor has enough balance for the required amount
        Double donorBalance = takenInChargeDonorRepository.findAvailableBalanceByDonorAndService(
                operation.getTakenInChargeDonor().getDonor().getId(),
                operation.getTakenInChargeDonor().getTakenInCharge().getService().getId()
        );
        Double requiredAmount = operation.getAmount();
        if (donorBalance < requiredAmount) {
            throw new TechnicalException(messages.get(DONOR_INSUFFICIENT_BALANCE));
        }

        // Retrieve all the available budget lines for the donor and service
        Donor donor = operation.getTakenInChargeDonor().getDonor();
        List<Donation> donations = donationRepository.findByDonorId(donor.getId());
        List<BudgetLine> budgetLines = new ArrayList<>();

        // Collect budget lines that are available and match the service
        for (Donation donation : donations) {
            budgetLines.addAll(budgetLineRepository.findByDonationIdAndServiceIdAndStatus(
                    donation.getId(),
                    operation.getTakenInChargeDonor().getTakenInCharge().getService().getId(),
                    BudgetLineStatus.DISPONIBLE
            ));
        }

        // Reserve the necessary budget lines
        Double accumulatedAmount = 0.0;
        for (BudgetLine budgetLine : budgetLines) {
            if (accumulatedAmount >= requiredAmount) break;

            Double remainingAmountNeeded = requiredAmount - accumulatedAmount;

            if (budgetLine.getAmount() <= remainingAmountNeeded) {
                // Fully use the budget line
                accumulatedAmount += budgetLine.getAmount();
                budgetLine.setStatus(BudgetLineStatus.RESERVED);
                budgetLine.setExecutionDate(LocalDateTime.now());
                budgetLine.setTakenInChargeOperation(operation);
            } else {
                // Partially use the budget line
                accumulatedAmount += remainingAmountNeeded;

                // Create a new budget line with the remaining balance
                BudgetLine partialBudgetLine = new BudgetLine();
                partialBudgetLine.setAmount(budgetLine.getAmount() - remainingAmountNeeded);
                partialBudgetLine.setDonation(budgetLine.getDonation());
                partialBudgetLine.setService(budgetLine.getService());
                partialBudgetLine.setStatus(BudgetLineStatus.DISPONIBLE);
                partialBudgetLine.setType(budgetLine.getType());
                partialBudgetLine.setProductsCategory(budgetLine.getProductsCategory());
                partialBudgetLine.setNatureBudgetLine(budgetLine.getNatureBudgetLine());
                partialBudgetLine.setCreatedAt(LocalDateTime.now());
                partialBudgetLine.setCode(donationService.generateBudgetLineCode(partialBudgetLine));
                budgetLineRepository.save(partialBudgetLine);

                // Update the original budget line to reflect the reserved amount
                budgetLine.setAmount(remainingAmountNeeded);
                budgetLine.setAmountByBeneficiary(null);
                budgetLine.setStatus(BudgetLineStatus.RESERVED);
                budgetLine.setTakenInChargeOperation(operation);
                budgetLine.setExecutionDate(LocalDateTime.now());
            }
            budgetLineRepository.save(budgetLine);
        }

        Map<String ,String> params=new HashMap<>();
        TakenInCharge takenInCharge = takenInChargeRepository.findByTakenInChargeDonorsId(takenInChargeOperationDTO.getTakenInChargeDonor().getId());
        Optional<TakenInChargeDonor> takenInChargeDonorOptional = takenInChargeDonorRepository.findById(takenInChargeOperationDTO.getTakenInChargeDonor().getId());
        if (!takenInChargeDonorOptional.isPresent()) {
            throw new TechnicalException(messages.get(TAKEN_IN_CHARGE_DONOR_NOT_FOUND));
        }

        List<TakenInChargeBeneficiary> takenInChargeBeneficiary=takenInChargeBeneficiaryRepository.findByTakenInChargeId(takenInCharge.getId());

        params.put("service",takenInCharge.getService().getName());
        params.put("donorCode",donor.getCode());
        params.put("takeInChargeCode",takenInCharge.getCode());
        params.put("beneficiary",takenInChargeBeneficiary.get(0).getBeneficiary().getCode()+" - "+takenInChargeBeneficiary.get(0).getBeneficiary().getPerson().getLastName()+" "+takenInChargeBeneficiary.get(0).getBeneficiary().getPerson().getFirstName());
        String oldAudit=operation.getAudit(params);

        // Mark the operation as reserved and save it
        operation.setReserved(true);
        operation.setStatus("Réservé");
        takenInChargeOperationRepository.save(operation);


        String audit=operation.getAudit(params);

        auditApplicationService.audit("Réservation d'une opération de prise en charge : " + takenInCharge.getCode(), getUsernameFromJwt(), "Update New TakeInCharge",
                oldAudit, audit, PRISEENCHARGE, UPDATE);

        // Log the reservation history
        createOperationHistory(operation, "Réservation");

        // Return the DTO of the updated operation
        return takenInChargeOperationMapper.takenInChargeOperationToTakenInChargeOperationDTO(operation);
    }


    public TakenInChargeOperationDTO cancelReservationOperation(TakenInChargeOperationDTO takenInChargeOperationDTO) throws TechnicalException {
        Optional<TakenInChargeOperation> optional = takenInChargeOperationRepository.findById(takenInChargeOperationDTO.getId());
        if (optional.isEmpty()) {
            throw new TechnicalException(messages.get(TAKEN_IN_CHARGE_OPERATION_NOT_FOUND));
        }

        TakenInChargeOperation operation = optional.get();
        List<BudgetLine> budgetLines = budgetLineRepository.findByTakenInChargeOperationId(operation.getId());

        for (BudgetLine budgetLine : budgetLines) {
            // Check for an existing budget line with the same service and status DISPONIBLE
            BudgetLine existingBudgetLine = budgetLineRepository.findFirstByDonationIdAndServiceIdAndStatus(
                    budgetLine.getDonation().getId(),
                    budgetLine.getService().getId(),
                    BudgetLineStatus.DISPONIBLE
            );

            if (existingBudgetLine != null) {
                // Merge amounts
                existingBudgetLine.setAmount(existingBudgetLine.getAmount() + budgetLine.getAmount());
                budgetLineRepository.save(existingBudgetLine);
                // Delete the canceled budget line
                budgetLineRepository.delete(budgetLine);
            } else {
                // Revert to DISPONIBLE
                budgetLine.setStatus(BudgetLineStatus.DISPONIBLE);
                budgetLine.setTakenInChargeOperation(null);
                budgetLine.setExecutionDate(null);
                budgetLineRepository.save(budgetLine);
            }
        }
        Donor donor = operation.getTakenInChargeDonor().getDonor();

        Map<String ,String> params=new HashMap<>();
        TakenInCharge takenInCharge = takenInChargeRepository.findByTakenInChargeDonorsId(operation.getTakenInChargeDonor().getId());
        Optional<TakenInChargeDonor> takenInChargeDonorOptional = takenInChargeDonorRepository.findById(operation.getTakenInChargeDonor().getId());
        if (!takenInChargeDonorOptional.isPresent()) {
            throw new TechnicalException(messages.get(TAKEN_IN_CHARGE_DONOR_NOT_FOUND));
        }

        List<TakenInChargeBeneficiary> takenInChargeBeneficiary=takenInChargeBeneficiaryRepository.findByTakenInChargeId(takenInCharge.getId());

        params.put("service",takenInCharge.getService().getName());
        params.put("donorCode",donor.getCode());
        params.put("takeInChargeCode",takenInCharge.getCode());
        params.put("beneficiary",takenInChargeBeneficiary.get(0).getBeneficiary().getCode()+" - "+takenInChargeBeneficiary.get(0).getBeneficiary().getPerson().getLastName()+" "+takenInChargeBeneficiary.get(0).getBeneficiary().getPerson().getFirstName());
        String oldAudit=operation.getAudit(params);

        operation.setReserved(false);
        operation.setStatus("Planifié");
        takenInChargeOperationRepository.save(operation);
        String audit=operation.getAudit(params);

        auditApplicationService.audit("Annulation de la réservation d'une opération de prise en charge: " + takenInCharge.getCode(), getUsernameFromJwt(), "Update New TakeInCharge",
                oldAudit, audit, PRISEENCHARGE, UPDATE);

        createOperationHistory(operation, "Réservation annulée");

        return takenInChargeOperationMapper.takenInChargeOperationToTakenInChargeOperationDTO(operation);
    }

    // let'as add the funtions of cancelExecution in this case we gonna just got tothe operation exectured and make them reserved same for the budgetLine
    public TakenInChargeOperationDTO cancelExecutionOperation(TakenInChargeOperationDTO takenInChargeOperationDTO) throws TechnicalException {
        // Fetch the operation by its ID, throwing an exception if not found
        Optional<TakenInChargeOperation> optional = takenInChargeOperationRepository.findById(takenInChargeOperationDTO.getId());

        if (optional.isEmpty()) {
            throw new TechnicalException(messages.get(TAKEN_IN_CHARGE_OPERATION_NOT_FOUND));
        }

        TakenInChargeOperation operation = optional.get();

        // Fetch the associated budget lines for the given operation
        List<BudgetLine> budgetLines = budgetLineRepository.findByTakenInChargeOperationId(operation.getId());

        // Iterate through each budget line and reset execution-related values
        for (BudgetLine budgetLine : budgetLines) {
            // Set status to RESERVED and clear execution date
            budgetLine.setStatus(BudgetLineStatus.RESERVED);
            budgetLine.setExecutionDate(LocalDateTime.now());

            // Save the updated budget line
            budgetLineRepository.save(budgetLine);
        }
        Donor donor = operation.getTakenInChargeDonor().getDonor();

        Map<String ,String> params=new HashMap<>();
        TakenInCharge takenInCharge = takenInChargeRepository.findByTakenInChargeDonorsId(operation.getTakenInChargeDonor().getId());
        Optional<TakenInChargeDonor> takenInChargeDonorOptional = takenInChargeDonorRepository.findById(operation.getTakenInChargeDonor().getId());
        if (!takenInChargeDonorOptional.isPresent()) {
            throw new TechnicalException(messages.get(TAKEN_IN_CHARGE_DONOR_NOT_FOUND));
        }

        List<TakenInChargeBeneficiary> takenInChargeBeneficiary=takenInChargeBeneficiaryRepository.findByTakenInChargeId(takenInCharge.getId());

        params.put("service",takenInCharge.getService().getName());
        params.put("donorCode",donor.getCode());
        params.put("takeInChargeCode",takenInCharge.getCode());
        params.put("beneficiary",takenInChargeBeneficiary.get(0).getBeneficiary().getCode()+" - "+takenInChargeBeneficiary.get(0).getBeneficiary().getPerson().getLastName()+" "+takenInChargeBeneficiary.get(0).getBeneficiary().getPerson().getFirstName());
        String oldAudit=operation.getAudit(params);
        // Set the operation's execution date to null and save it
        operation.setExecutionDate(null);
        operation.setStatus("Réservé");
        takenInChargeOperationRepository.save(operation);
        String audit=operation.getAudit(params);

        auditApplicationService.audit("Annulation de l'exécution d'une opération de prise en charge : " + takenInCharge.getCode(), getUsernameFromJwt(), "Update New TakeInCharge",
                oldAudit, audit, PRISEENCHARGE, UPDATE);
        // Record the action in the operation history
        createOperationHistory(operation, "Exécution annulée");

        // Return the updated operation data
        return takenInChargeOperationMapper.takenInChargeOperationToTakenInChargeOperationDTO(operation);
    }

    public TakenInChargeOperationDTO encloseOperation(TakenInChargeOperationDTO takenInChargeOperationDTO) throws TechnicalException {
        Optional<TakenInChargeOperation> optional = takenInChargeOperationRepository.findById(takenInChargeOperationDTO.getId());

        if (optional.isEmpty()) {
            throw new TechnicalException(messages.get(TAKEN_IN_CHARGE_OPERATION_NOT_FOUND));
        }
        var operation = optional.get();
        Donor donor = operation.getTakenInChargeDonor().getDonor();

        Map<String ,String> params=new HashMap<>();
        TakenInCharge takenInCharge = takenInChargeRepository.findByTakenInChargeDonorsId(operation.getTakenInChargeDonor().getId());
        Optional<TakenInChargeDonor> takenInChargeDonorOptional = takenInChargeDonorRepository.findById(operation.getTakenInChargeDonor().getId());
        if (!takenInChargeDonorOptional.isPresent()) {
            throw new TechnicalException(messages.get(TAKEN_IN_CHARGE_DONOR_NOT_FOUND));
        }

        List<TakenInChargeBeneficiary> takenInChargeBeneficiary=takenInChargeBeneficiaryRepository.findByTakenInChargeId(takenInCharge.getId());

        params.put("service",takenInCharge.getService().getName());
        params.put("donorCode",donor.getCode());
        params.put("takeInChargeCode",takenInCharge.getCode());
        params.put("beneficiary",takenInChargeBeneficiary.get(0).getBeneficiary().getCode()+" - "+takenInChargeBeneficiary.get(0).getBeneficiary().getPerson().getLastName()+" "+takenInChargeBeneficiary.get(0).getBeneficiary().getPerson().getFirstName());
        String oldAudit=operation.getAudit(params);
        optional.get().setClosureDate(takenInChargeOperationDTO.getClosureDate());
        optional.get().setStatus("Clôturé");
        createOperationHistory(optional.get(), "Clôture");

        TakenInChargeOperation updated = takenInChargeOperationRepository.save(optional.get());
        String audit=operation.getAudit(params);

        auditApplicationService.audit("Clôture d'une opération de prise en charge : " + takenInCharge.getCode(), getUsernameFromJwt(), "Update New TakeInCharge",
                oldAudit, audit, PRISEENCHARGE, UPDATE);
        return takenInChargeOperationMapper.takenInChargeOperationToTakenInChargeOperationDTO(updated);
    }

    public void deleteOperation(Long idOperation) throws TechnicalException {
        Optional<TakenInChargeOperation> existing = takenInChargeOperationRepository.findById(idOperation);

        if (existing.isEmpty()) {
            throw new TechnicalException(messages.get(TAKEN_IN_CHARGE_OPERATION_NOT_FOUND));
        }
        // Get the associated TakenInCharge ID before deletion
        TakenInChargeOperation operation = existing.get();
        Long takenInChargeId = operation.getTakenInChargeDonor().getTakenInCharge().getId();
        // Delete the operation
        takenInChargeOperationRepository.deleteById(idOperation);

        Donor donor = operation.getTakenInChargeDonor().getDonor();

        Map<String ,String> params=new HashMap<>();
        TakenInCharge takenInCharge1 = takenInChargeRepository.findByTakenInChargeDonorsId(operation.getTakenInChargeDonor().getId());
        Optional<TakenInChargeDonor> takenInChargeDonorOptional = takenInChargeDonorRepository.findById(operation.getTakenInChargeDonor().getId());
        if (!takenInChargeDonorOptional.isPresent()) {
            throw new TechnicalException(messages.get(TAKEN_IN_CHARGE_DONOR_NOT_FOUND));
        }

        List<TakenInChargeBeneficiary> takenInChargeBeneficiary=takenInChargeBeneficiaryRepository.findByTakenInChargeId(takenInCharge1.getId());

        params.put("service",takenInCharge1.getService().getName());
        params.put("donorCode",donor.getCode());
        params.put("takeInChargeCode",takenInCharge1.getCode());
        params.put("beneficiary",takenInChargeBeneficiary.get(0).getBeneficiary().getCode()+" - "+takenInChargeBeneficiary.get(0).getBeneficiary().getPerson().getLastName()+" "+takenInChargeBeneficiary.get(0).getBeneficiary().getPerson().getFirstName());
        String oldAudit=operation.getAudit(params);

        auditApplicationService.audit("Suppression d'une opération de prise en charge : " + takenInCharge1.getCode(), getUsernameFromJwt(), "Update New TakeInCharge",
                oldAudit, null, PRISEENCHARGE, DELETE);
        // Check if any operations remain for the TakenInCharge
        long remainingOperations = takenInChargeOperationRepository.countByTakenInChargeDonorId(operation.getTakenInChargeDonor().getId());

        if (remainingOperations == 0) {
            // Update the status of TakenInCharge to "Inactif"
            Optional<TakenInCharge> takenInChargeOptional = takenInChargeRepository.findById(takenInChargeId);
            if (takenInChargeOptional.isPresent()) {
                TakenInCharge takenInCharge = takenInChargeOptional.get();
                takenInCharge.setStatus("Inactif");
                takenInChargeRepository.save(takenInCharge);
            }
        }
    }

    public List<TakenInChargeOperationDTO> handleBatchOperation(List<Long> operationIds, String actionType) throws TechnicalException {
        List<TakenInChargeOperationDTO> updatedOperations = new ArrayList<>();
        LocalDateTime currentDate = LocalDateTime.now();
        Date closureDate = Date.from(currentDate.atZone(ZoneId.systemDefault()).toInstant());

        for (Long id : operationIds) {
            Optional<TakenInChargeOperation> optional = takenInChargeOperationRepository.findById(id);

            if (optional.isEmpty()) {
                throw new TechnicalException("TAKEN_IN_CHARGE_OPERATION_NOT_FOUND");
            }

            TakenInChargeOperation operation = optional.get();
            TakenInChargeOperationDTO operationDTO = takenInChargeOperationMapper.takenInChargeOperationToTakenInChargeOperationDTO(operation);
            switch (actionType) {
                case "reserve":
                    reserveOperation(operationDTO);
                    break;
                case "cancelReservation":
                    cancelReservationOperation(operationDTO);
                    break;
                case "execute":
                    executeOperation(operationDTO);
                    break;
                case "cancelExecution":
                    cancelExecutionOperation(operationDTO);
                    break;
                case "enclose":
                    operationDTO.setClosureDate(closureDate);
                    encloseOperation(operationDTO);
                    break;
                case "delete":
                    deleteOperation(operation.getId());
                    break;
                default:
                    throw new TechnicalException("Invalid action type: " + actionType);
            }

            updatedOperations.add(operationDTO);
        }
        return updatedOperations;
    }

    public void createOperationHistory(TakenInChargeOperation operation, String action) {
        TakenInChargeOperationHistorique history = new TakenInChargeOperationHistorique();
        history.setAction(action);
        history.setAmount(operation.getAmount());
        history.setUserName(getUsernameFromJwt());
        history.setOperation(operation);
        history.setManagementFees(operation.getManagementFees());
        takenInChargeOperationHistoriqueRepository.save(history);
    }

    public TakenInChargeOperationDTO executeOperation(TakenInChargeOperationDTO takenInChargeOperationDTO) throws TechnicalException {
        Optional<TakenInChargeOperation> optional = takenInChargeOperationRepository.findById(takenInChargeOperationDTO.getId());

        if (optional.isEmpty()) {
            throw new TechnicalException(messages.get(TAKEN_IN_CHARGE_OPERATION_NOT_FOUND));
        }

        // get the list of the budget lines reserved for this operation and set them to Executed
        List<BudgetLine> budgetLines = budgetLineRepository.findByTakenInChargeOperationId(optional.get().getId());
        for (BudgetLine budgetLine : budgetLines) {
            budgetLine.setStatus(BudgetLineStatus.EXECUTED);
            LocalDateTime executionDate = null ;
            if(takenInChargeOperationDTO.getExecutionDate() != null) {
                executionDate  = toLocalDateTime(takenInChargeOperationDTO.getExecutionDate());
            }
            else if(takenInChargeOperationDTO.getPlanningDate() != null) {
                executionDate = toLocalDateTime(takenInChargeOperationDTO.getPlanningDate());
            }
            else {
                executionDate = LocalDateTime.now();
            }
            budgetLine.setExecutionDate(executionDate);
            budgetLineRepository.save(budgetLine);
        }
        if (takenInChargeOperationDTO.getExecutionDate() != null) {
            optional.get().setExecutionDate(takenInChargeOperationDTO.getExecutionDate());
        } else {
            optional.get().setExecutionDate(optional.get().getPlanningDate());
        }
        var operation=optional.get();
        Donor donor = operation.getTakenInChargeDonor().getDonor();

        Map<String ,String> params=new HashMap<>();
        TakenInCharge takenInCharge = takenInChargeRepository.findByTakenInChargeDonorsId(operation.getTakenInChargeDonor().getId());
        Optional<TakenInChargeDonor> takenInChargeDonorOptional = takenInChargeDonorRepository.findById(operation.getTakenInChargeDonor().getId());
        if (!takenInChargeDonorOptional.isPresent()) {
            throw new TechnicalException(messages.get(TAKEN_IN_CHARGE_DONOR_NOT_FOUND));
        }

        List<TakenInChargeBeneficiary> takenInChargeBeneficiary=takenInChargeBeneficiaryRepository.findByTakenInChargeId(takenInCharge.getId());

        params.put("service",takenInCharge.getService().getName());
        params.put("donorCode",donor.getCode());
        params.put("takeInChargeCode",takenInCharge.getCode());
        params.put("beneficiary",takenInChargeBeneficiary.get(0).getBeneficiary().getCode()+" - "+takenInChargeBeneficiary.get(0).getBeneficiary().getPerson().getLastName()+" "+takenInChargeBeneficiary.get(0).getBeneficiary().getPerson().getFirstName());
        String oldAudit=operation.getAudit(params);
        // Set the operation's execution date to null and save it
        optional.get().setStatus("Exécuté");
        createOperationHistory(optional.get(), "Exécution");
        TakenInChargeOperation updated = takenInChargeOperationRepository.save(optional.get());
        String audit=operation.getAudit(params);

        auditApplicationService.audit(" Exécution d'une opération de prise en charge: " + takenInCharge.getCode(), getUsernameFromJwt(), "Update New TakeInCharge",
                oldAudit, audit, PRISEENCHARGE, UPDATE);
        return takenInChargeOperationMapper.takenInChargeOperationToTakenInChargeOperationDTO(updated);
    }

    public void closeTakenInCharge(Long id, String clotureMotif, Long motifTypeId) throws EntityNotFoundException, InvalidOperationException {
        // Fetch the TakenInCharge entity or throw an exception if not found
        TakenInCharge takenInCharge = takenInChargeRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("TakenInCharge not found"));

        // Check if the donor exists and has operations
        List<TakenInChargeDonor> donors = takenInCharge.getTakenInChargeDonors();
        if (donors == null || donors.isEmpty()) {
            throw new InvalidOperationException("No donor associated with this TakenInCharge.");
        }

        TakenInChargeDonor donor = donors.get(0);  // Assuming only one donor for simplicity
        List<TakenInChargeOperation> operations = donor.getTakenInChargeOperations();

        // Validate that all operations are completed
        boolean canBeClosed = operations == null || operations.stream()
                .allMatch(operation -> operation.getExecutionDate() != null);

        if (!canBeClosed) {
            throw new InvalidOperationException("Cannot close this TakenInCharge. Some operations are incomplete.");
        }

        // Update the status and save changes
        takenInCharge.setStatus("Fermé");
        takenInCharge.setClotureMotifTypeId(motifTypeId);
        takenInCharge.setClotureMotif(clotureMotif);
        takenInCharge.setEndDate(new Date());
        takenInChargeRepository.save(takenInCharge);

        String donorName = getDonorName(takenInCharge.getTakenInChargeDonors().get(0).getDonor());
        String motifType ="";
        try {
             motifType = referentialService.findMotifTypeById(motifTypeId).getName();
        } catch (TechnicalException e) {
            throw new RuntimeException(e);
        }
        String auditCloture = takenInCharge.getAuditCloture(motifType,donorName);
        auditApplicationService.audit("Fermeture de la prise en charge : "+takenInCharge.getCode(),getUsernameFromJwt(),"Fermeture de la prise en charge",auditCloture,auditCloture,PRISEENCHARGE,UPDATE);
    }


    private String convertMapToJsonString(Map<String, String> map) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.writeValueAsString(map);
        } catch (Exception e) {
            // Gérer les exceptions de sérialisation JSON ici
            e.printStackTrace();
            return "{}"; // Retourne une chaîne JSON vide en cas d'erreur
        }
    }

    public Page<TakenInChargeDTO> getAllTakenInChargesByCriteria(Integer page, Integer size, String searchByNom, String searchByBeneficiary, String searchByBeneficiaryAr, String lastNameAr, Long searchByService, String searchByStatus, java.util.Date minStartDate, java.util.Date maxStartDate, java.util.Date minEndDate, java.util.Date maxEndDate,Long searchByTagId) {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service getAllTakenInCharges with criteria: {} and value1: {} and value2: {}", searchByNom, lastNameAr, searchByService, searchByStatus, minStartDate, maxStartDate, minEndDate, maxEndDate);

        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdAt"));

        Map<String, String> searchParams = new HashMap<>();
        if (searchByNom != null) {
            searchParams.put("Nom du Donateur", searchByNom);
        }
        if (searchByBeneficiary != null) {
            searchParams.put("Nom du bénéficiaire", searchByBeneficiary);
        }
        if (searchByBeneficiaryAr != null) {
            searchParams.put("Nom arabe du bénéficiaire", searchByBeneficiaryAr);
        }
        if (lastNameAr != null) {
            searchParams.put("Nom arabe du Donateur", lastNameAr);
        }
        if (searchByService != null) {
            searchParams.put("Service", String.valueOf(searchByService));
        }
        if (searchByStatus != null) {
            searchParams.put("Statut", searchByStatus);
        }
        if (minStartDate != null) {
            searchParams.put("Date de debut minimale", String.valueOf(minStartDate));
        }
        if (maxStartDate != null) {
            searchParams.put("Date de debut maximale", String.valueOf(searchByStatus));
        }
        if (minEndDate != null) {
            searchParams.put("Date de fin minimale", String.valueOf(minEndDate));
        }
        if (maxEndDate != null) {
            searchParams.put("Date de fin maximale", String.valueOf(maxEndDate));
        }

        String jsonSearchParams = convertMapToJsonString(searchParams);

        Page<TakenInCharge> listTakenInCharges;
        if (searchByNom != null || searchByBeneficiary != null || searchByBeneficiaryAr != null || lastNameAr != null || searchByService != null || searchByStatus != null || minStartDate != null || maxStartDate != null || minEndDate != null || maxEndDate != null || searchByTagId != null) {
            listTakenInCharges = filterTakenInCharges(searchByNom, searchByBeneficiary, lastNameAr, searchByBeneficiaryAr, searchByService, searchByStatus, minStartDate, maxStartDate, minEndDate, maxEndDate,searchByTagId, pageable);
            auditApplicationService.audit("Recherche par filtre dans la liste des Prises en charge", getUsernameFromJwt(), "Liste des Prises en charge",
                    jsonSearchParams, null, PRISEENCHARGE, VIEW);
        } else {
            listTakenInCharges = takenInChargeRepository.findAll(pageable);
            auditApplicationService.audit("Consultation de la liste des Prises en charge globale", getUsernameFromJwt(), "Liste des Prises en charge",
                    null, null, PRISEENCHARGE, CONSULTATION);
        }
        List<TakenInChargeDTO> akenInChargeDTOList = listTakenInCharges.getContent().stream()
                .map(takenInCharge -> {
                    TakenInChargeDTO takenInChargeDTO = takenInChargeMapper.takenInChargeToTakenInChargeDTOForList(takenInCharge);
                    setDonorName(takenInCharge, takenInChargeDTO);
                    setFullServiceAndStatus(takenInChargeDTO);
                    return takenInChargeDTO;
                })
                .collect(Collectors.toList());
        akenInChargeDTOList= akenInChargeDTOList.stream().peek(dto->{
            List<Taggable> taggables = taggableRepository.findByTaggableIdAndTaggableType(dto.getId(), "takenInCharge");
            List<TagDTO> tagDTOs = new ArrayList<>();
            for (Taggable taggable : taggables) {
                Tag tag = taggable.getTag();
                TagDTO tagDTO = new TagDTO();
                tagDTO.setId(tag.getId());
                tagDTO.setName(tag.getName());
                tagDTO.setColor(tag.getColor());

                tagDTOs.add(tagDTO);
            }
            dto.setTags(tagDTOs);
        }).collect(Collectors.toList());
        log.debug("End service getAllTakenInCharges with a total of: {}, took {}", akenInChargeDTOList.size(), watch.toMS());
        return new PageImpl<>(akenInChargeDTOList, pageable, listTakenInCharges.getTotalElements());
    }

    public Page<TakenInCharge> filterTakenInCharges(
            String searchByNom, String searchByBeneficiary, String searchByBeneficiaryAr, String lastNameAr,
            Long searchByService, String searchByStatus, Date minStartDate, Date maxStartDate, Date minEndDate, Date maxEndDate,Long searchByTagId,
            Pageable pageable) {

        CriteriaQuery<TakenInCharge> criteriaQuery = createCriteriaQuery(
                searchByNom, searchByBeneficiary, searchByBeneficiaryAr, lastNameAr,
                searchByService, searchByStatus, minStartDate, maxStartDate, minEndDate, maxEndDate,searchByTagId
        );
        TypedQuery<TakenInCharge> typedQuery = entityManager.createQuery(criteriaQuery);

        long totalCount = typedQuery.getResultList().size();
        typedQuery.setFirstResult((int) pageable.getOffset());
        typedQuery.setMaxResults(pageable.getPageSize());

        List<TakenInCharge> resultList = typedQuery.getResultList();

        return new PageImpl<>(resultList, pageable, totalCount);
    }

    private Predicate addCaseInsensitiveLikePredicate(Predicate predicate, CriteriaBuilder criteriaBuilder, Expression<String> expression, String searchString) {
        return criteriaBuilder.and(predicate,
                criteriaBuilder.like(
                        criteriaBuilder.lower(expression),
                        "%" + searchString.toLowerCase() + "%"
                )
        );
    }

    @Transactional
    public void deleteTakenInCharge(Long id) throws TechnicalException {
        TakenInCharge takenInChargeTemp = takenInChargeRepository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("Prise en charge introuvable"));

        TakenInCharge takenInCharge = takenInChargeTemp;


        List<TakenInChargeOperation> operations = takenInChargeOperationRepository.findTakenInChargeOperationsByTakenInChargeDonor(takenInCharge.getTakenInChargeDonors().get(0));
        if (!operations.isEmpty()) {
            throw new IllegalStateException("Impossible de supprimer prise en charge avec les opérations existantes.");
        }

        List<NoteTakenInCharge> notes = noteTakenInChargeRepository.findByTakenInChargeId(id);
        noteTakenInChargeRepository.deleteAll(notes);

        List<DocumentTakenInCharge> documents = documentTakenInChargeRepository.findByTakenInChargeId(id);
        documentTakenInChargeRepository.deleteAll(documents);

        List<ActionTakenInCharge> actions = actionTakenInChargeRepository.findByTakenInChargeId(id);
        actionTakenInChargeRepository.deleteAll(actions);

        // Get all beneficiaries before deleting the associated TakenInChargeBeneficiaries
        List<TakenInChargeBeneficiary> beneficiaries = takenInChargeBeneficiaryRepository.findByTakenInChargeId(id);

        // Delete all associated TakenInChargeBeneficiaries
        takenInChargeBeneficiaryRepository.deleteAll(beneficiaries);

        // Delete all associated TakenInChargeDonors
        List<TakenInChargeDonor> donors = takenInChargeDonorRepository.findByTakenInChargeId(id);
        takenInChargeDonorRepository.deleteAll(donors);

        // Delete the TakenInCharge itself
        takenInChargeRepository.delete(takenInChargeTemp);

        auditApplicationService.audit("Suppression du take in charge : "+takenInCharge.getCode(),getUsernameFromJwt(),"suppression",takenInCharge.getAuditModification(getDonorName(takenInCharge.getTakenInChargeDonors().get(0).getDonor()),takenInCharge.getTakenInChargeBeneficiaries().get(0).getBeneficiary().getPerson().getFirstName()+" "+takenInCharge.getTakenInChargeBeneficiaries().get(0).getBeneficiary().getPerson().getLastName(),takenInCharge.getService().getName(),takenInCharge.getType(),takenInCharge.getCode()),null,TAKENINCHARGE,DELETE);

        // Update status of each beneficiary if they no longer have any active TakenInCharge
        for (TakenInChargeBeneficiary beneficiary : beneficiaries) {
            Long beneficiaryId = beneficiary.getBeneficiary().getId();
            updateBeneficiaryStatusIfNoTakenInCharge(beneficiaryId);
        }
    }

    private void updateBeneficiaryStatusIfNoTakenInCharge(Long beneficiaryId) throws TechnicalException {
        Beneficiary beneficiaryEntity = beneficiaryRepository.findById(beneficiaryId).orElse(null);

        if (beneficiaryEntity != null) {
            // Check if the beneficiary has any active TakenInCharge relationships
            boolean hasActiveTakenInCharge = takenInChargeBeneficiaryRepository.existsByBeneficiaryId(beneficiaryId);

            if (!hasActiveTakenInCharge) {
                // If no active TakenInCharge, set status to "en attente"
                beneficiaryEntity.setBeneficiaryStatut(new BeneficiaryStatut(BeneficiaryStatus.BENEFICIAIRE_EN_ATTENTE.getId()));
                beneficiaryRepository.save(beneficiaryEntity);
            }
        } else {
            throw new TechnicalException(messages.get(TAKEN_IN_CHARGE_BENEFICIARY_NOT_FOUND));
        }
    }


    public Page<OperationListDto> getAllOperations(int page, int size, String searchByNameDonor, String searchByNameDonorAr, String searchByBeneficiary,
                                                   String searchByNameBeneficiaryAr, String searchByStatusOperation, Long searchByServiceId,
                                                   Long searchByStatusId, Date minPlanningDate, Date maxPlanningDate, Date minExecutionDate,
                                                   Date maxExecutionDate, Date minClosureDate, Date maxClosureDate) {
        Pageable pageable = PageRequest.of(page, size);

        // Check if all filters are empty
        boolean allFiltersEmpty = areAllFiltersEmpty(searchByNameDonor, searchByNameDonorAr, searchByBeneficiary, searchByNameBeneficiaryAr,
                searchByServiceId != null ? searchByServiceId.toString() : null, searchByStatusId != null ? searchByStatusId.toString() : null,
                searchByStatusOperation, minPlanningDate != null ? minPlanningDate.toString() : null, maxPlanningDate != null ? maxPlanningDate.toString() : null,
                minExecutionDate != null ? minExecutionDate.toString() : null, maxExecutionDate != null ? maxExecutionDate.toString() : null,
                minClosureDate != null ? minClosureDate.toString() : null, maxClosureDate != null ? maxClosureDate.toString() : null);


        // If all filters are empty, fetch all records
        if (allFiltersEmpty) {
            Page<TakenInChargeOperation> operationsPage = takenInChargeOperationRepository.findAll(pageable);
            // Map to DTO
            List<OperationListDto> operations = operationsPage.stream()
                    .map(this::mapOperationToDto)
                    .toList();
        }

        // Build the dynamic specification
        Specification<TakenInChargeOperation> spec = buildSpecification(searchByNameDonor, searchByNameDonorAr, searchByBeneficiary,
                searchByNameBeneficiaryAr, searchByServiceId, searchByStatusId,
                minPlanningDate, maxPlanningDate, minExecutionDate, maxExecutionDate,
                minClosureDate, maxClosureDate , searchByStatusOperation);

        // Fetch operations based on the specification
        Page<TakenInChargeOperation> operationsPage = takenInChargeOperationRepository.findAll(spec, pageable);

        // Filter and map to DTO
        List<OperationListDto> filteredOperations = operationsPage.stream()
                .map(this::mapOperationToDto)
                .collect(Collectors.toList());

        return new PageImpl<>(filteredOperations, pageable, operationsPage.getTotalElements());
    }

    private boolean areAllFiltersEmpty(String... filters) {
        return Arrays.stream(filters).allMatch(filter -> filter == null || filter.isEmpty());
    }

    private Specification<TakenInChargeOperation> buildSpecification(String searchByNameDonor, String searchByNameDonorAr, String searchByBeneficiary,
                                                                     String searchByNameBeneficiaryAr, Long searchByServiceId, Long searchByStatusId,
                                                                     Date minPlanningDate, Date maxPlanningDate, Date minExecutionDate,
                                                                     Date maxExecutionDate, Date minClosureDate, Date maxClosureDate , String searchByStatusOperation) {
        Specification<TakenInChargeOperation> spec = Specification.where(null);

        if (searchByNameDonor != null && !searchByNameDonor.isEmpty()) {
            String lowerSearch = "%" + searchByNameDonor.toLowerCase() + "%";  // Convert search term to lowercase
            spec = spec.and((root, query, cb) -> cb.or(
                    cb.like(cb.lower(root.get("takenInChargeDonor").get("donor").get("lastName")), lowerSearch),  // Search in the donor's last name (Physical Donor)
                    cb.like(cb.lower(root.get("takenInChargeDonor").get("donor").get("company")), lowerSearch),    // Search in the donor's company name (Moral Donor)
                    cb.like(cb.lower(root.get("takenInChargeDonor").get("donor").get("name")), lowerSearch)         // Search in the donor's name (Anonymous Donor)
            ));
        }
        if(searchByStatusOperation != null && !searchByStatusOperation.isEmpty()) {
            spec = spec.and((root, query, cb) -> cb.equal(root.get("status"), searchByStatusOperation));
        }

        // Case-insensitive search for Beneficiary Name
        if (searchByBeneficiary != null && !searchByBeneficiary.isEmpty()) {
            String lowerBeneficiarySearch = "%" + searchByBeneficiary.toLowerCase() + "%";  // Convert search term to lowercase
            spec = spec.and((root, query, cb) -> cb.like(cb.lower(root.get("takenInChargeDonor").get("takenInCharge").get("takenInChargeBeneficiaries").get("beneficiary").get("person").get("firstName")), lowerBeneficiarySearch));
        }

        if (searchByServiceId != null) {
            spec = spec.and((root, query, cb) -> cb.equal(root.get("takenInChargeDonor").get("takenInCharge").get("service").get("id"), searchByServiceId));
        }
        if (searchByStatusId != null) {
            spec = spec.and((root, query, cb) -> cb.equal(root.get("takenInChargeDonor").get("takenInCharge").get("statusId"), searchByStatusId));
        }
        if (minPlanningDate != null) {
            spec = spec.and((root, query, cb) -> cb.greaterThanOrEqualTo(root.get("planningDate"), minPlanningDate));
        }
        if (maxPlanningDate != null) {
            spec = spec.and((root, query, cb) -> cb.lessThanOrEqualTo(root.get("planningDate"), maxPlanningDate));
        }
        if (minExecutionDate != null) {
            spec = spec.and((root, query, cb) -> cb.greaterThanOrEqualTo(root.get("executionDate"), minExecutionDate));
        }
        if (maxExecutionDate != null) {
            spec = spec.and((root, query, cb) -> cb.lessThanOrEqualTo(root.get("executionDate"), maxExecutionDate));
        }
        if (minClosureDate != null) {
            spec = spec.and((root, query, cb) -> cb.greaterThanOrEqualTo(root.get("closureDate"), minClosureDate));
        }
        if (maxClosureDate != null) {
            spec = spec.and((root, query, cb) -> cb.lessThanOrEqualTo(root.get("closureDate"), maxClosureDate));
        }

        return spec;
    }


    private OperationListDto mapOperationToDto(TakenInChargeOperation operation) {
        // Get donor and beneficiary information
        Donor donor = operation.getTakenInChargeDonor().getDonor();
        Beneficiary beneficiary = operation.getTakenInChargeDonor().getTakenInCharge()
                .getTakenInChargeBeneficiaries().stream()
                .findFirst()
                .map(TakenInChargeBeneficiary::getBeneficiary)
                .orElse(null);

        // Get the donor and beneficiary names
        String donorName = getDonorName(donor);
        String beneficiaryName = (beneficiary != null) ? beneficiary.getPerson().getFirstName() + " " + beneficiary.getPerson().getLastName() : "";
        // Get service information
        Services service = servicesRepository.findById(operation.getTakenInChargeDonor().getTakenInCharge().getService().getId()).orElse(null);
        ServicesDTO serviceDTO = servicesMapper.toDto(service);

        // Build and return the DTO
        return OperationListDto.builder()
                .id(operation.getId())
                .code(operation.getCode())
                .amount(operation.getAmount())
                .reserved(operation.isReserved())
                .managementFees(operation.getManagementFees())
                .planningDate(operation.getPlanningDate())
                .executionDate(operation.getExecutionDate())
                .closureDate(operation.getClosureDate())
                .comment(operation.getComment())
                .donorId(donor.getId())  // Assuming donor has an ID
                .beneficiaryId(beneficiary != null ? beneficiary.getId() : null)  // Assuming beneficiary has an ID
                .takenInChargeId(operation.getTakenInChargeDonor().getTakenInCharge().getId())
                .donorName(donorName)
                .beneficiaryName(beneficiaryName)
                .status(operation.getStatus())
                .services(serviceDTO)
                .build();
    }

    // Get donor name based on type (Physical, Moral, or Anonymous)
    public String getDonorName(Donor donor) {
        if (donor instanceof DonorPhysical donorPhysical) {
            return donorPhysical.getFirstName() + " " + donorPhysical.getLastName();
        } else if (donor instanceof DonorMoral donorMoral) {
            return donorMoral.getCompany();
        } else if (donor instanceof DonorAnonyme donorAnonyme) {
            return donorAnonyme.getName();
        }
        return "";
    }

    public List<TakenInChargeOperationDTO> encloseMultipleOperations(List<Long> ids) throws TechnicalException {
        List<TakenInChargeOperationDTO> enclosedOperations = new ArrayList<>();
        LocalDateTime currentDate = LocalDateTime.now();
        Date closureDate = Date.from(currentDate.atZone(ZoneId.systemDefault()).toInstant());

        for (Long id : ids) {
            Optional<TakenInChargeOperation> optional = takenInChargeOperationRepository.findById(id);

            if (!optional.isPresent()) {
                throw new TechnicalException(messages.get(TAKEN_IN_CHARGE_OPERATION_NOT_FOUND));
            }

            optional.get().setClosureDate(closureDate);
            TakenInChargeOperation updated = takenInChargeOperationRepository.save(optional.get());
            TakenInChargeOperationDTO updatedDTO = takenInChargeOperationMapper.takenInChargeOperationToTakenInChargeOperationDTO(updated);
            enclosedOperations.add(updatedDTO);
        }

        return enclosedOperations;
    }

    private Predicate buildPredicate(
            CriteriaBuilder criteriaBuilder, Root<TakenInCharge> root,
            Join<TakenInChargeDonor, Donor> donorEntityJoin, Join<Beneficiary, Person> personJoin,
            Join<TakenInCharge, Services> servicesJoin,
            String searchByNom, String searchByBeneficiary, String searchByBeneficiaryAr, String lastNameAr,
            Long searchByService, String searchByStatus, Date minStartDate, Date maxStartDate, Date minEndDate, Date maxEndDate) {

        Predicate predicate = criteriaBuilder.conjunction();

        // Date Range Filters
        if (minStartDate != null) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.greaterThanOrEqualTo(root.get("startDate"), minStartDate));
        }
        if (maxStartDate != null) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.lessThanOrEqualTo(root.get("startDate"), maxStartDate));
        }
        if (minEndDate != null) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.greaterThanOrEqualTo(root.get("endDate"), minEndDate));
        }
        if (maxEndDate != null) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.lessThanOrEqualTo(root.get("endDate"), maxEndDate));
        }

        // Service and Status Filters

        if (searchByStatus != null) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("status"), searchByStatus));
        }

        // Beneficiary Filters
        if (searchByBeneficiary != null) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(
                    criteriaBuilder.lower(personJoin.get("lastName")),
                    "%" + searchByBeneficiary.toLowerCase() + "%"
            ));
        }


        if (searchByService != null) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(
                    servicesJoin.get("id"), searchByService
            ));
        }
        if (searchByBeneficiaryAr != null) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(
                    criteriaBuilder.lower(personJoin.get("lastNameAr")),
                    "%" + searchByBeneficiaryAr.toLowerCase() + "%"
            ));
        }

        // Donor Filters
        if (lastNameAr != null) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(
                    criteriaBuilder.lower(donorEntityJoin.get("lastNameAr")),
                    "%" + lastNameAr.toLowerCase() + "%"
            ));
        }
        if (searchByNom != null) {
            Predicate predicateMoral = criteriaBuilder.like(
                    criteriaBuilder.lower(donorEntityJoin.get("company")),
                    "%" + searchByNom.toLowerCase() + "%"
            );
            Predicate predicatePhysical = criteriaBuilder.like(
                    criteriaBuilder.lower(donorEntityJoin.get("lastName")),
                    "%" + searchByNom.toLowerCase() + "%"
            );
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.or(predicateMoral, predicatePhysical));
        }

        return predicate;
    }

    private CriteriaQuery<TakenInCharge> createCriteriaQuery(
            String searchByNom, String searchByBeneficiary, String searchByBeneficiaryAr, String lastNameAr,
            Long searchByService, String searchByStatus, Date minStartDate, Date maxStartDate, Date minEndDate, Date maxEndDate,Long searchByTagId
    ) {

        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<TakenInCharge> criteriaQuery = criteriaBuilder.createQuery(TakenInCharge.class);
        Root<TakenInCharge> root = criteriaQuery.from(TakenInCharge.class);

        Join<TakenInCharge, TakenInChargeDonor> donorJoin = root.join("takenInChargeDonors", JoinType.LEFT);
        Join<TakenInChargeDonor, Donor> donorEntityJoin = donorJoin.join("donor", JoinType.LEFT);
        Join<TakenInCharge, TakenInChargeBeneficiary> beneficiaryJoin = root.join("takenInChargeBeneficiaries", JoinType.LEFT);
        Join<TakenInChargeBeneficiary, Beneficiary> beneficiaryEntityJoin = beneficiaryJoin.join("beneficiary", JoinType.LEFT);
        Join<Beneficiary, Person> personJoin = beneficiaryEntityJoin.join("person", JoinType.LEFT);
        Join<TakenInCharge, Services> servicesJoin = root.join("service", JoinType.LEFT);

        Predicate predicate = buildPredicate(
                criteriaBuilder, root, donorEntityJoin, personJoin, servicesJoin,
                searchByNom, searchByBeneficiary, searchByBeneficiaryAr, lastNameAr,
                searchByService, searchByStatus, minStartDate, maxStartDate, minEndDate, maxEndDate
        );
        if (searchByTagId != null) {
            // Create a subquery to find donors with the specified tag
            Subquery<Long> subquery = criteriaBuilder.createQuery().subquery(Long.class);
            Root<Taggable> taggableRoot = subquery.from(Taggable.class);
            subquery.select(taggableRoot.get("taggableId"))
                    .where(
                            criteriaBuilder.and(
                                    criteriaBuilder.equal(taggableRoot.get("taggableType"), "takenInCharge"),
                                    criteriaBuilder.equal(taggableRoot.get("tag").get("id"), searchByTagId)
                            )
                    );

            // Add the subquery condition to the main predicate
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.in(root.get("id")).value(subquery));
        }
        criteriaQuery.where(predicate);
        criteriaQuery.orderBy(criteriaBuilder.desc(root.get("createdAt")));

        return criteriaQuery;
    }

    public List<TakenInCharge> filterTakeInChargeToExport(
            String searchByNom, String searchByBeneficiary, String searchByBeneficiaryAr, String lastNameAr,
            Long searchByService, String searchByStatus, Date minStartDate, Date maxStartDate, Date minEndDate, Date maxEndDate) {

        CriteriaQuery<TakenInCharge> criteriaQuery = createCriteriaQuery(
                searchByNom, searchByBeneficiary, searchByBeneficiaryAr, lastNameAr,
                searchByService, searchByStatus, minStartDate, maxStartDate, minEndDate, maxEndDate,null
        );
        TypedQuery<TakenInCharge> typedQuery = entityManager.createQuery(criteriaQuery);

        return typedQuery.getResultList();
    }


    public TakenInChargeExportDTO mapTakeInchargeDTO(TakenInCharge takenInCharge) {
        TakenInChargeExportDTO dto = takenInChargeMapper.takenInChargeToTakenInChargeExportDTO(takenInCharge);

        // Set the start and end dates
        dto.setStartDate(convertToSqlDate(takenInCharge.getStartDate()));
        dto.setEndDate(convertToSqlDate(takenInCharge.getEndDate()));

        // Fetch and set the service name and status name
        setServiceAndStatusNames(takenInCharge, dto);

        // Set the donor name(s) and beneficiary name(s)
        dto.setDonorName(getDonorNames(takenInCharge));
        dto.setBeneficiaryName(getBeneficiaryNames(takenInCharge));

        // Calculate operation-related fields
        calculateOperationDetails(takenInCharge, dto);

        return dto;
    }

    private java.sql.Date convertToSqlDate(Date date) {
        return date != null ? java.sql.Date.valueOf(date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate()) : null;
    }

    private void setServiceAndStatusNames(TakenInCharge takenInCharge, TakenInChargeExportDTO dto) {
        Services service = servicesRepository.findById(takenInCharge.getService().getId()).orElse(null);
        ServicesDTO serviceDTO = servicesMapper.toDto(service);
        dto.setServiceName(serviceDTO != null ? serviceDTO.getName() : null);

    }

    private String getDonorNames(TakenInCharge takenInCharge) {
        return takenInCharge.getTakenInChargeDonors().stream()
                .map(takenInChargeDonor -> {
                    Donor donor = takenInChargeDonor.getDonor();
                    if (donor != null) {
                        if (donor instanceof DonorPhysical physicalDonor) {
                            return physicalDonor.getLastName() + " " + physicalDonor.getFirstName();
                        } else if (donor instanceof DonorMoral moralDonor) {
                            return moralDonor.getCompany();
                        } else if (donor instanceof DonorAnonyme anonymeDonor) {
                            return anonymeDonor.getName();
                        }
                    }
                    return "Donateur Inconnu";
                })
                .collect(Collectors.joining(", "));
    }

    private String getBeneficiaryNames(TakenInCharge takenInCharge) {
        return takenInCharge.getTakenInChargeBeneficiaries().stream()
                .map(takenInChargeBeneficiary -> {
                    Beneficiary beneficiary = takenInChargeBeneficiary.getBeneficiary();
                    if (beneficiary != null) {
                        Person person = beneficiary.getPerson();
                        return person.getLastName() + " " + person.getFirstName();
                    } else {
                        return "Beneficiary Inconnu";
                    }
                })
                .collect(Collectors.joining(", "));
    }

    private void calculateOperationDetails(TakenInCharge takenInCharge, TakenInChargeExportDTO dto) {
        int numberOfOperations = 0;
        int numberOfPlanned = 0;
        int numberOfExecuted = 0;
        int numberOfClosed = 0;
        double totalAmount = 0.0;

        for (TakenInChargeDonor takenInChargeDonor : takenInCharge.getTakenInChargeDonors()) {
            for (TakenInChargeOperation operation : takenInChargeDonor.getTakenInChargeOperations()) {
                numberOfOperations++;
                totalAmount += operation.getAmount();

                if (operation.getPlanningDate() != null) {
                    numberOfPlanned++;
                }
                if (operation.getExecutionDate() != null) {
                    numberOfExecuted++;
                }
                if (operation.getClosureDate() != null) {
                    numberOfClosed++;
                }
            }
        }

        dto.setNumberOfOperations(numberOfOperations);
        dto.setTotalAmount(totalAmount);
        dto.setNumberOfPlannedOperations(numberOfPlanned);
        dto.setNumberOfExecutedOperations(numberOfExecuted);
        dto.setNumberOfClosedOperations(numberOfClosed);
    }


    public List<TakenInChargeExportDTO> getAllTakenInChargeToExportDTOS(List<TakenInCharge> takenInCharges) {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service getAllTakenInChargeToExportDTOS with {} takenInCharges", takenInCharges.size());
        List<TakenInChargeExportDTO> takenInChargeExportDTOs = takenInCharges.stream()
                .map(this::mapTakeInchargeDTO)
                .collect(Collectors.toList());
        log.debug("End service getAllTakenInChargeToExportDTOS, took {}", watch.toMS());
        return takenInChargeExportDTOs;
    }


    public ExportFileDTO exportFileWithName(String searchByNom, String searchByBeneficiary, String searchByBeneficiaryAr, String lastNameAr, Long searchByService, String searchByStatus, java.util.Date minStartDate, java.util.Date maxStartDate, java.util.Date minEndDate, java.util.Date maxEndDate) {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service exportTakenInChargeFile");

        // Filter TakenInCharge entities based on criteria
        List<TakenInCharge> takenInCharges = filterTakeInChargeToExport(searchByNom, searchByBeneficiary, searchByBeneficiaryAr, lastNameAr, searchByService, searchByStatus, minStartDate, maxStartDate, minEndDate, maxEndDate);

        // Convert entities to DTOs
        List<TakenInChargeExportDTO> listExportDTO = getAllTakenInChargeToExportDTOS(takenInCharges);

        // Define export parameters
        String sheetName = "Rapport des Prise en charge";
        String[] headers = Arrays.stream(TakenInChargeExportHeaders.values())
                .map(TakenInChargeExportHeaders::getHeaderName)
                .toArray(String[]::new);

        // Perform export
        return exportService.exportEntities(sheetName, headers, listExportDTO, this::mapToExportRow);
    }

    private Object[] mapToExportRow(TakenInChargeExportDTO dto) {
        return new Object[]{
                dto.getCode(),
                dto.getCreatedAt().format(DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss")),
                dto.getDonorName(),
                dto.getBeneficiaryName(),
                dto.getServiceName(),
                dto.getStatus(),
                dto.getFormattedStartDate(),
                dto.getFormattedEndDate(),
                dto.getNumberOfOperations(),
                dto.getTotalAmount(),
                dto.getNumberOfPlannedOperations(),
                dto.getNumberOfExecutedOperations(),
                dto.getNumberOfClosedOperations()
        };

    }

    public List<TakenInChargeOperationDTO> getOperationsByTakenInChargeId(Long takenInChargeDonorId) {
        // First, find the TakenInChargeDonors by takenInChargeId
        List<TakenInChargeDonor> takenInChargeDonors = takenInChargeDonorRepository.findByTakenInChargeId(takenInChargeDonorId);

        // Check if there are any donors
        if (takenInChargeDonors.isEmpty()) {
            return Collections.emptyList(); // Return an empty list if no donors are found
        }

        // Get the first TakenInChargeDonor (assuming there is at least one donor)
        TakenInChargeDonor donor = takenInChargeDonors.get(0);

        // Fetch the operations for the given TakenInChargeDonor
        List<TakenInChargeOperation> operations = takenInChargeOperationRepository.findTakenInChargeOperationsByTakenInChargeDonor(donor);

        // Map the operations to DTOs and set history for each operation
        List<TakenInChargeOperationDTO> operationDTOs = operations.stream()
                .map(operation -> {
                    // Map the operation to DTO
                    TakenInChargeOperationDTO operationDTO = takenInChargeOperationMapper.takenInChargeOperationToTakenInChargeOperationDTO(operation);
                    // Set the history for the current operation
                    setListHistorytoOperations(operationDTO);
                    return operationDTO;
                })
                .collect(Collectors.toList());


        return operationDTOs;
    }

    public String getDonorName(Donation donation) {
        if (donation.getDonor() instanceof DonorPhysical donorPhysical) {
            return donorPhysical.getFirstName() + " " + donorPhysical.getLastName();
        } else if (donation.getDonor() instanceof DonorMoral donorMoral) {
            return donorMoral.getCompany();
        } else if (donation.getDonor() instanceof DonorAnonyme donorAnonyme) {
            return donorAnonyme.getName();
        } else return "test";
    }


}
