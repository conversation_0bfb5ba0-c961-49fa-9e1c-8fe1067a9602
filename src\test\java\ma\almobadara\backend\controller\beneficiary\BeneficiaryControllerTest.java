package ma.almobadara.backend.controller.beneficiary;

import com.fasterxml.jackson.databind.ObjectMapper;
import ma.almobadara.backend.config.Messages;
import ma.almobadara.backend.config.TestSecurityConfig;
import ma.almobadara.backend.dto.beneficiary.*;
import ma.almobadara.backend.dto.getbeneficiary.GetListDTO;
import ma.almobadara.backend.dto.takenInCharge.GetBeneficiariesForTakeInchargeDTO;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.service.beneficiary.AddedBeneficiaryResponse;
import ma.almobadara.backend.service.beneficiary.BeneficiaryService;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.time.Instant;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.powermock.api.mockito.PowerMockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@WebMvcTest(BeneficiaryController.class)
@ExtendWith(MockitoExtension.class)
@Import({TestSecurityConfig.class, Messages.class})
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class BeneficiaryControllerTest {
    @Autowired
    private MockMvc mockMvc;



    @MockBean
    private BeneficiaryService beneficiaryService;

    ObjectMapper objectMapper = new ObjectMapper();


    @Test
    @Order(1)
    void BeneficiaryController_createBeneficiary_Success() throws Exception {
        BeneficiaryAddDTO beneficiaryDTO = BeneficiaryAddDTO
                .builder()
                .code("code")
                .accountingCode("accountingCode")
                .build();

        AddedBeneficiaryResponse addedBeneficiaryResponse = new AddedBeneficiaryResponse();
        addedBeneficiaryResponse.setCode("code");
        addedBeneficiaryResponse.setId(1L);
        addedBeneficiaryResponse.setPersonId(1L);
        addedBeneficiaryResponse.setAge(99);
        addedBeneficiaryResponse.setIndependent(false);

        when(beneficiaryService.addBeneficiary(any(BeneficiaryAddDTO.class))).thenReturn((addedBeneficiaryResponse));

        mockMvc.perform(post("/beneficiaries")
                        .flashAttr("beneficiaryAddDTO", beneficiaryDTO)
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(addedBeneficiaryResponse)));

        verify(beneficiaryService, times(1)).addBeneficiary(any(BeneficiaryAddDTO.class));

    }

    @Test
    @Order(2)
    void BeneficiaryController_addSanitaryToBeneficiary_Success() throws Exception {
        BeneficiarySanitary beneficiarySanitary=BeneficiarySanitary
                .builder()
                .beneficiaryId(1L)
                .beneficiaryAllergies(List.of(AllergyBeneficiaryAddDto.builder().allergyId(1L).build()))
                .beneficiaryDiseases(List.of(DiseaseBeneficiaryAddDto.builder().diseaseId(1L).build()))
                .diseaseTreatments(List.of(DiseaseTreatmentBeneficiaryAddDto.builder().comment("").build()))
                .build();

        AddedBeneficiaryResponse addedBeneficiaryResponse = new AddedBeneficiaryResponse();
        addedBeneficiaryResponse.setCode("code");
        addedBeneficiaryResponse.setId(1L);
        addedBeneficiaryResponse.setPersonId(1L);
        addedBeneficiaryResponse.setAge(99);
        addedBeneficiaryResponse.setIndependent(false);

        when(beneficiaryService.addSanitaryToBeneficiary(any(BeneficiarySanitary.class))).thenReturn((addedBeneficiaryResponse));


        mockMvc.perform(post("/beneficiaries/sanitary")
                        .flashAttr("beneficiaryAddDTO", beneficiarySanitary)
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(addedBeneficiaryResponse)));

        verify(beneficiaryService, times(1)).addSanitaryToBeneficiary(any(BeneficiarySanitary.class));
    }

    @Test
    @Order(3)
    void BeneficiaryController_addEducationToBeneficiary_Success() throws Exception {
        BeneficiaryEducation beneficiaryEducation=BeneficiaryEducation
                .builder()
                .beneficiaryId(1L)
                .educated(true)
                .educations(List.of(EducationDTO.builder().educationType("test").build()))
                .schoolLevelId(1L)
                .schoolName("test")
                .build();

        AddedBeneficiaryResponse addedBeneficiaryResponse = new AddedBeneficiaryResponse();
        addedBeneficiaryResponse.setCode("code");
        addedBeneficiaryResponse.setId(1L);
        addedBeneficiaryResponse.setPersonId(1L);
        addedBeneficiaryResponse.setAge(99);
        addedBeneficiaryResponse.setIndependent(false);

        when(beneficiaryService.addEducationToBeneficiary(any(BeneficiaryEducation.class))).thenReturn((addedBeneficiaryResponse));


        mockMvc.perform(post("/beneficiaries/education")
                        .flashAttr("beneficiaryEducation", beneficiaryEducation)
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(addedBeneficiaryResponse)));

        verify(beneficiaryService, times(1)).addEducationToBeneficiary(any(BeneficiaryEducation.class));
    }

    @Test
    @Order(4)
    void BeneficiaryController_addPieceJointeToBeneficiary_Success() throws Exception{
        BeneficiaryPieceJointe beneficiaryPieceJointe=BeneficiaryPieceJointe
                .builder()
                .beneficiaryId(1L)
                .cin(DocumentBeneficiaryAddDto.builder().code("code").build())
                .build();
        AddedBeneficiaryResponse addedBeneficiaryResponse = new AddedBeneficiaryResponse();
        addedBeneficiaryResponse.setCode("code");
        addedBeneficiaryResponse.setId(1L);
        addedBeneficiaryResponse.setPersonId(1L);
        addedBeneficiaryResponse.setAge(99);
        addedBeneficiaryResponse.setIndependent(false);

        when(beneficiaryService.addPieceJointeToBeneficiary(any(BeneficiaryPieceJointe.class))).thenReturn((addedBeneficiaryResponse));


        mockMvc.perform(post("/beneficiaries/addPieceJointe")
                        .flashAttr("beneficiaryPieceJointe", beneficiaryPieceJointe)
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(addedBeneficiaryResponse)));

        verify(beneficiaryService, times(1)).addPieceJointeToBeneficiary(any(BeneficiaryPieceJointe.class));
    }

    @Test
    @Order(5)
    void BeneficiaryController_addPieceJointeToBeneficiary_InternalServerError() throws Exception {
        BeneficiaryPieceJointe beneficiaryPieceJointe=BeneficiaryPieceJointe
                .builder()
                .beneficiaryId(1L)
                .cin(DocumentBeneficiaryAddDto.builder().code("code").build())
                .build();
        AddedBeneficiaryResponse addedBeneficiaryResponse = new AddedBeneficiaryResponse();
        addedBeneficiaryResponse.setCode("code");
        addedBeneficiaryResponse.setId(1L);
        addedBeneficiaryResponse.setPersonId(1L);
        addedBeneficiaryResponse.setAge(99);
        addedBeneficiaryResponse.setIndependent(false);

        doThrow(new IllegalStateException("Error in adding Piece Joint")).when(beneficiaryService).addPieceJointeToBeneficiary(any(BeneficiaryPieceJointe.class));
        mockMvc.perform(post("/beneficiaries/addPieceJointe")
                        .flashAttr("beneficiaryPieceJointe", beneficiaryPieceJointe)
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isInternalServerError());

        verify(beneficiaryService, times(1)).addPieceJointeToBeneficiary(any(BeneficiaryPieceJointe.class));

    }

    @Test
    @Order(6)
    void BeneficiaryController_getAllBeneficiaries_Success() throws Exception {
        Pageable pageable= PageRequest.of(0, 10);
        List<GetListDTO> listDTOS= List.of(
                GetListDTO
                        .builder()
                        .code("code 2")
                        .id(1L)
                        .build()
                ,
                GetListDTO
                        .builder()
                        .code("code 1")
                        .id(2L)
                        .build()
        );

        when(beneficiaryService.getAllBeneficiaries(any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any()))
                .thenReturn(new PageImpl<>(listDTOS,pageable,2));

        mockMvc.perform(get("/beneficiaries/list").param("page", "1")
                .param("criteria", "Criteria")
                .param("value1", "Value1")
                .param("value2", "Value2")
                .param("searchByNom", "Name")
                .param("lastNameAr", "LastName")
                .param("searchByTypeBeneficiaire", "true")
                .param("searchByService", "Service")
                .param("searchByStatut", "Status")
                .param("searchByNumTel", "123456789")
                .param("isCandidateInitial", "true")
                .param("isValidateAssistant", "false")
                .param("isValidateKafalat", "true")
                .param("isOldBeneficiary", "false")
                .param("isBeneficiaryRejete", "true")
                .param("isBeneficiaryArchived", "false")
                .param("isCandidateRejete", "true")
                .param("isBeneficiaryWaiting", "false")
                .param("isBeneficiaryActif", "true"))
                .andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(new PageImpl<>(listDTOS, pageable, 2))));

        verify(beneficiaryService, times(1)).getAllBeneficiaries(any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any());
    }

    @Test
    @Order(7)
    void BeneficiaryController_getAllBeneficiaries_InternalServerError() throws Exception {

        doThrow( new IllegalStateException("failed")).when(beneficiaryService).getAllBeneficiaries(any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any());

        mockMvc.perform(get("/beneficiaries/list")).andExpect(status().isInternalServerError());

        verify(beneficiaryService, times(1)).getAllBeneficiaries(any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any());


    }

    @Test
    @Order(8)
    void BeneficiaryController_getAllCondidates_Success() throws Exception {
        Pageable pageable= PageRequest.of(0, 10);
        List<GetListDTO> listDTOS= List.of(
                GetListDTO
                        .builder()
                        .code("code 2")
                        .id(1L)
                        .build()
                ,
                GetListDTO
                        .builder()
                        .code("code 1")
                        .id(2L)
                        .build()
        );

        when(beneficiaryService.getAllCandidates(any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any()))
                .thenReturn(new PageImpl<>(listDTOS,pageable,2));

        mockMvc.perform(get("/beneficiaries/candidates").param("page", "1")
                        .param("criteria", "Criteria")
                        .param("value1", "Value1")
                        .param("value2", "Value2")
                        .param("searchByNom", "Name")
                        .param("lastNameAr", "LastName")
                        .param("searchByTypeBeneficiaire", "true")
                        .param("searchByService", "Service")
                        .param("searchByStatut", "Status")
                        .param("searchByNumTel", "123456789")
                        .param("isCandidateInitial", "true")
                        .param("isValidateAssistant", "false")
                        .param("isCandidate", "false")
                        .param("isValidateKafalat", "true")
                        .param("isOldBeneficiary", "false")
                        .param("isBeneficiaryRejete", "true")
                        .param("isBeneficiaryArchived", "false")
                        .param("isCandidateRejete", "true")
                        .param("isBeneficiaryWaiting", "false")
                        .param("isBeneficiaryActif", "true")).andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(new PageImpl<>(listDTOS, pageable, 2))));

        verify(beneficiaryService, times(1)).getAllCandidates(any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any());

    }

    @Test
    @Order(9)
    void BeneficiaryController_getAllCondidates_InternalServerError() throws Exception {
        doThrow( new IllegalStateException("failed")).when(beneficiaryService).getAllCandidates(any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any());

        mockMvc.perform(get("/beneficiaries/candidates")).andExpect(status().isInternalServerError());

        verify(beneficiaryService, times(1)).getAllCandidates(any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any());

    }

    @Test
    @Order(10)
    void BeneficiaryController_getAllOldBeneficiaries_Success() throws Exception {
        Pageable pageable= PageRequest.of(0, 10);
        List<GetListDTO> listDTOS= List.of(
                GetListDTO
                        .builder()
                        .code("code 2")
                        .id(1L)
                        .build()
                ,
                GetListDTO
                        .builder()
                        .code("code 1")
                        .id(2L)
                        .build()
        );

        when(beneficiaryService.getAllOldBeneficiaries(any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any()))
                .thenReturn(new PageImpl<>(listDTOS,pageable,2));

        mockMvc.perform(get("/beneficiaries/old/beneficiary").param("page", "1")
                        .param("criteria", "Criteria")
                        .param("value1", "Value1")
                        .param("value2", "Value2")
                        .param("searchByNom", "name")
                        .param("lastNameAr", "LastName")
                        .param("searchByTypeBeneficiaire", "true")
                        .param("searchByService", "Service")
                        .param("searchByStatut", "Status")
                        .param("searchByNumTel", "123456789")
                        .param("isCandidateInitial", "true")
                        .param("isValidateAssistant", "false")
                        .param("isValidateKafalat", "true")
                        .param("isOldBeneficiary", "false")
                        .param("isBeneficiaryRejete", "true")
                        .param("isBeneficiaryArchived", "false")
                        .param("isCandidateRejete", "true")
                        .param("isBeneficiaryWaiting", "false")
                        .param("isBeneficiaryActif", "true")).andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(new PageImpl<>(listDTOS, pageable, 2))));
        verify(beneficiaryService, times(1)).getAllOldBeneficiaries(any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any());

    }

    @Test
    @Order(11)
    void BeneficiaryController_getAllOldBeneficiaries_InternalServerError() throws Exception {
        doThrow( new IllegalStateException("failed")).when(beneficiaryService).getAllOldBeneficiaries(any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any());

        mockMvc.perform(get("/beneficiaries/old/beneficiary")).andExpect(status().isInternalServerError());
        verify(beneficiaryService, times(1)).getAllOldBeneficiaries(any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any());

    }

    @Test
    @Order(12)
    void BeneficiaryController_getBeneficiaryById_Success() throws Exception {
        BeneficiaryDTO beneficiaryDTO=BeneficiaryDTO
                .builder()
                .id(1L)
                .code("code 2")
                .accountingCode("code 1")
                .oldBeneficiary(true)
                .build();
        Long id=1L;

        when(beneficiaryService.getBeneficiaryById(id)).thenReturn(beneficiaryDTO);

        mockMvc.perform(get("/beneficiaries/"+id)).andExpect(status().isOk()).andExpect(content().json(objectMapper.writeValueAsString(beneficiaryDTO)));
        verify(beneficiaryService, times(1)).getBeneficiaryById(any());

    }

    @Test
    @Order(13)
    void BeneficiaryController_getBeneficiaryById_NotFound() throws Exception {

        Long id=1L;

        when(beneficiaryService.getBeneficiaryById(id)).thenReturn(null);

        mockMvc.perform(get("/beneficiaries/"+id)).andExpect(status().isNotFound());
        verify(beneficiaryService, times(1)).getBeneficiaryById(any());

    }
    @Test
    @Order(14)
    void BeneficiaryController_getBeneficiaryById_InternalServerError() throws Exception {

        Long id=1L;

        doThrow(new TechnicalException("not working")).when(beneficiaryService).getBeneficiaryById(id);

        mockMvc.perform(get("/beneficiaries/"+id)).andExpect(status().isInternalServerError());
        verify(beneficiaryService, times(1)).getBeneficiaryById(any());

    }

    @Test
    @Order(15)
    void BeneficiaryController_deleteBeneficiary_InternalServerError() throws Exception {

        Long id=1L;

        doThrow(new TechnicalException("not working")).when(beneficiaryService).deleteBeneficiary(id,"test");

        mockMvc.perform(delete("/beneficiaries/delete/"+id).param("rqReject","test")).andExpect(status().isInternalServerError());

        verify(beneficiaryService, times(1)).deleteBeneficiary(any(),any());

    }

    @Test
    @Order(16)
    void BeneficiaryController_deleteBeneficiary_Success() throws Exception {

        Long id=1L;

        doNothing().when(beneficiaryService).deleteBeneficiary(id,"test");

        mockMvc.perform(delete("/beneficiaries/delete/"+id)).andExpect(status().isNoContent());

        verify(beneficiaryService, times(1)).deleteBeneficiary(any(),any());
    }

    @Test
    @Order(17)
    void BeneficiaryController_unArchiveBeneficiary_InternalServerError() throws Exception {

        doThrow(new RuntimeException("Unexpected error"))
                .when(beneficiaryService).unArchiveBeneficiary(1L);
        mockMvc.perform(put("/beneficiaries/unArchive-beneficiary/1")
                        .header("Accept", "application/json"))
                .andExpect(status().isBadRequest());

        verify(beneficiaryService, times(1)).unArchiveBeneficiary(any());
    }

    @Test
    @Order(18)
    void BeneficiaryController_unArchiveBeneficiary_Success() throws Exception {

        Long id=1L;

        doNothing().when(beneficiaryService).unArchiveBeneficiary(id);

        mockMvc.perform(put("/beneficiaries/unArchive-beneficiary/"+id)).andExpect(status().isNoContent());

        verify(beneficiaryService, times(1)).unArchiveBeneficiary(any());
    }

    @Test
    @Order(19)
    void BeneficiaryController_beneficiaryValidation_Success() throws Exception {

        Long id=1L;

        doNothing().when(beneficiaryService).beneficiaryValidation(id);

        mockMvc.perform(post("/beneficiaries/validate-beneficiary/"+id)).andExpect(status().isNoContent());

        verify(beneficiaryService, times(1)).beneficiaryValidation(any());
    }



    @Test
    @Order(20)
    void BeneficiaryController_beneficiaryValidation_InternalServerError() throws Exception {

        doThrow(new RuntimeException("Unexpected error"))
                .when(beneficiaryService).beneficiaryValidation(1L);
        mockMvc.perform(post("/beneficiaries/validate-beneficiary/1")
                        .header("Accept", "application/json"))
                .andExpect(status().isBadRequest());

        verify(beneficiaryService, times(1)).beneficiaryValidation(any());
    }

    @Test
    @Order(21)
    void BeneficiaryController_candidateToComplete_InternalServerError() throws Exception {
        doThrow(new RuntimeException("Unexpected error")).when(beneficiaryService).candidateToComplete(1L,1L,"test");
        mockMvc.perform(post("/beneficiaries/complete-candidate/1")
                        .header("Accept", "application/json"))
                .andExpect(status().isBadRequest());
    }

    @Test
    @Order(22)
    void BeneficiaryController_candidateToComplete_Success() throws Exception {
       when(beneficiaryService.candidateToComplete(1L,1L,"test")).thenReturn(2L);
        mockMvc.perform(post("/beneficiaries/complete-candidate/1")
                        .param("rqComplete","test")
                        .param("idStatutTarget","1")
                        .header("Accept", "application/json"))
                .andExpect(status().isOk())
                .andExpect(content().string(objectMapper.writeValueAsString(2)));

        verify(beneficiaryService, times(1)).candidateToComplete(any(),any(),any());
    }

    @Test
    @Order(23)
    void BeneficiaryController_updateCandidate_Success() throws Exception {
        when(beneficiaryService.validateUpdateCandidate(1L)).thenReturn(2L);
        mockMvc.perform(post("/beneficiaries/update-candidate/1")
                        .header("Accept", "application/json"))
                .andExpect(status().isOk())
                .andExpect(content().string(objectMapper.writeValueAsString(2)));

        verify(beneficiaryService, times(1)).validateUpdateCandidate(any());
    }

    @Test
    @Order(24)
    void BeneficiaryController_updateCandidate_InternalServerError() throws Exception {
        doThrow(new RuntimeException("Unexpected error")).when(beneficiaryService).validateUpdateCandidate(1L);
        mockMvc.perform(post("/beneficiaries/update-candidate/1")
                        .header("Accept", "application/json"))
                .andExpect(status().isInternalServerError());

        verify(beneficiaryService, times(1)).validateUpdateCandidate(any());

    }

    @Test
    @Order(25)
    void BeneficiaryController_rejectBeneficiary_Success() throws Exception {
        when(beneficiaryService.rejectBeneficiary(1L,"test")).thenReturn(2L);
        mockMvc.perform(post("/beneficiaries/reject-beneficiary/1")
                        .param("rqReject","test")
                        .header("Accept", "application/json"))
                .andExpect(status().isOk())
                .andExpect(content().string(objectMapper.writeValueAsString(2)));

        verify(beneficiaryService, times(1)).rejectBeneficiary(any(),any());

    }

    @Test
    @Order(26)
    void BeneficiaryController_rejectBeneficiary_InternalServerError() throws Exception {
        doThrow(new RuntimeException("Unexpected error")).when(beneficiaryService).rejectBeneficiary(1L,"test");
        mockMvc.perform(post("/beneficiaries/reject-beneficiary/1")
                        .param("rqReject","test")
                        .header("Accept", "application/json"))
                .andExpect(status().isInternalServerError());
        verify(beneficiaryService, times(1)).rejectBeneficiary(any(),any());

    }
    @Test
    @Order(27)
    void BeneficiaryController_getBeneficiariesNotInKafalat_Success() throws Exception {
        GetBeneficiariesForTakeInchargeDTO dto = GetBeneficiariesForTakeInchargeDTO.builder()
                .id(1L)
                .independent(true)
                .firstName("hamza")
                .lastName("nachid")
                .zoneName("Zone A")
                .categoryBeneficiaryId(101L)
                .categoryBeneficiaryName("Test 1")
                .pictureUrl("https://hamza.nachid.com/picture.jpg")
                .pictureBase64(null)
                .beneficiaryStatutId(5L)
                .beneficiaryStatut("Active")
                .numberOfTakenInCharge(3L)
                .build();
        Pageable pageable= PageRequest.of(0, 10);

        when(beneficiaryService.loadBeneficiariesForTakeInCharge(any(),any(),any(),any(),any())).thenReturn(new PageImpl<>(List.of(dto),pageable,1));

        mockMvc.perform(get("/beneficiaries/loadBeneficiariesForTakeInCharge")
                        .param("page","1")
                        .param("independent","false")
                        .param("lastName","nachid")
                        .param("status","1")
                        .param("category","1")).andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(new PageImpl<>(List.of(dto), pageable, 1))));
        verify(beneficiaryService, times(1)).loadBeneficiariesForTakeInCharge(any(),any(),any(),any(),any());

    }

    @Test
    @Order(28)
    void BeneficiaryController_getBeneficiariesNotInKafalat_InternalServerError() throws Exception {
        doThrow( new IllegalStateException("failed")).when(beneficiaryService).loadBeneficiariesForTakeInCharge(any(),any(),any(),any(),any());

        mockMvc.perform(get("/beneficiaries/loadBeneficiariesForTakeInCharge")).andExpect(status().isInternalServerError());

        verify(beneficiaryService, times(1)).loadBeneficiariesForTakeInCharge(any(),any(),any(),any(),any());

    }

    @Test
    @Order(29)
    void BeneficiaryController_ajouterBeneficiaryAdHocPerson_Success() throws Exception {
        BeneficiaryAdHocPersonneDto beneficiaryAdHocPersonneDto = BeneficiaryAdHocPersonneDto
                .builder()
                .id(1L)
                .code("hamza-123")
                .firstName("hamza")
                .lastName("nachid")
                .statusBeneficiaryAdHoc("Active")
                .identityCode("ID123456")
                .typeIdentityId(2L)
                .personId(1001L)
                .createdAt(Instant.now())
                .typePriseEnChargeIds(List.of(10L, 20L))
                .build();
        AddedBeneficiaryResponse addedBeneficiaryResponse = new AddedBeneficiaryResponse();
        addedBeneficiaryResponse.setCode("code");
        addedBeneficiaryResponse.setId(1L);
        addedBeneficiaryResponse.setPersonId(1L);
        addedBeneficiaryResponse.setAge(99);
        addedBeneficiaryResponse.setIndependent(false);

        when(beneficiaryService.ajouterBeneficiaryAdHocPerson(any(BeneficiaryAdHocPersonneDto.class))).thenReturn(addedBeneficiaryResponse);

        mockMvc.perform(post("/beneficiaries/addAdHocPerson")
                        .flashAttr("beneficiaryAddDTO", beneficiaryAdHocPersonneDto)
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isCreated())
                .andExpect(content().json(objectMapper.writeValueAsString(addedBeneficiaryResponse)));
        verify(beneficiaryService, times(1)).ajouterBeneficiaryAdHocPerson(any(BeneficiaryAdHocPersonneDto.class));

    }
    @Test
    @Order(30)
    void BeneficiaryController_updateBeneficiaryAdHocPerson_Success() throws Exception {
        BeneficiaryAdHocPersonneDto beneficiaryAdHocPersonneDto = BeneficiaryAdHocPersonneDto
                .builder()
                .id(1L)
                .code("hamza-123")
                .firstName("hamza")
                .lastName("nachid")
                .statusBeneficiaryAdHoc("Active")
                .identityCode("ID123456")
                .typeIdentityId(2L)
                .personId(1001L)
                .createdAt(Instant.now())
                .typePriseEnChargeIds(List.of(10L, 20L))
                .build();
        AddedBeneficiaryResponse addedBeneficiaryResponse = new AddedBeneficiaryResponse();
        addedBeneficiaryResponse.setCode("code");
        addedBeneficiaryResponse.setId(1L);
        addedBeneficiaryResponse.setPersonId(1L);
        addedBeneficiaryResponse.setAge(99);
        addedBeneficiaryResponse.setIndependent(false);

        when(beneficiaryService.updateBeneficiaryAdHocPerson(any(BeneficiaryAdHocPersonneDto.class))).thenReturn(addedBeneficiaryResponse);

        mockMvc.perform(put("/beneficiaries/updateAdHocPerson")
                        .flashAttr("beneficiaryAddDTO", beneficiaryAdHocPersonneDto)
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(addedBeneficiaryResponse)));
        verify(beneficiaryService, times(1)).updateBeneficiaryAdHocPerson(any(BeneficiaryAdHocPersonneDto.class));

    }

    @Test
    @Order(31)
    void BeneficiaryController_getAllBeneficiariesAdHocPerson_Success() throws Exception {
        Pageable pageable= PageRequest.of(0, 10);
        List<GetListDTO> listDTOS= List.of(
                GetListDTO
                        .builder()
                        .code("code 2")
                        .id(1L)
                        .build()
                ,
                GetListDTO
                        .builder()
                        .code("code 1")
                        .id(2L)
                        .build()
        );

        when(beneficiaryService.getAllBeneficiariesAdHocPersonne(any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any()))
                .thenReturn(new PageImpl<>(listDTOS,pageable,2));

        mockMvc.perform(get("/beneficiaries/all-beneficiary-ad-hoc")
                        .param("criteria", "Criteria")
                        .param("value1", "Value1")
                        .param("value2", "Value2")
                        .param("searchByNom", "Name")
                        .param("lastNameAr", "LastName")
                        .param("searchByTypeBeneficiaire", "true")
                        .param("searchByStatut", "Status")
                        .param("searchByNumTel", "123456789")).andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(new PageImpl<>(listDTOS, pageable, 2))));

        verify(beneficiaryService, times(1)).getAllBeneficiariesAdHocPersonne(any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any());

    }

    @Test
    @Order(32)
    void BeneficiaryController_getAllBeneficiaryAdHocPerson_InternalServerError() throws Exception {
        doThrow( new IllegalStateException("failed")).when(beneficiaryService).getAllBeneficiariesAdHocPersonne(any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any());

        mockMvc.perform(get("/beneficiaries/all-beneficiary-ad-hoc")).andExpect(status().isInternalServerError());

        verify(beneficiaryService, times(1)).getAllBeneficiariesAdHocPersonne(any(),any(),any(),any(),any(),any(),any(),any(),any(),any(),any());

    }


}
