package ma.almobadara.backend.service.aideComplemenatire;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityNotFoundException;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.Audit.AuditApplicationService;
import ma.almobadara.backend.clients.RefFeignClient;
import ma.almobadara.backend.dto.administration.TagDTO;
import ma.almobadara.backend.dto.aideComplemenatire.*;
import ma.almobadara.backend.dto.beneficiary.AideComplementaireListDto;
import ma.almobadara.backend.dto.beneficiary.BeneficiaryForAideComplementaireDTO;
import ma.almobadara.backend.dto.beneficiary.BeneficiaryForTypeKafalatDTO;
import ma.almobadara.backend.dto.communs.DocumentAndEntityDto;
import ma.almobadara.backend.dto.communs.DocumentDTO;

import ma.almobadara.backend.dto.donor.DonorForAideComplementaireDTO;

import ma.almobadara.backend.dto.referentiel.TypePriseEnChargeDTO;
import ma.almobadara.backend.enumeration.*;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.mapper.*;
import ma.almobadara.backend.model.administration.Tag;
import ma.almobadara.backend.model.administration.Taggable;
import ma.almobadara.backend.model.aideComplemenatire.AideComplementaire;
import ma.almobadara.backend.model.aideComplemenatire.AideComplementaireBeneficiaryAdHocGroupe;
import ma.almobadara.backend.model.aideComplemenatire.AideComplementaireDonorBeneficiary;
import ma.almobadara.backend.model.beneficiary.Beneficiary;
import ma.almobadara.backend.model.beneficiary.BeneficiaryAdHocGroup;
import ma.almobadara.backend.model.beneficiary.Person;
import ma.almobadara.backend.model.caisse.Caisse;
import ma.almobadara.backend.model.communs.Document;
import ma.almobadara.backend.model.donation.BudgetLine;
import ma.almobadara.backend.model.donation.Donation;
import ma.almobadara.backend.model.donor.Donor;
import ma.almobadara.backend.model.donor.DonorAnonyme;
import ma.almobadara.backend.model.donor.DonorMoral;
import ma.almobadara.backend.model.donor.DonorPhysical;
import ma.almobadara.backend.model.family.FamilyMember;
import ma.almobadara.backend.model.service.Services;
import ma.almobadara.backend.model.takenInCharge.TakenInChargeBeneficiary;
import ma.almobadara.backend.model.takenInCharge.TakenInChargeDonor;
import ma.almobadara.backend.repository.administration.TaggableRepository;
import ma.almobadara.backend.repository.aideComplemenatire.AideComplementaireBeneficiaryAdHocGroupeRepository;
import ma.almobadara.backend.repository.aideComplemenatire.AideComplementaireDonorBeneficiaryRepository;
import ma.almobadara.backend.repository.aideComplemenatire.AideComplementaireRepository;
import ma.almobadara.backend.repository.beneficiary.BeneficiaryAdHocGroupRepository;
import ma.almobadara.backend.repository.beneficiary.BeneficiaryRepository;
import ma.almobadara.backend.repository.caisse.CaisseRepository;
import ma.almobadara.backend.repository.donation.BudgetLineRepository;
import ma.almobadara.backend.repository.donation.DonationHistoryRepository;
import ma.almobadara.backend.repository.donation.DonationRepository;
import ma.almobadara.backend.repository.donor.DonorRepository;
import ma.almobadara.backend.repository.donor.TakenInChargeDonorRepository;
import ma.almobadara.backend.repository.family.FamilyMemberRepository;
import ma.almobadara.backend.repository.services.ServicesRepository;
import ma.almobadara.backend.repository.takenInCharge.TakenInChargeBeneficiaryRepository;
import ma.almobadara.backend.service.communs.DocumentService;
import ma.almobadara.backend.service.Donation.DonationService;
import ma.almobadara.backend.util.times.TimeWatch;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static ma.almobadara.backend.service.administration.CacheAdUserService.getUsernameFromJwt;
import static ma.almobadara.backend.util.constants.GlobalConstants.*;

@RequiredArgsConstructor
@Slf4j
@Service
public class AideComplementaireService {

    private final AideComplementaireRepository aideComplementaireRepository;
    private final BeneficiaryRepository beneficiaryRepository;
    private final AideComplementaireMapper aideComplementaireMapper;
    private final AuditApplicationService auditApplicationService;
    private final TaggableRepository taggableRepository;
    private final EntityManager entityManager;
    private final RefFeignClient refFeignClient;
    private final CaisseRepository caisseRepository;
    private final BudgetLineRepository budgetLineRepository;
    private final DonorRepository donorRepository;
    private final DonationRepository donationRepository;
    private final BeneficiaryMapper beneficiaryMapper;
    private final TakenInChargeBeneficiaryRepository takenInChargeBeneficiaryRepository;
    private final TakenInChargeDonorRepository takenInChargeDonorRepository;
    private final AideComplementaireDonorBeneficiaryRepository aideComplementaireDonorBeneficiaryRepository;
    private final ServicesRepository servicesRepository ;
    private final BudgetLineMapper budgetLineMapper ;
    private final DonationService donationService;
    private final FamilyMemberRepository familyMemberRepository;
    private final BeneficiaryAdHocGroupRepository beneficiaryAdHocGroupRepository;
    private final DonationHistoryRepository donationHistoryRepository;
    private final BeneficiaryAdHocGroupeMapper beneficiaryAdHocGroupeMapper;
    private final AideComplementaireBeneficiaryAdHocGroupeRepository aideComplementaireBeneficiaryAdHocGroupeRepository;
    private final DocumentService documentService;
    private final DocumentMapper documentMapper;

    private static final String PREFIX = "AIDE_";

    /**
     * Checks if all beneficiaries and ad hoc groups in an aide complementaire have valid status
     * @param aideComplementaireId the ID of the aide complementaire to check
     * @return a map containing isAllValid flag and counts of valid/invalid items
     */
    public Map<String, Object> checkAllValids(Long aideComplementaireId) {
        Map<String, Object> result = new HashMap<>();

        // Check if aide complementaire exists
        AideComplementaire aideComplementaire = aideComplementaireRepository.findById(aideComplementaireId)
                .orElseThrow(() -> new EntityNotFoundException("Aide complementaire not found with ID: " + aideComplementaireId));

        // Use SQL query to check all beneficiaries validation status
        List<AideComplementaireDonorBeneficiary> beneficiaryRelations = 
                aideComplementaireDonorBeneficiaryRepository.findByAideComplementaire_Id(aideComplementaireId)
                .stream()
                .filter(relation -> relation.getBeneficiary() != null)
                .collect(Collectors.toList());

        long totalBeneficiaries = beneficiaryRelations.size();
        long validBeneficiaries = beneficiaryRelations.stream()
                .filter(relation -> Boolean.TRUE.equals(relation.getStatutValidation()))
                .count();

        // Use SQL query to check all ad hoc groups validation status
        List<AideComplementaireBeneficiaryAdHocGroupe> adHocRelations = 
                aideComplementaireBeneficiaryAdHocGroupeRepository.findByAideComplementaire_Id(aideComplementaireId);

        long totalAdHoc = adHocRelations.size();
        long validAdHoc = adHocRelations.stream()
                .filter(relation -> Boolean.TRUE.equals(relation.getStatutValidation()))
                .count();

        // Calculate if all are valid
        boolean isAllValid = (totalBeneficiaries == validBeneficiaries) && (totalAdHoc == validAdHoc);

        // Prepare result
        result.put("isAllValid", isAllValid);
        result.put("totalBeneficiaries", totalBeneficiaries);
        result.put("validBeneficiaries", validBeneficiaries);
        result.put("totalAdHoc", totalAdHoc);
        result.put("validAdHoc", validAdHoc);

        return result;
    }


    @Transactional
    public AideComplementaireDTO addAideComplementaire(AddAideComplementaireDTO aideComplementaireDTO) {
        AideComplementaire aideComplementaire = aideComplementaireMapper.AddDtotoEntity(aideComplementaireDTO);

        if (aideComplementaire.getId() == null){
            String uniqueCode = generateAideComplementaireCode();
            aideComplementaire.setCode(uniqueCode);
        }

        if (aideComplementaireDTO.getId() != null ) {
            Optional<AideComplementaire> existingAide = aideComplementaireRepository.findByIdWithQuery(aideComplementaireDTO.getId());
            existingAide.ifPresent(complementaire -> aideComplementaire.setId(complementaire.getId()));
            aideComplementaire.setModifiedAt(Instant.now());
        }

        String statut = checkAideComplementaireStatut(
                aideComplementaireDTO.getDateDebut(),
                aideComplementaireDTO.getDateFin()
        );

        if (statut != null) {
            aideComplementaire.setStatut(statut);
        }
        aideComplementaire.setSlogan(aideComplementaireDTO.getSlogan());
        AideComplementaire savedAideComplementaire = aideComplementaireRepository.save(aideComplementaire);
        savedAideComplementaire = aideComplementaireRepository.save(savedAideComplementaire);

        if (aideComplementaireDTO.getId() == null ) {
            List<BudgetLine> budgetLineList = budgetLineRepository.findByServiceIdAndStatus(savedAideComplementaire.getService().getId(), BudgetLineStatus.DISPONIBLE);
            for (BudgetLine budgetLine : budgetLineList) {
                reserveBudgetLineForAideComplementaire(budgetLine.getId(), savedAideComplementaire.getId());
            }
        }
        taggableRepository.deleteAllByTaggableIdAndTaggableType(aideComplementaire.getId(),"aideComplementaire");
        if(aideComplementaireDTO.getTags()!=null){
            aideComplementaireDTO.getTags().forEach(tagDTO -> {
                Taggable taggable=new Taggable();
                taggable.setTaggableType("aideComplementaire");
                taggable.setTaggableId(aideComplementaire.getId());
                Tag tag=new Tag();
                tag.setId(tagDTO.getId());
                taggable.setTag(tag);
                taggableRepository.save(taggable);
            });
        }

        return aideComplementaireMapper.toDto(savedAideComplementaire);
    }

    public Page<BeneficiaryAideComplemenatireDTO> getBeneficiariesForAide(
            Long aideId, Pageable pageable,String searchFullName,String category,String type,Boolean relatedToDonor,Boolean  validationStatus) throws TechnicalException {

        AideComplementaire aideComplementaire = aideComplementaireRepository.findById(aideId)
                .orElseThrow(() -> new TechnicalException("Aide Complementaire not found"));

        List<BeneficiaryAideComplemenatireDTO> beneficiaryList = new ArrayList<>();

        List<AideComplementaireDonorBeneficiary> donorBeneficiaries =
                aideComplementaireDonorBeneficiaryRepository.findByAideComplementaire_IdAndDonorIsNotNullAndBeneficiaryIsNotNull(aideId);

        for (AideComplementaireDonorBeneficiary entry : donorBeneficiaries) {
            Beneficiary beneficiary = entry.getBeneficiary();
            if (beneficiary != null) {
                BeneficiaryAideComplemenatireDTO dto = beneficiaryMapper.beneficiarytoBeneficiaryAideComplemenatireDTO(beneficiary);
                dto.setMontantAbeneficier(entry.getMontantAffecter() != null ? entry.getMontantAffecter() : 0.0);
                dto.setStatutValidation(entry.getStatutValidation());
                if(entry.getDonor()!=null){
                    dto.setDonorId(entry.getDonor().getId());
                    if(entry.getDonor() instanceof DonorPhysical){
                        DonorPhysical donorPhysical=(DonorPhysical) entry.getDonor();
                        dto.setNomCompletDuDonateur(donorPhysical.getFirstName()+" "+donorPhysical.getLastName());
                    }
                    else if(entry.getDonor() instanceof DonorMoral){
                        DonorMoral donorMoral=(DonorMoral) entry.getDonor();
                        dto.setNomCompletDuDonateur(donorMoral.getCompany());
                    }
                    else if(entry.getDonor() instanceof DonorAnonyme){
                        DonorAnonyme donorAnonyme=(DonorAnonyme) entry.getDonor();
                        dto.setNomCompletDuDonateur(donorAnonyme.getName());
                    }
                }
                dto.setRelatedToDonor(entry.getDonor() != null);
                dto.setType(beneficiary.getIndependent() ? BeneficiaryType.INDEPENDENT.getValue() : BeneficiaryType.MEMBRE_FAMILLE.getValue());
                beneficiaryList.add(dto);
            }
        }

        List<AideComplementaireDonorBeneficiary> beneficiariesWithoutDonor =
                aideComplementaireDonorBeneficiaryRepository.findByAideComplementaire_IdAndDonorIsNull(aideId);

        for (AideComplementaireDonorBeneficiary entry : beneficiariesWithoutDonor) {
            Beneficiary beneficiary = entry.getBeneficiary();
            if (beneficiary != null) {
                BeneficiaryAideComplemenatireDTO dto = beneficiaryMapper.beneficiarytoBeneficiaryAideComplemenatireDTO(beneficiary);
                dto.setMontantAbeneficier(entry.getMontantAffecter() != null ? entry.getMontantAffecter() : 0.0);

                dto.setStatutValidation(entry.getStatutValidation());
                dto.setRelatedToDonor(false);
                dto.setType(beneficiary.getIndependent() ? BeneficiaryType.INDEPENDENT.getValue() : BeneficiaryType.MEMBRE_FAMILLE.getValue());
                beneficiaryList.add(dto);
            }
        }

        List<AideComplementaireBeneficiaryAdHocGroupe> adHocGroupEntries =
                aideComplementaireBeneficiaryAdHocGroupeRepository.findByAideComplementaire_IdAndBeneficiaryIsNull(aideId);

        for (AideComplementaireBeneficiaryAdHocGroupe entry : adHocGroupEntries) {
            BeneficiaryAdHocGroup adHocGroup = entry.getBeneficiaryAdHocGroup();
            BeneficiaryAideComplemenatireDTO dto = beneficiaryAdHocGroupeMapper
                    .beneficiaryAdHocGroupetoBeneficiaryAdHocGroupeAideComplemenatireDTO(adHocGroup);
            dto.setMontantAbeneficier(entry.getMontantAffecter() != null ? entry.getMontantAffecter() : 0.0);
            dto.setStatutValidation(entry.getStatutValidation());
            dto.setNumberOfMembers(entry.getNumberOfMembersBenefiting());
            dto.setRelatedToDonor(false);
            dto.setMontantAbeneficier(entry.getMontantAffecter());
            dto.setType(BeneficiaryType.GROUPE.getValue());
            beneficiaryList.add(dto);
        }

        beneficiaryList = beneficiaryList.stream()
                .filter(dto -> searchFullName == null || (dto.getFirstName() + " " + dto.getLastName()).toLowerCase().contains(searchFullName.toLowerCase()))
                .filter(dto -> type == null || type.isEmpty() || dto.getType().equalsIgnoreCase(type))
                .filter(dto -> relatedToDonor == null || dto.getRelatedToDonor().equals(relatedToDonor))
                .filter(dto -> validationStatus == null || dto.getStatutValidation().equals(validationStatus))
                .filter(dto -> category == null || dto.getStatut().equals(category))
                .collect(Collectors.toList());

        // Sort the beneficiary list by ID to ensure consistent ordering
        beneficiaryList.sort(Comparator.comparing(BeneficiaryAideComplemenatireDTO::getId));

        int start = Math.min((int) pageable.getOffset(), beneficiaryList.size());
        int end = Math.min((start + pageable.getPageSize()), beneficiaryList.size());
        List<BeneficiaryAideComplemenatireDTO> pageContent = beneficiaryList.subList(start, end);

        return new PageImpl<>(pageContent, pageable, beneficiaryList.size());
    }

    public AideComplementaireDTO getAideComplementaireById(Long id) throws TechnicalException {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service getAideComplementaireById: {}", id);

        if (id == null) {
            throw new TechnicalException("Aide Complemenataire not found");
        }
        Optional<AideComplementaire> optionalAideComplementaire = aideComplementaireRepository.findById(id);
        if (optionalAideComplementaire.isEmpty()) {
            throw new TechnicalException("Aide Complemenataire not found");
        }
        AideComplementaire aideComplementaire = optionalAideComplementaire.get();
        aideComplementaire.updateStatut();
        aideComplementaireRepository.save(aideComplementaire);
        AideComplementaireDTO aideComplementaireDTO = aideComplementaireMapper.toDto(aideComplementaire);
        if (aideComplementaire.getDocument() != null){
            DocumentDTO documentDTO = documentMapper.documentModelToDto(aideComplementaire.getDocument());
            aideComplementaireDTO.setDocumentDto(documentDTO);
        }

        if (aideComplementaire.getService() != null && aideComplementaire.getService().getId() != null){
            Services services = servicesRepository.findById(aideComplementaire.getService().getId()).orElseThrow();
            aideComplementaireDTO.setServices(services);
        }

        List<DonorAideComplemenatireDTO> donorAideComplemenatireDTOList = new ArrayList<>();
        double total;
        double totalRestant;
        double totalAffecter = 0;
        long nbrBeneficiariesValidated = 0L;
        List<BudgetLine> budgetLines = budgetLineRepository.findByServiceId(aideComplementaire.getService().getId());

        List<BudgetLine> filteredBudgetLines = budgetLines.stream()
                .filter(line ->
                        (line.getStatus() == BudgetLineStatus.RESERVED &&
                                line.getAideComplementaire() != null &&
                                line.getAideComplementaire().getId().equals(aideComplementaire.getId())) ||
                                (line.getStatus() == BudgetLineStatus.EXECUTED &&
                                        line.getAideComplementaire() != null &&
                                        line.getAideComplementaire().getId().equals(aideComplementaire.getId())))
                .toList();
        total = filteredBudgetLines.stream().mapToDouble(BudgetLine::getAmount).sum();
        List<AideComplementaireDonorBeneficiary> emptyBeneficiariesEntries =
                aideComplementaireDonorBeneficiaryRepository.findByAideComplementaire_IdAndBeneficiaryIsNull(aideComplementaire.getId());

        for (AideComplementaireDonorBeneficiary entry : emptyBeneficiariesEntries) {
            DonorAideComplemenatireDTO donorAideComplemenatireDTO = new DonorAideComplemenatireDTO();
            Donor donor = entry.getDonor();


            donorAideComplemenatireDTO = mapDonorBasedOnType(donor, donorAideComplemenatireDTO);
            donorAideComplemenatireDTO.setMontantTotalDuDonateur(entry.getMontantTotalDuDonateur());
            donorAideComplemenatireDTO.setMontantRestantDuDonateur(entry.getMontantRestantDuDonateur());
            donorAideComplemenatireDTO.setMontantPoserDuDonateur(entry.getMontantPoserDuDonateur());
            donorAideComplemenatireDTO.setIsRelatedToBeneficiary(false);
            donorAideComplemenatireDTOList.add(donorAideComplemenatireDTO);
        }

        List<AideComplementaireDonorBeneficiary> aideComplementaireDonorBeneficiary=aideComplementaireDonorBeneficiaryRepository.findByAideComplementaire_Id(aideComplementaire.getId());
        List<AideComplementaireBeneficiaryAdHocGroupe> aideComplementaireBeneficiaryAdHocGroupes=aideComplementaireBeneficiaryAdHocGroupeRepository.findByAideComplementaire_Id(aideComplementaire.getId());

        for(AideComplementaireDonorBeneficiary aideComplementaireDonorBeneficiary1:aideComplementaireDonorBeneficiary){
            if(aideComplementaireDonorBeneficiary1.getStatutValidation()){
                totalAffecter+=aideComplementaireDonorBeneficiary1.getMontantAffecter();
                nbrBeneficiariesValidated++;
            }
        }

        for(AideComplementaireBeneficiaryAdHocGroupe aideComplementaireBeneficiaryAdHocGroupe:aideComplementaireBeneficiaryAdHocGroupes){
            if(aideComplementaireBeneficiaryAdHocGroupe.getStatutValidation()){
                totalAffecter += aideComplementaireBeneficiaryAdHocGroupe.getMontantAffecter()
                        * aideComplementaireBeneficiaryAdHocGroupe.getNumberOfMembersBenefiting();
                nbrBeneficiariesValidated++;
            }

        };
        totalRestant = total - totalAffecter;

        aideComplementaireDTO.setDonorAideComplemenatireDTOList(donorAideComplemenatireDTOList);
        aideComplementaireDTO.setMontantCollecter(total);
        aideComplementaireDTO.setMontantRestant(totalRestant);
        aideComplementaireDTO.setMontantTotalAffecter(totalAffecter);
        aideComplementaireDTO.setNbrBeneficiariesValidated(nbrBeneficiariesValidated);
        aideComplementaireDTO.setSlogan(aideComplementaire.getSlogan());
        List<Taggable> taggables=taggableRepository.findByTaggableIdAndTaggableType(aideComplementaireDTO.getId(),"aideComplementaire");
        List<TagDTO> tags = new ArrayList<>();
        for (Taggable taggable : taggables) {
            TagDTO tagDTO = new TagDTO();
            tagDTO.setId(taggable.getTag().getId());
            tagDTO.setName(taggable.getTag().getName());
            tagDTO.setColor(taggable.getTag().getColor());
            tags.add(tagDTO);
        }
        aideComplementaireDTO.setTags(tags);
        log.debug("End service getAideComplementaireById : {}, took {}", id, watch.toMS());
        return aideComplementaireDTO;
    }



    public static DonorAideComplemenatireDTO mapDonorBasedOnType(Donor donor, DonorAideComplemenatireDTO donorAideComplemenatireDTO) {
        if (donor instanceof DonorPhysical donorPhysical) {
            donorAideComplemenatireDTO.setId(donor.getId());
            donorAideComplemenatireDTO.setCode(donor.getCode());
            donorAideComplemenatireDTO.setFirstName(donorPhysical.getFirstName());
            donorAideComplemenatireDTO.setLastName(donorPhysical.getLastName());
        } else if (donor instanceof DonorMoral donorMoral) {
            donorAideComplemenatireDTO.setId(donor.getId());
            donorAideComplemenatireDTO.setCode(donor.getCode());
            donorAideComplemenatireDTO.setFirstName(donorMoral.getCompany());
            donorAideComplemenatireDTO.setLastName(donorMoral.getShortCompany());
        }


        return donorAideComplemenatireDTO;
    }



    public static String generateAideComplementaireCode() {
        String uniqueId = UUID.randomUUID().toString().substring(0, 8).toUpperCase();
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));

        return PREFIX + uniqueId + "_" + timestamp;
    }

    @Transactional
    public List<DonorForAideComplementaireDTO> getActiveDonorsForAideComplementaire(Long aideComplementaireId) {
        AideComplementaire aideComplementaire = aideComplementaireRepository.findById(aideComplementaireId)
                .orElseThrow(() -> new EntityNotFoundException("AideComplementaire not found with ID: " + aideComplementaireId));

        Services services = servicesRepository.findById(aideComplementaire.getService().getId())
                .orElseThrow(() -> new EntityNotFoundException("Service not found for AideComplementaire"));

        List<BudgetLine> budgetLines = budgetLineRepository.findByServiceId(services.getId());

        Set<Long> excludedDonorIds = aideComplementaireDonorBeneficiaryRepository
                .findAideComplementaireDonorBeneficiariesByAideComplementaire_Id(aideComplementaireId)
                .stream()
                .filter(aide -> aide.getDonor() != null)
                .map(aide -> aide.getDonor().getId())
                .collect(Collectors.toSet());

        Set<Long> processedDonorIds = new HashSet<>();

        List<DonorForAideComplementaireDTO> activeDonors = new ArrayList<>();

        for (BudgetLine budgetLine : budgetLines) {
            if (budgetLine.getStatus().equals(BudgetLineStatus.DISPONIBLE) && budgetLine.getDonation() != null) {
                Donation donation = budgetLine.getDonation();
                Donor donor = donation.getDonor();

                if (donor != null && !excludedDonorIds.contains(donor.getId()) && !processedDonorIds.contains(donor.getId())) {
                    activeDonors.add(mapDonorForAideComplementaire(donor, new DonorForAideComplementaireDTO()));
                    processedDonorIds.add(donor.getId());
                }
            }
        }

        return activeDonors;
    }



    public static DonorForAideComplementaireDTO mapDonorForAideComplementaire(Donor donor, DonorForAideComplementaireDTO donorForAideComplementaireDTO) {
        if (donor instanceof DonorPhysical donorPhysical) {
            donorForAideComplementaireDTO.setId(donor.getId());
            donorForAideComplementaireDTO.setCode(donor.getCode());
            donorForAideComplementaireDTO.setFirstName(donorPhysical.getFirstName());
            donorForAideComplementaireDTO.setLastName(donorPhysical.getLastName());
        } else if (donor instanceof DonorMoral donorMoral) {
            donorForAideComplementaireDTO.setId(donor.getId());
            donorForAideComplementaireDTO.setCode(donor.getCode());
            donorForAideComplementaireDTO.setFirstName(donorMoral.getCompany());
            donorForAideComplementaireDTO.setLastName(donorMoral.getShortCompany());
        }

        return donorForAideComplementaireDTO;
    }




    public Page<AideComplementaireDTO> getAllAideComplementaire(Integer page, Integer size, String searchByNom,  Long searchByTypePriseEnChargeId, String searchByStatut, Long searchByMontant, Date minDate, Date maxDate,Long searchByTagId) throws TechnicalException {
        Sort sort = Sort.by(Sort.Direction.DESC, "modifiedAt");

        Pageable pageable = PageRequest.of(page, size,sort);

        Map<String, String> searchParams = new HashMap<>();
        if (searchByNom != null) {
            searchParams.put("nom de l'aide complemenatire", searchByNom);
        }
        if (searchByTypePriseEnChargeId != null) {
            searchParams.put("Typ de l'aide complemenatire", String.valueOf(searchByTypePriseEnChargeId));
        }

        String jsonSearchParams = convertMapToJsonString(searchParams);

        Page<AideComplementaire> aidesComplementaires;
        if (searchByNom != null || searchByTypePriseEnChargeId != null || searchByStatut != null || searchByMontant != null || minDate != null || maxDate != null|| searchByTagId != null) {
            aidesComplementaires = filterAideComplementaire(searchByNom, searchByTypePriseEnChargeId, searchByStatut,searchByMontant,minDate,maxDate,searchByTagId, pageable);
            auditApplicationService.audit("Recherche par filtre dans la liste des Aides Complémentaires", getUsernameFromJwt(), "Liste des Aides Complémentaires",
                    jsonSearchParams, null, AIDE_COMPLEMENTAIRE, VIEW);
        } else {
            aidesComplementaires = aideComplementaireRepository.findAll(pageable);
            auditApplicationService.audit("Consultation de la liste globale des Aides Complémentaires", getUsernameFromJwt(), "Liste des Aides Complémentaires",
                    null, null, AIDE_COMPLEMENTAIRE, CONSULTATION);
        }

        List<AideComplementaireDTO> aideComplementaireDTOList = aidesComplementaires.getContent().stream()
                .map(aideComplementaire -> {
                    aideComplementaire.updateStatut();
                    aideComplementaireRepository.save(aideComplementaire);
                    AideComplementaireDTO dto = aideComplementaireMapper.toDto(aideComplementaire);
                    dto.setSlogan(aideComplementaire.getSlogan());
                    List<AideComplementaireDonorBeneficiary> aideComplementaireDonor =  aideComplementaireDonorBeneficiaryRepository.findByAideComplementaire_IdAndBeneficiaryIsNull(aideComplementaire.getId());
                    List<AideComplementaireDonorBeneficiary> aideComplementaireBeneficiaries =  aideComplementaireDonorBeneficiaryRepository.findByAideComplementaire_IdAndDonorIsNotNullAndBeneficiaryIsNotNull(aideComplementaire.getId());
                    List<AideComplementaireDonorBeneficiary> aideComplementaireDonorBeneficiaries =  aideComplementaireDonorBeneficiaryRepository.findByAideComplementaire_IdAndDonorIsNull(aideComplementaire.getId());
                    long nbrTotalDonor =  0;
                    long nbrTotalBeny =  0;

                    if (!aideComplementaireDonor.isEmpty()){
                        nbrTotalDonor = aideComplementaireDonor.size();
                    }

                    dto.setNbrDonorsParticipating(nbrTotalDonor);

                    if (!aideComplementaireBeneficiaries.isEmpty()){
                        for (AideComplementaireDonorBeneficiary beneficiary : aideComplementaireBeneficiaries){
                            if (beneficiary.getStatutValidation()){
                                nbrTotalBeny += 1;
                            }
                        }
                    }
                    if (!aideComplementaireDonorBeneficiaries.isEmpty()){
                        for (AideComplementaireDonorBeneficiary beneficiary : aideComplementaireDonorBeneficiaries){
                            if (beneficiary.getStatutValidation()){
                                nbrTotalBeny += 1;
                            }
                        }
                    }

                    dto.setNbrBeneficiariesValidated(nbrTotalBeny);

                    // Calculate and set montantCollecter
                    List<BudgetLine> budgetLines = budgetLineRepository.findByServiceId(aideComplementaire.getService().getId());
                    List<BudgetLine> filteredBudgetLines = budgetLines.stream()
                            .filter(line ->
                                    (line.getStatus() == BudgetLineStatus.RESERVED &&
                                            line.getAideComplementaire() != null &&
                                            line.getAideComplementaire().getId().equals(aideComplementaire.getId())) ||
                                            (line.getStatus() == BudgetLineStatus.EXECUTED &&
                                                    line.getAideComplementaire() != null &&
                                                    line.getAideComplementaire().getId().equals(aideComplementaire.getId())))
                            .toList();
                    double total = filteredBudgetLines.stream().mapToDouble(BudgetLine::getAmount).sum();
                    dto.setMontantCollecter(total);
                    return dto;
                })
                .collect(Collectors.toList());

        aideComplementaireDTOList= aideComplementaireDTOList.stream().peek(dto->{
            List<Taggable> taggables = taggableRepository.findByTaggableIdAndTaggableType(dto.getId(), "aideComplementaire");
            List<TagDTO> tagDTOs = new ArrayList<>();
            for (Taggable taggable : taggables) {
                Tag tag = taggable.getTag();
                TagDTO tagDTO = new TagDTO();
                tagDTO.setId(tag.getId());
                tagDTO.setName(tag.getName());
                tagDTO.setColor(tag.getColor());
                tagDTOs.add(tagDTO);
            }
            dto.setTags(tagDTOs);
        }).collect(Collectors.toList());

        return new PageImpl<>(aideComplementaireDTOList, pageable, aidesComplementaires.getTotalElements());
    }

    public Page<AideComplementaire> filterAideComplementaire(String searchByNom, Long searchByTypePriseEnChargeId, String searchByStatut, Long searchByMontant, Date minDate, Date maxDate,Long searchByTagId, Pageable pageable) {
        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<AideComplementaire> criteriaQuery = criteriaBuilder.createQuery(AideComplementaire.class);
        Root<AideComplementaire> root = criteriaQuery.from(AideComplementaire.class);

        Predicate predicate = buildPredicate(criteriaBuilder, root, searchByNom, searchByTypePriseEnChargeId, searchByStatut,searchByMontant,minDate,maxDate,searchByTagId);

        criteriaQuery.where(predicate);

        TypedQuery<AideComplementaire> typedQuery = entityManager.createQuery(criteriaQuery);
        long totalCount = typedQuery.getResultList().size();

        typedQuery.setFirstResult((int) pageable.getOffset());
        typedQuery.setMaxResults(pageable.getPageSize());

        List<AideComplementaire> resultList = typedQuery.getResultList();
        return new PageImpl<>(resultList, pageable, totalCount);
    }

    private Predicate buildPredicate(CriteriaBuilder criteriaBuilder, Root<AideComplementaire> root,
                                     String searchByNom, Long searchByTypePriseEnChargeId, String searchByStatut, Long searchByMontant, Date minDate, Date maxDate,Long searchByTagId) {
        Predicate predicate = criteriaBuilder.conjunction();


        if (searchByNom != null && !searchByNom.isEmpty()) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(criteriaBuilder.lower(root.get("name")), "%" + searchByNom.toLowerCase() + "%"));
        }


        if (searchByTypePriseEnChargeId != null) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.join("service").get("serviceCategoryTypeId"), searchByTypePriseEnChargeId));
        }

        if (searchByStatut != null) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("statut"), searchByStatut));
        }

        if (searchByMontant != null) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("montantPrevu"), searchByMontant));
        }

        if (minDate != null) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.greaterThanOrEqualTo(root.get("datePlanification"), minDate));
        }
        if (maxDate != null) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.lessThanOrEqualTo(root.get("datePlanification"), maxDate));
        }

        if (searchByTagId != null) {
            // Create a subquery to find donors with the specified tag
            Subquery<Long> subquery = criteriaBuilder.createQuery().subquery(Long.class);
            Root<Taggable> taggableRoot = subquery.from(Taggable.class);
            subquery.select(taggableRoot.get("taggableId"))
                    .where(
                            criteriaBuilder.and(
                                    criteriaBuilder.equal(taggableRoot.get("taggableType"), "aideComplementaire"),
                                    criteriaBuilder.equal(taggableRoot.get("tag").get("id"), searchByTagId)
                            )
                    );

            // Add the subquery condition to the main predicate
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.in(root.get("id")).value(subquery));
        }

        return predicate;
    }

    private String convertMapToJsonString(Map<String, String> map) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.writeValueAsString(map);
        } catch (Exception e) {
            e.printStackTrace();
            return "{}";
        }
    }
    public List<BeneficiaryForTypeKafalatDTO> getBeneficiariesWithTypeKafalatId(Long typePriseEnChargeId){

        List<Beneficiary> allBeneficiaries = beneficiaryRepository.findAll();
        List<BeneficiaryForTypeKafalatDTO> result = new ArrayList<>();

        for (Beneficiary beneficiary : allBeneficiaries) {
            Person person = beneficiary.getPerson();

            if (person != null && person.getTypePriseEnChargeIdsList() != null) {
                String typePriseEnChargeIdsList = person.getTypePriseEnChargeIdsList();

                if (Arrays.asList(typePriseEnChargeIdsList.split(","))
                        .contains(String.valueOf(typePriseEnChargeId))) {

                    BeneficiaryForTypeKafalatDTO dto = new BeneficiaryForTypeKafalatDTO();
                    dto.setId(beneficiary.getId());
                    dto.setCode(beneficiary.getCode());
                    dto.setFirstName(person.getFirstName());
                    dto.setLastName(person.getLastName());
                    dto.setFirstNameAr(person.getFirstNameAr());
                    dto.setLastNameAr(person.getLastNameAr());

                    result.add(dto);
                }
            }
        }

        return result;

    }

    public List<TypePriseEnChargeDTO> loadTypePriseEnChargeWithCaisse(){
        List<TypePriseEnChargeDTO> allTypes = refFeignClient.getAllParTypePriseEnCharge();
        List<Long> typeIdsLinkedToCaisse = caisseRepository.findAll()
                .stream()
                .map(Caisse::getTypePriseEnChargeId)
                .collect(Collectors.toList());

        return allTypes.stream()
                .filter(type -> typeIdsLinkedToCaisse.contains(type.getId()))
                .collect(Collectors.toList());

    }


    @Transactional
    public Page<BeneficiaryForAideComplementaireDTO> getActiveBeneficiariesForAideComplementaire(
            Long aideComplementaireId, String category, String type, Boolean withParticipatingMembers,
            Boolean withDonor, Boolean withParticipatingDonor, Boolean withOldCampagne, String text, Pageable pageable) {

        // Fetch aide complementaire once and cache the reference
        AideComplementaire aideComplementaire = aideComplementaireRepository.findById(aideComplementaireId)
                .orElseThrow();
        Long serviceCategoryTypeId = aideComplementaire.getService().getServiceCategoryTypeId();
        String serviceTypeIdStr = serviceCategoryTypeId.toString();

        // Fetch all active beneficiaries using statut IDs
        List<Beneficiary> activeBeneficiaries = beneficiaryRepository.findAllByArchivedFalseOrArchivedNullAndBeneficiaryStatutIdIn(
                List.of(
                        BeneficiaryStatus.BENEFICIAIRE_ACTIF.getId(),
                        BeneficiaryStatus.BENEFICIAIRE_EN_ATTENTE.getId(),
                        BeneficiaryStatus.CANDIDAT_A_UPDATER.getId(),
                        BeneficiaryStatus.BENEFICIARY_AD_HOC_GROUP.getId(),
                        BeneficiaryStatus.BENEFICIARY_AD_HOC_INDIVIDUAL.getId()
                )
        );

        // Create indexes for faster lookups
        Map<Long, Boolean> beneficiaryInAdHocGroupIndex = new HashMap<>();
        Map<Long, List<Beneficiary>> personIdToBeneficiaryIndex = new HashMap<>();
        Map<Long, Beneficiary> beneficiaryIdToEntity = new HashMap<>();

        // Apply text filter early if present
        if (text != null && !text.isEmpty()) {
            final String lowerText = text.toLowerCase();
            activeBeneficiaries.removeIf(b -> {
                String fullName = (b.getPerson().getFirstName() + " " +
                        b.getPerson().getLastName() + " " +
                        b.getPerson().getIdentityCode()).toLowerCase();
                return !fullName.contains(lowerText);
            });
        }

        // Apply category filter early
        if (category != null) {
            activeBeneficiaries.removeIf(b -> !b.getBeneficiaryStatut().getNameStatut().equals(category));
        }

        // Build indexes in one pass
        for (Beneficiary beneficiary : activeBeneficiaries) {
            beneficiaryIdToEntity.put(beneficiary.getId(), beneficiary);
            personIdToBeneficiaryIndex.computeIfAbsent(beneficiary.getPerson().getId(), k -> new ArrayList<>()).add(beneficiary);
        }

        // Fetch excluded beneficiary IDs (already in the aide)
        Set<Long> excludedBeneficiaryIds = aideComplementaireDonorBeneficiaryRepository
                .findAideComplementaireDonorBeneficiariesByAideComplementaire_Id(aideComplementaireId)
                .stream()
                .filter(aide -> aide.getBeneficiary() != null)
                .map(aide -> aide.getBeneficiary().getId())
                .collect(Collectors.toSet());

        // Fetch excluded adhoc group IDs (already in the aide)
        Set<Long> excludedBeneficiaryAdHocGroupIds = aideComplementaireBeneficiaryAdHocGroupeRepository
                .findByAideComplementaire_IdAndBeneficiaryIsNull(aideComplementaireId)
                .stream()
                .filter(aide -> aide.getBeneficiaryAdHocGroup() != null)
                .map(aide -> aide.getBeneficiaryAdHocGroup().getId())
                .collect(Collectors.toSet());

        // Build ad-hoc group membership index in one pass
        List<BeneficiaryAdHocGroup> beneficiaryAdHocGroups = beneficiaryAdHocGroupRepository.findAll();
        for (BeneficiaryAdHocGroup group : beneficiaryAdHocGroups) {
            for (Beneficiary b : group.getBeneficiaries()) {
                beneficiaryInAdHocGroupIndex.put(b.getId(), Boolean.TRUE);
            }
        }

        // Find excluded ad-hoc individual ids in one pass
        Set<Long> excludedBeneficiaryAdHocIndividuelIds = new HashSet<>();
        for (Beneficiary beneficiary : activeBeneficiaries) {
            if (beneficiary.getBeneficiaryStatut().getId().equals(BeneficiaryStatus.BENEFICIARY_AD_HOC_INDIVIDUAL.getId())) {
                if (Boolean.TRUE.equals(beneficiaryInAdHocGroupIndex.get(beneficiary.getId()))) {
                    excludedBeneficiaryAdHocIndividuelIds.add(beneficiary.getId());
                } else {
                    String typePriseEnChargeIdsList = beneficiary.getPerson().getTypePriseEnChargeIdsList();
                    if (typePriseEnChargeIdsList != null && !typePriseEnChargeIdsList.contains(serviceTypeIdStr)) {
                        excludedBeneficiaryAdHocIndividuelIds.add(beneficiary.getId());
                    }
                }
            }
        }

        // Apply specialized filters using pre-computed indexes when possible
        if (withParticipatingMembers != null) {
            activeBeneficiaries = filterBeneficiariesByParticipatingMembers(activeBeneficiaries, aideComplementaireId, withParticipatingMembers);
        }
        if (withDonor != null) {
            activeBeneficiaries = filterBeneficiariesWithDonor(activeBeneficiaries, withDonor);
        }
        if (withParticipatingDonor != null) {
            activeBeneficiaries = filterBeneficiariesWithParticipatingDonor(activeBeneficiaries, aideComplementaireId, withParticipatingDonor);
        }
        if (withOldCampagne != null) {
            activeBeneficiaries = filterBeneficiariesWithOldCampagne(activeBeneficiaries, aideComplementaireId, withOldCampagne);
        }

        // Create final DTOs with pre-filtered list
        List<BeneficiaryForAideComplementaireDTO> allActiveBeneficiaries = new ArrayList<>(activeBeneficiaries.size());
        for (Beneficiary beneficiary : activeBeneficiaries) {
            if (!excludedBeneficiaryIds.contains(beneficiary.getId()) &&
                    !excludedBeneficiaryAdHocIndividuelIds.contains(beneficiary.getId())) {

                BeneficiaryForAideComplementaireDTO dto = beneficiaryMapper.toBeneficiaryForAideComplementaireDTO(beneficiary);
                if (dto.getIndependent()) {
                    dto.setType(BeneficiaryType.INDEPENDENT.getValue());
                } else {
                    dto.setType(BeneficiaryType.MEMBRE_FAMILLE.getValue());
                }
                allActiveBeneficiaries.add(dto);
            }
        }

        // Add AdHoc groups only when no specialized filters are applied
        if (withParticipatingMembers == null && withDonor == null &&
                withParticipatingDonor == null && withOldCampagne == null) {

            for (BeneficiaryAdHocGroup group : beneficiaryAdHocGroups) {
                if (!excludedBeneficiaryAdHocGroupIds.contains(group.getId())) {
                    String typePriseEnChargeIdsList = group.getTypePriseEnChargeIdsList();
                    if (typePriseEnChargeIdsList != null && typePriseEnChargeIdsList.contains(serviceTypeIdStr)) {
                        BeneficiaryForAideComplementaireDTO dto = beneficiaryAdHocGroupeMapper.toBeneficiaryAdHocForAideComplementaireDTO(group);
                        dto.setIndependent(false);
                        dto.setType(BeneficiaryType.GROUPE.getValue());
                        allActiveBeneficiaries.add(dto);
                    }
                }
            }
        }

        // Apply type filter if needed
        if (type != null) {
            allActiveBeneficiaries.removeIf(dto -> !type.equals(dto.getType()));
        }

        // Apply pagination efficiently
        int start = (int) pageable.getOffset();
        int size = allActiveBeneficiaries.size();

        if (start >= size) {
            return new PageImpl<>(Collections.emptyList(), pageable, size);
        }

        int end = Math.min(start + pageable.getPageSize(), size);
        List<BeneficiaryForAideComplementaireDTO> pagedContent = allActiveBeneficiaries.subList(start, end);

        return new PageImpl<>(pagedContent, pageable, size);
    }

    private List<Beneficiary> filterBeneficiariesByParticipatingMembers(List<Beneficiary> beneficiaries, Long aideComplementaireId, Boolean withParticipatingMembers) {
        // Get family members for all non-independent beneficiaries in one database call
        Set<Long> nonIndependentPersonIds = beneficiaries.stream()
                .filter(b -> !b.getIndependent())
                .map(b -> b.getPerson().getId())
                .collect(Collectors.toSet());

        if (nonIndependentPersonIds.isEmpty()) {
            return withParticipatingMembers ? Collections.emptyList() : beneficiaries;
        }

        // Fetch all family members in one go
        List<FamilyMember> allFamilyMembers = familyMemberRepository.findByPersonIdIn(nonIndependentPersonIds);

        // Build indexes for quick lookups
        Map<Long, FamilyMember> personIdToFamilyMember = new HashMap<>();
        Map<Long, Set<Long>> familyIdToPersonIds = new HashMap<>();

        for (FamilyMember member : allFamilyMembers) {
            personIdToFamilyMember.put(member.getPerson().getId(), member);
            familyIdToPersonIds
                    .computeIfAbsent(member.getFamily().getId(), k -> new HashSet<>())
                    .add(member.getPerson().getId());
        }

        // Get all beneficiaries in the aide complementaire to check participation
        Set<Long> beneficiaryIdsInAide = aideComplementaireDonorBeneficiaryRepository
                .findAideComplementaireDonorBeneficiariesByAideComplementaire_Id(aideComplementaireId)
                .stream()
                .filter(a -> a.getBeneficiary() != null)
                .map(a -> a.getBeneficiary().getId())
                .collect(Collectors.toSet());

        // Map person IDs to beneficiary IDs for lookup
        Map<Long, Long> personIdToBeneficiaryId = new HashMap<>();
        List<Beneficiary> allRelatedBeneficiaries = beneficiaryRepository.findByPersonIdIn(
                familyIdToPersonIds.values().stream()
                        .flatMap(Set::stream)
                        .collect(Collectors.toSet())
        );

        for (Beneficiary b : allRelatedBeneficiaries) {
            personIdToBeneficiaryId.put(b.getPerson().getId(), b.getId());
        }

        return beneficiaries.stream()
                .filter(beneficiary -> {
                    if (!beneficiary.getIndependent()) {
                        FamilyMember familyMember = personIdToFamilyMember.get(beneficiary.getPerson().getId());
                        if (familyMember != null) {
                            Set<Long> familyPersonIds = familyIdToPersonIds.get(familyMember.getFamily().getId());
                            if (familyPersonIds != null) {
                                // Check if any family member is in the aide
                                for (Long personId : familyPersonIds) {
                                    if (!personId.equals(beneficiary.getPerson().getId())) {
                                        Long relatedBeneficiaryId = personIdToBeneficiaryId.get(personId);
                                        if (relatedBeneficiaryId != null && beneficiaryIdsInAide.contains(relatedBeneficiaryId)) {
                                            return withParticipatingMembers;
                                        }
                                    }
                                }
                            }
                        }
                    }
                    return !withParticipatingMembers;
                })
                .collect(Collectors.toList());
    }


    private List<Beneficiary> filterBeneficiariesWithDonor(List<Beneficiary> beneficiaries, Boolean withDonor) {
        Long statusToFilter = withDonor
                ? BeneficiaryStatus.BENEFICIAIRE_ACTIF.getId()
                : BeneficiaryStatus.BENEFICIAIRE_EN_ATTENTE.getId();

        return beneficiaries.stream()
                .filter(beneficiary -> beneficiary.getBeneficiaryStatut().getId().equals(statusToFilter))
                .collect(Collectors.toList());
    }

    private List<Beneficiary> filterBeneficiariesWithParticipatingDonor(List<Beneficiary> beneficiaries, Long aideComplementaireId, Boolean withParticipatingDonor) {
        if (beneficiaries.isEmpty()) {
            return beneficiaries;
        }

        // Get all beneficiary IDs to fetch related data in one go
        Set<Long> beneficiaryIds = beneficiaries.stream()
                .map(Beneficiary::getId)
                .collect(Collectors.toSet());

        // Fetch all taken-in-charge beneficiaries in one database call
        List<TakenInChargeBeneficiary> allTakenInChargeBeneficiaries = takenInChargeBeneficiaryRepository
                .findByBeneficiaryIdIn(beneficiaryIds);

        // Build index of beneficiary ID to taken-in-charge IDs
        Map<Long, Set<Long>> beneficiaryToTakenInChargeIds = new HashMap<>();
        for (TakenInChargeBeneficiary takenInChargeBeneficiary : allTakenInChargeBeneficiaries) {
            beneficiaryToTakenInChargeIds
                    .computeIfAbsent(takenInChargeBeneficiary.getBeneficiary().getId(), k -> new HashSet<>())
                    .add(takenInChargeBeneficiary.getTakenInCharge().getId());
        }

        // Fetch all related taken-in-charge donors in one batch
        Set<Long> takenInChargeIds = allTakenInChargeBeneficiaries.stream()
                .map(t -> t.getTakenInCharge().getId())
                .collect(Collectors.toSet());

        // Exit early if no taken-in-charge relationships found
        if (takenInChargeIds.isEmpty()) {
            return withParticipatingDonor ? Collections.emptyList() : beneficiaries;
        }

        // Get all donors for these taken-in-charge entries
        List<TakenInChargeDonor> allTakenInChargeDonors = takenInChargeDonorRepository
                .findByTakenInChargeIdIn(takenInChargeIds);

        // Build index of taken-in-charge ID to donor IDs
        Map<Long, Set<Long>> takenInChargeToDonorIds = new HashMap<>();
        for (TakenInChargeDonor donor : allTakenInChargeDonors) {
            takenInChargeToDonorIds
                    .computeIfAbsent(donor.getTakenInCharge().getId(), k -> new HashSet<>())
                    .add(donor.getDonor().getId());
        }

        // Get all donors participating in this aide
        Set<Long> donorsInAide = aideComplementaireDonorBeneficiaryRepository
                .findAideComplementaireDonorBeneficiariesByAideComplementaire_IdAndBeneficiaryIsEmpty(aideComplementaireId)
                .stream()
                .map(aide -> aide.getDonor().getId())
                .collect(Collectors.toSet());

        // Final filtering using pre-built indexes
        return beneficiaries.stream()
                .filter(beneficiary -> {
                    Set<Long> takenInChargeIdsForBeneficiary = beneficiaryToTakenInChargeIds.get(beneficiary.getId());
                    if (takenInChargeIdsForBeneficiary != null) {
                        for (Long takenInChargeId : takenInChargeIdsForBeneficiary) {
                            Set<Long> donorIds = takenInChargeToDonorIds.get(takenInChargeId);
                            if (donorIds != null) {
                                // Check if any donor is in the aide
                                for (Long donorId : donorIds) {
                                    if (donorsInAide.contains(donorId)) {
                                        return withParticipatingDonor;
                                    }
                                }
                            }
                        }
                    }
                    return !withParticipatingDonor;
                })
                .collect(Collectors.toList());
    }

    private List<Beneficiary> filterBeneficiariesWithOldCampagne(List<Beneficiary> beneficiaries, Long aideComplementaireId, Boolean withOldCampagne) {
        if (beneficiaries.isEmpty()) {
            return beneficiaries;
        }

        // Get current aide to check service
        AideComplementaire aideComplementaire = aideComplementaireRepository.findById(aideComplementaireId).orElseThrow();
        Long serviceId = aideComplementaire.getService().getId();

        // Get all beneficiary IDs to fetch related data in one go
        Set<Long> beneficiaryIds = beneficiaries.stream()
                .map(Beneficiary::getId)
                .collect(Collectors.toSet());

        // Fetch all aide relationships for all beneficiaries in one call
        List<AideComplementaireDonorBeneficiary> allAideRelationships = aideComplementaireDonorBeneficiaryRepository
                .findByBeneficiaryIdIn(beneficiaryIds);

        // Find all executed aides with the same service but different ID
        Map<Long, Boolean> beneficiaryHasOldCampagne = new HashMap<>();

        for (AideComplementaireDonorBeneficiary relation : allAideRelationships) {
            // Skip current aide
            if (relation.getAideComplementaire().getId().equals(aideComplementaireId)) {
                continue;
            }

            // Check if it's an executed aide with same service
            if (relation.getStatutValidation() &&
                    AideComplementaireStatut.EXECUTER.getValue().equals(relation.getAideComplementaire().getStatut()) &&
                    relation.getAideComplementaire().getService().getId().equals(serviceId)) {

                beneficiaryHasOldCampagne.put(relation.getBeneficiary().getId(), Boolean.TRUE);
            }
        }

        // Final filtering using the pre-built map
        return beneficiaries.stream()
                .filter(beneficiary -> {
                    boolean hasOldCampagne = Boolean.TRUE.equals(beneficiaryHasOldCampagne.get(beneficiary.getId()));
                    return withOldCampagne ? hasOldCampagne : !hasOldCampagne;
                })
                .collect(Collectors.toList());
    }




    @Transactional
    public void processDonorAndBeneficiaries(Long idAideComplementaire, List<String> beneficiaryIds) {
        AideComplementaire aideComplementaire = aideComplementaireRepository.findById(idAideComplementaire)
                .orElseThrow(() -> new EntityNotFoundException("AideComplementaire not found"));

        for (String beneficiaryId : beneficiaryIds) {
            Optional<Beneficiary> beneficiaryOptional = beneficiaryRepository.findByCode(beneficiaryId);
            if (beneficiaryOptional.isPresent()){
                Beneficiary beneficiary = beneficiaryOptional.get();
                List<TakenInChargeBeneficiary> takenInChargeBeneficiaries = takenInChargeBeneficiaryRepository.findByBeneficiaryId(beneficiary.getId());
                boolean defaultEntryNeeded = true;

                if (!takenInChargeBeneficiaries.isEmpty()) {
                    for (TakenInChargeBeneficiary takenInChargeBeneficiary : takenInChargeBeneficiaries) {
                        List<TakenInChargeDonor> takenInChargeDonors = takenInChargeDonorRepository.findByTakenInChargeId(takenInChargeBeneficiary.getTakenInCharge().getId());
                        if (!takenInChargeDonors.isEmpty()) {
                            for (TakenInChargeDonor takenInChargeDonor : takenInChargeDonors) {
                                Optional<AideComplementaireDonorBeneficiary> aideComplementaireDonorBeneficiary = aideComplementaireDonorBeneficiaryRepository.findAideComplementaireDonorBeneficiariesByAideComplementaire_IdAndDonor_IdAndBeneficiaryIsEmpty(idAideComplementaire, takenInChargeDonor.getDonor().getId());

                                if (aideComplementaireDonorBeneficiary.isPresent()) {
                                    AideComplementaireDonorBeneficiary entry = AideComplementaireDonorBeneficiary.builder()
                                            .aideComplementaire(aideComplementaire)
                                            .donor(aideComplementaireDonorBeneficiary.get().getDonor())
                                            .beneficiary(beneficiary)
                                            .montantAffecter(aideComplementaireDonorBeneficiary.get().getMontantPoserDuDonateur() > 0.0 ? aideComplementaireDonorBeneficiary.get().getMontantPoserDuDonateur() : aideComplementaire.getAmountPerBeneficiary())
                                            .statutValidation(false)
                                            .build();
                                    aideComplementaireDonorBeneficiaryRepository.save(entry);

                                    defaultEntryNeeded = false;
                                }
                            }
                        }
                        //if (!defaultEntryNeeded) break;
                    }
                }

                if (defaultEntryNeeded) {
                    AideComplementaireDonorBeneficiary entry = AideComplementaireDonorBeneficiary.builder()
                            .aideComplementaire(aideComplementaire)
                            .beneficiary(beneficiary)
                            .montantAffecter(aideComplementaire.getAmountPerBeneficiary())
                            .statutValidation(false)
                            .build();
                    aideComplementaireDonorBeneficiaryRepository.save(entry);
                }
            }else {
                BeneficiaryAdHocGroup beneficiaryAdHocGroup = beneficiaryAdHocGroupRepository.findByCode(beneficiaryId).orElseThrow(() -> new EntityNotFoundException("Beneficiary Ad Hoc Groupe not found"));
                AideComplementaireBeneficiaryAdHocGroupe entry = AideComplementaireBeneficiaryAdHocGroupe.builder()
                        .aideComplementaire(aideComplementaire)
                        .beneficiaryAdHocGroup(beneficiaryAdHocGroup)
                        .numberOfMembersBenefiting(beneficiaryAdHocGroup.getNumberOfMembers())
                        .montantAffecter(aideComplementaire.getAmountPerBeneficiary())
                        .statutValidation(false)
                        .build();
                aideComplementaireBeneficiaryAdHocGroupeRepository.save(entry);

                List<Beneficiary> beneficiaryList = beneficiaryAdHocGroup.getBeneficiaries();
                for(Beneficiary beneficiary : beneficiaryList){
                    AideComplementaireBeneficiaryAdHocGroupe entryBeneficiary = AideComplementaireBeneficiaryAdHocGroupe.builder()
                            .aideComplementaire(aideComplementaire)
                            .beneficiaryAdHocGroup(beneficiaryAdHocGroup)
                            .beneficiary(beneficiary)
                            .montantAffecter(aideComplementaire.getAmountPerBeneficiary())
                            .statutValidation(false)
                            .build();
                    aideComplementaireBeneficiaryAdHocGroupeRepository.save(entryBeneficiary);
                }
            }

        }
    }



    @Transactional
    public void removeBeneficiaryFromAideComplementaire(Long idAideComplementaire, Long idBeneficiary,Long idDonor, String type) {
        if (type.equals(BeneficiaryType.GROUPE.getValue())){
            List<AideComplementaireBeneficiaryAdHocGroupe> beneficiaryAdHocGroupeList = aideComplementaireBeneficiaryAdHocGroupeRepository.findByAideComplementaire_IdAndBeneficiaryAdHocGroupId(idAideComplementaire,idBeneficiary);
            aideComplementaireBeneficiaryAdHocGroupeRepository.deleteAll(beneficiaryAdHocGroupeList);

        }else{
            Optional<AideComplementaireDonorBeneficiary> existingEntry =
                    aideComplementaireDonorBeneficiaryRepository
                            .findByAideComplementaireIdAndDonorIdAndBeneficiaryId(idAideComplementaire,idDonor, idBeneficiary);

            if (existingEntry.isPresent()) {
                AideComplementaireDonorBeneficiary entry = existingEntry.get();

                if (entry.getDonor() != null ) {
                    Long donorId = entry.getDonor().getId();

                    Optional<AideComplementaireDonorBeneficiary> donorEntry =
                            aideComplementaireDonorBeneficiaryRepository
                                    .findAideComplementaireDonorBeneficiariesByAideComplementaire_IdAndDonor_IdAndBeneficiaryIsEmpty(
                                            idAideComplementaire, donorId);

                    if (donorEntry.isPresent()) {
                        AideComplementaireDonorBeneficiary donor = donorEntry.get();
                        double montantAffecter = (entry.getMontantAffecter() != null) ? entry.getMontantAffecter() : 0.0;
                        double montantRestant = (donor.getMontantRestantDuDonateur() != null) ? donor.getMontantRestantDuDonateur() : 0.0;
                        double montantPoser = (donor.getMontantPoserDuDonateur() != null) ? donor.getMontantPoserDuDonateur() : 0.0;


                        if (montantAffecter > montantPoser) {
                            montantRestant += (montantAffecter - montantPoser);
                        } else {
                            montantRestant += montantAffecter;
                        }

                        donor.setMontantRestantDuDonateur(montantRestant);
                    }
                }

                aideComplementaireDonorBeneficiaryRepository.delete(entry);
                System.out.println("Le bénéficiaire a été supprimé avec succès de l'aide complémentaire.");
            } else {
                System.out.println("Aucun enregistrement trouvé pour ce bénéficiaire dans cette aide complémentaire.");
            }
        }
    }

    @Transactional
    public void removeDonorFromAideComplementaire(Long idAideComplementaire, Long idDonor) {
        List<AideComplementaireDonorBeneficiary> donorEntries =
                aideComplementaireDonorBeneficiaryRepository
                        .findAideComplementaireDonorBeneficiariesByDonorIdAndAideComplementaireId(idDonor, idAideComplementaire);

        AideComplementaire aideComplementaire = aideComplementaireRepository.findById(idAideComplementaire).orElseThrow();
        Services services = aideComplementaire.getService();

        if (!donorEntries.isEmpty()) {
            for (AideComplementaireDonorBeneficiary entry : donorEntries) {
                if (entry.getBeneficiary() == null){
                    List<Donation> donationList = donationRepository.findByDonorIdAndArchivedIsFalseOrArchivedNull(idDonor);
                    for (Donation donation : donationList) {
                        List<BudgetLine> budgetLineList = budgetLineRepository.findByDonationIdAndServiceId(donation.getId(), services.getId());
                        for (BudgetLine  line : budgetLineList){
                            if (line.getStatus().equals(BudgetLineStatus.RESERVED)){
                                line.setStatus(BudgetLineStatus.DISPONIBLE);
                                budgetLineRepository.save(line);
                            }
                        }

                    }
                }

                aideComplementaireDonorBeneficiaryRepository.delete(entry);
            }
            System.out.println("Les donateurs ont été supprimés avec succès de l'aide complémentaire.");
        } else {
            System.out.println("Aucune entrée trouvée pour ce donateur dans cette aide complémentaire.");
        }
    }



    @Transactional
    public void updateMontantParBeneficiary(Long idAideComplementaire, Long idBeneficiary,Long idDonor, String type, Double newMontant,Long numberOfMembers) {
        Optional<AideComplementaireDonorBeneficiary> entryOpt;
        System.out.println(type);
        if(type.equals("Group")){
            AideComplementaireBeneficiaryAdHocGroupe aideComplementaireBeneficiaryAdHocGroupe1=aideComplementaireBeneficiaryAdHocGroupeRepository.findByAideComplementaire_IdAndBeneficiaryAdHocGroupIdAndBeneficiaryIsNull(idAideComplementaire,idBeneficiary).get();
            aideComplementaireBeneficiaryAdHocGroupe1.setNumberOfMembersBenefiting(numberOfMembers);
            aideComplementaireBeneficiaryAdHocGroupe1.setMontantAffecter(newMontant);
            aideComplementaireBeneficiaryAdHocGroupeRepository.save(aideComplementaireBeneficiaryAdHocGroupe1);
        }
        else{
            if (idDonor != null){
                entryOpt = aideComplementaireDonorBeneficiaryRepository
                        .findByAideComplementaireIdAndDonorIdAndBeneficiaryId(idAideComplementaire, idDonor,idBeneficiary);
            }else {
                entryOpt = aideComplementaireDonorBeneficiaryRepository
                        .findByAideComplementaireIdAndBeneficiaryId(idAideComplementaire, idBeneficiary);
            }



            if (entryOpt.isPresent()) {
                AideComplementaireDonorBeneficiary entry = entryOpt.get();
                entry.setMontantAffecter(newMontant);
                aideComplementaireDonorBeneficiaryRepository.save(entry);


            } else {
                throw new EntityNotFoundException("AideComplementaireDonorBeneficiary non trouvé pour l'idAideComplementaire: "
                        + idAideComplementaire + " et idBeneficiary: " + idBeneficiary);
            }
        }

    }

    @Transactional
    public Double updateMontantParBeneficiaryAdHocGroupe(Long idAideComplementaire, Long idGroupe, String type, Map<Long, Double> beneficiaryAmounts) {
        Double totalAmountAffecter = 0.0;

        for (Map.Entry<Long, Double> entry : beneficiaryAmounts.entrySet()) {
            Long beneficiaryId = entry.getKey();
            Double amount = entry.getValue();

            AideComplementaireBeneficiaryAdHocGroupe entity = aideComplementaireBeneficiaryAdHocGroupeRepository.findByAideComplementaire_IdAndBeneficiaryAdHocGroupIdAndBeneficiaryId(
                    idAideComplementaire, idGroupe, beneficiaryId
            ).orElse(null);

            if (entity != null) {
                entity.setMontantAffecter(amount);
                aideComplementaireBeneficiaryAdHocGroupeRepository.save(entity);
                totalAmountAffecter += amount;
            }
        }

        AideComplementaireBeneficiaryAdHocGroupe entity = aideComplementaireBeneficiaryAdHocGroupeRepository.findByAideComplementaire_IdAndBeneficiaryAdHocGroupIdAndBeneficiaryIsNull(
                idAideComplementaire, idGroupe).orElse(null);
        if (entity != null) {
            entity.setMontantAffecter(totalAmountAffecter);
            aideComplementaireBeneficiaryAdHocGroupeRepository.save(entity);
        }

        return totalAmountAffecter;
    }




    @Transactional
    public void refreshDonorsForAideComplementaire(Long idAideComplementaire, List<Long> idDonors, boolean priority) {
        AideComplementaire aideComplementaire = aideComplementaireRepository.findById(idAideComplementaire)
                .orElseThrow(() -> new EntityNotFoundException("AideComplementaire not found with ID: " + idAideComplementaire));

        Services services = servicesRepository.findById(aideComplementaire.getService().getId())
                .orElseThrow(() -> new EntityNotFoundException("Service not found for AideComplementaire"));

        List<BudgetLine> budgetLines = budgetLineRepository.findByServiceId(services.getId());
        Set<Long> processedDonorIds = new HashSet<>();

        for (BudgetLine budgetLine : budgetLines) {
            if (budgetLine.getStatus().equals(BudgetLineStatus.DISPONIBLE) && budgetLine.getDonation() != null) {
                Donation donation = budgetLine.getDonation();
                Donor donor = donation.getDonor();

                if (donor != null && idDonors.contains(donor.getId()) && !processedDonorIds.contains(donor.getId())) {
                    Optional<AideComplementaireDonorBeneficiary> existingEntry = aideComplementaireDonorBeneficiaryRepository
                            .findAideComplementaireDonorBeneficiariesByAideComplementaire_IdAndDonor_IdAndBeneficiaryIsEmpty(aideComplementaire.getId(), donor.getId());

                    if (existingEntry.isEmpty()) {
                        AideComplementaireDonorBeneficiary newEntryWithoutBeneficiary = AideComplementaireDonorBeneficiary.builder()
                                .aideComplementaire(aideComplementaire)
                                .donor(donor)
                                .statutValidation(false)
                                .build();
                        aideComplementaireDonorBeneficiaryRepository.save(newEntryWithoutBeneficiary);
                        processedDonorIds.add(donor.getId());

                        if (!priority) {
                            continue;
                        }

                        List<TakenInChargeDonor> takenInChargeDonors = takenInChargeDonorRepository.findByDonorId(donor.getId());

                        for (TakenInChargeDonor takenInChargeDonor : takenInChargeDonors) {
                            List<TakenInChargeBeneficiary> takenInChargeBeneficiaries = takenInChargeBeneficiaryRepository
                                    .findByTakenInChargeId(takenInChargeDonor.getTakenInCharge().getId());

                            for (TakenInChargeBeneficiary takenInChargeBeneficiary : takenInChargeBeneficiaries) {
                                Beneficiary beneficiary = takenInChargeBeneficiary.getBeneficiary();
                                Person person = beneficiary.getPerson();
                                String typePriseEnChargeIdsList = person.getTypePriseEnChargeIdsList();

                                if (typePriseEnChargeIdsList != null) {
                                    List<String> idsList = Arrays.asList(typePriseEnChargeIdsList.split(","));

                                    if (idsList.contains(services.getServiceCategoryTypeId().toString())) {
                                        AideComplementaireDonorBeneficiary newEntryWithBeneficiary = AideComplementaireDonorBeneficiary.builder()
                                                .aideComplementaire(aideComplementaire)
                                                .donor(donor)
                                                .beneficiary(beneficiary)
                                                .montantAffecter((double) 5000L)
                                                .statutValidation(false)
                                                .build();
                                        aideComplementaireDonorBeneficiaryRepository.save(newEntryWithBeneficiary);
                                    }
                                }
                            }
                        }
                    }
                    budgetLine.setStatus(BudgetLineStatus.RESERVED);
                    budgetLineRepository.save(budgetLine);
                }
            }
        }
    }


    @Transactional
    public void updateStatutValidationBenenficiaryInAide(Long idAideComplementaire, Long idBeneficiary, Long idDonor ,String type) {
        if (type.equals(BeneficiaryType.GROUPE.getValue())){
            Optional<AideComplementaireBeneficiaryAdHocGroupe> adHocGroupeOptional = aideComplementaireBeneficiaryAdHocGroupeRepository.findByAideComplementaire_IdAndBeneficiaryAdHocGroupIdAndBeneficiaryIsNull(idAideComplementaire,idBeneficiary);
            if (adHocGroupeOptional.isPresent()){
                AideComplementaireBeneficiaryAdHocGroupe adHocGroupe = adHocGroupeOptional.get();
                adHocGroupe.setStatutValidation(adHocGroupe.getStatutValidation() == null || !adHocGroupe.getStatutValidation());
            }else {
                throw new EntityNotFoundException("AideComplementaireBeneficiaryAdHocGroupe not found for the provided ids.");
            }

        }else {
            Optional<AideComplementaireDonorBeneficiary> optionalEntity;
            if (idDonor != null){
                optionalEntity = aideComplementaireDonorBeneficiaryRepository.findByAideComplementaireIdAndDonorIdAndBeneficiaryId(idAideComplementaire, idDonor,idBeneficiary);
            }else {
                optionalEntity = aideComplementaireDonorBeneficiaryRepository.findByAideComplementaireIdAndBeneficiaryId(idAideComplementaire, idBeneficiary);
            }


            if (optionalEntity.isPresent()) {
                AideComplementaireDonorBeneficiary entity = optionalEntity.get();

                entity.setStatutValidation(entity.getStatutValidation() == null || !entity.getStatutValidation());

                aideComplementaireDonorBeneficiaryRepository.save(entity);
            } else {
                throw new EntityNotFoundException("AideComplementaireDonorBeneficiary not found for the provided ids.");
            }
        }

    }

    @Transactional
    public void validateAllAideComplementaireDonorBeneficiary(
            Long aideComplementaireId, String category, String type, Boolean withParticipatingMembers,
            Boolean withDonor, Boolean withParticipatingDonor, Boolean withOldCampagne, String text, Boolean invalidate) {

        // Fetch aide complementaire once and cache the reference
        AideComplementaire aideComplementaire = aideComplementaireRepository.findById(aideComplementaireId)
                .orElseThrow(() -> new EntityNotFoundException("AideComplementaire not found for id: " + aideComplementaireId));

        // Get all AideComplementaireDonorBeneficiary for this aideComplementaire
        List<AideComplementaireDonorBeneficiary> allDonorBeneficiaries =
                aideComplementaireDonorBeneficiaryRepository.findAideComplementaireDonorBeneficiariesByAideComplementaire_Id(aideComplementaireId);

        // Get all AideComplementaireBeneficiaryAdHocGroupe for this aideComplementaire
        List<AideComplementaireBeneficiaryAdHocGroupe> allAdHocGroupes =
                aideComplementaireBeneficiaryAdHocGroupeRepository.findByAideComplementaire_Id(aideComplementaireId);

        // If no filters are applied, validate all or invalidate all based on the invalidate parameter
        if (category == null && type == null && withParticipatingMembers == null &&
                withDonor == null && withParticipatingDonor == null && withOldCampagne == null &&
                (text == null || text.isEmpty())) {

            boolean validationStatus = !invalidate; // If invalidate is true, set validation status to false

            // Process donor beneficiaries
            for (AideComplementaireDonorBeneficiary donorBeneficiary : allDonorBeneficiaries) {
                if (donorBeneficiary.getBeneficiary() != null) {
                    donorBeneficiary.setStatutValidation(validationStatus);
                    aideComplementaireDonorBeneficiaryRepository.save(donorBeneficiary);
                }
            }

            // Process ad hoc groups
            for (AideComplementaireBeneficiaryAdHocGroupe adHocGroupe : allAdHocGroupes) {
                adHocGroupe.setStatutValidation(validationStatus);
                aideComplementaireBeneficiaryAdHocGroupeRepository.save(adHocGroupe);
            }

            return;
        }

        // Get all beneficiaries that are already in the aide
        List<Beneficiary> beneficiariesInAide = allDonorBeneficiaries.stream()
                .filter(donorBeneficiary -> donorBeneficiary.getBeneficiary() != null)
                .map(AideComplementaireDonorBeneficiary::getBeneficiary)
                .collect(Collectors.toList());

        // Get all ad hoc groups that are already in the aide
        List<BeneficiaryAdHocGroup> adHocGroupsInAide = allAdHocGroupes.stream()
                .filter(adHocGroupe -> adHocGroupe.getBeneficiaryAdHocGroup() != null)
                .map(AideComplementaireBeneficiaryAdHocGroupe::getBeneficiaryAdHocGroup)
                .collect(Collectors.toList());

        // Apply filters to the beneficiaries that are already in the aide
        List<Beneficiary> filteredBeneficiaries = new ArrayList<>(beneficiariesInAide);
        List<BeneficiaryAdHocGroup> filteredAdHocGroups = new ArrayList<>(adHocGroupsInAide);

        // Apply text filter if present
        if (text != null && !text.isEmpty()) {
            final String lowerText = text.toLowerCase();
            filteredBeneficiaries.removeIf(b -> {
                String fullName = (b.getPerson().getFirstName() + " " +
                        b.getPerson().getLastName() + " " +
                        b.getPerson().getIdentityCode()).toLowerCase();
                return !fullName.contains(lowerText);
            });

            // Apply text filter to ad hoc groups
            filteredAdHocGroups.removeIf(g -> {
                String groupName = g.getName().toLowerCase();
                return !groupName.contains(lowerText);
            });
        }

        // Apply category filter if present
        if (category != null) {
            filteredBeneficiaries.removeIf(b -> !b.getBeneficiaryStatut().getNameStatut().equals(category));
        }

        // Apply type filter if present
        if (type != null) {
            filteredBeneficiaries.removeIf(b -> {
                if (BeneficiaryType.INDEPENDENT.getValue().equals(type)) {
                    return !b.getIndependent();
                } else if (BeneficiaryType.MEMBRE_FAMILLE.getValue().equals(type)) {
                    return b.getIndependent();
                }
                return true; // Remove if type doesn't match
            });
        }

        // Apply specialized filters if present
        if (withParticipatingMembers != null) {
            filteredBeneficiaries = filterBeneficiariesByParticipatingMembers(filteredBeneficiaries, aideComplementaireId, withParticipatingMembers);
        }
        if (withDonor != null) {
            filteredBeneficiaries = filterBeneficiariesWithDonor(filteredBeneficiaries, withDonor);
        }
        if (withParticipatingDonor != null) {
            filteredBeneficiaries = filterBeneficiariesWithParticipatingDonor(filteredBeneficiaries, aideComplementaireId, withParticipatingDonor);
        }
        if (withOldCampagne != null) {
            filteredBeneficiaries = filterBeneficiariesWithOldCampagne(filteredBeneficiaries, aideComplementaireId, withOldCampagne);
        }

        // Extract beneficiary IDs from filtered results
        Set<Long> filteredBeneficiaryIds = filteredBeneficiaries.stream()
                .map(Beneficiary::getId)
                .collect(Collectors.toSet());

        // Extract ad hoc group IDs from filtered results
        Set<Long> filteredAdHocGroupIds = filteredAdHocGroups.stream()
                .map(BeneficiaryAdHocGroup::getId)
                .collect(Collectors.toSet());

        // Determine validation status based on the invalidate parameter
        boolean validationStatus = !invalidate; // If invalidate is true, set validation status to false

        // Validate or invalidate only the AideComplementaireDonorBeneficiary records that match the filter
        for (AideComplementaireDonorBeneficiary donorBeneficiary : allDonorBeneficiaries) {
            if (donorBeneficiary.getBeneficiary() != null &&
                    filteredBeneficiaryIds.contains(donorBeneficiary.getBeneficiary().getId())) {
                donorBeneficiary.setStatutValidation(validationStatus);
                aideComplementaireDonorBeneficiaryRepository.save(donorBeneficiary);
            }
        }

        // Validate or invalidate only the AideComplementaireBeneficiaryAdHocGroupe records that match the filter
        for (AideComplementaireBeneficiaryAdHocGroupe adHocGroupe : allAdHocGroupes) {
            if (adHocGroupe.getBeneficiaryAdHocGroup() != null &&
                    filteredAdHocGroupIds.contains(adHocGroupe.getBeneficiaryAdHocGroup().getId())) {
                adHocGroupe.setStatutValidation(validationStatus);
                aideComplementaireBeneficiaryAdHocGroupeRepository.save(adHocGroupe);
            }
        }
    }

    public static String checkAideComplementaireStatut(LocalDateTime dateDebut, LocalDateTime dateFin) {
        LocalDateTime now = LocalDateTime.now();

        if (now.isBefore(dateDebut)) {
            return AideComplementaireStatut.PLANIFIER.getValue();
        } else if (now.isAfter(dateDebut) && now.isBefore(dateFin)) {
            return AideComplementaireStatut.ENCOURS.getValue();
        } else if (now.isAfter(dateFin)) {
            return AideComplementaireStatut.ENATTENTEDEXECUTION.getValue();
        }

        return null;
    }

    @Transactional
    public List<DonorAideComplemenatireDTO> getBudgetLineForAideComplementaire(Long serviceId, Long aideComplementaireId) {
        List<BudgetLine> budgetLines = budgetLineRepository.findByServiceId(serviceId);

        List<BudgetLine> filteredBudgetLines = budgetLines.stream()
                .filter(line -> line.getStatus() == BudgetLineStatus.DISPONIBLE ||
                        (line.getStatus() == BudgetLineStatus.RESERVED &&
                                line.getAideComplementaire() != null &&
                                line.getAideComplementaire().getId().equals(aideComplementaireId)) ||
                        (line.getStatus() == BudgetLineStatus.EXECUTED &&
                                line.getAideComplementaire() != null &&
                                line.getAideComplementaire().getId().equals(aideComplementaireId)))
                .toList();

        Map<Long, Map<Boolean, List<BudgetLine>>> groupedByDonorAndNatureBudgetLine = filteredBudgetLines.stream()
                .filter(line -> line.getDonation() != null &&
                        line.getDonation().getDonor() != null)
                .collect(Collectors.groupingBy(
                        line -> line.getDonation().getDonor().getId(),
                        Collectors.groupingBy(
                                line -> line.getNatureBudgetLine() != null && line.getNatureBudgetLine(),
                                Collectors.toList()
                        )
                ));
        return groupedByDonorAndNatureBudgetLine.entrySet().stream()
                .flatMap(entry -> {
                    Long donorId = entry.getKey();
                    Map<Boolean, List<BudgetLine>> budgetLinesMap = entry.getValue();

                    return budgetLinesMap.entrySet().stream().map(maps -> {
                        boolean isNature=maps.getKey();
                        List<BudgetLine> donorBudgetLines = maps.getValue();
                        Double montantTotalDuDonateur = donorBudgetLines.stream()
                                .mapToDouble(BudgetLine::getAmount)
                                .sum();

                        Double montantReserverDuDonateur = donorBudgetLines.stream()
                                .filter(line -> line.getStatus() == BudgetLineStatus.RESERVED || line.getStatus() == BudgetLineStatus.EXECUTED)
                                .mapToDouble(BudgetLine::getAmount)
                                .sum();

                        Double montantPoserDuDonateur = donorBudgetLines.stream()
                                .mapToDouble(line -> line.getAmountByBeneficiary() != null ? line.getAmountByBeneficiary() : 0.0)
                                .sum();

                        String donorStatus = String.valueOf(donorBudgetLines.stream()
                                .map(BudgetLine::getStatus)
                                .reduce((status1, status2) -> {
                                    if (status1 == BudgetLineStatus.EXECUTED || status2 == BudgetLineStatus.EXECUTED) {
                                        return BudgetLineStatus.EXECUTED;
                                    } else if (status1 == BudgetLineStatus.RESERVED || status2 == BudgetLineStatus.RESERVED) {
                                        return BudgetLineStatus.RESERVED;
                                    } else {
                                        return BudgetLineStatus.DISPONIBLE;
                                    }
                                }).orElse(BudgetLineStatus.DISPONIBLE));

                        Donor donor = donorBudgetLines.get(0).getDonation().getDonor();
                        DonorAideComplemenatireDTO dto = new DonorAideComplemenatireDTO();
                        dto.setId(donor.getId());
                        dto.setCode(donor.getCode());

                        dto.setIsNature(isNature);
                        dto.setMontantTotalDuDonateur(montantTotalDuDonateur);
                        dto.setMontantReserverDuDonateur(montantReserverDuDonateur);
                        dto.setMontantPoserDuDonateur(montantPoserDuDonateur);
                        dto.setStatus(donorStatus);

                        if (donor instanceof DonorPhysical donorPhysical) {
                            dto.setFirstName(donorPhysical.getFirstName());
                            dto.setLastName(donorPhysical.getLastName());
                            dto.setFullNameDonor(donorPhysical.getFirstName() + " " + donorPhysical.getLastName());
                            dto.setTypeDonor("Physique");
                        } else if (donor instanceof DonorMoral donorMoral) {
                            dto.setFirstName(donorMoral.getCompany());
                            dto.setFullNameDonor(donorMoral.getCompany());
                            dto.setTypeDonor("Moral");
                        }else if(donor instanceof DonorAnonyme donorAnonyme){
                            dto.setFullNameDonor(donorAnonyme.getName());
                            dto.setTypeDonor("Anonyme");
                            dto.setFirstName(donorAnonyme.getName());
                        }
                        return dto;
                    });
                })
                .collect(Collectors.toList());
    }

    @Transactional
    public void updateReservedAmountForBudgetLine(Long budgetLineId, Double newAmount) {
        Optional<BudgetLine> budgetLineOpt = budgetLineRepository.findById(budgetLineId);

        if (budgetLineOpt.isPresent()) {
            BudgetLine budgetLine = budgetLineOpt.get();

            budgetLine.setMontantReserve(newAmount);

            budgetLineRepository.save(budgetLine);

        } else {
            throw new EntityNotFoundException("BudgetLine non trouvée avec l'ID: " + budgetLineId);
        }
    }



    @Transactional
    public void reserveBudgetLineForAideComplementaire(Long budgetLineId, Long aideComplementaireId) {
        Optional<AideComplementaire> aideComplementaireOptional = aideComplementaireRepository.findById(aideComplementaireId);
        if (aideComplementaireOptional.isEmpty()) {
            throw new EntityNotFoundException("Aide Complémentaire not found");
        }

        AideComplementaire aideComplementaire = aideComplementaireOptional.get();
        Services services = servicesRepository.findById(aideComplementaire.getService().getId()).orElseThrow();

        Optional<BudgetLine> budgetLineOptional = budgetLineRepository.findById(budgetLineId);
        if (budgetLineOptional.isEmpty()) {
            throw new EntityNotFoundException("Budget Line not found");
        }

        BudgetLine budgetLine = budgetLineOptional.get();

        if (budgetLine.getStatus().equals(BudgetLineStatus.DISPONIBLE)) {
            handleBudgetLineReservation(budgetLine, aideComplementaire, services);
        } else if (budgetLine.getStatus().equals(BudgetLineStatus.RESERVED)) {
            handleBudgetLineRelease(budgetLine, aideComplementaire);
        }
    }

    @Transactional
    public void reserveAllBudgetLines(Long idAideComplementaire, Map<Long, Double> donorAmounts , String operation) {
        if (operation == null || (!operation.equals("ReserveAll") && !operation.equals("UnReserveAll"))) {
            throw new IllegalArgumentException("Opération invalide.");
        }

        Optional<AideComplementaire> aideComplementaireOptional = aideComplementaireRepository.findById(idAideComplementaire);
        if (aideComplementaireOptional.isEmpty()) {
            throw new EntityNotFoundException("Aide Complémentaire not found");
        }
        AideComplementaire aideComplementaire = aideComplementaireOptional.get();
        Services services = servicesRepository.findById(aideComplementaire.getService().getId()).orElseThrow();

        for (Map.Entry<Long, Double> entry : donorAmounts.entrySet()) {
            Long donorId = entry.getKey();
            Double amount = entry.getValue();
            if (operation.equals("ReserveAll")) {
                reserveBudgetLinesByDonor(idAideComplementaire, donorId, services.getId(), amount,true,false);
            } else if (operation.equals("UnReserveAll")) {
                releaseBudgetLinesByDonor(idAideComplementaire, donorId, services.getId(),true,false);
            }
        }
    }

    private void handleBudgetLineReservation(BudgetLine budgetLine, AideComplementaire aideComplementaire, Services services) {
        budgetLine.setStatus(BudgetLineStatus.RESERVED);
        budgetLine.setAideComplementaire(aideComplementaire);
        if (budgetLine.getMontantReserve() == null){
            budgetLine.setMontantReserve(budgetLine.getAmount());
        }
        budgetLineRepository.save(budgetLine);

        if (budgetLine.getDonation() != null) {
            Donation donation = budgetLine.getDonation();
            Donor donor = donation.getDonor();

            if (donor != null) {
                Optional<AideComplementaireDonorBeneficiary> existingEntry =
                        aideComplementaireDonorBeneficiaryRepository
                                .findAideComplementaireDonorBeneficiariesByAideComplementaire_IdAndDonor_IdAndBeneficiaryIsEmpty(
                                        aideComplementaire.getId(), donor.getId() );

                if (existingEntry.isPresent()) {
                    AideComplementaireDonorBeneficiary entry = existingEntry.get();
                    if (budgetLine.getMontantReserve() != null){
                        entry.setMontantTotalDuDonateur(entry.getMontantTotalDuDonateur() + budgetLine.getMontantReserve());
                    }else {
                        entry.setMontantTotalDuDonateur(entry.getMontantTotalDuDonateur() + budgetLine.getAmount());
                    }
                    if (budgetLine.getAmountByBeneficiary() != null) {
                        entry.setMontantPoserDuDonateur(entry.getMontantPoserDuDonateur() + budgetLine.getAmountByBeneficiary());
                    }
                    aideComplementaireDonorBeneficiaryRepository.save(entry);

                    if (aideComplementaire.getPriority()) {
                        List<AideComplementaireDonorBeneficiary> beneficiaries = aideComplementaireDonorBeneficiaryRepository
                                .findAideComplementaireDonorBeneficiariesByDonorIdAndAideComplementaireId(donor.getId(), aideComplementaire.getId());

                        for (AideComplementaireDonorBeneficiary beneficiary : beneficiaries) {
                            if (beneficiary.getBeneficiary() != null) {
                                if (beneficiary.getStatutValidation()) {
                                    //beneficiary.setMontantAffecter(entry.getMontantPoserDuDonateur());
                                    beneficiary.setStatutValidation(false);
                                    aideComplementaireDonorBeneficiaryRepository.save(beneficiary);
                                }

                            }
                        }
                    }
                } else {
                    AideComplementaireDonorBeneficiary newEntry = AideComplementaireDonorBeneficiary.builder()
                            .aideComplementaire(aideComplementaire)
                            .donor(donor)
                            .isNature(budgetLine.getNatureBudgetLine())
                            .montantTotalDuDonateur(budgetLine.getMontantReserve())
                            .montantPoserDuDonateur(budgetLine.getAmountByBeneficiary() != null ? budgetLine.getAmountByBeneficiary() : 0.0)
                            .statutValidation(false)
                            .build();
                    aideComplementaireDonorBeneficiaryRepository.save(newEntry);

                    if (aideComplementaire.getPriority()) {
                        List<TakenInChargeDonor> takenInChargeDonors = takenInChargeDonorRepository.findByDonorId(donor.getId());

                        for (TakenInChargeDonor takenInChargeDonor : takenInChargeDonors) {
                            List<TakenInChargeBeneficiary> takenInChargeBeneficiaries = takenInChargeBeneficiaryRepository
                                    .findByTakenInChargeId(takenInChargeDonor.getTakenInCharge().getId());

                            for (TakenInChargeBeneficiary takenInChargeBeneficiary : takenInChargeBeneficiaries) {
                                Beneficiary beneficiary = takenInChargeBeneficiary.getBeneficiary();
                                Person person = beneficiary.getPerson();
                                String typePriseEnChargeIdsList = person.getTypePriseEnChargeIdsList();

                                if (typePriseEnChargeIdsList != null) {
                                    List<String> idsList = Arrays.asList(typePriseEnChargeIdsList.split(","));

                                    if (idsList.contains(services.getServiceCategoryTypeId().toString())) {
                                        if (idsList.contains(services.getServiceCategoryTypeId().toString())) {
                                            Optional<AideComplementaireDonorBeneficiary> existingBenenficiryEntry = aideComplementaireDonorBeneficiaryRepository
                                                    .findByAideComplementaireIdAndBeneficiaryIdAndDonorIsNull(aideComplementaire.getId(), beneficiary.getId());


                                            Optional<AideComplementaireDonorBeneficiary> existingBenenficiryWithDonorEntry = aideComplementaireDonorBeneficiaryRepository
                                                    .findByAideComplementaireIdAndDonorIdAndBeneficiaryId(aideComplementaire.getId(), donor.getId(),beneficiary.getId());

                                            if (existingBenenficiryWithDonorEntry.isPresent()) {
                                                continue;
                                            } else if (existingBenenficiryEntry.isPresent()) {
                                                AideComplementaireDonorBeneficiary beneficiary1 = existingBenenficiryEntry.get();
                                                beneficiary1.setDonor(donor);
                                                beneficiary1.setStatutValidation(false);
                                                aideComplementaireDonorBeneficiaryRepository.save(beneficiary1);
                                            }  else {
                                                AideComplementaireDonorBeneficiary newEntryWithBeneficiary = AideComplementaireDonorBeneficiary.builder()
                                                        .aideComplementaire(aideComplementaire)
                                                        .donor(donor)
                                                        .beneficiary(beneficiary)
                                                        .montantAffecter(budgetLine.getAmountByBeneficiary() != null ? budgetLine.getAmountByBeneficiary() : aideComplementaire.getAmountPerBeneficiary())
                                                        .statutValidation(false)
                                                        .build();

                                                aideComplementaireDonorBeneficiaryRepository.save(newEntryWithBeneficiary);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }


            }
        }
    }

    private void handleBudgetLineRelease(BudgetLine budgetLine, AideComplementaire aideComplementaire) {
        budgetLine.setStatus(BudgetLineStatus.DISPONIBLE);
        budgetLine.setAideComplementaire(null);


        if (budgetLine.getDonation() != null) {
            Donation donation = budgetLine.getDonation();
            Donor donor = donation.getDonor();

            if (donor != null) {
                Optional<AideComplementaireDonorBeneficiary> existingEntry =
                        aideComplementaireDonorBeneficiaryRepository
                                .findAideComplementaireDonorBeneficiariesByAideComplementaire_IdAndDonor_IdAndBeneficiaryIsEmptyAndIsNature(
                                        aideComplementaire.getId(), donor.getId(),budgetLine.getNatureBudgetLine());

                if (existingEntry.isPresent()) {
                    AideComplementaireDonorBeneficiary entry = existingEntry.get();
                    if (budgetLine.getMontantReserve().equals(entry.getMontantTotalDuDonateur())){
                        List<AideComplementaireDonorBeneficiary> aideComplementaireDonorBeneficiaries = aideComplementaireDonorBeneficiaryRepository.findAideComplementaireDonorBeneficiariesByDonorIdAndAideComplementaireId(donor.getId(), aideComplementaire.getId());
                        aideComplementaireDonorBeneficiaryRepository.deleteAll(aideComplementaireDonorBeneficiaries);
                    }else {
                        entry.setMontantTotalDuDonateur(entry.getMontantTotalDuDonateur() - budgetLine.getMontantReserve());
                        if (budgetLine.getAmountByBeneficiary() != null && aideComplementaire.getPriority()){
                            entry.setMontantPoserDuDonateur(entry.getMontantPoserDuDonateur() - budgetLine.getAmountByBeneficiary());
                            aideComplementaireDonorBeneficiaryRepository.save(entry);
                            List<AideComplementaireDonorBeneficiary> aideComplementaireDonorBeneficiaries = aideComplementaireDonorBeneficiaryRepository.findAideComplementaireDonorBeneficiariesByDonorIdAndAideComplementaireId(donor.getId(), aideComplementaire.getId());
                            for (AideComplementaireDonorBeneficiary beneficiary : aideComplementaireDonorBeneficiaries) {
                                if (beneficiary.getStatutValidation()) {
                                    //beneficiary.setMontantAffecter(beneficiary.getMontantAffecter() - budgetLine.getAmountByBeneficiary());
                                    beneficiary.setStatutValidation(false);
                                    aideComplementaireDonorBeneficiaryRepository.save(beneficiary);
                                }
                            }
                        }
                    }
                }
            }
        }

        budgetLine.setMontantReserve(null);
        budgetLineRepository.save(budgetLine);
    }

    @Transactional
    public void reserveBudgetLineForAideComplementaireByDonor(Long donorId, Long aideComplementaireId, Double reservedAmount, String operation,Boolean isNature) {
        Optional<AideComplementaire> aideComplementaireOptional = aideComplementaireRepository.findById(aideComplementaireId);
        if (aideComplementaireOptional.isEmpty()) {
            throw new EntityNotFoundException("Aide Complémentaire not found");
        }

        AideComplementaire aideComplementaire = aideComplementaireOptional.get();
        Services services = servicesRepository.findById(aideComplementaire.getService().getId()).orElseThrow();

        if (reservedAmount != null && operation.equals("Reserve")) {
            reserveBudgetLinesByDonor(aideComplementaireId, donorId, services.getId(),reservedAmount ,isNature,true);
        } else if (operation.equals("unReserve")) {
            releaseBudgetLinesByDonor(aideComplementaireId, donorId, services.getId(),isNature,true);
        }
    }


    private void reserveBudgetLinesByDonor(Long aideComplementaireId,Long donorId, Long serviceId, Double reservedAmount,boolean isNature,boolean isNotReserveAll){
        AideComplementaire aideComplementaire = aideComplementaireRepository.findById(aideComplementaireId).orElseThrow();
        Donor donor = donorRepository.findById(donorId).orElseThrow();
        List<Donation> donationList = donationRepository.findByDonorId(donorId);
        List<BudgetLine> budgetLineList = new ArrayList<>();
        for (Donation donation : donationList){
            List<BudgetLine> budgetLines = new ArrayList<>();
            if(isNotReserveAll){
                budgetLines = budgetLineRepository.findByDonationIdAndServiceIdAndStatusAndNatureBudgetLine(donation.getId(), serviceId, BudgetLineStatus.DISPONIBLE,isNature);
            }
            else{
                budgetLines = budgetLineRepository.findByDonationIdAndServiceIdAndStatus(donation.getId(), serviceId, BudgetLineStatus.DISPONIBLE);

            }
            budgetLineList.addAll(budgetLines);
        }
        budgetLineList.sort(Comparator.comparing(BudgetLine::getCreatedAt));

        Double amount = reservedAmount;
        Double amountPerBeneficiary = 0.0;
        for (BudgetLine budgetLine : budgetLineList){
            if (amount > 0.0 && (budgetLine.getAmount() < amount || budgetLine.getAmount().equals(amount))){
                budgetLine.setStatus(BudgetLineStatus.RESERVED);
                budgetLine.setAideComplementaire(aideComplementaire);
                budgetLineRepository.save(budgetLine);
                amount = amount - budgetLine.getAmount();
            } else if (amount > 0.0 && budgetLine.getAmount() > amount) {
                var remainingAmount = budgetLine.getAmount() - amount;
                budgetLine.setStatus(BudgetLineStatus.RESERVED);
                budgetLine.setAideComplementaire(aideComplementaire);
                budgetLine.setAmount(amount);


                BudgetLine remainingBudgetLine = BudgetLine.builder()
                        .amount(remainingAmount)
                        .type(budgetLine.getType())
                        .createdAt(budgetLine.getCreatedAt())
                        .valueCurrency(budgetLine.getValueCurrency())
                        .currencyId(budgetLine.getCurrencyId())
                        .natureBudgetLine(budgetLine.getNatureBudgetLine())
                        .status(BudgetLineStatus.DISPONIBLE)

                        .donation(budgetLine.getDonation())
                        .service(budgetLine.getService())
                        .montantReserve(null)
                        .executionDate(null)
                        .build();

                String code = donationService.generateBudgetLineCode(remainingBudgetLine);
                remainingBudgetLine.setCode(code);

                budgetLineRepository.save(budgetLine);
                budgetLineRepository.save(remainingBudgetLine);
                amount = 0.0;
            }
            if (budgetLine.getAmountByBeneficiary() != null){
                amountPerBeneficiary = amountPerBeneficiary + budgetLine.getAmountByBeneficiary();
            }
        }

        for (Donation donation : donationList) {
            List<BudgetLine> budgetLines = new ArrayList<>();
            if(isNotReserveAll){
                budgetLines = budgetLineRepository.findByDonationIdAndServiceIdAndStatusAndNatureBudgetLine(donation.getId(), serviceId, BudgetLineStatus.DISPONIBLE,isNature);

            }
            else{
                budgetLines = budgetLineRepository.findByDonationIdAndServiceIdAndStatus(donation.getId(), serviceId, BudgetLineStatus.DISPONIBLE);

            }

            if (budgetLines.size() > 1) {
                BudgetLine oldestBudgetLine = budgetLines.stream()
                        .min(Comparator.comparing(BudgetLine::getCreatedAt))
                        .orElseThrow();

                Double totalAmount = budgetLines.stream()
                        .mapToDouble(BudgetLine::getAmount)
                        .sum();

                oldestBudgetLine.setAmount(totalAmount);
                budgetLineRepository.save(oldestBudgetLine);

                List<Long> otherLineIds = budgetLines.stream()
                        .map(BudgetLine::getId)
                        .filter(id -> !id.equals(oldestBudgetLine.getId()))
                        .toList();

                budgetLineRepository.deleteAllById(otherLineIds);
            }
        }


        //Donor Treatment
        Optional<AideComplementaireDonorBeneficiary> existingEntry =
                aideComplementaireDonorBeneficiaryRepository
                        .findAideComplementaireDonorBeneficiariesByAideComplementaire_IdAndDonor_IdAndBeneficiaryIsEmpty(
                                aideComplementaireId, donorId);

        if (existingEntry.isPresent()) {
            AideComplementaireDonorBeneficiary entry = existingEntry.get();
            entry.setMontantTotalDuDonateur(reservedAmount);
            entry.setMontantPoserDuDonateur(amountPerBeneficiary);
            aideComplementaireDonorBeneficiaryRepository.save(entry);

            if (aideComplementaire.getPriority()) {
                List<AideComplementaireDonorBeneficiary> beneficiaries = aideComplementaireDonorBeneficiaryRepository
                        .findAideComplementaireDonorBeneficiariesByDonorIdAndAideComplementaireId(donor.getId(), aideComplementaire.getId());

                for (AideComplementaireDonorBeneficiary beneficiary : beneficiaries) {
                    if (beneficiary.getBeneficiary() != null) {
                        if (beneficiary.getStatutValidation()) {
                            //beneficiary.setMontantAffecter(entry.getMontantPoserDuDonateur());
                            beneficiary.setStatutValidation(false);
                            aideComplementaireDonorBeneficiaryRepository.save(beneficiary);
                        }

                    }
                }
            }
        }
        else {
            AideComplementaireDonorBeneficiary newEntry = AideComplementaireDonorBeneficiary.builder()
                    .aideComplementaire(aideComplementaire)
                    .donor(donor)
                    .montantTotalDuDonateur(reservedAmount)
                    .montantPoserDuDonateur(amountPerBeneficiary)
                    .statutValidation(false)
                    .build();
            aideComplementaireDonorBeneficiaryRepository.save(newEntry);

            if (aideComplementaire.getPriority()) {
                List<TakenInChargeDonor> takenInChargeDonors = takenInChargeDonorRepository.findByDonorId(donor.getId());

                for (TakenInChargeDonor takenInChargeDonor : takenInChargeDonors) {
                    List<TakenInChargeBeneficiary> takenInChargeBeneficiaries = takenInChargeBeneficiaryRepository
                            .findByTakenInChargeId(takenInChargeDonor.getTakenInCharge().getId());

                    for (TakenInChargeBeneficiary takenInChargeBeneficiary : takenInChargeBeneficiaries) {
                        Beneficiary beneficiary = takenInChargeBeneficiary.getBeneficiary();
                        Person person = beneficiary.getPerson();
                        String typePriseEnChargeIdsList = person.getTypePriseEnChargeIdsList();
                        //here
                        if (typePriseEnChargeIdsList != null && (beneficiary.getBeneficiaryStatut().getId().equals(BeneficiaryStatus.CANDIDAT_A_UPDATER.getId()) || beneficiary.getBeneficiaryStatut().getId().equals(BeneficiaryStatus.BENEFICIAIRE_EN_ATTENTE.getId()) || beneficiary.getBeneficiaryStatut().getId().equals(BeneficiaryStatus.BENEFICIAIRE_ACTIF.getId())) ) {
                            List<String> idsList = Arrays.asList(typePriseEnChargeIdsList.split(","));

                            if (idsList.contains(aideComplementaire.getService().getServiceCategoryTypeId().toString())) {
                                Optional<AideComplementaireDonorBeneficiary> existingBenenficiryEntry = aideComplementaireDonorBeneficiaryRepository
                                        .findByAideComplementaireIdAndBeneficiaryIdAndDonorIsNull(aideComplementaire.getId() ,beneficiary.getId());


                                Optional<AideComplementaireDonorBeneficiary> existingBenenficiryWithDonorEntry = aideComplementaireDonorBeneficiaryRepository
                                        .findByAideComplementaireIdAndDonorIdAndBeneficiaryId(aideComplementaire.getId(), donor.getId(),beneficiary.getId());

                                if (existingBenenficiryWithDonorEntry.isPresent()) {
                                    continue;
                                } else if (existingBenenficiryEntry.isPresent()) {
                                    AideComplementaireDonorBeneficiary beneficiary1 = existingBenenficiryEntry.get();
                                    beneficiary1.setDonor(donor);
                                    beneficiary1.setStatutValidation(false);
                                    aideComplementaireDonorBeneficiaryRepository.save(beneficiary1);
                                } else {
                                    AideComplementaireDonorBeneficiary newEntryWithBeneficiary = AideComplementaireDonorBeneficiary.builder()
                                            .aideComplementaire(aideComplementaire)
                                            .donor(donor)
                                            .beneficiary(beneficiary)
                                            .montantAffecter(amountPerBeneficiary > 0.0 ? amountPerBeneficiary : aideComplementaire.getAmountPerBeneficiary())
                                            .statutValidation(false)
                                            .build();

                                    aideComplementaireDonorBeneficiaryRepository.save(newEntryWithBeneficiary);
                                }
                            }
                        }
                    }
                }
            }
        }
    }


    private void releaseBudgetLinesByDonor(Long aideComplementaireId,Long donorId, Long serviceId,boolean isNature,boolean isNotReserveAll){
        List<Donation> donationList = donationRepository.findByDonorId(donorId);
        List<BudgetLine> budgetLineList = new ArrayList<>();
        for (Donation donation : donationList){
            List<BudgetLine> budgetLines=new ArrayList<>();
            if(isNotReserveAll){
                budgetLines = budgetLineRepository.findByDonationIdAndServiceIdAndStatusAndNatureBudgetLine(donation.getId(), serviceId, BudgetLineStatus.RESERVED,isNature);

            }
            else{
                budgetLines = budgetLineRepository.findByDonationIdAndServiceIdAndStatus(donation.getId(), serviceId, BudgetLineStatus.RESERVED);

            }

            budgetLineList.addAll(budgetLines);
        }

        for (BudgetLine budgetLine : budgetLineList){
            budgetLine.setStatus(BudgetLineStatus.DISPONIBLE);
            budgetLine.setAideComplementaire(null);
            budgetLineRepository.save(budgetLine);
        }


        List<AideComplementaireDonorBeneficiary> donors = aideComplementaireDonorBeneficiaryRepository
                .findAideComplementaireDonorBeneficiariesByDonorIdAndAideComplementaireId(donorId, aideComplementaireId);
        aideComplementaireDonorBeneficiaryRepository.deleteAll(donors);


        for (Donation donation : donationList) {
            List<BudgetLine> budgetLines=new ArrayList<>();
            if(isNotReserveAll){
                budgetLines = budgetLineRepository.findByDonationIdAndServiceIdAndStatusAndNatureBudgetLine(
                        donation.getId(), serviceId, BudgetLineStatus.DISPONIBLE,isNature
                );
            }
            else{
                budgetLines = budgetLineRepository.findByDonationIdAndServiceIdAndStatus(
                        donation.getId(), serviceId, BudgetLineStatus.DISPONIBLE
                );
            }


            if (budgetLines.size() > 1) {
                BudgetLine oldestBudgetLine = budgetLines.stream()
                        .min(Comparator.comparing(BudgetLine::getCreatedAt))
                        .orElseThrow();

                Double totalAmount = budgetLines.stream()
                        .mapToDouble(BudgetLine::getAmount)
                        .sum();

                oldestBudgetLine.setAmount(totalAmount);
                budgetLineRepository.save(oldestBudgetLine);

                List<Long> otherLineIds = budgetLines.stream()
                        .map(BudgetLine::getId)
                        .filter(id -> !id.equals(oldestBudgetLine.getId()))
                        .toList();

                budgetLineRepository.deleteAllById(otherLineIds);
            }
        }

    }


    @Transactional
    public void executeAideComplementaire(Long aideComplementaireId, Double validatedAmount) {
        AideComplementaire aideComplementaire = aideComplementaireRepository.findById(aideComplementaireId)
                .orElseThrow(() -> new EntityNotFoundException("Aide Complementaire not found with ID: " + aideComplementaireId));

        LocalDateTime dateExecution = LocalDateTime.now();
        aideComplementaire.setStatut(AideComplementaireStatut.EXECUTER.getValue());
        aideComplementaire.setMontantExecuter(validatedAmount);
        aideComplementaire.setDateExecution(dateExecution);

        aideComplementaireRepository.save(aideComplementaire);

        List<BudgetLine> reservedBudgetLines  = budgetLineRepository.findByAideComplementaireIdAndStatus(aideComplementaireId, BudgetLineStatus.RESERVED);

        reservedBudgetLines.forEach(budgetLine -> {
            budgetLine.setStatus(BudgetLineStatus.EXECUTED);
            budgetLine.setExecutionDate(aideComplementaire.getDateExecution());
        });

        budgetLineRepository.saveAll(reservedBudgetLines);
    }

    @Transactional
    public void unExecuteAideComplementaire(Long aideComplementaireId) {
        AideComplementaire aideComplementaire = aideComplementaireRepository.findById(aideComplementaireId)
                .orElseThrow(() -> new EntityNotFoundException("Aide Complementaire not found with ID: " + aideComplementaireId));


        aideComplementaire.setStatut(AideComplementaireStatut.ENCOURS.getValue());
        aideComplementaire.setMontantExecuter(null);
        aideComplementaire.setDateExecution(null);

        aideComplementaireRepository.save(aideComplementaire);

        List<BudgetLine> reservedBudgetLines  = budgetLineRepository.findByAideComplementaireIdAndStatus(aideComplementaireId, BudgetLineStatus.EXECUTED);
        reservedBudgetLines.forEach(budgetLine -> {
            budgetLine.setStatus(BudgetLineStatus.RESERVED);
            budgetLine.setExecutionDate(null);
        });

        budgetLineRepository.saveAll(reservedBudgetLines);
    }



    @Transactional
    public void closeAideComplementaire(DocumentAndEntityDto documentAndEntityDto) throws TechnicalException, IOException {
        AideComplementaire aideComplementaire = aideComplementaireRepository.findById(documentAndEntityDto.getEntityId())
                .orElseThrow(() -> new EntityNotFoundException("Aide Complementaire not found with ID: " + documentAndEntityDto.getEntityId()));

        DocumentDTO documentDTO = documentService.createDocumentAndAssignToEntity(documentAndEntityDto);
        Document document = documentMapper.documentToModelToModel(documentDTO);

        aideComplementaire.setDocument(document);
        aideComplementaire.setDateCloture(LocalDateTime.now());
        aideComplementaire.setStatut(AideComplementaireStatut.CLOTURER.getValue());
        aideComplementaireRepository.save(aideComplementaire);

    }



    public List<AideComplementaireListDto> loadAllAideComplementaire() {
        List<AideComplementaire> aideComplementaires = aideComplementaireRepository.findAll();
        return aideComplementaires.stream()
                .map(aideComplementaireMapper::toListDto)
                .collect(Collectors.toList());
    }


    @Transactional
    public void deleteAideComplementaire(Long id) {
        Optional<AideComplementaire> optionalAideComplementaire = aideComplementaireRepository.findById(id);
        if (optionalAideComplementaire.isEmpty()) {
            throw new EntityNotFoundException("AideComplementaire not found with id: " + id);
        }
        AideComplementaire aideComplementaire = optionalAideComplementaire.get();

        List<AideComplementaireDonorBeneficiary> donorBeneficiaries = aideComplementaireDonorBeneficiaryRepository.findByAideComplementaire_Id(id);
        aideComplementaireDonorBeneficiaryRepository.deleteAll(donorBeneficiaries);

        List<BudgetLine> budgetLines = budgetLineRepository.findByAideComplementaireId(id);
        for (BudgetLine budgetLine : budgetLines) {
            if (budgetLine.getStatus() == BudgetLineStatus.RESERVED) {
                budgetLine.setStatus(BudgetLineStatus.DISPONIBLE);
                budgetLine.setAideComplementaire(null);
                budgetLine.setMontantReserve(null);
                budgetLineRepository.save(budgetLine);
            }
        }

        aideComplementaireRepository.delete(aideComplementaire);
    }

    @Transactional
    public GroupeAndBeneficiariesDTO getGroupeAdHocBeneficiries(Long aideComplementaireId, Long groupeId) {
        GroupeAndBeneficiariesDTO groupeDTO = new GroupeAndBeneficiariesDTO();
        List<BeneficiaryForGroupeDTO> beneficiaryDTOs = new ArrayList<>();

        List<AideComplementaireBeneficiaryAdHocGroupe> entities = aideComplementaireBeneficiaryAdHocGroupeRepository
                .findByAideComplementaire_IdAndBeneficiaryAdHocGroupId(aideComplementaireId, groupeId);

        for (AideComplementaireBeneficiaryAdHocGroupe entity : entities) {
            if (entity.getBeneficiary() == null) {
                groupeDTO.setId(entity.getBeneficiaryAdHocGroup().getId());
                groupeDTO.setCode(entity.getBeneficiaryAdHocGroup().getCode());
                groupeDTO.setName(entity.getBeneficiaryAdHocGroup().getName());
                groupeDTO.setMontantAffecter(entity.getMontantAffecter());
            } else {
                BeneficiaryForGroupeDTO beneficiaryDTO = BeneficiaryForGroupeDTO.builder()
                        .id(entity.getBeneficiary().getId())
                        .code(entity.getBeneficiary().getCode())
                        .fullName(entity.getBeneficiary().getPerson().getFirstName() + " " + entity.getBeneficiary().getPerson().getLastName())
                        .identityCode(entity.getBeneficiary().getPerson().getIdentityCode())
                        .montantAffecter(entity.getMontantAffecter())
                        .build();
                beneficiaryDTOs.add(beneficiaryDTO);
            }
        }

        groupeDTO.setBeneficiaries(beneficiaryDTOs);

        return groupeDTO;
    }

}
