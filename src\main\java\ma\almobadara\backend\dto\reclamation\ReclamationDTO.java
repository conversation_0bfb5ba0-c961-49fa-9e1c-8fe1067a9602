package ma.almobadara.backend.dto.reclamation;

import jakarta.persistence.Column;
import jakarta.persistence.Enumerated;
import jakarta.persistence.EnumType;
import lombok.*;
import ma.almobadara.backend.dto.donor.DonorDTO;
import ma.almobadara.backend.enumeration.ReclamationStatus;

import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ReclamationDTO {

    private Long id;
    private String title;
    private String description;
    private LocalDateTime createdAt;
    private Long donorId;
    private String donorName; // Full name of the donor
    private DonorDTO donor; // Optional, for when you want to include donor details
    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private ReclamationStatus status = ReclamationStatus.ENVOYE; // Default status is ENVOYE

    @Column(name = "response", columnDefinition = "TEXT")
    private String response;

    @Column(name = "responded_by")
    private String respondedBy;

    @Column(name = "responded_at")
    private LocalDateTime respondedAt;

}
