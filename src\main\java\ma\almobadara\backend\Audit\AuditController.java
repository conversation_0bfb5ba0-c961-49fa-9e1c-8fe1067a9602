package ma.almobadara.backend.Audit;

import ma.almobadara.backend.Audit.entities.AuditEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/audit")
public class AuditController {

    @Autowired
    private AuditApplicationService auditApplicationService;

    @GetMapping(value = "/all")
    public Page<AuditEntity> getAllAudits(@RequestParam(defaultValue = "0") int page,
                                          @RequestParam(defaultValue = "10") int size,
                                          @RequestParam(required = false) final String searchByValue,
                                          @RequestParam(required = false) final String searchByOperation,
                                          @RequestParam(required = false) final String searchBySubOperation,
                                          @RequestParam(required = false) final String searchByUser,
                                          @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") final Date minDate,
                                          @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") final Date maxDate) {
        return auditApplicationService.getAllAudits(page, size,searchByValue, searchByOperation, searchBySubOperation, searchByUser, minDate, maxDate);
    }


    @DeleteMapping("/purge")
    public ResponseEntity<Void> purgeAuditEntries(
            @RequestParam(required = false) String operation,
            @RequestParam(required = false) String subOperation,
            @RequestParam(required = false) String userName,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate) {

        auditApplicationService.purgeAuditEntries(operation, subOperation, userName, startDate, endDate);
        return ResponseEntity.noContent().build();
    }
}