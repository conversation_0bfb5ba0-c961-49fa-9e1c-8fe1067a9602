package ma.almobadara.backend.dto.administration;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import ma.almobadara.backend.dto.CityWithRegionAndCountryDTO;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EpsListDTO {
    private Long id;
    private String code;
    private String name;
    private String nameAr;
    private String address;
    private String addressAr;
    private boolean isDeleted;
    private boolean status;
    private String beneficiaryType;
    private Long ageGroupIds;
    private String comment;
    private List<Long> cityIds;
    private List<CityWithRegionAndCountryDTO> cityDetails;
    private Long capacity;
    private Long beneficiariesCount;

    private LocalDateTime creationDate;
    private LocalDateTime updateDate;

    private ZoneDTOLight zone;

    private List<TagDTO> tags;
}

