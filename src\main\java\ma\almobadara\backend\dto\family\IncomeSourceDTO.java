package ma.almobadara.backend.dto.family;

import lombok.*;
import org.springframework.hateoas.RepresentationModel;

import java.io.Serializable;


@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class IncomeSourceDTO extends RepresentationModel<IncomeSourceDTO> implements Serializable {

	private static final long serialVersionUID = -5865536032247969990L;

	private Long id;

	private String name;
}
