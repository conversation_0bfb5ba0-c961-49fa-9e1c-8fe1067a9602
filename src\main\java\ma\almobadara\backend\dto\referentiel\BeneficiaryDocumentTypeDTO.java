package ma.almobadara.backend.dto.referentiel;

import lombok.*;
import org.springframework.hateoas.RepresentationModel;

import java.io.Serializable;

@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class BeneficiaryDocumentTypeDTO extends RepresentationModel<HonorDTO> implements Serializable {

	private static final long serialVersionUID = 1967786087551932601L;

	private Long id;
	private String code;
	private String name;
	private String nameAr;
	private String nameEn;

	private String folderName;


}
