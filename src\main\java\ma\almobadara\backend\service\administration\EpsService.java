package ma.almobadara.backend.service.administration;

import com.google.gson.Gson;
import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.Audit.AuditApplicationService;
import ma.almobadara.backend.controller.referentiel.RefController;
import ma.almobadara.backend.dto.CityWithRegionAndCountryDTO;
import ma.almobadara.backend.dto.administration.*;
import ma.almobadara.backend.dto.beneficiary.EpsBeneficiaryDTO;
import ma.almobadara.backend.dto.referentiel.CategoryBeneficiaryDTO;
import ma.almobadara.backend.dto.referentiel.DonorContactFunctionDTO;
import ma.almobadara.backend.enumeration.AgeGroup;
import ma.almobadara.backend.dto.administration.EpsDto;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.mapper.EpsMapper;
import ma.almobadara.backend.mapper.ZoneMapper;
import ma.almobadara.backend.model.administration.Tag;
import ma.almobadara.backend.model.administration.Taggable;
import ma.almobadara.backend.model.administration.Zone;
import ma.almobadara.backend.model.beneficiary.Beneficiary;
import ma.almobadara.backend.model.administration.Eps;
import ma.almobadara.backend.model.family.Family;
import ma.almobadara.backend.model.family.FamilyMember;
import ma.almobadara.backend.repository.administration.EpsRepository;

import ma.almobadara.backend.repository.administration.TaggableRepository;
import ma.almobadara.backend.repository.beneficiary.BeneficiaryRepository;
import ma.almobadara.backend.util.times.TimeWatch;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static ma.almobadara.backend.service.administration.CacheAdUserService.getUsernameFromJwt;
import static ma.almobadara.backend.util.constants.GlobalConstants.*;

@Service
@AllArgsConstructor
@Slf4j
public class EpsService {

    private final EpsRepository epsRepository;
    private final EpsMapper epsMapper;
    private final ZoneMapper zoneMapper;
    private final RefController refController;
    private final BeneficiaryRepository beneficiaryRepository;
    private final EntityManager entityManager;
    private final AuditApplicationService auditApplicationService;
    private final TaggableRepository taggableRepository;

    public EpsDto createEps(EpsDto epsDto) throws TechnicalException {

        Eps eps = epsMapper.epsDTOToEps(epsDto);
        String epsOldAuditString = null;
        String zoneName = null;
        if (epsDto.getId() == null) {
            eps.setStatus(false);
            eps.setCode(generateEpsCode());
        } else {
            Eps existingEps = epsRepository.findById(epsDto.getId()).orElseThrow();

            eps.setCode(existingEps.getCode());
            eps.setStatus(existingEps.isStatus());
            String ageGroups = existingEps.getAgeGroupIdList().stream()
                    .map(AgeGroup::fromId)
                    .map(AgeGroup::getLabel)
                    .collect(Collectors.joining(", "));

            zoneName = existingEps.getZone() != null ? existingEps.getZone().getName() : null;
            epsOldAuditString = existingEps.toAuditString(zoneName, ageGroups);
        }


        eps.setCityIdList(epsDto.getCityIds());
        eps.setAgeGroupIdList(epsDto.getAgeGroupIds());

        Eps savedEps = epsRepository.save(eps);



        EpsDto epsDto1 = epsMapper.epsToEpsDTO(savedEps);
        epsDto1.setCityIds(savedEps.getCityIdList());
        epsDto1.setAgeGroupIds(savedEps.getAgeGroupIdList());
        epsDto1.setCode(savedEps.getCode());

        String ageGroups = savedEps.getAgeGroupIdList().stream()
                .map(AgeGroup::fromId)
                .map(AgeGroup::getLabel)
                .collect(Collectors.joining(", "));

        String epsAuditString = savedEps.toAuditString(zoneName, ageGroups);

        taggableRepository.deleteAllByTaggableIdAndTaggableType(epsDto.getId(),"eps");
        if(epsDto.getTags()!=null){

            for (TagDTO tag : epsDto.getTags()) {
                Taggable taggable = new Taggable();
                Tag tag1=new Tag();
                taggable.setTaggableId(savedEps.getId());
                taggable.setTaggableType("eps");
                tag1.setId(tag.getId());
                taggable.setTag(tag1);
                taggableRepository.save(taggable);
            }
        }

        if (epsDto.getId() == null) {
            auditApplicationService.audit(
                    "Ajouter d'une  EPS : " + eps.getCode(),
                    getUsernameFromJwt(),
                    "Consult EPS",
                    null,
                    epsAuditString,
                    EPS,
                    CREATE
            );
        } else {
            auditApplicationService.audit(
                    "modification d'une  EPS : " + eps.getCode(),
                    getUsernameFromJwt(),
                    "Consult EPS",
                    epsOldAuditString,
                    epsAuditString,
                    EPS,
                    UPDATE
            );
        }

        return epsDto1;
    }

    public boolean deleteEps(Long id) throws TechnicalException {
        if (epsRepository.existsById(id)) {
            Eps eps = epsRepository.getOne(id);
            FicheEpsDTO ficheEpsDTO = epsMapper.epsToFicheEpsDTO(eps);
            //Audit
            List<Long> ageGroupIds = eps.getAgeGroupIdList();

            String ageGroups = ageGroupIds.stream()
                    .map(AgeGroup::fromId)
                    .map(AgeGroup::getLabel)
                    .collect(Collectors.joining(", "));

            String zoneName = eps.getZone() != null ? eps.getZone().getName() : null;

            String epsAudit = eps.toAuditString(zoneName, ageGroups);
            log.info("EPS audit string: {}", epsAudit);
            auditApplicationService.audit(
                    "Suppresion de l'EPS : " + eps.getCode(),
                    getUsernameFromJwt(),
                    "Suppression EPS",
                    epsAudit,
                    null,
                    EPS,
                    DELETE
            );
            // Soft delete the zone
            eps.setDeleted(true);
            epsRepository.save(eps);
            return true;
        }
        return false;
    }

    public EpsDto FindEpsByIdForAdd(Long id) throws TechnicalException {
        Optional<Eps> optionalEps = epsRepository.findById(id);
        if (optionalEps.isPresent()) {
            EpsDto epsDto = epsMapper.epsToEpsDTO(optionalEps.get());
            epsDto.setCityIds(optionalEps.get().getCityIdList());
            epsDto.setAgeGroupIds(optionalEps.get().getAgeGroupIdList());
            epsDto.setCode(optionalEps.get().getCode());
            List<Taggable> taggables=taggableRepository.findByTaggableIdAndTaggableType(epsDto.getId(),"eps");
            List<TagDTO> tags = new ArrayList<>();
            for (Taggable taggable : taggables) {
                TagDTO tagDTO = new TagDTO();
                tagDTO.setId(taggable.getTag().getId());
                tagDTO.setName(taggable.getTag().getName());
                tagDTO.setColor(taggable.getTag().getColor());
                tags.add(tagDTO);
            }
            epsDto.setTags(tags);
            return epsDto;
        } else {
            throw new TechnicalException("Eps not found");
        }
    }


    public String generateEpsCode() {
        // Fetch the latest zone code
        Eps lastEps = epsRepository.findFirstByOrderByCodeDesc();
        String lastCode = (lastEps != null) ? lastEps.getCode() : null;

        // Get the current year
        String currentYear = String.valueOf(LocalDateTime.now().getYear());
        String newCode;

        if (lastCode != null && lastCode.substring(3, 7).equals(currentYear)) {
            // Extract and increment the last number
            int lastNumber = Integer.parseInt(lastCode.substring(7));
            newCode = String.format("EPS%s%03d", currentYear, lastNumber + 1);
        } else {
            // Start with 001 if no code exists for the current year
            newCode = String.format("EPS%s001", currentYear);
        }

        return newCode;
    }


    public FicheEpsDTO loadFicheEps(Long epsId) {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service loadFicheEps: {}", epsId);

        Eps eps = epsRepository.findById(epsId)
                .orElseThrow(() -> new IllegalArgumentException("Eps entity with ID " + epsId + " not found"));

        FicheEpsDTO ficheEpsDTO = epsMapper.epsToFicheEpsDTO(eps);

        Optional.ofNullable(eps.getZone())
                .map(zoneMapper::zoneToZoneDTOLight)
                .ifPresent(ficheEpsDTO::setZone);

        // about the age group it a string of the ids of the age group
        List<Long> ageGroupIds = eps.getAgeGroupIdList();

        String ageGroups = ageGroupIds.stream()
                .map(AgeGroup::fromId)
                .map(AgeGroup::getLabel)
                .collect(Collectors.joining(", "));
        ficheEpsDTO.setAgeGroups(ageGroups);


        List<Long> cityIds = eps.getCityIdList();
        if (cityIds != null && !cityIds.isEmpty()) {
            ficheEpsDTO.setCityDetails(cityIds.stream()
                    .map(id -> refController.getCityWithRegionAndCountry(id).getBody())
                    .collect(Collectors.toList()));
        } else {
            log.warn("No city IDs found for Eps ID: {}", eps.getId());
        }

        // Audit
        Eps epsAudit = epsMapper.ficheEpsDTOToEps(ficheEpsDTO);
        String zoneName = eps.getZone() != null ? eps.getZone().getName() : null;

        String epsAuditString = epsAudit.toAuditString(zoneName, ageGroups);
        log.info("EPS audit string: {}", epsAuditString);
        auditApplicationService.audit(
                "Consultation de la Fiche EPS : " + eps.getCode(),
                getUsernameFromJwt(),
                "Consult EPS",
                epsAuditString,
                null,
                EPS,
                CONSULTATION
        );

        log.debug("End service loadFicheEps: {}, took {}", epsId, watch.toMS());
        return ficheEpsDTO;
    }

    public Page<EpsListDTO> getAllEps(int page, int size, String searchByName, String searchByNameAr, String searchByStatus, Long searchByMinBenificiary, Long searchByMaxBenificiary, String searchByCity,Long searchByTagId) {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service getAllZones with page: {}, size: {}, searchByCode: {}, searchByAssistantName: {}, searchByName: {}, searchByNameAr: {}, searchByStatus: {} , searchByCity: {}",
                page, size, searchByName, searchByNameAr, searchByStatus, searchByCity);

        Pageable pageable = PageRequest.of(page, size);
        Page<Eps> epsList;
        if (searchByName != null ||
                searchByNameAr != null || searchByStatus != null || searchByCity != null || searchByMinBenificiary != null || searchByMaxBenificiary != null || searchByTagId != null) {
            epsList = filterEps(searchByName, searchByNameAr, searchByStatus, searchByCity,searchByTagId, pageable);
            // Apply the beneficiaries filter
            List<Eps> filteredListMin = epsList.getContent().stream()
                    .filter(eps -> {
                        if (eps.getZone() != null && eps.getZone().getBeneficiaries() != null) {
                            log.info("Beneficiaries count: {}", eps.getZone().getBeneficiaries().size());
                            int beneficiarySize = eps.getZone().getBeneficiaries().size();

                            // Apply min filter if it's set
                            boolean minFilterValid = searchByMinBenificiary == null || beneficiarySize >= searchByMinBenificiary;

                            // Apply max filter if it's set
                            boolean maxFilterValid = searchByMaxBenificiary == null || beneficiarySize <= searchByMaxBenificiary;

                            // If both filters are set, apply both. Otherwise, apply only the non-null filter
                            return minFilterValid && maxFilterValid;
                        } else {
                            // If beneficiaries are null, we assume 0 beneficiaries
                            boolean minFilterValid = searchByMinBenificiary == null || 0 >= searchByMinBenificiary;
                            boolean maxFilterValid = searchByMaxBenificiary == null || 0 <= searchByMaxBenificiary;

                            return minFilterValid && maxFilterValid;
                        }
                    })
                    .collect(Collectors.toList());

            // Create a new Page object with the filtered list
            epsList = new PageImpl<>(filteredListMin, pageable, epsList.getTotalElements());
            log.info("Filtered EPS list size: {}", epsList.getTotalElements());
        } else {
            epsList = epsRepository.findAllWithDeletedIsFalse(pageable);

        }


        List<EpsListDTO> epsDTOs = epsList.getContent().stream()
                .map(epsMapper::epsToEpsListDTO)
                .collect(Collectors.toList());

        for (int i = 0; i < epsDTOs.size(); i++) {
            EpsListDTO epsListDTO = epsDTOs.get(i);
            Eps eps = epsList.getContent().get(i);

            if (eps.getZone() != null && !eps.getZone().getBeneficiaries().isEmpty()) {
                epsListDTO.setBeneficiariesCount((long) eps.getZone().getBeneficiaries().size());
                log.info("Beneficiaries count: {}", epsListDTO.getBeneficiariesCount());
            }
            // Populate detailed city information
            processCityDetails(epsListDTO);

            // Populate detailed zone information
            Optional.ofNullable(eps.getZone())
                    .map(zoneMapper::zoneToZoneDTOLight)
                    .ifPresent(epsListDTO::setZone);
        }

        // Populate detailed city information
        epsDTOs.forEach(this::processCityDetails);

        Map<String, String> searchParams = new LinkedHashMap<>();
        if (searchByName != null) {
            searchParams.put("Nom d'EPS", searchByName);
        }
        if (searchByNameAr != null) {
            searchParams.put("Nom arabe d'EPS'", searchByNameAr);
        }
        if (searchByStatus != null) {
            searchParams.put("Status", searchByStatus);
        }
        if (searchByCity != null) {
            searchParams.put("Ville", searchByCity);
        }
        if (searchByMinBenificiary != null) {
            searchParams.put("Minimum nombre de beneficiaires", searchByMinBenificiary.toString());
        }
        if (searchByMaxBenificiary != null) {
            searchParams.put("Maximum nombre de beneficiaires", searchByMaxBenificiary.toString());
        }
        Gson gson = new Gson();

        String jsonSearchParams = gson.toJson(searchParams);

        if (searchByName != null || searchByNameAr != null || searchByStatus != null ||
                searchByCity != null || searchByMinBenificiary != null ||
                searchByMaxBenificiary != null) {
            auditApplicationService.audit("Recherche par filtre dans la liste des Eps",
                    getUsernameFromJwt(),
                    "Liste des Eps",
                    jsonSearchParams,
                    null,
                    EPS,
                    VIEW);
        } else {
            auditApplicationService.audit(
                    "Consultation de la liste des EPS",
                    getUsernameFromJwt(),
                    "Consult des EPS",
                    null,
                    null,
                    EPS,
                    CONSULTATION
            );
        }

        epsDTOs= epsDTOs.stream().peek(dto->{
            List<Taggable> taggables = taggableRepository.findByTaggableIdAndTaggableType(dto.getId(), "eps");
            List<TagDTO> tagDTOs = new ArrayList<>();
            for (Taggable taggable : taggables) {
                Tag tag = taggable.getTag();
                TagDTO tagDTO = new TagDTO();
                tagDTO.setId(tag.getId());
                tagDTO.setName(tag.getName());
                tagDTO.setColor(tag.getColor());
                tagDTOs.add(tagDTO);
            }
            dto.setTags(tagDTOs);
        }).collect(Collectors.toList());
        log.debug("End service getAllZones with {} zones found, took {}", epsList.getTotalElements(), watch.toMS());
        return new PageImpl<>(epsDTOs, pageable, epsList.getTotalElements());
    }

    public List<EpsListDTO> getAllEpsList() {
        List<Eps> epsList = epsRepository.findAllByIsDeletedIsFalse();
        List<EpsListDTO> epsListDTOs = epsList.stream()
                .map(epsMapper::epsToEpsListDTO)
                .collect(Collectors.toList());

        // Populate detailed city information
        epsListDTOs.forEach(this::processCityDetails);

        return epsListDTOs;
    }

    private Page<Eps> filterEps(String searchByName, String searchByNameAr, String searchByStatus, String searchByCity,Long searchByEps, Pageable pageable) {
        log.debug("Start service filterEps with searchByName: {}, searchByNameAr: {}, searchByStatus: {}",
                searchByName, searchByNameAr, searchByStatus);

        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Eps> criteriaQuery = criteriaBuilder.createQuery(Eps.class);
        Root<Eps> rootEps = criteriaQuery.from(Eps.class);
        Predicate predicate = buildPredicate(criteriaBuilder, rootEps, criteriaQuery, searchByName, searchByNameAr, searchByStatus, searchByCity,searchByEps);
        criteriaQuery.where(predicate);

        TypedQuery<Eps> typedQuery = entityManager.createQuery(criteriaQuery);
        long totalCount = typedQuery.getResultList().size();
        typedQuery.setFirstResult((int) pageable.getOffset());
        typedQuery.setMaxResults(pageable.getPageSize());
        List<Eps> resultList = typedQuery.getResultList();
        log.debug("End service filterZones with {} zones found", totalCount);
        return new PageImpl<>(resultList, pageable, totalCount);
    }

    private Predicate buildPredicate(CriteriaBuilder criteriaBuilder, Root<Eps> root, CriteriaQuery<?> criteriaQuery,
                                     String searchByName, String searchByNameAr, String searchByStatus, String searchByCity,Long searchByTagId) {
        Predicate predicate = criteriaBuilder.conjunction();
        predicate = criteriaBuilder.and(predicate, criteriaBuilder.isFalse(root.get("isDeleted")));

        if (searchByName != null) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(criteriaBuilder.lower(root.get("name")),
                    "%" + searchByName.toLowerCase() + "%"));
        }

        if (searchByNameAr != null) {
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(criteriaBuilder.lower(root.get("nameAr")),
                    "%" + searchByNameAr.toLowerCase() + "%"));
        }

        if (searchByStatus != null) {
            // the actif  is just have active ( the zone that have a assistant)
            log.info("searchByStatus value : " + searchByStatus);
            if (searchByStatus.equals("Actif")) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.isTrue(root.get("status")));
            } else if (searchByStatus.equals("Inactif")) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.isFalse(root.get("status")));
            }
        }

        if (searchByCity != null) {

            predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(criteriaBuilder.lower(root.get("cityIds")),
                    "%" + searchByCity + "%"));
        }
        if (searchByTagId != null) {
            // Create a subquery to find donors with the specified tag
            Subquery<Long> subquery = criteriaBuilder.createQuery().subquery(Long.class);
            Root<Taggable> taggableRoot = subquery.from(Taggable.class);
            subquery.select(taggableRoot.get("taggableId"))
                    .where(
                            criteriaBuilder.and(
                                    criteriaBuilder.equal(taggableRoot.get("taggableType"), "eps"),
                                    criteriaBuilder.equal(taggableRoot.get("tag").get("id"), searchByTagId)
                            )
                    );

            // Add the subquery condition to the main predicate
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.in(root.get("id")).value(subquery));
        }

        return predicate;
    }


    private void processCityDetails(EpsListDTO epsListDTO) {
        List<Long> cityIds = epsListDTO.getCityIds();
        if (cityIds != null && !cityIds.isEmpty()) {
            List<CityWithRegionAndCountryDTO> cityDetails = cityIds.stream()
                    .map(this::fetchCityDetails)
                    .collect(Collectors.toList());
            epsListDTO.setCityDetails(cityDetails); // Assuming you have a method to set detailed city info
        }
    }

    public CityWithRegionAndCountryDTO fetchCityDetails(Long cityId) {
        // Implement the logic to fetch city details from the referential service
        return refController.getCityWithRegionAndCountry(cityId).getBody();
    }

    public List<CityWithRegionAndCountryDTO> getCityOfMorroco() {
        return refController.getCitiesByCountry(149L).getBody();

    }

    public List<EpsBeneficiaryDTO> getBeneficiariesByEps(Long epsId) {
        Eps eps = epsRepository.findById(epsId)
                .orElseThrow(() -> new IllegalArgumentException("Eps entity with ID " + epsId + " not found"));

        Zone zone = eps.getZone();
        if (zone == null) {
            log.warn("No zone associated with EPS ID: {}", epsId);
            return Collections.emptyList();
        }

        List<Beneficiary> beneficiaries = beneficiaryRepository.findByZone(zone);
        Set<Long> validStatusIds = new HashSet<>(Arrays.asList(5L, 6L, 7L, 10L,13L));

        List<Beneficiary> filteredBeneficiaries = beneficiaries.stream()
                .filter(beneficiary -> validStatusIds.contains(beneficiary.getBeneficiaryStatut().getId()))
                .toList();

        return filteredBeneficiaries.stream()
                .map(beneficiary -> {
                    EpsBeneficiaryDTO dto = epsMapper.beneficiaryToEpsBeneficiaryDTO(beneficiary);
                    if (beneficiary.getPerson() != null && beneficiary.getPerson().getCategoryBeneficiaryId() != null) {
                        CategoryBeneficiaryDTO categoryDTO = refController
                                .getCategoryBeneficiary(beneficiary.getPerson().getCategoryBeneficiaryId())
                                .getBody();
                        if (categoryDTO != null) {
                            dto.setCategory(categoryDTO.getName());
                        }
                    }
                    return dto;
                })
                .collect(Collectors.toList());
    }

    public List<EpsLightDTO> loadActifEps() {
        TimeWatch watch = TimeWatch.start();
        log.debug("Start service loadActifEps");

        List<Eps> activeEpsList = epsRepository.findByStatusTrueAndIsDeletedFalse();

        List<EpsLightDTO> epsLightList = activeEpsList.stream()
                .map(epsMapper::epsToEpsLightListDTO)
                .collect(Collectors.toList());



        log.debug("End service loadActifEps, took {}", watch.toMS());
        return epsLightList;
    }

    public EpsListDTO changeEpsStatus(Long epsId, boolean newStatus) throws TechnicalException {
        // Récupère la zone par son ID
        Eps eps = epsRepository.findById(epsId)
                .orElseThrow(() -> new TechnicalException("Eps with ID " + epsId + " does not exist"));

        // Met à jour le statut de la zone
        eps.setStatus(newStatus);
        eps.setUpdateDate(LocalDateTime.now());  // Met à jour le champ `updatedAt`
        Eps updatedEps = epsRepository.save(eps);

        // Retourne la zone mise à jour en tant que DTO
        return epsMapper.epsToEpsListDTO(updatedEps);
    }

}
