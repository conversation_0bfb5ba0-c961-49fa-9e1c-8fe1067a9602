package ma.almobadara.backend.repository.communs;
import ma.almobadara.backend.model.family.Family;
import ma.almobadara.backend.model.family.NoteFamily;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface NoteFamilyRepository extends JpaRepository<NoteFamily, Long> {

    List<NoteFamily> findByFamily(Family family);

    List<NoteFamily> findByNoteId(Long id);

    List<NoteFamily> findByFamilyId(Long familyId);
}
