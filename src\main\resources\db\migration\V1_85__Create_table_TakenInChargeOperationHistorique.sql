CREATE TABLE taken_in_charge_operation_historique
(
    id           BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    action       VARCHAR(255) NOT NULL,
    amount       DOUBLE PRECISION,
    user_name     VARCHAR(255),
    operation_id BIGINT       NOT NULL,
    management_fees DOUBLE PRECISION,
    created_at   TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    comment      VARCHAR(255),
    CONSTRAINT fk_operation FOREIGN KEY (operation_id) REFERENCES taken_in_charge_operation (id) ON DELETE CASCADE
);
