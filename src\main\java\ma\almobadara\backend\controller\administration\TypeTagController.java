package ma.almobadara.backend.controller.administration;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.model.administration.TypeTag;
import ma.almobadara.backend.service.administration.TypeTagService;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/type-tag")
@AllArgsConstructor
@Slf4j
public class TypeTagController {

    private final TypeTagService typeTagService;

    /**
     * Get all type tags with pagination
     * @param page page number (default 0)
     * @param size page size (default 5)
     * @return a page of type tags
     */
    @GetMapping
    public ResponseEntity<List<TypeTag>> getAllTypeTags(
    ) {
        log.info("Start resource getAllTypeTags");
        List<TypeTag> typeTagList = typeTagService.getAllTypeTags();
        return ResponseEntity.ok(typeTagList);
    }

    /**
     * Get all type tags as a list
     * @return a list of type tags
     */
    @GetMapping("/list")
    public ResponseEntity<List<TypeTag>> getAllTypeTagList() {
        log.info("Start resource getAllTypeTagList");
        List<TypeTag> typeTagList = typeTagService.getTypeTagList();
        return ResponseEntity.ok(typeTagList);
    }

    /**
     * Get a type tag by its ID
     * @param id the type tag ID
     * @return the type tag if found
     */
    @GetMapping("/{id}")
    public ResponseEntity<TypeTag> getTypeTagById(@PathVariable Long id) {
        log.info("Start resource getTypeTagById: {}", id);
        Optional<TypeTag> typeTag = typeTagService.getTypeTagById(id);
        return typeTag.map(ResponseEntity::ok)
                .orElseGet(() -> ResponseEntity.notFound().build());
    }

    /**
     * Create a new type tag
     * @param typeTag the type tag data
     * @return the created type tag
     * @throws TechnicalException if there's an error during creation
     */
    @PostMapping
    public ResponseEntity<TypeTag> createTypeTag(@ModelAttribute TypeTag typeTag) throws TechnicalException {
        log.info("Start resource createTypeTag: {}", typeTag.getName());
        TypeTag createdTypeTag = typeTagService.createTypeTag(typeTag);
        return new ResponseEntity<>(createdTypeTag, HttpStatus.CREATED);
    }

    /**
     * Delete a type tag by its ID
     * @param id the type tag ID to delete
     * @return no content on success
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteTypeTag(@PathVariable Long id) {
        log.info("Start resource deleteTypeTag: {}", id);
        typeTagService.deleteTypeTag(id);
        return ResponseEntity.noContent().build();
    }
}
